# CREDITS

## Components, Libraries, Frameworks, Packages

This application uses Open Source components and 3rd party libraries, which are licensed under their own respective Open-Source licenses.
You can find the links to source code of their open source projects along with license information below.
We acknowledge and are grateful to these developers for their contributions to open source.

- [Nx](https://nx.dev), a set of Angular CLI power-ups for modern development, released under [MIT](https://github.com/nrwl/nx/blob/master/LICENSE), `Copyright (c) 2017-2019 Narwhal Technologies Inc.`

- [ngx-admin](https://github.com/akveo/ngx-admin), Angular Dashboard template, released under [MIT](https://github.com/akveo/ngx-admin/blob/master/LICENSE), `Copyright (c) 2017 akveo.com`

- [Nest](https://github.com/nestjs/nest), progressive Node.js framework, released under [MIT](https://github.com/nestjs/nest/blob/master/LICENSE), `Copyright (c) 2017 <PERSON><PERSON><PERSON>` <http://kamilmysliwiec.com>

- [ngx-starter-kit](https://github.com/xmlking/ngx-starter-kit), Angular NestJS Starter Kit, released under [MIT](https://github.com/xmlking/ngx-starter-kit/blob/develop/LICENSE), `Copyright (c) 2018 Sumanth Chinthagunta`

- [date-holidays](https://github.com/commenthol/date-holidays), which allows us to get world-wide holidays in gregorian calender. Copyright (c) 2016 - commenthol ([ISC License](http://opensource.org/licenses/ISC)). Note: The data contained in library `holidays.json` and `./data/countries/*.yaml` is available under [CC BY-SA 3.0](http://creativecommons.org/licenses/by-sa/3.0/) as the majority of data obtained relies on wikipedia articles. The required attribution can be found inside the files `./data/countries/*.yaml` of the library.

- [docker-compose-wait](https://github.com/ufoscout/docker-compose-wait), A small command-line utility to wait for other docker images to be started while using docker-compose. Released under [Apache-2.0 License](https://github.com/ufoscout/docker-compose-wait/blob/master/LICENSE).

## Fonts

- https://fonts.google.com/specimen/Crimson+Text ([OFL web license](https://scripts.sil.org/cms/scripts/page.php?site_id=nrsi&id=OFL_web))

## Sounds

- https://freesound.org/people/xef6/sounds/61059 licensed under [CC0 1.0 Universal](https://creativecommons.org/publicdomain/zero/1.0)
