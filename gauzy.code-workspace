{"folders": [{"path": "apps"}, {"path": "libs"}], "settings": {"typescript.tsdk": "./node_modules/typescript/lib", "debug.node.autoAttach": "off", "search.usePCRE2": true, "git.ignoreLimitWarning": true, "checkpoints.showActiveFileOnly": true, "deepscan.enable": true, "cSpell.words": ["Upwork", "timesheet", "toastr"]}, "launch": {"configurations": [{"type": "node", "request": "attach", "name": "Debug Server", "port": 9229, "restart": true, "cwd": "${workspaceFolder:apps}/.."}]}}