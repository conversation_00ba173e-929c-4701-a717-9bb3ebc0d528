import { Auth<PERSON><PERSON><PERSON><PERSON>and<PERSON> } from './auth.login.handler';
import { AuthRegisterHandler } from './auth.register.handler';
import { WorkspaceSigninSendCodeCommandHandler } from './workspace-signin-send-code.handler';
import { WorkspaceSigninVerifyTokenHandler } from './workspace-signin-verify-token.handler';

export const CommandHandlers = [
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>th<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    WorkspaceSigninSendCodeCommandHandler,
    WorkspaceSigninVerifyTokenHandler
];
