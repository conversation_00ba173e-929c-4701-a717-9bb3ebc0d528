import { CandidateBulkCreateHandler } from './candidate.bulk.create.handler';
import { CandidateCreateHandler } from './candidate.create.handler';
import { CandidateHiredHandler } from './candidate.hired.handler';
import { CandidateRejectedHandler } from './candidate.rejected.handler';
import { CandidateUpdateHandler } from './candidate.update.handler';

export const CommandHandlers = [
	Candidate<PERSON><PERSON>k<PERSON><PERSON><PERSON>and<PERSON>,
	CandidateCreateHand<PERSON>,
	CandidateUpdateHandler,
	CandidateHiredHandler,
	CandidateRejectedHandler
];
