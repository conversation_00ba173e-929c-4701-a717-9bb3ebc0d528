import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { RequestApprovalEmployee } from './request-approval-employee.entity';
import { MikroOrmModule } from '@mikro-orm/nestjs';

@Module({
	imports: [
		TypeOrmModule.forFeature([RequestApprovalEmployee]),
		MikroOrmModule.forFeature([RequestApprovalEmployee]),
	]
})
export class RequestApprovalEmployeeModule { }
