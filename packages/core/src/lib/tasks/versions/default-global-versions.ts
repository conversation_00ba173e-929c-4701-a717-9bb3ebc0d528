import { ITaskVersion } from '@gauzy/contracts';

export const DEFAULT_GLOBAL_VERSIONS: ITaskVersion[] = [
	{
		name: 'Version 1',
		value: 'Version 1',
		description: 'Version 1',
		icon: null,
		color: '#FFFFFF',
		isSystem: true,
	},
	{
		name: 'Version 2',
		value: 'Version 2',
		description: 'Version 2',
		icon: null,
		color: '#FFFFFF',
		isSystem: true,
	},
	{
		name: 'Version 3',
		value: 'Version 3',
		description: 'Version 3',
		icon: null,
		color: '#FFFFFF',
		isSystem: true,
	},
	{
		name: 'Version 4',
		value: 'Version 4',
		description: 'Version 4',
		icon: null,
		color: '#FFFFFF',
		isSystem: true,
	},
];
