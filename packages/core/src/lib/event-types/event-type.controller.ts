import { IEventType, IEventTypeCreateInput, IPagination } from '@gauzy/contracts';
import { Body, Controller, Get, HttpCode, HttpStatus, Param, Put, Post, Query, UseGuards } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';
import { FindOptionsWhere } from 'typeorm';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { CrudController, BaseQueryDTO } from './../core/crud';
import { ParseJsonPipe, UUIDValidationPipe, UseValidationPipe } from './../shared/pipes';
import { TenantPermissionGuard } from './../shared/guards';
import { EventTypeCreateCommand } from './commands';
import { EventType } from './event-type.entity';
import { EventTypeService } from './event-type.service';

@ApiTags('EventType')
@UseGuards(TenantPermissionGuard)
@Controller('/event-type')
export class EventTypeController extends Crud<PERSON>ontroller<EventType> {
	constructor(private readonly eventTypeService: EventTypeService, private readonly commandBus: CommandBus) {
		super(eventTypeService);
	}

	/**
	 * GET event types counts
	 *
	 * @param filter
	 * @returns
	 */
	@Get('count')
	@UseValidationPipe({ transform: true })
	async getCount(@Query() options: FindOptionsWhere<EventType>): Promise<number> {
		return this.eventTypeService.countBy(options);
	}

	/**
	 * GET event types pagination
	 *
	 * @param filter
	 * @returns
	 */
	@Get('pagination')
	@UseValidationPipe({ transform: true })
	async pagination(@Query() filter: BaseQueryDTO<EventType>): Promise<IPagination<IEventType>> {
		return this.eventTypeService.paginate(filter);
	}

	/**
	 * GET all event types
	 *
	 * @param data
	 * @returns
	 */
	@ApiOperation({ summary: 'Find all event types' })
	@ApiResponse({
		status: HttpStatus.OK,
		description: 'Found expense',
		type: EventType
	})
	@ApiResponse({
		status: HttpStatus.NOT_FOUND,
		description: 'Record not found'
	})
	@Get()
	async findAll(@Query('data', ParseJsonPipe) data: any): Promise<IPagination<IEventType>> {
		const { relations, findInput } = data;
		return this.eventTypeService.findAll({
			where: findInput,
			relations
		});
	}

	/**
	 * GET event type by id
	 *
	 * @param id
	 * @param data
	 * @returns
	 */
	@ApiOperation({ summary: 'Find event type by id.' })
	@ApiResponse({
		status: HttpStatus.OK,
		description: 'Find Event type',
		type: EventType
	})
	@ApiResponse({
		status: HttpStatus.NOT_FOUND,
		description: 'Record not found'
	})
	@Get(':id')
	async findById(
		@Param('id', UUIDValidationPipe) id: string,
		@Query('data', ParseJsonPipe) data?: any
	): Promise<IEventType> {
		const { relations = [] } = data;
		return this.eventTypeService.findOneByIdString(id, {
			relations
		});
	}

	/**
	 * CREATE new event type
	 *
	 * @param entity
	 * @returns
	 */
	@ApiOperation({ summary: 'Create new record' })
	@ApiResponse({
		status: HttpStatus.CREATED,
		description: 'The record has been successfully created.'
	})
	@ApiResponse({
		status: HttpStatus.BAD_REQUEST,
		description: 'Invalid input, The response body may contain clues as to what went wrong'
	})
	@Post()
	async create(@Body() entity: IEventTypeCreateInput): Promise<IEventType> {
		return await this.commandBus.execute(new EventTypeCreateCommand(entity));
	}

	/**
	 * UPDATE event type by id
	 *
	 * @param id
	 * @param entity
	 * @returns
	 */
	@ApiOperation({ summary: 'Update record' })
	@ApiResponse({
		status: HttpStatus.CREATED,
		description: 'The record has been successfully updated.'
	})
	@ApiResponse({
		status: HttpStatus.BAD_REQUEST,
		description: 'Invalid input, The response body may contain clues as to what went wrong'
	})
	@HttpCode(HttpStatus.ACCEPTED)
	@Put(':id')
	async update(@Param('id', UUIDValidationPipe) id: string, @Body() entity: EventType): Promise<IEventType> {
		return this.eventTypeService.create({
			id,
			...entity
		});
	}
}
