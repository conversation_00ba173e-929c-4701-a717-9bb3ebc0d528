<mjml>
	<mj-body background-color="#fff">
		<mj-section>
			<mj-column>
				<mj-image
					width="100px"
					src="{{appLogo}}"
				></mj-image>
				<mj-divider
					border-color="#1B005D"
					border-width="1px"
				></mj-divider>
				<mj-text font-size="20px" font-family="helvetica"
					><bold>Новая задача {{task_update_status}}</bold></mj-text
				>

				<mj-text
					padding="20px 0px 20px 50px"
					font-size="16px"
					font-family="helvetica"
				>
					<p><bold>заглавие:</bold> {{task_update_title}}</p>
					<p><bold>Описание:</bold> {{task_update_description}}</p>
					<p><bold>Оценить:</bold> {{task_update_estimate}}</p>
					<p><bold>Срок:</bold> {{task_update_due_date}}</p>
					<p><bold>Положение дел:</bold> {{task_status}}</p>
					<p>
						<bold>название проекта:</bold> {{task_update_project}}
					</p>
					<p><bold>Назначить:</bold> {{task_update_assign_by}}</p>
				</mj-text>
				<mj-text font-size="12px" font-family="helvetica"
					>** URL задачи: {{task_update_url}}</mj-text
				>
				<mj-divider
					border-color="#1B005D"
					border-width="1px"
				></mj-divider>
				<mj-text align="center" font-size="12px" font-family="helvetica"
					>© 2019,
					<a href="{{appLink}}" style="color: #598bff"
						>{{appName}}</a
					>
					by
					<a href="{{companyLink}}" style="color: #598bff"
						>{{companyName}}</a
					>
					All rights reserved.
				</mj-text>
			</mj-column>
		</mj-section>
	</mj-body>
</mjml>
