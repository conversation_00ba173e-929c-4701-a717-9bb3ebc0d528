<mjml>
	<mj-body background-color="#fff">
		<mj-section>
			<mj-column>
				<mj-image
					width="100px"
					src="{{appLogo}}"
				></mj-image>
				<mj-divider
					border-color="#1B005D"
					border-width="1px"
				></mj-divider>
				<mj-text>
					<p>Hello {{userName}}!</p>
					<p>
						We received a request to reset the password for your Gauzy account
						{{#if tenantName}} for <strong>{{tenantName}}</strong>{{/if}}.
					</p>
					<p>If you requested to reset your password, click the button below:</p>
				</mj-text>
				<mj-button
					href="{{ generatedUrl }}"
					font-family="Helvetica"
					background-color="#3366ff"
					color="white"
				>
					Reset Password
				</mj-button>
				<mj-text
					font-size="14px"
					font-family="helvetica"
					color="#555"
				>
					<p>If you did not request this change, please ignore this email.</p>
					<p>This password reset link will expire shortly for security reasons.</p>
				</mj-text>
				<mj-divider
					border-color="#1B005D"
					border-width="1px"
				></mj-divider>
				<mj-text align="center" font-size="12px" font-family="helvetica">
					© 2019,
					<a href="{{appLink}}" style="color: #598bff">{{appName}}</a>
					by
					<a href="{{companyLink}}" style="color: #598bff">{{companyName}}</a>
					All rights reserved.
				</mj-text>
			</mj-column>
		</mj-section>
	</mj-body>
</mjml>
