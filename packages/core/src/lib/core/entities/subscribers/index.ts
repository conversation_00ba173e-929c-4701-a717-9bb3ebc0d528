export * from './base-entity-event.subscriber';
export * from './tenant-organization-base-entity.subscriber';

import { MultiORMEnum, getORMType } from '../../utils';
import {
	ActivitySubscriber,
	ActivityLogSubscriber,
	ApiCallLogSubscriber,
	CandidateSubscriber,
	CustomSmtpSubscriber,
	DashboardSubscriber,
	EmailResetSubscriber,
	EmailTemplateSubscriber,
	EmployeeSubscriber,
	EmployeeSettingSubscriber,
	EmployeeNotificationSettingSubscriber,
	FeatureSubscriber,
	ImageAssetSubscriber,
	ImportHistorySubscriber,
	IntegrationSettingSubscriber,
	IntegrationSubscriber,
	InviteSubscriber,
	InvoiceSubscriber,
	IssueTypeSubscriber,
	OrganizationContactSubscriber,
	OrganizationDocumentSubscriber,
	OrganizationProjectSubscriber,
	OrganizationSubscriber,
	OrganizationTeamEmployeeSubscriber,
	OrganizationTeamJoinRequestSubscriber,
	OrganizationTeamSubscriber,
	PaymentSubscriber,
	PipelineSubscriber,
	ProductCategorySubscriber,
	ReportSubscriber,
	ResourceLinkSubscriber,
	RoleSubscriber,
	ScreeningTaskSubscriber,
	ScreenshotSubscriber,
	TagSubscriber,
	TaskPrioritySubscriber,
	TaskRelatedIssueTypeSubscriber,
	TaskSizeSubscriber,
	TaskStatusSubscriber,
	TaskSubscriber,
	TaskVersionSubscriber,
	TenantSubscriber,
	TimeOffRequestSubscriber,
	TimesheetSubscriber,
	TimeSlotSubscriber,
	UserSubscriber
} from '../internal';
import { BaseEntitySubscriber } from './base.entity.subscriber';
import { TenantOrganizationBaseEntityEventSubscriber } from './tenant-organization-base-entity.subscriber';

// Get the ORM type from the MultiORMEnum
const ormType = getORMType();

/**
 * A map of the core TypeORM / MikroORM Subscribers.
 */
export const coreSubscribers = [
	// Add the subscriber only if the ORM type is MikroORM
	...(ormType === MultiORMEnum.MikroORM ? [TenantOrganizationBaseEntityEventSubscriber] : []),
	BaseEntitySubscriber,
	ActivitySubscriber,
	ActivityLogSubscriber,
	ApiCallLogSubscriber,
	CandidateSubscriber,
	CustomSmtpSubscriber,
	DashboardSubscriber,
	EmailResetSubscriber,
	EmailTemplateSubscriber,
	EmployeeSettingSubscriber,
	EmployeeSubscriber,
	EmployeeNotificationSettingSubscriber,
	FeatureSubscriber,
	ImageAssetSubscriber,
	ImportHistorySubscriber,
	IntegrationSettingSubscriber,
	IntegrationSubscriber,
	InviteSubscriber,
	InvoiceSubscriber,
	IssueTypeSubscriber,
	OrganizationContactSubscriber,
	OrganizationDocumentSubscriber,
	OrganizationProjectSubscriber,
	OrganizationSubscriber,
	OrganizationTeamEmployeeSubscriber,
	OrganizationTeamJoinRequestSubscriber,
	OrganizationTeamSubscriber,
	PaymentSubscriber,
	PipelineSubscriber,
	ProductCategorySubscriber,
	ReportSubscriber,
	ResourceLinkSubscriber,
	RoleSubscriber,
	ScreeningTaskSubscriber,
	ScreenshotSubscriber,
	TagSubscriber,
	TaskPrioritySubscriber,
	TaskRelatedIssueTypeSubscriber,
	TaskSizeSubscriber,
	TaskStatusSubscriber,
	TaskSubscriber,
	TaskVersionSubscriber,
	TenantSubscriber,
	TimeOffRequestSubscriber,
	TimesheetSubscriber,
	TimeSlotSubscriber,
	UserSubscriber
];
