import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { IPagination, ITag, ITagFindInput } from '@gauzy/contracts';
import { TagService } from './../../tag.service';
import { TagListCommand } from './../tag.list.command';

@CommandHandler(TagListCommand)
export class TagListHandler
	implements ICommandHandler<TagListCommand> {

	constructor(
		private readonly tagService: TagService
	) { }

	public async execute(command: TagListCommand): Promise<IPagination<ITag>> {
		const { input, relations = [] } = command;
		return await this.tagService.findTags(
			input as ITagFindInput,
			relations
		);
	}
}
