import { InviteAcceptCandidateHand<PERSON> } from './invite.accept-candidate.handler';
import { InviteAcceptEmployeeHandler } from './invite.accept-employee.handler';
import { InviteAcceptHandler } from './invite-accept.handler';
import { InviteAcceptOrganizationContactHandler } from './invite.accept-organization-contact.handler';
import { InviteAcceptUserHandler } from './invite.accept-user.handler';
import { InviteBulkCreateHandler } from './invite.bulk.create.handler';
import { InviteOrganizationContactHandler } from './invite.organization-contact.handler';
import { InviteResendHandler } from './invite.resend.handler';

export const CommandHandlers = [
	InviteAcceptCandidateHandler,
	InviteAccept<PERSON><PERSON>loy<PERSON><PERSON><PERSON><PERSON>,
	Invite<PERSON>ccept<PERSON><PERSON><PERSON>,
	InviteAcceptOrganization<PERSON>ontact<PERSON>and<PERSON>,
	Invi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
	InviteBulkCreate<PERSON><PERSON><PERSON>,
	InviteOrganizationContact<PERSON><PERSON><PERSON>,
	InviteR<PERSON>nd<PERSON>and<PERSON>,
];