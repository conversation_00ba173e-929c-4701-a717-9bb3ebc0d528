import { CandidateTechnologiesBulkDeleteHandler } from './candidate-technologies.bulk.delete.handler';
import { CandidateTechnologiesBulkCreateHandler } from './candidate-technologies.bulk.create.handler';
import { CandidateTechnologiesBulkUpdateHandler } from './candidate-technologies.bulk.update.handler';
export const CommandHandlers = [
	CandidateTechnologiesBulkDeleteHandler,
	CandidateTechnologiesBulkCreateHandler,
	CandidateTechnologiesBulkUpdateHandler
];
