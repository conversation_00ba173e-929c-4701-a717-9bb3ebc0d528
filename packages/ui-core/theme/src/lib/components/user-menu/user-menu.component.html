<div gauzyOutside (clickOutside)="onClickOutside($event)" class="main-menu">
	<div [nbSpinner]="isSubmit$ | async" class="left-menu">
		<!-- TODO: Implement commented features -->
		<!-- <div underConstruction class="sub-menu">
      <span><i class="far fa-smile"></i>
        {{ 'USER_MENU.STATUS' | translate }}</span>
      <div class="status">
        <div class="button selected">
          {{ 'USER_MENU.AVAILABLE' | translate }}
        </div>
        <div class="button">
          {{ 'USER_MENU.UNAVAILABLE' | translate }}
        </div>
      </div>
    </div>
	<div underConstruction class="sub-menu">
      <span>{{ 'USER_MENU.PAUSE_NOTIFICATIONS' | translate }}</span>
      <div class="notifications">
        <div class="button">
          {{ 'USER_MENU.FOR_1_HOUR' | translate }}
        </div>
        <div class="button">
          {{ 'USER_MENU.FOR_2_HOURS' | translate }}
        </div>
        <div class="button selected">
          {{ 'USER_MENU.UNTIL_TOMORROW' | translate }}
        </div>
        <div class="button">{{ 'USER_MENU.CUSTOM' | translate }}</div>
        <div class="button">
          <i class="far fa-calendar"></i>
          {{ 'USER_MENU.SET_AS_NOTIFICATION_SCHEDULE' | translate }}
        </div>
      </div>
    </div> -->
		<div class="links">
			<p
				*ngIf="employee$ | async"
				[ngClass]="{ 'disabled-table': (isSubmit$ | async) }"
				class="link"
				[innerHTML]="
					((employee$ | async)?.isAway
						? 'USER_MENU.SET_YOURSELF_AS_ACTIVE'
						: 'USER_MENU.SET_YOURSELF_AS_AWAY'
					) | translate
				"
				(click)="onChangeStatus()"
			></p>
			<p class="link" routerLink="/pages/auth/profile">
				{{ 'USER_MENU.PROFILE' | translate }}
			</p>
			<p class="link" routerLink="/auth/logout">
				{{ 'USER_MENU.SIGN_OUT' | translate }}
			</p>
		</div>
		<div class="sub-user-menu">
			<gauzy-user showIdentity="true" [user$]="user$" (clicked)="onClick()"></gauzy-user>
		</div>
	</div>

	<div class="right-menu">
		<span><i (click)="onClick()" class="fas fa-times"></i></span>
		<div class="download-apps-content">
			<span class="download-apps-label">Download Apps:</span>
			<span class="download-apps-icons">
				<a *ngFor="let app of downloadApps" target="_blank" [href]="app.link" rel="noopener">
					<i [class]="app.icon"></i>
				</a>
			</span>
		</div>
		<p>
			<a target="_blank" href="{{ platFormWebSiteUrl }}">{{ 'USER_MENU.HELP' | translate }}</a>
		</p>
		<p class="link" underConstruction>
			{{ 'USER_MENU.HOTKEYS' | translate }}
		</p>
		<p>
			<ngx-theme-language-selector class="theme"></ngx-theme-language-selector>
		</p>
		<p>
			<gauzy-switch-theme class="theme"></gauzy-switch-theme>
		</p>
		<p>
			<ngx-theme-selector-container class="theme"></ngx-theme-selector-container>
		</p>
	</div>
</div>
