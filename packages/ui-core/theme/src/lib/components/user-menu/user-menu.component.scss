@use 'themes' as *;

:host {
  .main-menu {
    width: 36rem;
    //height: 30.5625rem; // After implement features revert to this height
    height: 18rem;
    box-shadow: 0rem 0.25rem 1rem rgba(0, 0, 0, 0.25);
    border-radius: nb-theme(border-radius) nb-theme(border-radius)
      nb-theme(border-radius) 0rem;
    background: nb-theme(background-basic-color-1);
    display: flex;
    .right-menu {
      background: nb-theme(color-primary-transparent-default);
      border-radius: 0px nb-theme(border-radius) nb-theme(border-radius) 0px;
      width: 50%;
      padding: 13.5px 15.5px 13.5px 26px;
      span {
        display: flex;
        justify-content: flex-end;
      }
      p {
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: relative;
        cursor: pointer;
        .theme {
          width: 100%;
        }
      }
    }
    .left-menu {
      width: 50%;
      border-radius: 0px nb-theme(border-radius) nb-theme(border-radius) 0px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      @include nb-ltr(padding, 19px 47px 10px 11px);
      @include nb-rtl(padding, 19px 11px 10px 47px);
      .sub-menu {
        background: rgba(126, 126, 143, 0.05);
        padding: 10px;
        border-radius: nb-theme(border-radius);
        align-items: flex-start;
        display: flex;
        flex-direction: column;
      }
      .button {
        padding: 10px;
        background: nb-theme(background-basic-color-1);
        border-radius: nb-theme(border-radius);
        margin: 4px 3px 0px 0px;
        width: fit-content;
        font-size: 0.6875rem;
        &.selected {
          background: nb-theme(background-primary-color-1);
          color: nb-theme(text-control-color);
        }
      }
    }
  }
  .notifications,
  .status {
    display: flex;
    flex-wrap: wrap;
  }

  p.link,
  .button,
  i,
  a {
    cursor: pointer;
    text-decoration: none;
    color: nb-theme(text-basic-color);
  }
  .links {
    align-items: flex-start;
    display: flex;
    flex-direction: column;
  }

  .right-menu {
    .download-apps-content {
      display: flex;
      align-items: center;
      font-size: 12px;
      font-weight: 500;
      line-height: 15px;
      letter-spacing: 0em;
      text-align: left;
      min-height: 20px;
      margin-top: 0.5rem;
      margin-bottom: 1rem;

      .download-apps-label {
        color: #7e7e8f;
      }

      .download-apps-icons {
        margin-left: 0.3rem;
        a {
          margin: 0 0.2rem;
          transition: 0.2s;
          padding: 0.1rem 0.2rem;
          border: 1px solid transparent;

          i {
            padding: 0;
            margin: 0;
            color: #7e7e8f;
          }

          &:hover {
            color: nb-theme(text-primary-color);
            border: 1px solid nb-theme(text-primary-color);
            border-radius: calc(nb-theme(border-radius) - 4px);
            transform: scale(1.5);
          }
        }
      }
    }
  }
  .disabled-table {
	pointer-events: none;
	opacity: 0.5;
  }
}
