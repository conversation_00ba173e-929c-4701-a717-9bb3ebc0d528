@use '@nebular/theme/styles/global/breakpoints' as *;
@use 'bootstrap/scss/mixins/breakpoints';
@use 'themes' as *;

@include nb-install-component() {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  font-weight: 400;
  line-height: 11px;
  letter-spacing: 0em;
  color: var(--gauzy-text-color-2);
  a {
	text-decoration: none;
  }
  .socials {
    font-size: 1.25rem;
    display: flex;
    align-items: center;
    gap: 12px;
    a {
      color: nb-theme(text-hint-color);
      transition: color ease-out 0.1s;

      &:hover {
        color: nb-theme(text-basic-color);
      }
    }
    .ion-logo-gitlab svg {
      vertical-align: text-top;
      width: 1.25rem;
    }
  }
  .right-side {
    display: flex;
    align-items: center;
    padding: 0 1rem 0;
  }

  @include media-breakpoint-down(is) {
    .socials {
      font-size: 1rem;
    }
  }
}
