@use 'var' as *;

:host .switch-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;

  nb-toggle.switch ::ng-deep .toggle {
    height: 1.5rem;
    width: 3.375rem;
    font-family: 'Font Awesome 6 Free' !important;
    font-size: .625rem;
    display: flex;
    align-items: center;
    background-color: rgba(126, 126, 143, 0.5);
    border-width: 0;
    .toggle-switcher{
      height: 1rem;
      width: 1rem;
    }
    span {
      display: flex;
      justify-content: center;
      align-items: center;
      justify-self: flex-end;
    }

    nb-icon{
      display: none;
    }
  }
  nb-toggle.light ::ng-deep .toggle {
    justify-content: end;
    &:before {
      content: '\f186';
      @include nb-rtl(margin-left, .275rem);
      @include nb-ltr(margin-right, .275rem);
      color: rgba(255, 255, 255, 0.25);
    }
    span {
      &:before {
        content: '\f185';
      }
    }
    .toggle-switcher{
      @include nb-ltr(margin-left, .25rem);
      @include nb-rtl(margin-right, .25rem);
    }
  }

  nb-toggle.dark ::ng-deep .toggle {
    justify-content: flex-start;
    &:before {
      content: '\f185';
      @include nb-ltr(margin-left, .325rem);
      @include nb-rtl(margin-right, .325rem);
      color: rgba(255, 255, 255, 0.25);
    }
    span {
      &:before {
        content: '\f186';
      }
    }
  }
}

::ng-deep [dir=ltr] nb-toggle.switch .toggle.checked .toggle-switcher {
  left: calc(100% - 1.375rem);
}

::ng-deep [dir=rtl] nb-toggle.switch .toggle.checked .toggle-switcher {
  right: calc(100% - 1.375rem);
}
