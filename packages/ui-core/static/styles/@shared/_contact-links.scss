@import 'var';

:host .contact-links-container {
  width: 100%;

  .inner-wrapper {
    display: flex;
    align-items: center;
    @include nb-ltr(padding, 5px 14px 5px 7px);
    @include nb-rtl(padding, 5px 7px 5px 14px);
    gap: 8px;
    background: rgba(36, 189, 255, 0.1);
    border-radius: var(--button-rectangle-border-radius);
    overflow: hidden;
    width: fit-content;

    .names-wrapper {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      max-width: 110px;
    }

    .link-text {
      cursor: pointer;
      text-decoration: none;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 15px;
      letter-spacing: 0em;
      white-space: nowrap;
      width: 100%;
      text-overflow: ellipsis;
      color: var(--text-primary-color);
    }

    .link-text:hover {
      text-decoration: underline;
    }

    .avatar {
      display: flex;
      img {
        width: 18px;
        height: 18px;
        object-fit: cover;
        border-radius: var(--button-rectangle-border-radius);
      }
    }

    .prefix {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: 4px 5px;
      gap: 10px;

      width: 18px;
      height: 18px;

      background: #7e7e8f;
      border-radius: var(--button-rectangle-border-radius);

      /* Inside auto layout */

      flex: none;
      order: 0;
      flex-grow: 0;

      /* typography */

      font-size: 8px;
      font-weight: 600;
      line-height: 10px;
      letter-spacing: 0em;
      text-align: center;
      color: white;
    }
  }
}
