@use 'var' as *;
@use 'gauzy/_gauzy-dialogs' as *;

@include nb-install-component() {
  nb-card {
    background-color: nb-theme(gauzy-card-1);
    width: 645px;
    nb-card-header {
      nb-icon.close {
        cursor: pointer;
      }
    }
    nb-tag-list nb-tag::ng-deep {
      text-transform: initial;
    }
  }
}

:host {
  ::ng-deep div.step-content {
    padding: 20px 0;
  }
}

:host .button-container {
  display: flex;
  justify-content: flex-start;
  button {
    @include nb-ltr(margin, 0 0 0 1rem);
    @include nb-rtl(margin-left, 0 1rem 0 0);
  }
}
