@import 'var';
@import 'gauzy/_gauzy-overrides';

:host {
  nb-card {
    background-color: var(--gauzy-card-2);
    @include input-appearance(42px, var(--gauzy-card-1));
    ::ng-deep ngx-image-uploader input {
      height: 100% !important;
    }
  }
}
:host nb-card-body {
  padding: 50px 70px;
  @include respond(dsk) {
    padding: 30px 40px;
  }

  .navigate {
    display: flex;
    margin-bottom: 35px;
    cursor: pointer;

    span {
      margin-left: 7px;
      font-size: 14px;
      color: #2a2c39;
    }
  }

  .content {
    display: flex;
  }

  .employee-container {
    position: relative;
    display: flex;
    flex-direction: column;
    transition: transform 150ms ease-in-out;
    @include nb-rtl(padding-left, 70px);
    @include nb-ltr(padding-right, 70px);
    @include nb-rtl(margin-left, 70px);
    @include nb-ltr(margin-right, 70px);
    @include nb-rtl(border-left, 1px solid rgba(0, 0, 0, 0.1));
    @include nb-ltr(border-right, 1px solid rgba(0, 0, 0, 0.1));

    .employee-photo {
      width: fit-content;
      height: 200px;
      position: relative;

      div {
        pointer-events: none;
        background: black;
        position: absolute;
        height: 100%;
        width: 100%;
        border-radius: 13px;
      }

      img {
        width: 200px;
        height: 200px;
        border-radius: 13px;
        object-fit: cover;
      }

      input {
        width: 100%;
        height: 100% !important;
        opacity: 0;
        position: absolute;
        z-index: 3;
        cursor: pointer;
      }

      svg {
        z-index: 2;
        transition: opacity 0.2s ease-in;
        opacity: 0.3;
        position: absolute;
        top: calc(50% - 68px / 2);
        left: calc(50% - 68px / 2);
        g circle {
          fill: nb-theme(text-primary-color);
        }
      }
    }
  }

  .badge {
    position: relative;
    margin-top: 10px;
    display: flex;
  }

  .employee-form {
    width: 60%;
  }

  .actions {
    display: flex;
    justify-content: flex-start;
    width: 100%;
    margin-top: 30px;
  }
}
