@import 'themes';
@import './reusable';

.section-wrapper {
  background: rgba(245, 109, 88, 0.05);
  border-radius: nb-theme(border-radius);
  padding: 25px;
  box-sizing: border-box;
  width: 100%;
  & .learn-more {
    @include mobile-screen {
      display: none;
    }
  }
}
.whats-new-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  & .main-header {
    color: nb-theme(text-primary-color);
    font-family: Inter;
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px;
    letter-spacing: -0.009em;
    text-align: left;
    margin-bottom: 19px;
    padding-left: 5px;
  }
  & .entry {
    margin-bottom: 15px;
    border-bottom: 1px solid lightgray;
    padding-left: 5px;
    padding-right: 5px;
    &:last-of-type {
      margin-bottom: 83px;
      @include mobile-screen {
        margin-bottom: 0;
      }
    }
    & .entry-headings-wrapper {
      display: flex;
      margin-bottom: 5px;
      & .icon {
        margin-right: 15px;
        height: 14px;
        margin-top: 3px;
        color: nb-theme(text-primary-color);
      }
      & .entry-header {
        font-family: Inter;
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: 17px;
        letter-spacing: 0em;
        text-align: left;
        margin-bottom: 5px;
      }
      & .entry-header-date {
        font-family: Inter;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 16px;
        letter-spacing: 0em;
        text-align: left;
        color: var(--gauzy-text-2);
      }
    }
    & .paragraph {
      font-family: Inter;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 16px;
      letter-spacing: 0em;
      text-align: left;
    }
  }
}
