{"name": "ui-core", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/ui-core/src", "prefix": "lib", "projectType": "library", "tags": [], "implicitDependencies": [], "targets": {"build": {"executor": "@nx/angular:package", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "dependsOn": ["^build", {"target": "config", "projects": "self"}], "options": {"project": "packages/ui-core/ng-package.json", "tsConfig": "packages/ui-core/tsconfig.json"}, "configurations": {"production": {"tsConfig": "packages/ui-core/tsconfig.lib.prod.json"}, "development": {"tsConfig": "packages/ui-core/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "config": {"executor": "nx:run-commands", "configurations": {"production": {"commands": ["yarn nx build ui-config --configuration=production"]}, "development": {"commands": ["yarn nx build ui-config --configuration=development"]}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/ui-core/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}