<nb-accordion
	*ngxPermissionsOnly="item?.data?.permissionKeys"
	[ngClass]="state ? item?.class : item?.class + ' closed'"
>
	<nb-accordion-item [collapsed]="collapse" (collapsedChange)="onCollapse($event)" [ngClass]="{ opened: selected }">
		<nb-accordion-item-header
			[gaTooltip]="item?.title"
			[icon]="item?.icon"
			*ngIf="!state"
			[ngClass]="onCollapse ? 'collapsed' : ''"
			(click)="redirectTo()"
		>
			<i [class]="item?.icon"></i>
			<span *ngIf="state">
				<a [href]="getExternalUrl(item?.link)" onclick="return false;">
					{{ item?.title }}
				</a>
			</span>
		</nb-accordion-item-header>
		<nb-accordion-item-header
			[nbTooltip]="item?.title"
			nbTooltipPlacement="right"
			*ngIf="state"
			[ngClass]="onCollapse ? 'collapsed' : ''"
			(click)="redirectTo()"
		>
			<i [class]="item?.icon"></i>
			<span *ngIf="state">
				<a [href]="getExternalUrl(item?.link)" onclick="return false;">
					{{ item?.title }}
				</a>
			</span>
		</nb-accordion-item-header>
		<nb-accordion-item-body [ngClass]="{ 'item-collapsed': !state }" *ngIf="item?.children?.length > 0">
			<div *ngFor="let subItem of item.children; trackBy: subItem?.title">
				<ga-children-menu-item
					*ngIf="!item?.hidden"
					[id]="subItem?.id"
					[item]="subItem"
					[parent]="item"
					[collapse]="state"
					[selected]="subItem === selectedChildren && selected"
					(focusItemChange)="focusOn($event)"
				></ga-children-menu-item>
			</div>
		</nb-accordion-item-body>
	</nb-accordion-item>
</nb-accordion>
