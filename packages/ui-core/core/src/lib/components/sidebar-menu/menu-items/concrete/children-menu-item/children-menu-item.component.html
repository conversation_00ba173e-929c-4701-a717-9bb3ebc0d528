<ng-container *ngxPermissionsOnly="item?.data?.permissionKeys">
	<ng-container *ngIf="!collapse; else withNbTooltip">
		<ng-container *ngIf="!item?.hidden">
			<div
				[ngClass]="{ 'border-bottom': !isLast() }"
				[gaTooltip]="item?.title"
				[icon]="item?.icon"
				[class]="(selected ? 'selected ' : '') + (!collapse ? 'custom ' : '') + 'sub-item'"
				(click)="select()"
			>
				<span class="info">
					<i [class]="item?.icon"></i>
					<span *ngIf="collapse || mouseHover">
						<a [href]="getExternalUrl(item?.link)" onclick="return false;">
							{{ item?.title }}
						</a>
					</span>
				</span>
			</div>
		</ng-container>
	</ng-container>
	<ng-template #withNbTooltip>
		<ng-container *ngIf="!item?.hidden">
			<div
				[ngClass]="{ 'border-bottom': !isLast() }"
				[nbTooltip]="item?.title"
				nbTooltipPlacement="right"
				[class]="(selected ? 'sub-item selected' : 'sub-item') + (!collapse ? ' custom' : '')"
			>
				<span (click)="select()" class="info w-100">
					<i [class]="item?.icon"></i>
					<span *ngIf="collapse || mouseHover">
						<a [href]="getExternalUrl(item?.link)" onclick="return false;">
							{{ item?.title }}
						</a>
					</span>
				</span>
				<ng-template [ngIf]="item?.data?.add">
					<button class="float-right plus" status="basic" outline size="tiny" (click)="add()" nbButton>
						<i class="fas fa-plus"></i>
					</button>
				</ng-template>
			</div>
		</ng-container>
	</ng-template>
</ng-container>
