@use 'themes' as *;

:host {
  position: relative;
  nb-accordion-item-body.item-collapsed ::ng-deep .item-body {
    padding: 4px;
  }
  nb-accordion {
    box-shadow: unset;
    border-bottom: 1px solid nb-theme(gauzy-border-default-color);
    margin-top: 6px;
    nb-accordion-item.opened {
      box-shadow: 0px 1px 1px 0px rgba(0, 0, 0, 0.1);
      nb-accordion-item-header {
        background-color: nb-theme(gauzy-sidebar-background-3);
        &.accordion-item-header-expanded {
          border-radius: nb-theme(border-radius) nb-theme(border-radius) 0 0;
        }
      }
      nb-accordion-item-body {
        background-color: nb-theme(gauzy-sidebar-background-4);
        border-radius: 0 0 nb-theme(border-radius) nb-theme(border-radius);
      }
    }
    &.focus {
      box-shadow: 0px 1px 1px 0px rgba(0, 0, 0, 0.1);
      nb-accordion-item-header,
      i {
        color: rgba(245, 109, 88, 1);
      }
    }
    nb-accordion-item-header.accordion-item-header-collapsed {
      border-radius: nb-theme(border-radius);
    }
    &.closed {
      nb-accordion-item-header {
        display: flex;
        align-items: center;
        justify-content: center;
        i {
          margin-right: unset;
        }
        ::ng-deep nb-icon {
          display: none;
        }
      }
    }
    &.application {
      nb-accordion-item {
        nb-accordion-item-header {
          border: 1px solid nb-theme(background-primary-color-1);
          color: nb-theme(text-primary-color);
          border-radius: nb-theme(border-radius);
        }
        i {
          color: nb-theme(text-primary-color);
        }
        nb-accordion-item-header ::ng-deep nb-icon {
          border: 1px solid nb-theme(background-primary-color-1);
          border-radius: nb-theme(border-radius);
          width: 1.75rem;
          height: 1.75rem;
          @include nb-ltr(margin-right, calc(-1.75rem / 4));
          @include nb-rtl(margin-left, calc(-1.75rem / 4));
        }
      }
    }
  }
  hr.custom-separator {
    border: 0.5px solid nb-theme(gauzy-border-default-color);
    border-top: none;
    border-left: none;
    border-right: none;
  }
  i {
    color: nb-theme(gauzy-text-color-1);
  }

  nb-accordion-item-header {
    color: nb-theme(gauzy-text-color-2);
    font-size: 15px;
    font-weight: 600;
    line-height: 18px;
    letter-spacing: 0em;
    gap: 0.625rem;
  }

  a {
    text-decoration: none;
    color: unset;
  }
}

:host .sub-item {
  padding: 0px 9px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  color: nb-theme(gauzy-text-color-2);
  font-size: 13px;
  font-weight: 400;
  line-height: 16px;
  letter-spacing: 0em;
  cursor: pointer;
  &:hover,
  &.selected {
    background-color: nb-theme(gauzy-sidebar-background-3);
    border-radius: nb-theme(border-radius);
    font-weight: 600;
    color: nb-theme(gauzy-text-color-1);
    border: unset;
  }

  &:hover {
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.15);
  }
  &.custom {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  &.mouse-hover {
    display: flex;
    flex-direction: row;
    align-items: center;
    flex-wrap: nowrap;
    position: absolute;
    top: 0;
    left: 0;
  }
}

.info {
  padding: 11px 0;
  span {
    margin: 0 0.625rem;
  }
}
