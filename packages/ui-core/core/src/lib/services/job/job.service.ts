import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { firstValueFrom } from 'rxjs';
import {
	IGetEmployeeJobPostInput,
	IEmployeeJobPost,
	IPagination,
	IEmployeeJobApplicationAppliedResult,
	IUpdateEmployeeJobPostAppliedResult,
	IEmployeeJobApplication,
	IVisibilityJobPostInput,
	ID,
	UpdateEmployeeJobsStatistics
} from '@gauzy/contracts';
import { API_PREFIX, toParams } from '@gauzy/ui-core/common';

@Injectable({
	providedIn: 'root'
})
export class JobService {
	constructor(private readonly http: HttpClient) {}

	/**
	 * Fetches job posts based on the given request parameters.
	 *
	 * @param request - An optional object of type IGetEmployeeJobPostInput containing filter parameters for fetching job posts.
	 * @returns A promise that resolves to an IPagination<IEmployeeJobPost> object containing the paginated job posts.
	 */
	getJobs(request?: IGetEmployeeJobPostInput): Promise<IPagination<IEmployeeJobPost>> {
		return firstValueFrom(
			this.http.get<IPagination<IEmployeeJobPost>>(`${API_PREFIX}/employee-job`, {
				params: request ? toParams(request) : {}
			})
		);
	}

	/**
	 * Retrieves job statistics for employees based on the provided request parameters.
	 *
	 * @param request - Parameters for filtering and retrieving job statistics.
	 * @returns A promise that resolves with the job statistics data.
	 */
	getEmployeeJobsStatistics(request: any): Promise<any> {
		return firstValueFrom(
			this.http.get(`${API_PREFIX}/employee-job/statistics`, {
				params: toParams(request)
			})
		);
	}

	/**
	 * Updates the job search status and statistics for an employee.
	 *
	 * @param id - The ID of the employee.
	 * @param statistics - An object containing job search status and statistics to be updated.
	 * @returns A promise that resolves with the updated employee's job search status and statistics.
	 */
	updateJobSearchStatus(id: ID, statistics: UpdateEmployeeJobsStatistics): Promise<any> {
		return firstValueFrom(this.http.put(`${API_PREFIX}/employee-job/${id}/job-search-status`, statistics));
	}

	/**
	 * Hides a job post based on the given request parameters.
	 *
	 * @param request - An object of type IVisibilityJobPostInput containing the necessary parameters to hide a job post.
	 * @returns A promise that resolves to a boolean indicating whether the job post was successfully hidden.
	 */
	hideJob(request: IVisibilityJobPostInput): Promise<boolean> {
		return firstValueFrom(this.http.post<boolean>(`${API_PREFIX}/employee-job/hide`, request));
	}

	/**
	 * Updates the application status of a job post.
	 *
	 * @param request - An object of type IEmployeeJobApplication containing the necessary parameters to update the application status of a job post.
	 * @returns A promise that resolves to an IUpdateEmployeeJobPostAppliedResult object containing the result of the update operation.
	 */
	updateApplied(request: IEmployeeJobApplication): Promise<IUpdateEmployeeJobPostAppliedResult> {
		return firstValueFrom(
			this.http.post<IUpdateEmployeeJobPostAppliedResult>(`${API_PREFIX}/employee-job/updateApplied`, request)
		);
	}

	/**
	 * Applies for a job post based on the given request parameters.
	 *
	 * @param request - An object of type IEmployeeJobApplication containing the necessary parameters to apply for a job post.
	 * @returns A promise that resolves to an IEmployeeJobApplicationAppliedResult object containing the result of the application operation.
	 */
	applyJob(request: IEmployeeJobApplication): Promise<IEmployeeJobApplicationAppliedResult> {
		return firstValueFrom(
			this.http.post<IEmployeeJobApplicationAppliedResult>(`${API_PREFIX}/employee-job/apply`, request)
		);
	}

	/**
	 * Create employee job application record.
	 * We use AI to generate proposal for employee.
	 *
	 * @param request
	 * @returns
	 */
	preProcessEmployeeJobApplication(request: any): Promise<IEmployeeJobApplication> {
		return firstValueFrom(this.http.post<any>(`${API_PREFIX}/employee-job/pre-process`, request));
	}

	/**
	 * To generate proposal for specific employee job application
	 *
	 * @param employeeJobApplicationId
	 * @returns
	 */
	generateAIProposal(employeeJobApplicationId: string) {
		return firstValueFrom(
			this.http.post<any>(`${API_PREFIX}/employee-job/generate-proposal/${employeeJobApplicationId}`, {})
		);
	}

	/**
	 * Get employee job application where proposal generated by AI
	 *
	 * @param employeeJobApplicationId
	 * @returns
	 */
	getEmployeeJobApplication(employeeJobApplicationId: string) {
		return this.http.get<any>(`${API_PREFIX}/employee-job/application/${employeeJobApplicationId}`);
	}
}
