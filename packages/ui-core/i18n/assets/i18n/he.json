{"BUTTONS": {"PAY": "Pay", "ADD_EXISTING_USER": "Add Existing User", "ADD_NEW": "Add New", "ADD": "הוסף", "CREATE": "צור", "REGISTER": "Register", "CHANGE_PASSWORD": "שנה סיסמה", "LOGIN": "Log In", "SIGNIN": "התחברות", "SEND_CODE": "התח<PERSON>ר עם דו<PERSON>ר אלקטרוני", "ADD_NOTE": "Add note", "EDIT": "ערוך", "MANAGE": "Управление", "DETAILS": "Details", "DUPLICATE": "Duplicate", "DELETE": "מחק", "REMOVE": "Remove", "ADD_EXISTING": "Add Existing", "CONVERT_TO_EMPLOYEE": "המרה לעובד", "OK": "בסדר", "YES": "Yes", "NO": "No", "SAVE": "לשמור", "CLEAR_ALL": "Clear All", "BACK": "חז<PERSON>ר", "SENT": "<PERSON><PERSON>", "ACCEPTED": "Accepted", "MARK_AS_SENT": "<PERSON> as <PERSON><PERSON>", "MARK_AS_ACCEPTED": "<PERSON> as Accepted", "CANCEL": "בטל", "CLOSE": "סוֹף", "INVITE": "Invite", "SELECT_ALL": "Select All", "COPY_LINK": "Copy Link", "COPIED": "Copied!", "MANAGE_INTERVIEWS": "Manage Interviews", "MANAGE_INVITES": "Manage Invites", "MANAGE_SPRINTS": "Manage Sprints", "RESEND": "Resend", "NEXT": "Next", "PREVIOUS": "Previous", "INVITE_AGAIN": "Invite Again", "REQUEST": "Request", "HISTORY": "History", "SYNC": "Sync", "UPDATE": "Update", "AUTO_SYNC": "Auto sync", "VIEW": "View", "SEND": "Send", "ARCHIVE": "Archive", "HIRE": "<PERSON>re", "MANAGE_CATEGORIES": "Manage Categories", "REJECT": "Reject", "FIND_TIME": "Find time", "DOWNLOAD": "Download", "ADD_KNOWLEDGE_BASE": "Add Knowledge Base", "CHOOSE_ICON": "Choose icon", "MAKE_PRIVATE": "make private", "MAKE_PUBLIC": "make public", "KNOWLEDGE_BASES": "Knowledge bases", "SELECT": "Select", "EMAIL": "דוא\"ל", "CONVERT_TO_INVOICE": "Convert to invoice", "TO_INVOICE": "To invoice", "PUBLIC_APPOINTMENT_BOOK": "Book Public Appointment", "SAVE_AS_DRAFT": "Save as Draft", "SAVE_AND_SEND_CONTACT": "Save and send to contact in gauzy", "SAVE_AND_SEND_EMAIL": "Save and send via email", "EVENT_TYPES": "Event Types", "SEARCH": "Search", "RESET": "Reset", "LEAVE_FEEDBACK": "Leave a feedback", "SPRINT": {"CREATE": "Create Sprint", "EDIT": "ערוך", "DELETE": "מחק"}, "CANDIDATE_STATISTIC": "Statistic", "FILTER": "Filter", "REFRESH": "Refresh", "AUTO_REFRESH": "Auto Refresh", "PROPOSAL_DELETE_MESSAGE": "Proposal template successfully delete", "PROPOSAL_MAKE_DEFAULT_MESSAGE": "Proposal template successfully make as default", "MANAGE_TEMPLATES": "Manage Templates", "MAKE_DEFAULT": "Make Default", "HIDE_ALL": "Hide All", "SCHEDULES": "Schedules", "YES_HIDE_ALL_JOBS": "Yes, Hide All Jobs", "VALIDATE": "Validate", "VALIDATED": "Validated", "APPROVE": "Approve", "DENY": "<PERSON><PERSON>", "PAYMENTS": "Payments", "NOTE": "Note", "SKIP_CONTINUE": "Skip {{ label }} and Continue", "BUY": "Buy", "DELETE_ACCOUNT": "Delete your account", "DELETE_ALL_DATA": "Delete all data", "SELECT_AND_CONTINUE": "Select and Continue", "ADD_KPI": "Add KPI", "PAID_DAYS_OFF": "Paid days off", "UNPAID_DAYS_OFF": "Unpaid days off", "CLEAR": "Clear", "SET": "Set", "RECORD_FULL_PAYMENT": "Record Full Payment", "EXPORT_TO_CSV": "Export to CSV", "INVOICE_REMAINING_AMOUNT": "Invoice Remaining Amount", "PUBLIC_LINK": "Public Link", "GENERATE": "Generate", "SEND_RECEIPT": "Send Receipt", "ADD_COMMENT": "Add Comment", "CONTINUE": "Continue", "SUPER_ADMIN_DEMO": "Super Admin Demo", "ADMIN_DEMO": "<PERSON><PERSON>", "EMPLOYEE_DEMO": "Employee Demo", "DEMO_CREDENTIALS": "Demo Credentials", "CREATE_NEW_ROLE": "Create {{ name }} Role", "DELETE_EXISTING_ROLE": "Delete {{ name }} Role", "RESTORE": "Rest<PERSON>", "VIEW_ALL": "View All", "VIEW_REPORT": "View Report", "TIME_TRACKING_ENABLE": "Enable Time Tracking", "TIME_TRACKING_DISABLE": "Disable Time Tracking", "PRINT": "Print", "FEEDBACK": "<PERSON><PERSON><PERSON>", "EQUIPMENT_SHARING": "Equipment Sharing", "PRIVATE": "Private", "PUBLIC": "Public", "MANAGE_WIDGET": "Manage widgets", "MOVE": "Move", "COLLAPSE": "Collapse", "EXPAND": "Expand", "SHOW_MORE": "Show More", "START_WORK": "Start Work", "APPLY": "להגיש מועמדות", "GENERATE_PROPOSAL": "צור הצעה", "LET_S_GO": "בוא נלך", "CHECK": "בדיקה", "CHECK_UPDATE": "<PERSON><PERSON><PERSON><PERSON> עדכון", "DOWNLOAD_NOW": "הור<PERSON> עכשיו", "UPDATE_NOW": "ע<PERSON><PERSON><PERSON> עכשיו", "SAVE_RESTART": "שמור והפעל מחדש", "FILES": "קבצים", "START": "התחל", "STOP": "עצור", "ACKNOWLEDGE": "אשר", "RESTART": "אתחל מחדש", "LATER": "מאו<PERSON><PERSON> יותר", "UPGRADE": "שדרג", "SKIP_NOW": "דלג כעת", "EXIT": "יציאה", "LOGOUT": "התנתקות", "COPY": "העתקה", "CUT": "גזירה", "PASTE": "הדבקה", "FORWARD_PORTS": "העברת פורטים", "REPORT": "דו<PERSON>", "IGNORE": "התעלם", "INSTALL": "הת<PERSON>ן", "UNINSTALL": "הסר התקנה", "DEACTIVATE": "השבת", "ACTIVATE": "הפעל", "ADD_PLUGIN": "הוסף תוסף", "LOAD_PLUGIN": "טען תוסף", "FROM_CDN": "מ-CDN", "ADD_MODULE": "הוסף מודול", "SHARE": "שיתוף", "GET_INFO": "קבל מידע", "RETRY": "נסה שוב", "UPLOAD": "העלה", "MARKEPLACE": "<PERSON>ר<PERSON><PERSON>", "ADD_VERSION": "הוסף גרסה", "READ_MORE": "קרא עוד", "READ_LESS": "קרא פחות", "RECOVER": "שח<PERSON><PERSON>", "ADD_MORE_SOURCES": "הוסף מקורות נוספים", "REMOVE_FROM_FAVORITES": "הסר מהמועד<PERSON>ים", "ADD_TO_FAVORITES": "הוסף למועדפים"}, "SM_TABLE": {"NO_DATA": {"LOADING": "טוען, אנא המתן...", "RECEIVE_ESTIMATE": "לא קיבלתם אף הערכה.", "INCOME": "לא יצרתם הכנסות.", "EXPENSE_CATEGORY": "לא יצרתם קטגוריות הוצאה.", "REPORT": "לא יצרתם דוחות.", "CONTRACT": "לא יצרתם חוזים.", "TEAM": "לא יצרתם קבוצות.", "HISTORY_RECORD": "לא יצרתם רשומות.", "PROFIT_HISTORY": "לא יצרתם היסטוריות רווחים.", "EMPLOYEE": "לא יצרתם עובדים.", "EXPENSE": "לא יצרתם הוצאות.", "PAYMENT": "לא קיבלתם תשלומים.", "PROPOSAL_TEMPLATE": "לא יצרתם תבניות הצעה.", "PROPOSAL": "לא יצרתם הצעות.", "PIPELINE": "לא יצרתם ערוצי עבודה.", "TASK": "לא יצרתם משימות.", "INVITE": "לא הזמנתם משתמשים.", "APPROVAL_REQUEST": "לא יצרתם בקשות אישור.", "APPROVAL_POLICY": "לא יצרתם מדיניות אישור.", "TIME_OFF": "לא יצרתם ימי חופשה.", "TIME_OFF_POLICY": "לא יצרת מדיניות חופשה.", "CANDIDATE": "לא יצרתם מועמדים.", "INTERVIEW": "לא יצרתם ראיונות.", "EQUIPMENT": "לא יצרתם ציוד.", "EQUIPMENT_SHARING": "לא יצרתם רשומות שיתוף ציוד.", "EQUIPMENT_SHARING_POLICY": "לא יצרתם מדיניות שיתוף ציוד.", "INVENTORY": "לא יצרתם מלאי.", "MERCHANT": "לא יצרתם סוחרים.", "WAREHOUSE": "לא יצרתם מחסנים.", "WAREHOUSE_PRODUCT": "לא יצרתם מוצרי מחסן.", "PRODUCT_CATEGORY": "לא יצרתם קטגוריות מוצרים.", "TAGS": "לא יצרתם תגים.", "PROJECT": "לא יצרתם פרויקטים.", "DEPARTMENT": "לא יצרתם מחלקות.", "CONTACT": "לא יצרתם אנשי קשר.", "CLIENT": "לא יצרתם לקוחות.", "LEAD": "לא יצרתם לידים.", "TIME_FRAME": "לא יצרתם מסגרות זמן.", "KPI": "לא יצרתם KPI.", "INVOICE": "לא יצרתם חשבוניות.", "ESTIMATE": "לא יצרתם הערכות.", "EVENT_TYPE": "לא יצרתם סוגי אירועים.", "PRODUCT_TYPE_NO_DATA": "לא יצרתם סוגי מוצרים.", "TEAM_DASHBOARD": "אין לך שום צוותים.", "PLUGIN": "לא התקנת שום תוסף.", "PLUGIN_MARKETPLACE": "תוסף זה אינו זמין בשוק", "HISTORY": "<PERSON><PERSON><PERSON>טוריה"}, "TRANSACTION_TYPE": "סוג", "AMOUNT": "כמות", "DATE": "תאריך", "TITLE": "Title", "STAGE": "Stage", "START_DATE": "תאריך התחלה", "END_DATE": "תאריך סיום", "CLIENT_NAME": "שם הלקוח", "CONTACT_NAME": "Contact Name", "NAME": "שם", "VENDOR": "ספק", "CATEGORY": "קטגוריה", "CURRENCY": "מטבע", "VALUE": "ערך", "NOTES": "הערות", "EMPLOYEE": "עובד", "EMPLOYEES": "עובדים", "FULL_NAME": "שם מלא", "EMAIL": "אימייל", "INCOME": "הכנסה (ממוצע)", "EXPENSES": "הכנסה (הוצאות)", "BONUS": "בונוס", "BONUS_AVG": "הכנסה (בונוס)", "PROFIT_BASED_BONUS": "Profit Based Bonus", "REVENUE_BASED_BONUS": "Revenue Based Bonus", "STATUS": "עבודה", "SOURCE": "Source", "WORK_STATUS": "סטטוס עבודה", "TODAY": "היום", "END_OF_MONTH": "סוף חודש", "START_OF_MONTH": "תחילת החודש", "RATE": "ציון", "FLAT_FEE": "<PERSON><PERSON><PERSON><PERSON> שטוח", "MILESTONES": "אבני דרך", "JOB_TITLE": "Job Title", "JOB_POST_URL": "Job Post URL", "LINK_TO_JOBPOST": "Link to Job Post", "AUTHOR": "Author", "MONDAY": "Monday", "TUESDAY": "Tuesday", "WEDNESDAY": "Wednesday", "THURSDAY": "Thursday", "FRIDAY": "Friday", "SATURDAY": "Saturday", "SUNDAY": "Sunday", "NONE": "None", "ROLE": "Role", "PROJECTS": "פרויקטים", "PROJECT": "פרויקטים", "INVITED_BY": "Invited By", "EXPIRE_DATE": "Expires", "CLIENTS": "לקוחות", "CONTACTS": "Contacts", "CONTACT": "Contact", "DEPARTMENTS": "מחלקות", "DESCRIPTION": "Description", "POLICY": "Policy", "APPLIED": "Applied", "HIRED": "<PERSON><PERSON>", "REJECTED": "Rejected", "NO_RESULT": "No Result", "CLIENT": "לקוחות", "INTERNAL": "Internal", "START": "Start", "END": "End", "REQUEST_DATE": "Request Date", "REGION": {"BG": "Български (България)", "EN": "English (United States)", "RU": "Русский (Россия)", "HE": "עברית ישראל", "FR": "<PERSON><PERSON><PERSON> (France)", "ES": "<PERSON><PERSON><PERSON><PERSON><PERSON> (España)", "ZH": "中文 (中国)", "DE": "Deutsch (Deutschland)", "PT": "<PERSON>ug<PERSON><PERSON><PERSON> (Portugal)", "IT": "Italiano (Italia)", "NL": "Nederlands (Nederland)", "PL": "Polski (Polska)", "AR": "العربية (العراق)"}, "CURRENT_VALUE": "Current value", "TARGET_VALUE": "Target value", "LAST_UPDATED": "Last Updated", "CREATED_BY": "Created By", "NO_DATA_MESSAGE": "No Data", "TAGS": "Tags", "CREATED": "Created", "APPLIED_DATE": "Applied Date", "HIRED_DATE": "<PERSON><PERSON>", "REJECTED_DATE": "Rejected Date", "TIME_TRACKING": "Time Tracking", "CREATED_AT": "Created At"}, "FORM": {"USERNAME": "שם משתמש", "PASSWORD": "סיסמה", "CONFIRM": "אשר", "FILTER": "Filter", "EMAIL": "דוא\"ל", "LABELS": {"NAME": "שם", "PHONE_NUMBER": "מס<PERSON>ר טלפון", "WEBSITE": "Website", "FIRST_NAME": "שם פרטי", "LAST_NAME": "שם משפחה", "FROM": "From", "TO": "To", "EMPLOYEE": "עובד", "START_DATE": "Date when started work (optional)", "APPLIED_DATE_LABEL": "Date when applied", "IMAGE_URL": "תמונה URL", "CV_URL": "CV URL (optional)", "DOCUMENT_URL": "Document URL", "CURRENCY": "מטבע", "DATE_TYPE": "סוג תאריך ברירת מחדל", "ADD_TEAM": "Add New Team", "EDIT_TEAM": "Edit Team", "OFFICIAL_NAME": "Official Name", "PROFILE_LINK": "Profile Link", "START_WEEK_ON": "Start Week On", "TAX_ID": "Tax ID", "TIME_FORMAT": "Time Format", "COUNTRY": "מדינה", "CITY": "עיר", "ADDRESS": "Address", "ADDRESS_2": "Address 2", "LOGO_ALIGNMENT": "Logo Alignment", "BRAND_COLOR": "Brand Color", "DATE_FORMAT": "Date Format", "CHOOSE_TIME_ZONE": "Choose Time Zone", "START_TIME": "Default Work Day Start Time", "END_TIME": "Default Work Day End Time", "POSTCODE": "Postcode (Zip)", "PAY_PERIOD": "Pay Period", "BILL_RATE": "תעריף חיוב (לשעה)", "CURRENCY_PER_HOUR": "מטבע", "RECURRING_WEEKLY_LIMIT": "Recurring Weekly Limit (hours)", "ROLE": "Role", "SOURCE": "Source (optional)", "EMAILS": "Email Addresses", "PROJECTS_OPTIONAL": "Projects (Optional)", "CONTACTS_OPTIONAL": "Contacts (Optional)", "DEPARTMENTS_OPTIONAL": "Departments (Optional)", "TEAMS_OPTIONAL": "Teams (Optional)", "PROJECTS": "פרויקטים", "ADD_NEW_DEPARTMENT": "Add New Department", "EDIT_DEPARTMENT": "Edit Department", "TYPE_OF_BONUS": "Employee Bonus Type", "BONUS_PERCENTAGE": "Bonus Percentage", "ENABLE_DISABLE_INVITES": "Enable/Disable Invites", "ALLOW_USER_INVITES": "Allow Users to send invites", "INVITE_EXPIRY_PERIOD": "Invite Expiry Period (in Days)", "EMPLOYMENT_TYPES": "Employment Type", "ADD_NEW_EMPLOYMENT_TYPE": "Add New Employment Type", "OFFER_DATE": "Offer Date (optional)", "ACCEPT_DATE": "Accept Date (optional)", "APPLIED_DATE": "Applied Date (optional)", "EDUCATION": "Education (optional)", "EXPERIENCE": "Work experience (optional)", "SKILLS": "Skills (optional)", "HIRED_DATE": "Hired Date (optional)", "REJECT_DATE": "Reject Date (optional)", "DOCUMENT_NAME": "Document name", "FEEDBACK_DESCRIPTION": "Feedback description", "EMAIL_INVITATION": "Enter email to send invitation", "SELECT_EQUIPMENT": "Select equipment", "SELECT_SHARE_REQUEST_DATE": "Select request date", "SELECT_SHARE_START_DATE": "Select start date", "SELECT_SHARE_END_DATE": "Select end date", "SELECT_EMPLOYEE": "Select employee", "SELECT_TEAM": "<PERSON><PERSON><PERSON>ה", "ENABLE_DISABLE_FUTURE_DATE": "Enable/Disable future dates", "ALLOW_FUTURE_DATE": "Allow switching to future periods", "REGISTRATION_DATE": "Registration Date", "ORGANIZATION_NAME": "Organization Name", "MEETING_AGENDA": "Agenda", "MEETING_LOCATION": "Location", "MEETING_DESCRIPTION": "Description", "MEETING_INVITEES": "Invitees", "TITLE": "Title", "DATE": "תאריך", "TIME": "Time", "DURATION": "Duration", "CANDIDATE": "Candidate", "INTERVIEWERS": "Interviewers", "LOCATION": "Location", "NOTE": "Note", "PREFERRED_LANGUAGE": "Preferred Language", "DESCRIPTION": "Description", "DESCRIPTION_OPTIONAL": "Description (optional)", "ADD_OR_REMOVE_EMPLOYEES": "Add or remove employees", "ADD_REMOVE_MANAGERS": "Add or Remove Managers", "ADD_REMOVE_MEMBERS": "Add or Remove Members", "SHORT_DESCRIPTION": "Short Description", "ENABLE_EMPLOYEE_FEATURES": "Enable Employee Features", "REVOKE_EMPLOYEE_FEATURES": "Revoke Employee Features", "STATUS": "עבודה", "FISCAL_YEAR_START_DATE": "Fiscal Year Start Date", "FISCAL_YEAR_END_DATE": "Fiscal Year End Date", "TAX_AND_DISCOUNT_INVOICE_ITEMS_SEPARATELY": "Tax And Discount Invoice Items Separately", "ALLOW_TAXING_AND_DISCOUNTING_OF_INVOICE_ITEMS_SEPARATELY": "Allow taxing and discounting of invoice items separately", "DISCOUNT_AFTER_TAX": "Discount after tax", "APPLY_DISCOUNT_AFTER_TAX_FOR_INVOICES_AND_ESTIMATES": "Apply discount after tax for invoices and estimates", "FIND_ADDRESS": "Find Address", "LINKEDIN": "LinkedIn", "FACEBOOK": "Facebook", "INSTAGRAM": "Instagram", "TWITTER": "Twitter", "GITHUB": "<PERSON><PERSON><PERSON>", "GITLAB": "Gitlab", "UPWORK": "Upwork", "STACK_OVERFLOW": "Stackoverflow", "PROJECT_URL": "Project URL", "CLIENTS": "לקוחות", "IS_PROJECT_OPEN_SOURCE": "Is Project Open-Source", "OPEN_SOURCE_PROJECT_URL": "Open-Source Project URL", "EMPLOYEE_LEVEL": "Employee Level", "UNIT": "Unit", "SELECT_EXISTING_OBJECTIVE": "Select Existing Objective", "LENGTH": "Length", "DATE_START": "Start date", "END_DATE": "תאריך סיום", "GOAL": "Goal", "DOWNLOAD_REQUEST_FORM": "Download Request Form", "COORDINATE": {"TITLE": "COORDINATES", "LATITUDE": "Latitude", "LONGITUDE": "Longitude"}, "PUBLIC_LINK": "Public Link", "DEFAULT_TERMS": "Default terms for invoices and estimates", "CONVERT_ESTIMATES": "Convert Estimates", "ALLOW_CONVERTING": "Automatically convert accepted estimate to invoice", "DEFAULT_DAYS": "Default days until invoices and estimates are due.", "TEMPLATE_NAME": "Template Name", "TEMPLATE_BODY": "Template Body", "TEMPLATE_PREVIEW": "Template Preview", "LANGUAGE": "Language", "DEFAULT_INVOICE_TEMPLATE": "Default Invoice Template", "DEFAULT_ESTIMATE_TEMPLATE": "Default Estimate Template", "DEFAULT_RECEIPT_TEMPLATE": "Default Receipt Template", "DEFAULT": "Default Organization", "INVITATION_EXPIRATION": "Invitation Expiration", "PERIOD": "Period", "REGISTER_AS_EMPLOYEE_OF_ORGANIZATION": "Do you want to register as Employee of Organization?", "COVER_LETTER": "Cover Letter", "DETAILS": "Details", "HOURLY_RATE": "Hourly Rate", "ATTACHMENTS": "Attachments", "UPWORK_ORGANIZATION_ID": "Upwork Organization ID", "UPWORK_ORGANIZATION_NAME": "Upwork Organization Name", "UPWORK_ID": "Upwork ID", "LINKEDIN_ID": "LinkedIn ID", "AUTO_SYNC_TASKS": "Auto-sync tasks", "AUTO_SYNC_TASKS_BASED_ON_LABEL": "Is tasks Auto-sync based on Label?", "AUTO_SYNC_TAG": "Label", "GITHUB_REPOSITORY": "GitHub Repository", "PROJECT": "Project", "ENABLE_JOBS_SEARCH_MATCHING_ANALYSIS": "האם ברצונך להפעיל חיפוש משרות וניתוח התאמה?", "ENABLE_EMPLOYEE_PERFORMANCE_ANALYSIS": "האם ברצונך להפעיל ניתוח ביצועי עובדים?", "STANDARD_WORK_HOURS_PER_DAY": "שעות עבודה סטנדרטיות ליום"}, "PLACEHOLDERS": {"NAME": "שם", "PHONE_NUMBER": "מס<PERSON>ר טלפון", "DEFAULT": "Select Default", "FIRST_NAME": "שם פרטי", "LAST_NAME": "שם משפחה", "COMPANY_NAME": "שם החברה", "ALL_EMPLOYEES": "כל העובדים", "CURRENCY": "מטבע", "ALL_CURRENCIES": "כל המטבעות", "ALL_DEPARTMENTS": "כל המחלקות", "ALL_POSITIONS": "כל העמדות", "START_DATE": "תאריך", "PICK_DATE": "תבחר תאריך", "DATE_TYPE": "כל סוגי התאריכים", "BILLING": "Billing", "BILLABLE": "Billable", "CODE": "Code", "COLOR": "Color", "WEBSITE": "Website", "CURRENCY_POSITION": "Currency Position", "FISCAL_INFORMATION": "Fiscal Information", "IMAGE_URL": "תמונה", "ADD_DEPARTMENT": "הוסף מחלקה", "ADD_POSITION": "Add position", "ADD_VENDOR": "הוסף ספק", "ADD_SKILL": "Add skill", "ADD_CANDIDATE_QUALITY": "Add personal quality", "ADD_TECHNOLOGY": "Add technology", "ADD_EXPENSE_CATEGORY": "Add expense category", "CONTACTS": "Contacts", "START_DATE_PROJECT": "Project Start Date", "END_DATE_PROJECT": "Project End Date", "TEAM_NAME": "Team Name", "ADD_REMOVE_MEMBERS": "Add or Remove Team Members", "ADD_REMOVE_MANAGERS": "Add or Remove Team Managers", "MEMBERS_COUNT": "Members count", "OFFICIAL_NAME": "Enter Official Name", "PROFILE_LINK": "Enter Profile Link", "START_WEEK_ON": "Start Week On", "TAX_ID": "Tax ID", "COUNTRY": "מדינה", "CITY": "עיר", "ADDRESS": "Address", "ADDRESS_2": "Address 2", "EDUCATION": "Education", "EXPERIENCE": "Work experience", "SKILLS": "Skills", "POSTCODE": "Postcode (Zip)", "BILL_RATE": "Bill Rate / hr", "RECURRING_WEEKLY_LIMIT": "Recurring Weekly Limit (hours)", "EMAILS": "Type an email address and press enter", "ROLE": "Select Role", "PROJECTS": "Choose Projects", "CHOOSE_FORMAT": "Choose Format", "CHOOSE_TIME_ZONE": "Choose Time Zone", "START_TIME": "HH:mm", "END_TIME": "HH:mm", "ADD_COLOR": "Add Color", "ALIGN_LOGO_TO": "Align Logo To", "DEPARTMENTS": "מחלקות", "TEAMS": "Teams", "NUMBER_FORMAT": "Select Number Format", "REGIONS": "Select Region", "REMOVE_IMAGE": "Remove image", "UPLOADER_PLACEHOLDER": "תמונה", "UPLOADER_DOCUMENT_PLACEHOLDER": "URL", "ADD_REMOVE_EMPLOYEES": "Add or Remove Employees", "ADD_REMOVE_PROJECTS": "הוסף או הסר פרויקט", "ADD_REMOVE_TEAMS": "הוסף או הסר צוותים", "ADD_REMOVE_CANDIDATE": "Add Candidate", "ADD_REMOVE_EMPLOYEE": "Add Interviewer", "ADD_REMOVE_INTERVIEWER": "Select interviewer", "ADD_REMOVE_CANDIDATES": "Add or Remove Candidates", "ADD_REMOVE_USERS": "Add or Remove Users", "ADD_REMOVE_ORGANIZATIONS": "Add or Remove Organizations", "ADD_ORGANIZATIONS": "Add Organizations", "DATE": "תאריך", "VALUE": "ערך", "SELECT_CURRENCY": "Select Currency", "TYPE_OF_BONUS": "Select Type of Bonus", "BONUS_PERCENTAGE": "Bonus Percentage", "ENABLE_INVITES": "Enable Invites", "INVITE_EXPIRY_PERIOD": "Invites valid upto", "SWITCH_PROJECT_STATE": "Public", "CHOOSE_EMPLOYEES": "Choose employee/s", "CHOOSE_TEAMS": "Choose team/s", "CHOOSE_APPROVAL_POLICY": "<PERSON>ose Approval Policy", "EMPLOYMENT_TYPES": "Employment Type", "REGISTRATION_DATE": "Organization Registration Date", "ORGANIZATIONS": "Choose Organizations", "ORGANIZATION": "Select Organization", "DOCUMENT_NAME": "Document name", "FEEDBACK_DESCRIPTION": "Feedback description", "PREFERRED_LANGUAGE": "Preferred Language", "OWNER": "Owner", "TASK_VIEW_MODE": "Task view mode", "SELECT_STATUS": "Select Status", "LINKEDIN": "LinkedIn", "FACEBOOK": "Facebook", "INSTAGRAM": "Instagram", "TWITTER": "Twitter", "GITHUB": "<PERSON><PERSON><PERSON>", "GITLAB": "Gitlab", "UPWORK": "Upwork", "STACK_OVERFLOW": "Stackoverflow", "STATUS": "עבודה", "INVOICE_NUMBER": "Invoice Number", "PROJECT_URL": "Project URL", "CLIENTS": "לקוחות", "BUDGET_TYPE": "סוג", "BUDGET": "Budget", "HOURS": "Hours", "COST": "Cost", "MAKE_COM_CLIENT_ID": "הזן את מזהה הלקוח Make.com שלך", "MAKE_COM_CLIENT_SECRET": "הזן את סיסמת הלקוח Make.com שלך", "ZAPIER_CLIENT_ID": "הזן את מזהה הלקוח Zapier שלך", "ZAPIER_CLIENT_SECRET": "הזן את סיסמת הלקוח Zapier שלך", "ADD_EDUCATION": {"SCHOOL_NAME": "School name", "DEGREE": "Degree/Diploma", "FIELD_OF_STUDY": "Field(s) of study", "DATE_OF_COMPLETION": "Date of completion", "ADDITIONAL_NOTES": "Additional notes (optional)"}, "ADD_EXPERIENCE": {"OCCUPATION": "Occupation", "ORGANIZATION": "Organization name", "DURATION": "Duration", "DESCRIPTION": "Description (optional)"}, "ADD_INTERVIEW": {"TITLE": "Title", "DATE": "תאריך", "TIME": "Time", "DURATION": "Duration", "INTERVIEWERS": "Interviewers", "LOCATION": "Location", "NOTE": "Note (optional)", "TYPE": "Interview type", "CALL": "Call", "MEETING": "Meeting"}, "MEETING_AGENDA": "Agenda", "MEETING_LOCATION": "Location", "MEETING_DESCRIPTION": "Description", "BUFFER_TIME": "Buffer minutes", "BREAK_TIME": "Break minutes", "DESCRIPTION": "Description", "DURATION": "Duration", "TITLE": "Title", "SHORT_DESCRIPTION": "Short Description", "EG_FULL_STACK_WEB_DEVELOPER": "E.g. Full-Stack Web Developer", "COORDINATE": {"LATITUDE": "Latitude", "LONGITUDE": "Longitude"}, "SELECT_EXPENSE": "Select Expense", "ADD_TITLE": "Add title", "ALL_PROJECTS": "All Projects", "ALL_TEAMS": "All Teams", "UPWORK_API_KEY": "Upwork API key", "UPWORK_SECRET": "Upwork Secret", "SELECT_COMPANY": "Select Company", "TYPE_SEARCH_REQUEST": "Type your search request here...", "SELECT_ICON": "Select Icon", "SELECT": "Select", "SPRINT_LENGTH": "Sprint length", "SPRINT_START_DATE": "Sprint start date", "SPRINT_END_DATE": "Sprint end date", "SPRINT_GOAL": "Sprint goal", "POLICY_NAME": "Policy Name", "SELECT_DATE": "Select Date", "DAYS_UNTIL_DUE": "Days Until Due", "TEMPLATES": "Templates", "INVOICE_TEMPLATE": "Invoice Template", "ESTIMATE_TEMPLATE": "Estimate Template", "RECEIPT_TEMPLATE": "Receipt Template", "INVITATION_EXPIRATION": "Invitation Expiration", "ADD_PROJECT": "Add project", "ADD_TEAM": "Add team", "ADD_EMPLOYEE": "Add employee", "ADD_ORGANIZATION": "Add organization", "DRAG_DROP_FILE": "Drag and Drop the file here", "UPWORK_ORGANIZATION_ID": "Upwork Organization ID", "UPWORK_ORGANIZATION_NAME": "Upwork Organization Name", "UPWORK_ID": "Upwork ID", "LINKEDIN_ID": "LinkedIn ID", "AUTO_SYNC_TASKS": "Auto Sync Tasks", "AUTO_SYNC_TASKS_BASED_ON_LABEL": "Auto-sync Tasks On Label", "AUTO_SYNC_TAG": "Select Auto-sync Label", "SELECT_PROJECT": "Select Project", "ENABLE_JOBS_SEARCH_MATCHING_ANALYSIS": "אפשר חיפוש משרות וניתוח התאמהs", "ENABLE_EMPLOYEE_PERFORMANCE_ANALYSIS": "אפשר ניתו<PERSON> ביצועי עובדים", "STANDARD_WORK_HOURS_PER_DAY": "הזן את מספר שעות העבודה הסטנדרטיות ליום (לדוגמ<PERSON>, 8)"}, "RATES": {"DEFAULT_RATE": "Default Rate", "EXPECTED_RATE": "Expected Rate", "LIMITS": "Limits"}, "CHECKBOXES": {"INCLUDE_DELETED": "Include Deleted", "INCLUDE_ARCHIVED": "Include Archived", "ONLY_PAST": "Only Past", "ONLY_FUTURE": "Only Future"}, "NOTIFICATIONS": {"STARTED_WORK_ON": "It's required to enter the date when the employee started work to generate employee payroll, enable the employee to participate in split expenses, see employee statistics"}, "ARCHIVE_CONFIRMATION": {"SURE": "Are you sure you want to archive", "RECORD": "תקליט", "CANDIDATE": "candidate"}, "CANDIDATE_ACTION_CONFIRMATION": {"SURE": "Are you sure you want to ", "RECORD": "תקליט", "CANDIDATE": "candidate", "HIRE": "hire", "REJECT": "reject"}, "DELETE_CONFIRMATION": {"REMOVE_ALL_DATA": "Are you sure you want to remove all data?", "DELETE_ACCOUNT": "Are you sure you want to delete your account?", "REMOVE_USER": " from your organization", "SURE": "אתה בטוח שאתה רוצה למחוק", "RECORD": "תקליט", "USER_RECORD": "from your organization", "EMPLOYEE": "עובד", "CANDIDATE": "candidate", "EXPENSE": "הוצאה", "USER": "user", "INVITATION": "invitation", "DELETE_USER": " from database as it is associated only to current organization", "EVENT_TYPE": "Event type"}, "COUNTDOWN_CONFIRMATION": {"WAS": "was", "ENABLED": "enabled", "DISABLED": "disabled", "WAIT_UNTIL_RELOAD": "Please wait till application reloads"}, "ERROR": {"PROJECT_NAME": "נדרש שם הפרויקט.", "PROJECT_URL": "כתובת האתר של הפרויקט אינה חוקית.", "OPEN_SOURCE_PROJECT_URL": "כתובת האתר של פרוייקט קוד פתוח אינה חוקית.", "INVALID_IMAGE_URL": "כתובת URL של התמונה אינה תקינה."}}, "POP_UPS": {"SELECT_ORGANIZATION": "אנא בחר ארגונים מהתפריט שלמעלה", "ADD_INCOME": "הוסף הכנסה", "ADD_EXPENSE": "הוסף הוצאה", "EMPLOYEE": "עובד", "EDIT_INCOME": "ערוך הכנסה", "EDIT_EXPENSE": "ערוך הוצאות", "EDIT_PAGE": "Edit Page", "SHORT_DESCRIPTION": "Short Description", "OVERVIEW": "Overview", "COMPANY_NAME": "Company Name", "NAME": "שם", "YEAR": "Year", "BANNER": "Banner", "SIZE": "Size", "YEAR_FOUNDED": "Year Founded", "COMPANY_SIZE": "Company Size", "CLIENT_FOCUS": "Client Focus", "DUPLICATE": "Duplicate", "DATE": "תאריך", "PICK_DATE": "תבחר תאריך", "ALL_CONTACTS": "All Contacts", "CONTACT": "Contact", "ALL_VENDORS": "Vendor / Merchant", "ALL_CATEGORIES": "כל הקטגוריות", "CATEGORY_NAME": "שם קטגוריה", "EXPENSE_VALUE": "ערך הוצאה", "TAX_LABEL": "Tax Label", "TAX_TYPE": "Tax Type (% or Value)", "TAX_RATE": "Tax Rate", "RECURRING_EXPENSES": "הוצאות חודשיות חוזרות ונשנות", "PURPOSE": "Purpose", "BACK_TO_WORK": "לחזור לעבודה", "END_WORK": "סיום העבודה עבור", "START_WORK_FOR": "Start work for", "AMOUNT": "כמות", "NOTES": "הערות", "EDIT": "ערוך", "ADD": "הוסף", "DELETE_RECURRING_EXPENSE": "Delete Recurring Expense", "DELETE_ONLY_THIS": "Delete only this", "DELETE_THIS_FUTURE": "Delete this and future", "DELETE_ALL_ENTRIES": "Delete all entries", "CONFIRM": "אשר", "ARE_YOU_SURE_YOU_WANT_TO_RESEND_THE_INVITE_TO": "Are you sure you want to resend the invite to", "OK": "OK", "CANCEL": "בטל", "ARE_YOU_SURE_YOU_WANT_TO_CHANGE_THE": "Are you sure you want to change the", "RECURRING_EXPENSE": "This is a recurring expense", "SPLIT_EXPENSE_WITH_INFO": "This is a split expense with original value: {{ originalValue }}, divided by the number of employees: {{ employeeCount }}", "STARTS_ON": "Starts On", "EXPENSE_HISTORY": "Expense History", "NEW_EXPENSE_VALUE": "New Expense Value", "OFFICIAL_NAME": "This name would be used in tax invoices etc.", "ADD_EVENT_TYPE": "Add Event Type", "EDIT_EVENT_TYPE": "Edit Event Type", "AWARDS": "Awards", "LEVEL": "Level", "LANGUAGES": "Languages", "TOTAL_INCOME_OR_MONTHLY_INCOME": "Total Income/Monthly Income", "PROFITS": "Profits", "BONUSES_PAID": "Bonuses Paid", "TOTAL_HOURS_WORKED_OVER_GAUZY": "Total Hours worked over Gauzy", "MINIMUM_PROJECT_SIZE": "Minimum Project Size", "PROJECTS_COUNT": "Projects Count", "CLIENTS_COUNT": "Clients Count", "EMPLOYEES_COUNT": "Employees Count", "DETAILS": "Details", "SKILLS": "Skills", "PRIVACY": "Privacy", "SELECT_TIMEZONE": "Select Timezone", "SHOW_AVERAGE_BONUS": "Show Average Bonus", "SHOW_AVERAGE_INCOME": "Show Average Income", "SHOW_PAYPERIOD": "Show Pay Period", "SHOW_ANONYMOUS_BONUS": "Show Anonymous Bonus", "SHOW_AVERAGE_EXPENSES": "Show Average Expenses", "SHOW_BILLRATE": "Show Bill Rate", "SHOW_START_WORK_ON": "Show Started Work On", "SHOW_CLIENTS": "Show Clients", "DISPLAY_BONUS_ANONYMOUSLY": "Display bonus Anonymously", "SOURCE": "Source", "DESCRIPTION": "Description", "MAIN": "עיקרי", "CATEGORIES": "Categories", "ADD_LANGUAGE": "Add language", "REGISTER_AS_EMPLOYEE_TOOLTIP": "You must be employee in order to be able to track time, create split expenses and use other employees related features."}, "MENU": {"PIPELINES": "Pipelines", "DASHBOARD": "לוח מחוונים", "DASHBOARDS": "Dashboards", "APPOINTMENTS": "Appointments", "FAVORITES": "מועדפים", "ACCOUNTING": "Accounting", "INCOME": "הכנסה", "EXPENSES": "הוצאות", "RECURRING_EXPENSE": "Recurring Expenses", "POSITIONS": "עמדות", "INTEGRATIONS": "Apps & Integrations", "UPWORK": "Upwork", "MAKE_COM": "Make.com", "ZAPIER": "Zapier", "PROPOSALS": "Proposals", "TIME_OFF": "Time Off", "APPROVALS": "Approvals", "HELP": "עזרה", "ABOUT": "עלינו", "CONTACTS": "Contacts", "ADMIN": "מנהל", "EMPLOYEE_LEVEL": "Employee Level", "EMPLOYEES": "עובדים", "MANAGE": "Manage", "CANDIDATES": "Candidates", "ORGANIZATIONS": "ארגונים", "SETTINGS": "הגדרות", "GENERAL": "כללי", "EMAIL_HISTORY": "Email History", "USERS": "Users", "ROLES": "Roles & Permissions", "DANGER_ZONE": "Danger Zone", "FILE_STORAGE": "File Storage", "INVITE_PEOPLE": "Invite People", "IMPORT_EXPORT": {"IMPORT_EXPORT": "Import/Export", "IMPORT_EXPORT_DATA": "Import / Export Data", "IMPORT": "Import", "IMPORT_HISTORY": "Import History", "IMPORT_DATE_TIME": "Imported Date & Time", "EXPORT": "Export", "MERGE": "Merge data", "CLEAN_UP": "Clean up before import", "EXPORT_MESSAGE": "Click \"EXPORT\" to download all tables from the database. ", "IMPORT_MESSAGE": "Click \"IMPORT\" to insert all tables in the database. The name of the file for upload should be: \"archive.zip\". Choose also between options \"Merge data\" and \"Clean up before import\".", "IMPORT_HISTORY_MESSAGE": "You can track your recently imported files here", "SELECT_FILE": "Select File", "DROP_FILE": "Drop the file here", "NO_DROP_FILE": "Can't drop the file here", "BROWSE": "BROWSE", "NAME": "שם", "SIZE": "Size", "PROGRESS": "Progress", "STATUS": "עבודה", "ACTIONS": "Actions", "QUEUE_PROGRESS": "Queue progress:", "CANCEL": "בטל", "REMOVE": "Remove", "WRONG_FILE_NAME": "Wrong file name!!!", "CORRECT_FILE_NAME": "The file name should be: \"archive.zip\"", "DOWNLOAD_TEMPLATES": "Download templates", "MIGRATE_TO_CLOUD": "Migrate to <PERSON>", "IMPORT_INFO": "Click \"Import\" to import your data into Gauzy DB.", "EXPORT_INFO": "Click \"Export\" to export data from Gauzy DB.", "DOWNLOAD_TEMPLATES_INFO": "Click \"Download Templates\" to download Zip archive with CSV templates for the format of Gauzy data.", "MIGRATE_TO_CLOUD_INFO": "Click \"Migrate to Ever Gauzy Cloud\" to migrate from self-hosted to cloud edition.", "MIGRATE_SUCCESSFULLY": "Gauzy Cloud migration for '{{ tenant }}' successfully!", "ACCOUNTING_TEMPLATE": "Accounting Template", "ACTIVITY": "Activity", "APPROVAL_POLICY": "Approval Policy", "AVAILABILITY_SLOT": "Availability Slot", "CANDIDATE": "Candidate", "CONTACT": "Contact", "COUNTRY": "מדינה", "CURRENCY": "מטבע", "DEAL": "Deal", "EMAIL": "אימייל", "EMPLOYEE": "עובד", "EQUIPMENT": "Equipment", "EVENT_TYPES": "Event Types", "EXPENSE": "הוצאה", "GOAL": "Goal", "INCOME": "הכנסה", "INTEGRATION": "Integration", "INVITE": "Invite", "INVOICE": "Invoice", "JOB": "Job", "KEY_RESULT": "Key Results", "KNOWLEDGE_BASE": "Knowledge Base", "LANGUAGE": "Language", "MERCHANT": "Merchant", "ORGANIZATION": "Organization", "PAYMENT": "Payment", "PIPELINE": "Pipeline", "PIPELINE_STAGE": "Pipeline Stage", "PRODUCT": "Product", "PROPOSAL": "Предложение", "REPORT": "Report", "REQUEST_APPROVAL": "Request Approval", "ROLE": "Role", "SKILL": "Skill", "TAG": "Tag", "TASK": "Task", "TENANT": "Tenant", "TENANT_SETTING": "Tenant Setting", "TIME_OFF_POLICY": "Time off policy", "TIME_SHEET": "Time Sheet", "USER": "User", "CANDIDATE_CRITERION_RATING": "Candidate Criterion Rating", "CANDIDATE_DOCUMENT": "Candidate Document", "CANDIDATE_EDUCATION": "Candidate Education", "CANDIDATE_EXPERIENCE": "Candidate Experience", "CANDIDATE_FEEDBACK": "Candidate <PERSON><PERSON><PERSON>", "CANDIDATE_INTERVIEW": "Candidate Interview", "CANDIDATE_INTERVIEWER": "Candidate Interviewer", "CANDIDATE_PERSONAL_QUALITY": "Candidate Personal Quality", "CANDIDATE_SKILL": "Candidate <PERSON><PERSON>", "CANDIDATE_SOURCE": "Candidate Source", "CANDIDATE_TECHNOLOGY": "Candidate Technology", "ORGANIZATION_AWARD": "Organization Award", "ORGANIZATION_CONTACT": "Organization Contact", "ORGANIZATION_DEPARTMENT": "Organization Department", "ORGANIZATION_DOCUMENT": "Organization Document", "ORGANIZATION_EMPLOYEE_LEVEL": "Organization Employee Level", "ORGANIZATION_EMPLOYMENT_TYPE": "Organization Employment Type", "ORGANIZATION_LANGUAGES": "Organization Languages", "ORGANIZATION_POSITION": "Organization Position", "ORGANIZATION_PROJECT": "Organization Project", "ORGANIZATION_RECURRING_EXPENSE": "Organization Recurring Expense", "ORGANIZATION_SPRINT": "Organization Sprint", "ORGANIZATION_TEAM": "Organization Team", "ORGANIZATION_TEAM_EMPLOYEE": "Organization Team Employee", "ORGANIZATION_VENDOR": "Organization Vendor", "EMAIL_TEMPLATE": "<PERSON>ail Te<PERSON>late", "ESTIMATE_EMAIL": "Estimate Email", "EMPLOYEE_APPOINTMENT": "Employee Appointment", "EMPLOYEE_AWARD": "Employee Award", "EMPLOYEE_PROPOSAL_TEMPLATE": "Employee Proposal Template", "EMPLOYEE_RECURRING_EXPENSE": "Employee Recurring Expense", "EMPLOYEE_SETTING": "Employee Setting", "EMPLOYEE_UPWORK_JOB_SEARCH_CRITERION": "Employee Upwork Job Search Criterion", "INTEGRATION_ENTITY_SETTING": "Integration Entity Setting", "INTEGRATION_ENTITY_SETTING_TIED_ENTITY": "Integration Entity Setting Tied Entity", "INTEGRATION_MAP": "Integration Map", "INTEGRATION_SETTING": "Integration Setting", "INTEGRATION_TENANT": "Integration Tenant", "INTEGRATION_TYPE": "Integration Type", "INVITE_ORGANIZATION_CONTACT": "Invite Organization Contact", "INVITE_ORGANIZATION_DEPARTMENT": "Invite Organization Department", "INVITE_ORGANIZATION_PROJECT": "Invite Organization Project", "PRODUCT_CATEGORY": "Product Category", "PRODUCT_CATEGORY_TRANSLATION": "Product Category Translation", "PRODUCT_GALLERY_ITEM": "Product Gallery Item", "PRODUCT_OPTION": "Product Option", "PRODUCT_OPTION_GROUP": "Product Option Group", "PRODUCT_OPTION_GROUP_TRANSLATION": "Product Option Group Translation", "PRODUCT_OPTION_TRANSLATION": "Product Option Translation", "PRODUCT_STORE": "Product Store", "PRODUCT_TRANSLATION": "Product Translation", "PRODUCT_TYPE": "Product Type", "PRODUCT_TYPE_TRANSLATION": "Product Type Translation", "PRODUCT_VARIANT": "Product Variant", "PRODUCT_VARIANT_PRICE": "Product Variant Price", "PRODUCT_VARIANT_SETTING": "Product Variant Setting", "REPORT_CATEGORY": "Report Category", "REPORT_ORGANIZATION": "Report Organization", "REQUEST_APPROVAL_TAG": "Request Approval Tag", "REQUEST_APPROVAL_EMPLOYEE": "Request Approval Employee", "REQUEST_APPROVAL_TEAM": "Request Approval Team", "SKILL_EMPLOYEE": "<PERSON><PERSON> Employee", "SKILL_ORGANIZATION": "Skill Organization", "TAG_CANDIDATE": "Tag Candidate", "TAG_EMPLOYEE": "Tag Employee", "TAG_EQUIPMENT": "Tag Equipment", "TAG_EVENT_TYPE": "Tag Event Type", "TAG_EXPENSE": "Tag Expense", "TAG_INCOME": "Tag Income", "TAG_INVOICE": "Tag Invoice", "TAG_ORGANIZATION_CONTACT": "Tag Organization Contact", "TAG_ORGANIZATION_DEPARTMENT": "Tag Organization Department", "TAG_ORGANIZATION_EMPLOYEE_LEVEL": "Tag Organization Employee Level", "TAG_ORGANIZATION_EMPLOYEE_TYPE": "Tag Organization Employee Type", "TAG_ORGANIZATION_EXPENSES_CATEGORY": "Tag Organization Expense Category", "TAG_ORGANIZATION_POSITION": "Tag Organization Position", "TAG_ORGANIZATION_PROJECT": "Tag Organization Project", "TAG_ORGANIZATION_TEAM": "Tag Organization Team", "TAG_ORGANIZATION_VENDOR": "Tag Organization Vendor", "TAG_ORGANIZATIONS": "Tag Organizations", "TAG_PAYMENT": "Tag Payments", "TAG_PRODUCT": "Tag Product", "TAG_PROPOSAL": "Tag Proposal", "TAG_TASK": "Tag Task", "TAG_USER": "Tag User", "TASK_EMPLOYEE": "Tag Employee", "TASK_TEAM": "Tag Team", "EQUIPMENT_SHARING": "Equipment Sharing", "EQUIPMENT_SHARE_POLICY": "Equipment Share Policy", "EXPENSE_CATEGORY": "Expense Category", "GOAL_KPI": "Goal Kpi", "GOAL_GENERAL_SETTING": "Goal General Setting", "GOAL_KPI_TEMPLATE": "Goal Kpi Template", "GOAL_TEMPLATE": "Goal Template", "GOAL_TIME_FRAME": "Goal Time Frame", "KNOWLEDGE_BASE_ARTICLE": "Knowledge Base Article", "KNOWLEDGE_BASE_AUTHOR": "Knowledge Base Author", "INVOICE_ESTIMATE_HISTORY": "Invoice Estimate History", "INVOICE_ITEM": "Invoice Item", "JOB_PRESET": "Job Preset", "JOB_PRESET_UPWORK_SEARCH_CRITERION": "Job Preset Upwork Search Criterion", "JOB_SEARCH_OCCUPATION": "Job Search Occupation", "JOB_SEARCH_CATEGORY": "Job Search Category", "KEY_RESULT_TEMPLATE": "Key Result Template", "KEY_RESULT_UPDATE": "Key Result Update", "ROLE_PERMISSION": "Role Permission", "TIME_OFF_POLICY_EMPLOYEE": "Time Off Policy Employee", "TIME_OFF_REQUEST": "Time Off Request", "TIME_OFF_REQUEST_EMPLOYEE": "Time Off Request Employee", "SCREENSHOT": "Screenshot", "TIME_LOG": "Time Log", "TIME_SLOT": "Time Slot", "TIME_SLOT_MINUTES": "Time Slot Minutes", "TIME_SLOT_TIME_LOGOS": "Time Slot Time Logs", "USER_ORGANIZATION": "User Organization", "WAREHOUSE": "Warehouse", "WAREHOUSE_MERCHANT": "Warehouse Merchant", "WAREHOUSE_PRODUCT": "Warehouse Product", "WAREHOUSE_PRODUCT_VARIANT": "Warehouse Product Variant", "WAREHOUSE_STORE": "Warehouse Store", "PRODUCT_IMAGE_ASSET": "Product Image Asset", "CUSTOM_SMTP": "Custom SMTP", "FEATURE": "Feature", "FEATURE_ORGANIZATION": "Feature Organization", "EXPORT_DATA": "Export Data", "IMPORT_DATA": "Import Data", "ALL_ENTITIES": "כל הישויות", "ACTIVITY_LOG": "יו<PERSON><PERSON> פעילות", "DAILY_PLAN": "תוכנית יומית", "ENTITY_SUBSCRIPTION": "מנוי ישות", "FAVORITE": "מועדף", "MENTION": "אזכ<PERSON>ר", "REACTION": "תגובה", "RESOURCE_LINK": "קיש<PERSON><PERSON> משאב", "SCREENING_TASK": "משי<PERSON><PERSON> סינון", "SOCIAL_ACCOUNT": "<PERSON><PERSON><PERSON><PERSON><PERSON> חברתי", "CHANGELOG": "יו<PERSON><PERSON> שינויים", "ISSUE_TYPE": "סוג בעיה", "TASK_ESTIMATION": "הערכת משימה", "TASK_LINKED_ISSUE": "בעיה מקושרת למשימה", "TASK_PRIORITY": "עדיפות משימה", "TASK_RELATED_ISSUE": "בעיה קשורה למשימה", "TASK_SIZE": "גודל משימה", "TASK_STATUS": "סטטוס משימה", "TASK_VERSION": "גרסת משימה", "TASK_RELATED_ISSUE_TYPE": "סוג בעיה קשורה", "ORGANIZATION_PROJECT_EMPLOYEE": "עובד פרויקט בארגון", "ORGANIZATION_PROJECT_MODULE": "מודול פרויקט בארגון", "ORGANIZATION_PROJECT_MODULE_EMPLOYEE": "עובד מודול פרויקט בארגון", "ORGANIZATION_SPRINT_EMPLOYEE": "עובד ספרינט בארגון", "ORGANIZATION_SPRINT_TASK": "משימת ספרינט בארגון", "ORGANIZATION_SPRINT_TASK_HISTORY": "היסטוריית משימת ספרינט בארגון", "ORGANIZATION_TASK_SETTING": "הגדרת משימה בארגון", "ORGANIZATION_TEAM_JOIN_REQUEST": "בקשת הצטרפות לצוות בארגון", "ORGANIZATION_GITHUB_REPOSITORY": "<PERSON><PERSON><PERSON><PERSON> של הארגון", "ORGANIZATION_GITHUB_REPOSITORY_ISSUE": "בעיה ב<PERSON><PERSON><PERSON><PERSON> של הארגון", "EMPLOYEE_AVAILABILITY": "זמינות עובד", "EMPLOYEE_NOTIFICATION": "הודעת עובד", "EMPLOYEE_PHONE": "טל<PERSON>ון עובד", "APPOINTMENT_EMPLOYEE": "פגישת עובד", "EMPLOYEE_NOTIFICATION_SETTING": "הגדרת הודעת עובד", "PRODUCT_REVIEW": "סקירת מוצר", "TASK_LINKED_ISSUES": "בעיות מקושרות", "TASK_VIEW": "תצוגת משימה"}, "TAGS": "Tags", "LANGUAGE": "Language", "LANGUAGES": "Languages", "EQUIPMENT": "Equipment", "EQUIPMENT_SHARING": "Equipment Sharing", "TASKS": "Tasks", "TASKS_SETTINGS": "הגדרות", "INVOICES": "Invoices", "ORGANIZATION": "Organization", "TENANT": "Tenant", "RECURRING_INVOICES": "Invoices Recurring", "INVOICES_RECEIVED": "Invoices Received", "ESTIMATES_RECEIVED": "Estimates Received", "ESTIMATES": "Estimates", "MY_TASKS": "My Tasks", "JOBS": "Jobs", "PROPOSAL_TEMPLATE": "Proposal Template", "JOBS_SEARCH": "Browse", "JOBS_MATCHING": "Matching", "TEAM_TASKS": "Team's Tasks", "TIME_ACTIVITY": "Time & Activity", "TIMESHEETS": "Timesheets", "SCHEDULES": "Schedules", "EMAIL_TEMPLATES": "Email Templates", "REPORTS": "Reports", "GOALS": "Goals", "ALL_REPORTS": "All Reports", "TIME_REPORTS": "Time Report", "WEEKLY_TIME_REPORTS": "Weekly Report", "ACCOUNTING_REPORTS": "Accounting Reports", "PAYMENT_GATEWAYS": "Payment Gateways", "SMS_GATEWAYS": "SMS Gateways", "CUSTOM_SMTP": "Custom SMTP", "INVENTORY": "Inventory", "SALES": "Sales", "PAYMENTS": "Payments", "FEATURES": "Features", "ACCOUNTING_TEMPLATES": "Accounting Templates", "FOCUS": "Focus", "APPLICATIONS": "Applications", "OPEN_GA_BROWSER": "פתח את <PERSON> בדפדפן", "START_SERVER": "התחל את השרת", "STOP_SERVER": "עצור את השרת"}, "SETTINGS_MENU": {"THEMES": "Themes", "LIGHT": "Light", "DARK": "Dark", "COSMIC": "Cosmic", "CORPORATE": "Corporate", "MATERIAL_LIGHT_THEME": "Material Light", "MATERIAL_DARK_THEME": "Material Dark", "GAUZY_LIGHT": "Gauzy Light", "GAUZY_DARK": "Gauzy <PERSON>", "LANGUAGE": "Language", "ENGLISH": "אנגלית", "FRENCH": "צָרְפָתִית", "BULGARIAN": "בולגרית", "HEBREW": "עִברִית", "RUSSIAN": "רוּסִי", "SPANISH": "ספרדית", "CHINESE": "סינית", "GERMAN": "גרמנית", "PORTUGUESE": "פורטוגזית", "ITALIAN": "איטלקית", "DUTCH": "הולנדית", "POLISH": "פולני", "ARABIC": "עֲרָבִית", "PREFERRED_LAYOUT": "Layout", "PREFERRED_LAYOUT_TOOLTIP": "This will only set the default layout and not change the layout on each page if you have already changed it once.", "RESET_LAYOUT": "Reset Layout", "RESET_LAYOUT_TOOLTIP": "Reset layout to default for all pages", "TABLE": "Table", "CARDS_GRID": "Cards Grid", "SPRINT_VIEW": "Sprint View", "QUICK_SETTINGS": "Quick Settings", "NO_LAYOUT": "אין פריסת נתונים זמינה"}, "CHANGELOG_MENU": {"HEADER": "What's new?", "LEARN_MORE_URL": "Learn more", "GAUZY_FEATURES": "Gauzy features"}, "REPORT_PAGE": {"MEMBERS_WORKED": "Members Worked", "MANUAL_TIME_EDIT_REPORT": "Manual Time Edit Report", "GROUP_BY": "Group", "DATE": "תאריך", "TO_DO": "To Do", "TIME": "Time", "PROJECT": "פרויקטים", "CLIENT": "לקוחות", "NOTES": "הערות", "PROJECTS_WORKED": "Projects Worked", "APPS_AND_URLS_REPORT": "Apps & URLs Report", "ACTIVITY": "Activity", "TOTAL_HOURS": "Total Hours", "EMPLOYEE": "עובד", "EMPLOYEES/TEAMS": "Employees /Teams", "NO_PROJECT": "No Project", "NO_EMPLOYEE": "No Employee", "TITLE": "Title", "ACTION": "Action", "TIME_SPAN": "Time Span", "REASON": "Reason", "CHANGED_AT": "Changed at", "DURATION": "Duration", "NO_TASK": "No Task", "FROM": "From", "WEEKLY_TIME_AND_ACTIVITY_REPORT": "Weekly Time and Activity Report", "TIME_AND_ACTIVITY_REPORT": "Time and Activity Report", "NO_CLIENT": "No Client", "ALL_REPORTS": "All Reports", "EXPENSES_REPORT": "Expenses Report", "CATEGORY": "קטגוריה", "DESCRIPTION": "Description", "AMOUNT": "כמות", "NO_EXPENSES": "No Expenses", "PAYMENT_REPORT": "Payments Report", "NO_PAYMENTS": "No Payments", "CONTACT": "Contact", "CURRENCY": "מטבע", "NOTE": "Note", "AMOUNT_OWED": "Amount Owed Report", "CURRENT_RATE": "Current Rate", "HOURS": "Hours", "SPENT": "Spent", "BUDGET": "Budget", "REMAINING": "Remaining", "WEEKLY_LIMIT_REPORT": "Weekly Limit Report", "NO_EMPLOYEES": "No Employees", "DAILY_LIMIT_REPORT": "Daily Limit Report", "LIMIT": "Limit", "SPENT_HOURS": "Spent Hours", "REMAINING_HOURS": "Remaining Hours", "PROJECT_BUDGET_REPORTS": "Project Budget Reports", "CLIENT_BUDGET_REPORTS": "Client Budget Reports", "EXPENSE": "הוצאה", "PAYMENT": "Payment", "NO_EMPLOYEES_WORKED": "No employees worked", "WEEKLY_TOTAL": "Weekly Total", "NO_DATA": {"APP_AND_URL_ACTIVITY": "No records found. Please select date range, employee or project.", "MANUAL_ACTIVITY": "No records found. Please select date range, employee or project.", "AMOUNT_OWED": "No Amount Owed", "WEEKLY_TIME_AND_ACTIVITY": "You have not any tracked time and activity yet for these day's.", "DAILY_TIME_AND_ACTIVITY": "There is no time and activity tracked yet for this day. Please use the <a href={{downloadURL}} rel=\"noopener\" target=\"_blank\">Gauzy Desktop Timer App</a>.", "PROJECT_BUDGET": "You have not created any project with budget.", "CLIENT_BUDGET": "You have not created any client with budget."}, "STANDARD_WORK_HOURS": "שעות עבודה סטנדרטיות ({{hours}})"}, "INTEGRATIONS": {"AVAILABLE_INTEGRATIONS": "Available Apps & Integrations", "ADDED_UPWORK_TRANSACTION": "Added Upwork Transaction", "TOTAL_UPWORK_TRANSACTIONS_SUCCEED": "Total expense transactions succeed: {{ totalExpenses }}. Total income transactions succeed: {{ totalIncomes }}", "HUBSTAFF_PAGE": {"TITLE": "<PERSON><PERSON><PERSON><PERSON>", "SELECT_ORGANIZATION": "Select Organization", "SYNCED_PROJECTS": "Projects synced", "SETTINGS_UPDATED": "Updated settings for integration", "SYNCED_ENTITIES": "Auto synced entities", "TOOLTIP_ACTIVITY_INFO": "Date range Limit: 7 days Earliest Date: 6 full months", "DATE_RANGE_PLACEHOLDER": "Choose date between", "CLIENT_ID": "Hubstaff Client ID", "CLIENT_SECRET": "Hubstaff Client Secret", "GRANT_PERMISSION": "Next you will be taken to Hubstaff to grant permission to <PERSON><PERSON><PERSON>.", "ENTER_CLIENT_SECRET": "Enter client secret to get access token."}, "UPWORK_PAGE": {"ACTIVITIES": "Activities", "REPORTS": "Reports", "TRANSACTIONS": "Transactions", "SUCCESSFULLY_AUTHORIZED": "Successfully authorized", "API_KEY": "Upwork API key", "SECRET": "Upwork Secret", "NEXT_STEP_INFO": "Next you will be taken to Upwork to grant permission to <PERSON><PERSON><PERSON>.", "CONTRACTS": "Contracts", "SYNCED_CONTRACTS": "Synced Contracts", "SELECT_DATE": "Select Date", "ONLY_CONTRACTS": "Only Contracts", "CONTRACTS_RELATED_DATA": "Synced Contracts related entities", "DATE_RANGE_PLACEHOLDER": "Choose date between", "HOURLY": "Hourly"}, "MAKE_COM_PAGE": {"SCENARIOS": "סצנות", "EXECUTIONS": "מימושים", "HISTORY": "היסטוריה", "SETTINGS": "הגדרות", "ERRORS": {"INVALID_FORM": "אנא מלא את כל השדות הנדרשים", "START_AUTHORIZATION": "אנא התחיל את תהליך האישור"}, "CLIENT_ID": "מזהה לקוח", "CLIENT_SECRET": "סיסמת לקוח", "NEXT_STEP_INFO": "לאחר האיש<PERSON><PERSON>, תועבר ל-Make.com להשלמת הגדרות האינטגרציה"}, "MAKE_COM": {"SETTINGS": {"TITLE": "הגדרות Make.com", "ENABLE_INTEGRATION": "הפעל אינטגרציית Make.com", "WEBHOOK_URL": "כתובת Webhook", "WEBHOOK_URL_PLACEHOLDER": "הזן כתובת webhook", "WEBHOOK_URL_INVALID": "אנא הזן כתובת webhook תקינה", "SAVE": "שמור הגדרות"}}, "ZAPIER_PAGE": {"CLIENT_ID": "מזהה לקוח", "CLIENT_SECRET": "סיסמת לקוח", "NEXT_STEP_INFO": "לא<PERSON><PERSON> האיש<PERSON><PERSON>, תו<PERSON><PERSON>ר ל-<PERSON><PERSON><PERSON> להשלמת הגדרות האינטגרציה", "ERRORS": {"LOAD_SETTINGS": "נכשל בטעינת הגדרות Zapier", "SAVE_SETTINGS": "נכשל בשמירת הגדרות Zapier", "INVALID_FORM": "אנא מלא את כל השדות הנדרשים", "MISSING_CREDENTIALS": "חסרים פרטי גישה של הלקוח", "NO_ACCESS_TOKEN": "אין אסימון גישה זמין", "LOAD_WEBHOOKS": "נכשל בטעינת וובחוקים", "DELETE_WEBHOOK": "נכשל במ<PERSON><PERSON><PERSON>ת וובחוק", "LOAD_ACTIONS": "נכשל בטעינת פעולות", "LOAD_TRIGGERS": "נכ<PERSON>ל בטעינת טריגרים", "START_AUTHORIZATION": "אנא התחיל את תהליך האישור", "INVALID_TOKEN": "אסימון לא תקין סופק", "TOKEN_NOT_FOUND": "אסימון לא נמצא"}, "SUCCESS": {"SETTINGS_SAVED": "הגדר<PERSON><PERSON> נשמרו בהצלחה", "DELETE_WEBHOOK": "וובחוק נמחק בהצלחה"}, "TAB": {"TRIGGERS": "טריגרים", "ACTIONS": "פעולות", "WEBHOOKS": "וובחוקים", "SETTINGS": "הגדרות"}, "TOOLTIP": {"TRIGGERS": "טריגרים הם אירועים שמתרחשים ב-G<PERSON><PERSON> וניתן להשתמש בהם כדי להפעיל פעולות ב-Zapier.", "ACTIONS": "פעולות הן אירועים שמתרחשים ב-<PERSON><PERSON><PERSON> וניתן להשתמש בהם כדי להפעיל פעולות ב-Gauzy.", "WEBHOOKS": "וובחוקים הם אירועים שמתרחשים ב-<PERSON><PERSON>ier וניתן להשתמש בהם כדי להפעיל פעולות ב-Gauzy.", "SETTINGS": "הגדרות הן ההגדרות לשיל<PERSON><PERSON>."}, "MESSAGE": {"TRIGGERS_ENABLED": "טריגרים הופעלו בהצלחה.", "TRIGGERS_DISABLED": "טריגרים הושבתו בהצלחה.", "ACTIONS_ENABLED": "פעולות הופעלו בהצלחה.", "ACTIONS_DISABLED": "פעולות הושבתו בהצלחה.", "WEBHOOKS_ENABLED": "וובחוקים הופעלו בהצלחה.", "WEBHOOKS_DISABLED": "וובחוקים הושבתו בהצלחה."}, "SETTINGS": {"TITLE": "הגדר<PERSON><PERSON>", "ENABLE_INTEGRATION": "הפעל שילו<PERSON>", "WEBHOOK_URL": "כתובת אתר של וובחוק", "WEBHOOK_URL_PLACEHOLDER": "הזן כתובת אתר של וובחוק", "WEBHOOK_URL_INVALID": "אנא הזן כתובת אתר חוקית של וובחוק", "WEBHOOK_INFO": "וובחוק הוא כתובת אתר שמשמשת לעדכון נתונים ב-Gauzy בהתאם לאירוע מסוים ב-<PERSON><PERSON><PERSON>.", "SAVE": "שמור הגדרות"}, "ACTIONS": {"TITLE": "פעולות", "NO_ACTIONS": "אין פעולות זמינות"}, "TRIGGERS": {"TITLE": "טריגרים", "NO_TRIGGERS": "אין טריגרים זמינים"}, "WEBHOOKS": {"TITLE": "וובחוקים", "NO_WEBHOOKS": "אין וובחוקים מוגדרים"}}, "GAUZY_AI_PAGE": {"TITLE": "Gauzy AI Integration", "API_KEY": "Gauzy AI key", "API_SECRET": "Gauzy AI Secret", "OPEN_AI_API_SECRET_KEY": "Open AI Secret Key", "DESCRIPTION": "Activate Gauzy AI integration for smarter job hunting.", "CONSUMER_KEYS": "Consumer Keys", "GENERATED": "Generated", "TAB": {"KEYS": "Keys", "SETTINGS": "Settings"}, "TOOLTIP": {"API_KEY": "ה-API Key משמש כזהות האפליקציה שלך לבקשות API, נשמרת מסתירה לצמיתות, עם חלק מהתווים נראים.", "API_SECRET": "ה-API Secret משמש כזהות האפליקציה שלך לבקשות API, נשמרת מסתירה לצמיתות, עם חלק מהתווים נראים.", "OPEN_AI_API_SECRET_KEY": "ה-OpenAI API Secret משמש כזהות האפליקציה שלך לבקשות API, נשמרת מסתירה לצמיתות, עם חלק מהתווים נראים.", "ENABLE_JOBS_SEARCH_MATCHING_ANALYSIS": "מאפשר ניתוח מתקדם של חיפוש והתאמה למשתמש לשיפור הדיוק. בהשבתה, פריטי התפריט 'גלישה' ו-'התאמה' בתפריט המשרות ייחבאו כדי להבטיח ממשק משתמש קל ופשוט.", "ENABLE_EMPLOYEE_PERFORMANCE_ANALYSIS": "ניהול העברת מדדי עבודה של עובד, כולל קלטי מקלדת, תנועות עכבר וצילומי מסך, לניתוח על ידי Gauzy AI. הפעלה מאפשרת ניתוח ביצועים מקיף. בכיבוי, Gauzy AI לא יקבל ולא ינתח צילומי מסך או כל קלט פרטני נוסף, מבטיח פרטיות מחמירה ושליטה מוחלטת על שיתוף המידע."}}, "GITHUB_PAGE": {"TITLE": "GitHub Integration", "AUTO_SYNC_TABLE_LABEL": "Syncing GitHub Repositories and Projects", "SELECT_REPOSITORY": "Select repository", "SEARCH_REPOSITORY": "Type to search repository", "SYNCED_ISSUES": "'{{repository}}' issues & labels synced successfully", "HAS_SYNCED_ENABLED": "'{{repository}}' sync successfully enabled", "HAS_SYNCED_DISABLED": "'{{repository}}' sync successfully disabled", "DESCRIPTION": "Activate GitHub integration for project & repository sync.", "TAB": {"AUTO_SYNC": "Auto sync", "MANUAL_SYNC": "Manual sync"}}, "COMING_SOON": "Coming soon", "RE_INTEGRATE": "Re-integrate", "SETTINGS": "הגדרות", "SELECT_GROUPS": "Select Groups", "FILTER_INTEGRATIONS": "Filter integrations", "SEARCH_INTEGRATIONS": "Search integrations", "PAID": "Paid", "INTEGRATION": "Integration", "MESSAGE": {"SETTINGS_UPDATED": "Updated settings for '{{provider}}' integration", "INTEGRATION_DELETED": "Integration '{{provider}}' deleted successfully", "NO_INTEGRATIONS": "You have not configured any integrations", "INTEGRATION_ENABLED": "'{{provider}}' integration successfully enabled", "INTEGRATION_DISABLED": "'{{provider}}' integration successfully disabled", "INTEGRATION_ADDED": "Integration '{{provider}}' has been added to '{{organization}}'"}, "ENABLED": "Enabled", "DISABLED": "Disabled"}, "DASHBOARD_PAGE": {"ACCOUNTING": "Accounting", "HUMAN_RESOURCES": "Human Resources", "TIME_TRACKING": "Time Tracking", "PROJECT_MANAGEMENT": "Project Management", "EMPLOYEE_STATISTICS": "סטטיסטיקת עובדים", "SELECT_A_MONTH_AND_EMPLOYEE": "אנא ב<PERSON>ר חודש ועובד מהתפריט שלמעלה", "INSERT_TEXT_FOR_NOT_AUTHENTICATED_USERS": "Insert text for not authenticated users", "CHARTS": {"BAR": "Bar", "DOUGHNUT": "Doughnut", "STACKED_BAR": "Stacked Bar", "CHART_TYPE": "Chart Type", "REVENUE": "Revenue", "EXPENSES": "הוצאות", "PROFIT": "רווח", "BONUS": "בונוס", "NO_MONTH_DATA": "No Data for this month", "CASH_FLOW": "Cash Flow", "WORKING": "Working", "WORKING_NOW": "Working now", "NOT_WORKING": "Not working", "WORKING_TODAY": "Working today"}, "PROFIT_HISTORY": {"PROFIT_REPORT": "Profit Report", "TOTAL_EXPENSES": "סך ההוצאות", "TOTAL_INCOME": "הכנ<PERSON>ה כוללת", "TOTAL_PROFIT": "Total Profit", "DATE": "תאריך", "EXPENSES": "הוצאות", "INCOME": "הכנסה", "DESCRIPTION": "Description"}, "TITLE": {"PROFIT_REPORT": "Profit Report", "TOTAL_EXPENSES": "סך ההוצאות", "TOTAL_INCOME": "הכנ<PERSON>ה כוללת", "PROFIT": "רווח", "TOTAL_BONUS": "Total Bonus", "TOTAL_DIRECT_INCOME": "Direct Income", "SALARY": "Salary", "TOTAL_DIRECT_INCOME_INFO": "Income from direct bonus", "TOTAL_INCOME_CALC": "Total Income = Income {{ totalNonBonusIncome }} + Direct Income {{ totalBonusIncome }}", "TOTAL_PROFIT_BONUS": "Total Profit Bonus", "TOTAL_DIRECT_BONUS": "Direct Income Bonus", "TOTAL_DIRECT_BONUS_INFO": "This is equal to the direct income", "TOTAL_PROFIT_BONUS_INFO": "{{ bonusPercentage }}% of the profit {{ difference }}", "TOTAL_INCOME_BONUS": "Total Income Bonus", "TOTAL_INCOME_BONUS_INFO": "{{ bonusPercentage }}% of the income {{ totalIncome }}", "TOTAL_EXPENSE_CALC": "Total = Employee Expenses + Split Expenses + Recurring + Salary", "TOTAL_EXPENSES_WITHOUT_SALARY": "Total Expense without salary", "TOTAL_EXPENSES_WITHOUT_SALARY_CALC": "Expense = Employee Expenses + Split Expenses + Recurring", "TOTAL_BONUS_CALC": "Total Bonus = Direct Income Bonus {{ totalBonusIncome }} + Bonus {{ calculatedBonus }}"}, "DEVELOPER": {"DEVELOPER": "מפתח", "AVERAGE_BONUS": "Average Monthly Bonus", "TOTAL_INCOME": "הכנ<PERSON>ה כוללת", "TOTAL_EXPENSES": "סך ההוצאות", "PROFIT": "רווח", "PROFIT_CALC": "Profit (Net Income) = Total Income {{ totalAllIncome }} - Total Expenses {{ totalExpense }}", "NOTE": "הערה: יש לנכות את הבונוסים השליליים בחודשים הקרובים מהבונוסים החיוביים לפני תשלומי הבונוס הסופיים", "BONUS": "בונוס", "EMPLOYEES": "עובדים"}, "ADD_INCOME": "Add New Income Entry", "ADD_EXPENSE": "Add New Expense Entry", "RECURRING_EXPENSES": "Recurring Expenses", "ADD_ORGANIZATION_RECURRING_EXPENSE": "Add New Recurring Expense Entry for Organization", "ADD_EMPLOYEE_RECURRING_EXPENSE": "Add New Recurring Expense Entry for Employee", "PLAN_MY_DAY": "Plan My Day", "ADD_TODO": "Add <PERSON>", "MOST_VIEW_PROJECTS": "Most Viewed Projects", "INBOX": "Inbox", "RECENTLY_ASSIGNED": "Recently Assigned", "NO_TODO_ASSIGNED": "No Todo Assigned"}, "INCOME_PAGE": {"INCOME": "הכנסה", "BONUS_HELP": "If set, company % fee will NOT be applied to the Bonus Income", "BONUS_TOOLTIP": "This is a direct bonus", "EMPLOYEES_GENERATE_INCOME": "Employees that generate income"}, "EXPENSES_PAGE": {"EXPENSES": "הוצאות", "MUTATION": {"CONTACT_IS_REQUIRED": "Contact is required!", "PLEASE_SELECT_A_CONTACT_OR_CHANGE_EXPENSE_TYPE": "Please select a Contact from the dropdown menu below or change Expense type setting.", "ASSIGN_TO": "Assign to", "INCLUDE_TAXES": "Include Taxes", "ATTACH_A_RECEIPT": "Attach a receipt", "EMPLOYEES_GENERATE_EXPENSE": "Employees that generate expense", "TAX_DEDUCTIBLE": "Tax Deductible", "NOT_TAX_DEDUCTIBLE": "Not Tax Deductible", "BILLABLE_TO_CONTACT": "Billable to Contact", "PERCENTAGE": "Percentage", "VALUE": "ערך", "TAX_AMOUNT": "Tax Amount", "TAX_RATE": "Tax Rate", "INVOICED": "Invoiced", "UNINVOICED": "Uninvoiced", "PAID": "Paid", "NOT_BILLABLE": "Not Billable"}, "DEFAULT_CATEGORY": {"SALARY": "Salary", "SALARY_TAXES": "Salary Taxes", "RENT": "Rent", "EXTRA_BONUS": "Extra Bonus"}, "SPLIT_HELP": "If set, this expense will be equally divided amongst all employees of the company & will be considered in each one's expenses.", "SPLIT_WILL_BE_TOOLTIP": "This expense will be equally split amongst all employees", "SPLIT_EXPENSE": "Split Expense", "ADD_EXPENSE_CATEGORY": "Add New Expense category", "EXPENSE_CATEGORY": "Expense Category", "RECURRING_EXPENSES": {"WARNING": "Warning: This period overlaps with an existing previous record(s):", "FROM": "From", "TO": "to", "VALUE_OVERWRITTEN": "The value will be overwritten from", "ERROR": "Error: This period overlaps with existing future record(s).", "NOT_SUPPORTED": "This is not supported, please edit the future expenses instead.", "EDIT_FUTURE_VALUE": "This will only edit the future value from", "EXISTING_VALUE": "existing value of", "STARTED_ON": "Started on", "AFFECTED": "will not be affected until", "SET_EXPENSE_VALUE": "This will set the expense value", "ONWARDS": "onwards and the existing value of", "ENDING_ON": "and ending on", "SET_UNTIL": "will be now set until", "REDUCE_START_DATE": "This will reduce the start date and include all the months from", "FOR_EXPENSE_VALUE": "for expense value", "CHANGE_START_DATE": "This will change the start date to"}}, "EMPLOYEES_PAGE": {"HEADER": "Manage Employees", "ADD_EMPLOYEE": "הוסף עובד", "ACTIVE": "פעיל", "END_WORK": "סיום עבודה", "WORK_ENDED": "העבודה הסתיימה", "DELETED": "Deleted", "ENABLED": "Enabled", "DISABLED": "Disabled", "RECURRING_EXPENSE": "Employee Recurring Expense", "RECURRING_EXPENSE_EDIT": "Edit the current & all future expenses. There will be no change to any past expense.", "RECURRING_EXPENSE_ADD": "This will add an expense recurring every month.", "RECURRING_EXPENSE_SET": "'{{ name }}' recurring expense set.", "RECURRING_EXPENSE_EDITED": "'{{ name }}' recurring expense edited.", "RECURRING_EXPENSE_DELETED": "'{{ name }}' recurring expense deleted.", "EMPLOYEE_NAME": "עובד", "BACK_TO_WORK": "לחזור לעבודה", "SELECT_EMPLOYEE_MSG": "Please select employee from the menu above.", "EDIT_EMPLOYEE": {"HEADER": "Manage Employee", "DEVELOPER": "מפתח", "DEPARTMENT": "המחלקה", "POSITION": "עמדה", "EMPLOYEE_DEPARTMENTS": "Employee's Departments:", "EMPLOYEE_PROJECTS": "Employee's Projects:", "EMPLOYEE_CONTACTS": "Employee's Contacts:", "EMPLOYMENT_TYPE": "Employment Type", "ACCOUNT": "Account", "EMPLOYMENT": "Employment", "LOCATION": "Location", "RATES": "Rates", "PROJECTS": "פרויקטים", "CONTACTS": "Contacts", "HIRING": "Hiring", "NETWORKS": "Networks", "EMPLOYEE_LEVEL": "Employee Level", "DISPLAY_BONUS_ANONYMOUSLY": "Display bonus Anonymously", "JOB_SUCCESS": "Job Success", "TOTAL_JOBS": "Total Jobs", "TOTAL_HOURS": "Total Hours", "RATE": "ציון", "VETTED": "Vetted", "HR": "hr", "SETTINGS": "הגדרות", "GENERAL_SETTINGS": "הגדרות כלליות"}, "ADD_EMPLOYEES": {"STEP_1": "Step 1", "STEP_2": "Step 2", "STEP_3": "Step 3", "ADD_ANOTHER_EMPLOYEE": "Add Another Employee", "FINISHED_ADDING": "I’ve Added All Current Employees", "NEXT": "Next", "PREVIOUS": "Previous"}, "NOT_STARTED": "Not Started", "NOT_STARTED_HELP": "Started work on date is not set for this employee. The employee will not be considered in accounts, split expenses etc."}, "GOALS_PAGE": {"HEADER": "All Objectives", "GOAL": "Goal", "GOALS_EMPTY": "You haven't created any Objectives yet. Click Add New to create one", "ADD_NEW_KEY_RESULT": "Add new key result", "ADD_NEW_OBJECTIVE": "Add new Objective", "EDIT_OBJECTIVE": "Edit Objective", "SESSION": "Session", "GOAL_SETTINGS": "Goals Settings", "NO_DESCRIPTION": "No Description", "PROGRESS": "Progress", "EXPECTED": "Expected", "UPDATES": "Updates", "UPDATE": "Update", "COMMENTS": "Comments", "KEY_RESULTS": "Key Results", "GROUP_BY": "Group By", "DELETE_OBJECTIVE": "Delete Objective", "DELETE_KEY_RESULT": "Delete Key Result", "ARE_YOU_SURE": "Are you sure? This action is irreversible.", "ALL_OBJECTIVES": "All Objectives", "MY_TEAMS_OBJECTIVES": "My Team's Objectives", "MY_ORGANIZATIONS_OBJECTIVES": "'My Organization Objectives", "MY_OBJECTIVES": "My Objectives", "OBJECTIVE_LEVEL": "Objective Level", "TIME_FRAME": "Time Frames", "CREATE_NEW": "+ create new", "GOAL_TEMPLATES": "Goal Templates", "CREATE_NEW_MENU": "Create New", "CREATE_FROM_PRESET": "Create from Preset", "OWNERSHIP": {"EMPLOYEES": "עובדים", "TEAMS": "Teams", "EMPLOYEES_AND_TEAMS": "Employees and Teams"}, "SETTINGS": {"ADD_TIME_FRAME_TITLE": "Add Time Frame", "EDIT_TIME_FRAME_TITLE": "Edit Time Frame", "TIME_FRAME_PAGE_TITLE": "Set Time Frame", "PREDEFINED_TIME_FRAMES": "Predefined Time Frames", "ADD_KPI": "Add KPI", "EDIT_KPI": "Edit KPI", "MAX_ENTITIES": "Max Number of entities that can be created", "EMPLOYEE_OBJECTIVES": "Employees can create their own objectives", "WHO_CAN_OWN_OBJECTIVES": "Who can own Objectives?", "WHO_CAN_OWN_KEY_RESULTS": "Who can own Key Results?", "ADD_KPI_TO_KEY_RESULT": "Add KPI to Key Result Type?", "ADD_TASK_TO_KEY_RESULT": "Add Task to Key Result Type?", "GENERAL": "כללי", "KPI": "KPI", "DELETE_TIME_FRAME_TITLE": "Delete Time Frame", "DELETE_TIME_FRAME_CONFIRMATION": "Are you sure you want to delete Time Frame?", "DELETE_KPI_TITLE": "Delete KPI", "DELETE_KPI_CONFIRMATION": "Are you sure you want to delete KPI?", "ANNUAL": "Annual"}, "MESSAGE": {"NO_KEY_RESULT": "No Key Results to display.", "NO_UPDATES": "No Updates yet.", "NO_ALIGNMENT": "No Alignments yet."}, "LEVELS": {"ORGANIZATION": "Organization", "TEAM": "Team", "EMPLOYEE": "עובד"}, "TIME_FRAME_STATUS": {"ACTIVE": "פעיל", "INACTIVE": "Inactive"}, "KPI_OPERATOR": {"GREATER_THAN_EQUAL_TO": "Greater than or equal to", "LESSER_THAN_EQUAL_TO": "Lesser than or equal to"}, "KPI_METRIC": {"NUMERICAL": "Numerical", "PERCENTAGE": "Percentage", "CURRENCY": "מטבע"}, "TOOLTIPS": {"PROGRESS": "Overall progress of this Objective based on its Key result's progress", "DETAILS": "Objective Details", "EDIT": "Edit Objective"}, "FORM": {"LABELS": {"LEVEL": "Level", "OWNER": "Owner", "LEAD": "Lead", "LEAD_OPTIONAL": "Lead (optional)", "DEADLINE": "Deadline", "STATUS": "עבודה", "KPI_SHOULD_BE": "This KPI should be", "KPI_METRIC": "KPI Metric", "CURRENT_VALUE": "Current Value", "OBJECTIVE": "Objectives", "KEY_RESULT": "Key Results"}, "PLACEHOLDERS": {"NAME": "Objective Name. eg. Improve Website SEO", "DESCRIPTION": "Objective Description.", "LEVEL": "Goal Level", "TIME_FRAME_NAME": "Time frame name eg. 'Q4-2020'", "KPI_DESCRIPTION": "A short description to give some context about the KPI", "KPI_NAME": "KPI Title. eg. Maintain page views - 200/day"}, "ERROR": {"START_DATE_GREATER": "End Date must be greater than Start Date"}}, "BUTTONS": {"ADD_TIME_FRAME": "Add Time Frame"}, "HELPER_TEXT": {"OBJECTIVE_GENERAL": "An <b>Objective</b> is a description of a goal to be achieved in the future.", "OBJECTIVE_TITLE": "<p>Create an ambitious title that best describes your goal.</p><p>The title should not contain any metric</p>eg.<ul><li>Generate more revenue than last year</li><li>Become market leader</li><li>Increase website engagement</li></ul>", "OBJECTIVE_DESCRIPTION": "Write a short description to give some context to your Objective", "OBJECTIVE_LEVEL": "The group to which this Objective belongs to.", "OBJECTIVE_OWNER": "The group/ individual responsible for completing the Objective.", "OBJECTIVE_LEAD": "The person responsible for organizing and planning that is needed to achieve the Objective.", "OBJECTIVE_TIMEFRAME": "Time Frame for which this Objective is created. If a time frame doesn't exist, create one here.", "KPI_GENERAL": "Organizations use <b> Key Performance Indicators (KPIs) </b> to evaluate their success at reaching targets. KPIs can be used as measurable metrics for Key results.", "KPI_NAME": "<p>Name your new KPI</p><p>Examples for good KPI title</p><ul><li># of Followers</li><li>Annual Recurring Revenue</li><li>Customer Lifetime Value</li></ul>", "KPI_DESCRIPTION": "Write a short description to give some context to your KPI", "KPI_METRIC": "KPI measurable unit", "KPI_LEAD": "Person who is responsible for updating the values of this KPI"}}, "KEY_RESULT_PAGE": {"UPDATE_KEY_RESULT": "Update Key Result", "EDIT_KEY_RESULT": "Edit Key Result", "EDIT_KEY_RESULT_PARAMETERS": "Edit Key Result Parameters", "ADD_KEY_RESULT": "Add Key Result", "UPDATE": {"STATUS": {"ON_TRACK": "on track", "NEEDS_ATTENTION": "needs attention", "OFF_TRACK": "off track", "NONE": "none"}}, "WEIGHT": {"DEFAULT": "<PERSON><PERSON><PERSON>", "INCREASE_BY_2X": "Increase 2X", "INCREASE_BY_4X": "Increase 4X", "MESSAGE": "Weights can be used to increase or decrease the importance of a single Key Result when calculating overall Objective Progress %", "OBJECTIVE_PROGRESS": "{{ weight }}% of Objective's Progress"}, "MESSAGE": {"TIME_FRAME_ENDED": "You can't update this key result now. The Time Frame for this key result has ended on {{ date }}", "TIME_FRAME_NOT_STARTED": "You can't update this key result now. The Time Frame for this key result starts on {{ date }}. Then you'll be able to update it"}, "TYPE": {"NUMERICAL": "Numerical", "TRUE_OR_FALSE": "True/False", "CURRENCY": "מטבע", "TASK": "Task", "KPI": "KPI"}, "DEADLINE": {"NO_CUSTOM_DEADLINE": "No Custom Deadline", "HARD_DEADLINE": "Hard Deadline", "HARD_AND_SOFT_DEADLINE": "Hard and Soft Deadline"}, "TOOLTIPS": {"PROGRESS": "This Key result's progress contributes to {{weight}}% of the Objective's progress", "DETAILS": "Key Result Details", "EDIT": "Edit Key Result", "WEIGHT": "Edit Weight/Type"}, "FORM": {"LABELS": {"KEY_RESULT_TYPE": "Key Result Type", "INITIAL_VALUE": "Initial value", "TARGET_VALUE": "Target value", "OWNER": "Owner", "LEAD": "Lead (optional)", "DEADLINE": "Deadline", "SOFT_DEADLINE": "Soft Deadline", "HARD_DEADLINE": "Hard Deadline", "UPDATED_VALUE": "Updated Value", "MARK_COMPLETE": "Mark as Complete", "STATUS": "עבודה", "WEIGHT": "Weight", "TYPE": "סוג", "SELECT_PROJECT": "Select Project", "SELECT_TASK": "Select Task", "SELECT_KPI": "Select KPI", "ASSIGN_AS_OBJECTIVE": "Assign as objective"}, "PLACEHOLDERS": {"NAME": "Key Result Name. eg. Add Metadata to improve SEO", "DESCRIPTION": "A short description to give some context about the Key Result"}}, "HELPER_TEXT": {"KEY_RESULT_GENERAL": "A <b>Key Result</b> is how you plan to measure that you have achieved your objective .", "KEY_RESULT_OWNER": "The person responsible for completing the Key Result.", "KEY_RESULT_LEAD": "The person responsible for organizing and planning that is needed to achieve the Key Result."}}, "CANDIDATES_PAGE": {"HEADER": "Manage Candidates", "ADD_CANDIDATE": "Add Candidates", "APPLIED": "Applied", "HIRED": "<PERSON><PERSON>", "REJECTED": "Rejected", "SHOW_REJECTED": "הצגה נדחתה", "DELETED": "Deleted", "ARCHIVED": "נ<PERSON><PERSON><PERSON> ב<PERSON><PERSON><PERSON><PERSON><PERSON>ן", "SELECT_EMPLOYEE_MSG": "Please select employee from the menu above.", "JOBS_CANDIDATES": "Job Candidates", "SOURCE": "Source", "RATING": "Rating", "STATUS": "עבודה", "EDIT_CANDIDATE": {"HEADER": "Manage Candidate", "DEVELOPER": "מפתח", "DEPARTMENT": "המחלקה", "POSITION": "עמדה", "CANDIDATE_DEPARTMENTS": "Employee's Departments:", "EMPLOYMENT_TYPE": "Employment Type", "CANDIDATES_LEVEL": "Candidate Level", "ACCOUNT": "Account", "EMPLOYMENT": "Employment", "LOCATION": "Location", "RATE": "Rates", "HIRING": "Hiring", "EXPERIENCE": "Experience", "SKILLS": "Skills", "ALL_FEEDBACKS": "All feedbacks", "FEEDBACKS": "Feedbacks", "EDUCATION": "Education", "SCHOOL_NAME": "School name", "DEGREE": "Degree/Diploma", "FIELD": "Field(s)", "COMPLETION_DATE": "Date of completion", "ADDITIONAL_NOTES": "Additional notes", "OCCUPATION": "Occupation", "ORGANIZATION": "Organization name", "DURATION": "Duration", "DESCRIPTION": "Description", "TASKS": "Tasks", "HISTORY": "History", "DOCUMENTS": "Documents", "DOCUMENT_NAME": "Document name", "NAME": "שם", "DOCUMENT": "Document", "FEEDBACK_DESCRIPTION": "Feedback description", "INTERVIEW_FEEDBACK": "Interview: ", "INTERVIEWER": "Interviewer", "FEEDBACK_STATUS": "עבודה", "LEAVE_FEEDBACK": "Leave a feedback about the ", "STATUS": "Selected status: ", "INTERVIEW": {"INTERVIEW": "Interview", "INTERVIEWS": "Interviews", "INTERVIEWER": "Interviewer: ", "ADD_INTERVIEW": "Add interview", "HIDE_PAST": "<PERSON>de past interviews", "ON": "on", "TO": "To", "FROM": "from", "WITH": "with", "SCHEDULE_INTERVIEW": "Schedule interview", "SCHEDULED_INTERVIEWS": "Scheduled interviews", "CONTINUE": "Continue ", "PAST_DATE": "You have chosen a day in the past", "EDIT_INTERVIEW": "Edit interview", "STEP_1": "Step 1", "STEP_2": "Step 2", "STEP_3": "Step 3", "NEXT": "Next", "PREVIOUS": "Previous", "SAVE": "לשמור", "CREATE_CRITERIONS": "Create criterions", "CLOSE_CRITERIONS": "סגור קריטריונים", "EMAIL_NOTIFICATION": "Email notification", "DELETE_INTERVIEW": "Delete interview", "DELETE_FEEDBACK": "Delete feedback", "DELETE_INTERVIEW_ARE_YOU_SURE": "Are you sure you want to delete this interview?", "DELETE_FEEDBACK_ARE_YOU_SURE": "Are you sure you want to delete this feedback?", "SUMMARY": "Summary", "DETAILS": "Send interview details to", "NOTIFY_CANDIDATE": "Notify candidate", "NOTIFY_INTERVIEWERS": "Notify interviewer(s)", "INTERVIEWERS": "interviewer(s)", "HIRE": "<PERSON>re", "RATING": "Rating", "DESCRIPTION": "Description: ", "POSTPONE": "Postpone", "REJECT": "Reject", "INTERVIEW_TITLE_EXIST": "An interview with such name already exists", "SET_AS_ARCHIVED": "{{ title }} set as archived."}}, "INTERVIEW_INFO_MODAL": {"SCHEDULED": "Scheduled", "HOURS_AGO": " hour(s) ago", "LESS_MINUTE": " less than a minute ago", "MINUTES_AGO": " minutes ago", "DAYS_AGO": " day(s) ago", "DATE": "תאריך", "TIME": "Time", "CANDIDATE": "Candidate", "INTERVIEWERS": "Interviewers", "LOCATION": "Location", "NOTE": "Note", "OF": "of", "INTERVIEWS_LOWER_CASE": "interviews"}, "ADD_CANDIDATES": {"STEP_1": "Step 1", "STEP_2": "Step 2", "STEP_3": "Step 3", "UPLOAD_CV": "Upload CV", "ADD_ANOTHER_CANDIDATE": "Add Another Candidate", "FINISHED_ADDING": "I’ve Added All Current Candidates", "NEXT": "Next", "PREVIOUS": "Previous"}, "MANAGE_INTERVIEWS": {"CALENDAR": "Calendar", "INTERVIEWS": "Interviews", "MANAGE_INTERVIEWS": "Manage Interviews", "CRITERIONS": "Criterions", "DEFAULT": "Default list", "DATE": "תאריך", "SORT_BY": "Sort by", "SEARCH_BY_INTERVIEW": "Search by interview's title", "SEARCH_BY_CANDIDATE": "Search by candidate's name", "CANDIDATE_NAME": "Candi<PERSON>'s name", "RATING": "Rating", "RESET_FILTERS": "Reset filters", "TITLE": "Title", "INTERVIEWERS": "Interviewers", "ADD_FEEDBACK": "Add a feedback", "LOCATION": "Location", "NOTES": "הערות", "ACTIONS": "Actions", "EDIT": "ערוך", "ARCHIVE": "Archive", "PAST": "Past", "DELETE": "מחק", "UPDATED": "Updated", "CANDIDATE": "Candidate", "FEEDBACK_PROHIBIT": "Adding feedback about future interviews is prohibited", "START_DATE": "תאריך התחלה"}, "STATISTIC": {"STATISTICS": "Statistic", "RATING": "Overall rating", "INTERVIEWER_ASSESSMENT": "Rating per interview", "CRITERIONS_RATING": "Criterion's rating per interview", "CANDIDATE_CRITERIONS_RATING": "Average criterion's rating", "NO_DATA": "No data yet ", "SELECT_INTERVIEW": "Select an interview", "SELECT_INTERVIEW_INTERVIEWER": "Select an interview and interviewer", "SELECT_CANDIDATE": "Select a candidate"}, "CRITERIONS": {"CANDIDATE_CRITERIONS": "Candidates criterions", "RATE_CANDIDATE_BY_CRITERIONS": "Rate candidate by criterions", "TECHNOLOGY_STACK": "Technology Stack", "ALREADY_EXISTED": "Already existed", "TOASTR_ALREADY_EXIST": "A criterion with such name already exists", "PERSONAL_QUALITIES": "Personal Qualities", "CHOOSE_CRITERIONS": "Choose criterions for candidate", "TECHNOLOGY_PLACEHOLDER": "If you want to add criteria from the technology stack, you need to create them", "PERSONAL_QUALITIES_PLACEHOLDER": "If you want to add criteria from the personal quality list, you need to create them"}}, "ORGANIZATIONS_PAGE": {"ORGANIZATIONS": "ארגונים", "EMPLOYEES": "עובדים", "POSITIONS": "עמדות", "EDIT_PUBLIC_PAGE": "Edit Public Page", "SELECT_ORGANIZATION": "אנא בחר ארגונים מהתפריט שלמעלה", "MAIN": "עיקרי", "TAGS_OPTIONS": "Tags & Options", "VARIANTS": "Variants", "DESCRIPTION": "Description", "DEPARTMENTS": "מחלקות", "VENDORS": "ספקים", "VENDOR": "ספק", "NAME": "שם", "EXPENSE_CATEGORIES": "Expense Categories", "PROJECTS": "פרויקטים", "ACTIVE": "פעיל", "EMPLOYMENT_TYPE": "Employment Type", "ARCHIVED": "נ<PERSON><PERSON><PERSON> ב<PERSON><PERSON><PERSON><PERSON><PERSON>ן", "LOCATION": "Location", "SETTINGS": "הגדרות", "REGISTER_AS_EMPLOYEE": "Register as Employee", "TEAMS": "Teams", "TEAM_NAME": "{{ name }} Team", "NOT_WORKED": "Not Worked", "ROLES": "Roles", "HELP_CENTER": "Help Center", "DOCUMENTS": "Documents", "DOCUMENTS_NO_DATA_MESSAGE": "You have not created any department.", "EXPENSE_RECURRING": "Recurring Expenses", "RECURRING_EXPENSE": "Organization Recurring Expense", "EMPLOYMENT_TYPES": "Employment Types", "INVITE_CONTACT": "Invite Contact", "EMAIL_INVITE": "<PERSON><PERSON>", "ADD_LEVEL_OF_EMPLOYEE": "Add level of employee", "LEVEL_OF_EMPLOYEE": "Employee Levels", "EMPLOYEE_LEVEL_NO_DATA_MESSAGE": "You have not created any employee level.", "POSITION_NO_DATA_MESSAGE": "You have not created any position.", "PHONE": "Phone", "EMAIL": "דוא\"ל", "WEBSITE": "Website", "DOCUMENT_URL": "Document URL", "UPDATED": "Updated", "LEVEL_NAME": "Level name", "EXPENSE_NAME": "Expense name", "EMPLOYMENT_TYPE_NAME": "Employment type name", "EMPLOYMENT_TYPE_NO_DATA_MESSAGE": "You have not created any employment type.", "VENDORS_NO_DATA_MESSAGE": "You have not created any vendor.", "EXPENSE_NO_DATA_MESSAGE": "You have not created any recurring expense.", "CONTACTS": "Contacts", "LEVEL": "Level", "ORGANIZATION": "ORGANIZATION", "HOURS_WORKED": "hours worked", "CLIENTS": "לקוחות", "PROFILE": "Profile", "PORTFOLIO": "Portfolio", "BROWSE": "Browse", "SEARCH": "Search", "EDIT": {"SETTINGS_SECTION": "Settings Section", "ALL": "All", "ACCOUNTING": "Accounting", "HEADER": "Manage", "CLIENT": "לקוחות", "CONTACT": "Contact", "NEW_CLIENT": "הוסף לקוח חדש", "NAME": "שם", "PRIMARY_EMAIL": "דוא\"ל עיקרי", "PHONE": "מספר טלפון ראשי", "COUNTRY": "מדינה", "CITY": "עיר", "STREET": "<PERSON>ח<PERSON><PERSON>", "PROJECTS": "פרויקטים", "FAX": "Fax", "FISCAL_INFORMATION": "Fiscal Information", "WEBSITE": "Website", "SECOND_ADDRESS": "Street 2", "IMAGE_URL": "Image URL", "POSTCODE": "Postcode", "DEPARTMENT_NAME": "שם מחלקה", "POSITION_NAME": "שם המיקום", "NEW_PROJECT": "הוסף פרויקט חדש", "START_DATE": "תאריך התחלה", "END_DATE": "תאריך סיום", "BILLING": "Billing", "CURRENCY": "מטבע", "OWNER": "Owner", "TEAMS": "Teams", "ADD_NEW_CONTACT": "Add New Contact", "EDIT_CONTACT": "Edit Contact", "GENERAL_SETTINGS": "General Settings", "DESIGN": "Design", "BONUS": "בונוס", "INVITE": "<PERSON><PERSON><PERSON>", "CLICK_EMPLOYEE": "Click to edit employee", "EDIT_PROJECT": "Edit Project", "REGIONS": "Regions", "ROLES_PERMISSIONS": "Roles & Permissions", "DATE_LIMIT": "Date Limit", "USER_ORGANIZATIONS": "{{ name }}'s List of Organizations", "ADDED_TO_ORGANIZATION": " the organization", "USER_WAS_DELETED": "'{{ name }}' was removed", "USER_WAS_REMOVED": "'{{ name }}' was removed from organization.", "EMPLOYEE_POSITION": "Employee Position", "PROJECT_URL": "Project Url", "VISIBILITY": "Visibility", "MEMBERS": "Employee/Teams", "SETTINGS": {"TIMER_SETTINGS": "Timer <PERSON>s", "AGENT_SETTINGS": "הגדרות סוכן", "ALLOW_MODIFY_TIME": "Allow Modify Time", "ALLOW_MODIFY_TIME_INFO": "Allow employee to modify manual time.", "ALLOW_DELETE_TIME": "Allow Delete Time", "ALLOW_DELETE_TIME_INFO": "Allow employee to delete time.", "ALLOW_AGENT_APP_EXIT": "אפ<PERSON>ר יציאה מאפליקציית הסוכן", "ALLOW_AGENT_APP_EXIT_INFO": "אפשר לעובד לצאת מאפליקציית הסוכן. כאשר מושבת, אפשרות היציאה תיחסם באפליקציית הסוכן.", "ALLOW_LOGOUT_FROM_AGENT_APP": "אפשר התנתקות מאפליקציית הסוכן", "ALLOW_LOGOUT_FROM_AGENT_APP_INFO": "אפשר לעובד להתנתק מאפליקציית הסוכן. כאשר מושבת, אפשרות ההתנתקות תיחסם באפליקציית הסוכן.", "TRACK_KEYBOARD_MOUSE_ACTIVITY": "עקוב אחר פעילות המקלדת והעכבר", "TRACK_KEYBOARD_MOUSE_ACTIVITY_INFO": "הפעל מעקב מלא אחר פעילות מקלדת ועכבר, כולל לחיצות מקשים ותנועות עכבר מדויקות בכל אפליקציות שולחן העבודה והסוכן.", "TRACK_ALL_DISPLAYS": "עקוב אחר כל המסכים", "TRACK_ALL_DISPLAYS_INFO": "הפעל מעקב בכל הצגים או הגבל רק לצג הראשי.", "ALLOW_MANUAL_TIME": "Allow Manual Time", "ALLOW_MANUAL_TIME_INFO": "Allow employee to add manual time.", "REQUIRE_REASON": "Require Reason", "REQUIRE_REASON_INFO": "Reason of the add manual time or edit time logs.", "REQUIRE_DESCRIPTION": "Require Description", "REQUIRE_DESCRIPTION_INFO": "Description of the tracked time will be required when timer starting and also for the time logs adding or editing manual time.", "REQUIRE_PROJECT": "Require Project", "REQUIRE_PROJECT_INFO": "Project of the tracked time will be required when timer starting and also for the time logs adding or editing manual time.", "REQUIRE_TASK": "Require Task", "REQUIRE_TASK_INFO": "Task of the tracked time will be required when timer starting and also for the time logs adding or editing manual time.", "REQUIRE_CLIENT": "Require Client", "REQUIRE_CLIENT_INFO": "Client of the tracked time will be required when timer starting and also for the time logs adding or editing manual time.", "ALLOW_TO_SET_DEFAULT_ORGANIZATION": "Allow user to set default organization", "INACTIVITY_TIME_LIMIT": "Inactivity Limit", "INACTIVITY_TIME_LIMIT_INFO": "Inactivity tracking timeout (i.e., how many seconds of inactivity is allowed)", "ACTIVITY_PROOF_DURATION": "Activity proof duration", "ACTIVITY_PROOF_DURATION_INFO": "Duration of activity proof countdown dialog, it represent an amount of seconds given to prove activity", "ALLOW_TRACK_INACTIVITY": "Allow to track inactivity", "SCREENSHOT_FREQUENCY": "תדירות צילומי מסך", "SCREENSHOT_FREQUENCY_INFO": "ציין את תדירות העדכון או המרווחים הזמניים לסנכרון.", "RANDOM_SCREENSHOT": "צילום מסך אקראי", "RANDOM_SCREENSHOT_INFO": "אפשר - לקחת צילומי מסך במרווחים אקראיים. בטל - לקחת צילומי מסך במרווחים קבועים.", "TRACK_ON_SLEEP": "מעקב בשינה", "TRACK_ON_SLEEP_INFO": "הפעל - מע<PERSON><PERSON> כאשר המכ<PERSON><PERSON>ר נכ<PERSON>ס למצב שינה. השבת - עצירת הטיימר שמעקב אחרי הזמן כאשר המכשיר נכנס למצב שינה.", "ENFORCED": "אוכף", "ENFORCED_INFO": "מופעל - מבט<PERSON><PERSON> שהגדרות המעקב בשינה, צילום מסך אקראי ותדירות הצילום המסך, המפורטות על ידי המנהל, הן חובה ואינן יכולות להיעדר או להיתעלם על ידי המשתמשים.", "PROOF_OF_COMPLETION": "הוכחת השלמה", "PROOF_OF_COMPLETION_TYPE": "סוג הוכחת השלמה", "LINKED_ISSUE": "בעיה מקושרת", "COMMENT": "תגובה", "HISTORY": "הִיסטוֹרִיָה", "ACCEPTANCE_CRITERIA": "קריטריונים לקבלה", "DRAFT_ISSUE": "טיוטת סוגיות", "PROOF_OF_COMPLETION_TYPE_DROPDOWN": {"NONE": "אף אחד", "PRIVATE": "פְּרָטִי", "PUBLIC": "פּוּמְבֵּי"}, "NOTIFY_TASK_LEFT": "הודע על משימה שנשארה", "NOTIFY_TASK_LEFT_PERIOD": "הודע על תקופת המשימה (בימים)", "AUTO_CLOSE_ISSUE": "בעיה אוטומטית בסגירה", "AUTO_CLOSE_ISSUE_PERIOD": "תקופת סגירה אוטומטית (בימים)", "AUTO_ARCHIVE_ISSUE": "בעיה באר<PERSON><PERSON>ון אוטומטי", "AUTO_ARCHIVE_ISSUE_PERIOD": "תקופת הנפקת ארכיון אוטומטי (בימים)", "AUTO_STATUS": "מצב אוטומטי", "TOOLTIP": {"ENABLE_DETECTION_INACTIVITY_INFO": "מופעל - המ<PERSON>רכת תזהה חוסר פעילות ותציג אזהרה. מושבת - לא תוצג אזהרה כאשר עובד אינו פעיל.", "ALLOW_MANUAL_TIME_INFO": "מציין אם הזנת זמן ידנית מותרת למעקב אחר זמן.", "ALLOW_MODIFY_TIME_INFO": "מציין אם מותר לשנות את כניסות הזמן למעקב אחר זמן.", "ALLOW_DELETE_TIME_INFO": "מציין אם מחיקת ערכי זמן מותרת למעקב אחר זמן.", "STANDARD_WORK_HOURS_PER_DAY": "בחר את מספר שעות העבודה הסטנדרטיות ליום. ערך זה ישמש בדוחות מעקב זמן כדי להשוות כמה שעות עובד עבד ביחס לסטנדרט זה."}}, "TEAMS_PAGE": {"MANAGERS": "Managers", "MEMBERS": "Members"}}, "PERMISSIONS": {"ADMIN_DASHBOARD_VIEW": "View Admin Dashboard", "TEAM_DASHBOARD": "View Team Dashboard", "PROJECT_MANAGEMENT_DASHBOARD": "View Project Management Dashboard", "TIME_TRACKING_DASHBOARD": "View Time Tracking Dashboard", "ACCOUNTING_DASHBOARD": "View Accounting Dashboard", "HUMAN_RESOURCE_DASHBOARD": "View Human Resources Dashboard", "ORG_PAYMENT_VIEW": "View Payments", "ORG_PAYMENT_ADD_EDIT": "Create/Edit/Delete Payments", "ORG_EXPENSES_VIEW": "View All Expenses", "ORG_EXPENSES_EDIT": "Create/Edit/Delete Expenses", "EMPLOYEE_EXPENSES_VIEW": "View All Employee Expenses", "EMPLOYEE_EXPENSES_EDIT": "Create/Edit/Delete Employee Expenses", "ORG_INCOMES_EDIT": "Create/Edit/Delete Incomes", "ORG_INCOMES_VIEW": "View All Incomes", "ORG_PROPOSALS_EDIT": "Create/Edit/Delete Proposals Register", "ORG_PROPOSALS_VIEW": "View Proposals Page", "ORG_PROPOSAL_TEMPLATES_VIEW": "View Proposal Templates Page", "ORG_PROPOSAL_TEMPLATES_EDIT": "Create/Edit/Delete Proposal Templates", "ORG_EMPLOYEES_ADD": "צור עובדי ארגון", "ORG_EMPLOYEES_VIEW": "צ<PERSON>ה בעובדי ארגון", "ORG_EMPLOYEES_EDIT": "ערוך עובדי ארגון", "ORG_EMPLOYEES_DELETE": "<PERSON><PERSON><PERSON> עוב<PERSON>י ארגון", "ORG_CANDIDATES_VIEW": "View Organization Candidates", "ORG_CANDIDATES_EDIT": "Create/Edit/Delete Organization Candidates", "ORG_USERS_VIEW": "View Organization Users", "ORG_USERS_EDIT": "Create/Edit/Delete Organization Users", "ORG_INVITE_VIEW": "View Organization Invites", "ORG_INVITE_EDIT": "Create/Resend/Delete Invites", "ORG_CANDIDATES_DOCUMENTS_VIEW": "View All Candidates Documents", "ORG_CANDIDATES_TASK_EDIT": "Create/Edit Task", "ORG_CANDIDATES_INTERVIEW_EDIT": "Create/Edit Interview", "ORG_CANDIDATES_INTERVIEW_VIEW": "View Interview", "ORG_INVENTORY_PRODUCT_EDIT": "Management Product", "ORG_TAGS_ADD": "Create Tags", "ORG_TAGS_VIEW": "View Tags", "ORG_TAGS_EDIT": "Edit Tags", "ORG_TAGS_DELETE": "Delete Tags", "ORG_TAG_TYPES_ADD": "הוסף סוג תג", "ORG_TAG_TYPES_VIEW": "צפה בסוגי תגיות", "ORG_TAG_TYPES_EDIT": "ערוך סוג תג", "ORG_TAG_TYPES_DELETE": "מחק סוג תג", "ORG_CANDIDATES_FEEDBACK_EDIT": "Create/Edit/Delete Candidate Feedback", "ALL_ORG_VIEW": "View All Organizations", "ALL_ORG_EDIT": "Create/Edit/Delete All Organizations", "TIME_OFF_POLICY_ADD": "הוסף מדיניות חופשה", "TIME_OFF_POLICY_VIEW": "צ<PERSON>ה במדיניות חופשה", "TIME_OFF_POLICY_EDIT": "ערוך מדיניות חופשה", "TIME_OFF_POLICY_DELETE": "מח<PERSON> מדיניות חופשה", "SELECT_EMPLOYEE": "<PERSON><PERSON><PERSON> עובד", "CHANGE_SELECTED_EMPLOYEE": "Change Selected Employee", "CHANGE_SELECTED_CANDIDATE": "Change Selected Candidate", "CHANGE_SELECTED_ORGANIZATION": "Change Selected Organization", "CHANGE_ROLES_PERMISSIONS": "Change Roles & Permissions", "ACCESS_PRIVATE_PROJECTS": "Access Private Projects", "TIMESHEET_EDIT_TIME": "Edit Time in Timesheet", "INVOICES_VIEW": "View Invoices", "INVOICES_EDIT": "Edit Invoices Add", "ESTIMATES_VIEW": "View Estimates", "ESTIMATES_EDIT": "Edit Estimates Add", "EDIT_SALES_PIPELINES": "Edit Sales Pipelines", "VIEW_SALES_PIPELINES": "View Sales Pipelines", "APPROVALS_POLICY_EDIT": "Edit Approvals Policy", "APPROVALS_POLICY_VIEW": "View Approvals Policy", "REQUEST_APPROVAL_EDIT": "Edit Approval Request", "REQUEST_APPROVAL_VIEW": "View Approval Request", "ORG_CANDIDATES_INTERVIEWERS_EDIT": "Create/Edit Interviewers", "ORG_CANDIDATES_INTERVIEWERS_VIEW": "View Interviewers", "VIEW_ALL_EMAILS": "View All Emails", "VIEW_ALL_EMAIL_TEMPLATES": "View All Emails Templates", "ORG_HELP_CENTER_EDIT": "Edit Organization Help Center", "PUBLIC_PAGE_EDIT": "Edit Organization Public Page", "CAN_APPROVE_TIMESHEET": "Approve Timesheet", "EVENT_TYPES_VIEW": "View Event Types", "TIME_OFF_ADD": "הוסף חופשה", "TIME_OFF_VIEW": "צ<PERSON>ה בחופשה", "TIME_OFF_EDIT": "ערוך חופשה", "TIME_OFF_DELETE": "<PERSON><PERSON><PERSON>", "ORG_INVENTORY_VIEW": "View Organization Inventory", "INVENTORY_GALLERY_VIEW": "View Inventory Gallery", "INVENTORY_GALLERY_EDIT": "Edit Inventory Gallery", "EQUIPMENT_SHARING_POLICY_ADD": "הוסף מדיניות שיתוף ציוד", "EQUIPMENT_SHARING_POLICY_VIEW": "צפה במדיניות שיתוף ציוד", "EQUIPMENT_SHARING_POLICY_EDIT": "ערוך מדיניות שיתוף ציוד", "EQUIPMENT_SHARING_POLICY_DELETE": "מחק מדיניות שיתוף ציוד", "ORG_EQUIPMENT_VIEW": "View Organization Equipment", "ORG_EQUIPMENT_EDIT": "Edit Organization Equipment", "ORG_EQUIPMENT_SHARING_VIEW": "View Organization Equipment Sharing", "ORG_EQUIPMENT_SHARING_EDIT": "Edit Organization Equipment Sharing", "EQUIPMENT_MAKE_REQUEST": "Request Make Equipment Make", "EQUIPMENT_APPROVE_REQUEST": "Request Approve Equipment", "ORG_PRODUCT_TYPES_VIEW": "View Organization Product Types", "ORG_PRODUCT_TYPES_EDIT": "Edit Organization Product Types", "ORG_PRODUCT_CATEGORIES_VIEW": "View Organization Product Categories", "ORG_PRODUCT_CATEGORIES_EDIT": "Edit Organization Product Categories", "VIEW_ALL_ACCOUNTING_TEMPLATES": "View All Accounting Templates", "GROUPS": {"GENERAL": "כללי", "ADMINISTRATION": "Administration"}, "ONLY_ADMIN": "These permissions are read-only and enabled only for admin", "INSUFFICIENT": "You do not have sufficient permissions. The following permissions are missing:", "ORG_SPRINT_EDIT": "עריכת ספרינט", "ORG_SPRINT_VIEW": "צ<PERSON>י<PERSON>ה בספרינט", "ORG_SPRINT_DELETE": "מחיקת ספרינטים", "ORG_SPRINT_ADD": "הוספת ספרינט", "ORG_PROJECT_EDIT": "יצירה/עריכת פרויקטים", "ORG_CONTACT_EDIT": "יצירה/עריכת אנשי קשר", "ORG_CONTACT_VIEW": "צ<PERSON><PERSON><PERSON>ה באנ<PERSON>י קשר", "PROJECT_MODULE_CREATE": "צור מודו<PERSON>י פרויקט", "PROJECT_MODULE_READ": "<PERSON><PERSON><PERSON> במודולי פרויקט", "PROJECT_MODULE_UPDATE": "עד<PERSON><PERSON> מודולי פרויקט", "PROJECT_MODULE_DELETE": "מח<PERSON> מודו<PERSON><PERSON> פרויקט", "DASHBOARD_CREATE": "צור לוח שלט", "DASHBOARD_READ": "צ<PERSON>ה בלוח שלט", "DASHBOARD_UPDATE": "עדכן לוח שלט", "DASHBOARD_DELETE": "מח<PERSON> לוח שלט", "ORG_TEAM_ADD": "Add Teams", "ORG_TEAM_VIEW": "View Teams", "ORG_TEAM_EDIT_ACTIVE_TASK": "ערוך משימות פעילות", "ORG_TEAM_EDIT": "Edit Teams", "ORG_TEAM_DELETE": "Delete Teams", "ORG_TEAM_JOIN_REQUEST_VIEW": "הצג בקשות הצטרפות לצוותים", "ORG_TEAM_JOIN_REQUEST_EDIT": "מחק ב<PERSON>שות הצטרפות לצוותים", "ORG_CONTRACT_EDIT": "Create/Edit Contracts", "TIME_TRACKER": "Access Time Tracker", "TENANT_ADD_EXISTING_USER": "Tenant Add User To Organization", "INTEGRATION_ADD": "הוסף אינטגרציות", "INTEGRATION_VIEW": "הצג אינטגרציות", "INTEGRATION_EDIT": "ערוך אינטגרציות", "INTEGRATION_DELETE": "<PERSON><PERSON><PERSON> אינ<PERSON><PERSON><PERSON>ציות", "FILE_STORAGE_VIEW": "View File Storage", "PAYMENT_GATEWAY_VIEW": "View Payment Gateway", "SMS_GATEWAY_VIEW": "View SMS Gateway", "CUSTOM_SMTP_VIEW": "View Custom SMTP", "IMPORT_EXPORT_VIEW": "View Import/Export", "ORG_JOB_APPLY": "הגש מועמדות למשרות", "ORG_JOB_EDIT": "ערוך משרות", "ORG_JOB_SEARCH": "צ<PERSON>ה במשרות", "ORG_JOB_EMPLOYEE_VIEW": "צ<PERSON>ה בעובדי משרה", "ORG_JOB_MATCHING_VIEW": "צפה בהתאמות משרות", "ACCESS_DELETE_ACCOUNT": "Access Delete Account", "ACCESS_DELETE_ALL_DATA": "Access Delete All Data", "TENANT_SETTING": "Create/Edit/Delete tenant settings", "ALLOW_DELETE_TIME": "Allow Delete Time", "ALLOW_MODIFY_TIME": "Allow Modify Time", "ALLOW_MANUAL_TIME": "Allow Manual Time", "DELETE_SCREENSHOTS": "Allow Delete Screenshot", "ORG_TASK_SETTING": "Task Settings", "ORG_MEMBER_LAST_LOG_VIEW": "צ<PERSON><PERSON> ביומן האחרון", "API_CALL_LOG_READ": "קרא יומן קריאות API", "API_CALL_LOG_DELETE": "<PERSON><PERSON><PERSON> יומן קריאות API", "TENANT_API_KEY_CREATE": "יצירת מפתח API", "TENANT_API_KEY_VIEW": "הצגת מפתח API", "TENANT_API_KEY_DELETE": "מחיקת מפתח API", "EMPLOYEE_AVAILABILITY_CREATE": "יצירת זמינות לעובד", "EMPLOYEE_AVAILABILITY_READ": "צ<PERSON>ה בזמינות העובד", "EMPLOYEE_AVAILABILITY_UPDATE": "ע<PERSON><PERSON><PERSON> זמינות לעובד", "EMPLOYEE_AVAILABILITY_DELETE": "<PERSON><PERSON><PERSON> ז<PERSON>ינות לעובד"}, "BILLING": "Billing", "BUDGET": "Budget", "OPEN_SOURCE": "Open-Source", "ORGANIZATION_ADD": "Add Organization", "IMAGE": "תמונה", "SPRINTS": "Sprints", "NO_IMAGE": "Image no available"}, "CONTACTS_PAGE": {"VISITORS": "Visitors", "LEADS": "Leads", "CUSTOMERS": "Customers", "CLIENTS": "לקוחות", "CITY": "עיר", "STREET": "<PERSON>ח<PERSON><PERSON>", "COUNTRY": "מדינה", "PROJECTS": "פרויקטים", "EMAIL": "דוא\"ל עיקרי", "PHONE": "מספר טלפון ראשי", "CONTACT_TYPE": "Contact type", "MAIN": "עיקרי", "ADDRESS": "Address", "ADDRESS_2": "Address 2", "MEMBERS": "Members", "BUDGET": "Budget"}, "PUBLIC_PAGE": {"LANGUAGES": "Languages", "AWARDS": "Awards", "COMPANY_SKILLS": "Company Skills", "SKILLS": "Skills", "PROFILE": "Profile", "PORTFOLIO": "Portfolio", "OVERVIEW": "Overview", "DESCRIPTION": "Description", "TOTAL_BONUSES_PAID": "Total Bonuses Paid", "COMPANY_PROFILE": "Company Profile", "TOTAL_CLIENTS": "Total Clients", "YEAR_FOUNDED": "Year Founded", "COMPANY_SIZE": "Company Size", "CLIENT_FOCUS": "Client Focus", "MONTHLY_INCOME": "Monthly Income", "TOTAL_INCOME": "הכנ<PERSON>ה כוללת", "TOTAL_PROJECTS": "Total Projects", "MINIMUM_PROJECT_SIZE": "Minimum Project Size", "EMPLOYEES": "עובדים", "PROFITS": "Profits", "RATE": "ציון", "ACTIVE": "פעיל", "STARTED_WORK_ON": "Started work on", "PAY_PERIOD": "Pay period", "AVERAGE_BONUS": "Average Bonus", "AVERAGE_EXPENSES": "Average Expenses", "AVERAGE_INCOME": "Average Income", "EMPLOYEE_UPDATED": "Employee has been updated.", "IMAGE_UPDATED": "The image has been updated.", "FAIL_TO_UPDATE_IMAGE": "Update failed.", "ANONYMOUS": "Anonymous"}, "PROPOSALS_PAGE": {"HEADER": "הצעות - ניהול", "STATISTICS": "סטטיסטיקה", "ACCEPTED_PROPOSALS": "הצעות שהתקבלו", "TOTAL_PROPOSALS": "סך כל ההצעות", "SUCCESS_RATE": "שיעור הצלחה", "PROPOSALS": "הצעות", "REGISTER": {"REGISTER_PROPOSALS": "רישום הצעה", "AUTHOR": "<PERSON><PERSON><PERSON><PERSON>", "TEMPLATE": "תבנית", "JOB_POST_URL": "כתובת URL של משרה", "PICK_A_DATE": "בחר תאריך", "PROPOSAL_DATE": "תאריך הצעה", "JOB_POST_CONTENT": "תו<PERSON>ן משרה", "UPLOAD": "העלה", "PROPOSALS_CONTENT": "תו<PERSON><PERSON> הצעה", "REGISTER_PROPOSALS_BUTTON": "רשום הצעה"}, "PROPOSAL_DETAILS": {"PROPOSAL_DETAILS": "פרטי הצעה", "AUTHOR": "<PERSON><PERSON><PERSON><PERSON>", "JOB_POST_URL": "כתובת URL של משרה", "PROPOSAL_SENT_ON": "הצעה נשלחה בתאריך", "STATUS": "סטטוס", "JOB_POST_CONTENT": "תו<PERSON>ן משרה", "PROPOSAL_CONTENT": "תו<PERSON><PERSON> הצעה", "VIEW_JOB_POST": "צ<PERSON>ה במשרה"}, "EDIT_PROPOSAL": {"EDIT_PROPOSAL": "ערוך הצעה", "AUTHOR": "<PERSON><PERSON><PERSON><PERSON>", "JOB_POST_URL": "כתובת URL של משרה", "PROPOSAL_SENT_ON": "הצעה נשלחה בתאריך", "JOB_POST_CONTENT": "תו<PERSON>ן משרה", "PROPOSAL_CONTENT": "תו<PERSON><PERSON> הצעה", "EDIT_PROPOSAL_BUTTON": "ערוך הצעה", "PLACEHOLDER": {"JOB_POST_URL": "כתובת URL של משרה"}}}, "APPROVAL_REQUEST_PAGE": {"APPROVAL_REQUEST_NAME": "שם", "APPROVAL_REQUEST_TYPE": "סוג", "APPROVAL_REQUEST_MIN_COUNT": "Min Count", "APPROVAL_REQUEST_APPROVAL_POLICY": "Approval Policy", "APPROVAL_REQUEST_STATUS": "עבודה", "APPROVAL_REQUEST_ACTIONS": "Actions", "CREATED_BY": "Created By", "APPROVE": "Approve", "REFUSE": "Refuse", "HEADER": "Approval Request", "EMPLOYEES": "עובדים", "TEAMS": "Teams", "APPROVAL_POLICY": "Approval Policy", "CHOOSE_POLICIES": "Choose policies/s", "EDIT_APPROVAL_REQUEST": "Edit Request Approval", "ADD_APPROVAL_REQUEST": "Add Request Approval", "APPROVAL_REQUEST_CREATED": "Request Approval '{{ name }}' was added", "APPROVAL_REQUEST_UPDATED": "Request Approval '{{ name }}' was changed", "APPROVAL_REQUEST_DELETED": "Request Approval '{{ name }}' was removed", "APPROVAL_SUCCESS": "Approval '{{ name }}' was approved", "REFUSE_SUCCESS": "Approval '{{ name }}' was refused", "APPROVED": "Approved", "REFUSED": "Refused", "REQUESTED": "Requested", "ACTION": "Action", "CREATED_AT": "Created At"}, "APPROVAL_POLICY_PAGE": {"EDIT_APPROVAL_POLICY": "Edit Approval Policy", "ADD_APPROVAL_POLICY": "Add Approval Policy", "HEADER": "Approval Policy", "APPROVAL_POLICY_NAME": "שם", "APPROVAL_POLICY_TYPE": "סוג", "APPROVAL_POLICY_DESCRIPTION": "Description", "BUSINESS_TRIP": "Business Trip", "EQUIPMENT_SHARING": "Equipment Sharing", "TIME_OFF": "Time Off"}, "TIME_OFF_PAGE": {"HEADER": "Time Off", "REQUEST": "Request", "REQUEST_TIME_OFF": "Request Time Off", "EDIT": "ערוך", "ADD_HOLIDAYS": "Add Holidays", "DISPLAY_HOLIDAYS": "Display Holidays", "HOLIDAY_NAME": "Holiday name", "SELECT_EMPLOYEES": "Select Employees", "SETTINGS": "הגדרות", "EDI": "Edit Time Off record", "SELECT_HOLIDAY_NAME": "Select Holiday name", "ADD_OR_REMOVE_EMPLOYEES": "Add or Remove Employees", "SELECT_TIME_OFF_POLICY": "Select Time-off Policy", "ADD_A_DESCRIPTION": "Add a description", "DESCRIPTION": "Description", "START_DATE": "תאריך התחלה", "END_DATE": "תאריך סיום", "REQUEST_DATE": "Request Date", "STATUS": "עבודה", "TIME_OFF_REQUEST": "Time off request", "VIEW_REQUEST_DOCUMENT": "View Request Document", "MULTIPLE_EMPLOYEES": "Multiple employees", "UPLOAD_REQUEST_DOCUMENT": "Upload Request Document", "STATUSES": {"REQUESTED": "Requested", "APPROVED": "Approved", "DENIED": "Denied", "ALL": "All"}, "ACTIONS": {"EDIT": "Edit Time Off record", "APPROVE_DAYS_OFF_REQUEST": "Approve Time Off Request", "DENY_DAYS_OFF_REQUEST": "Deny Time Off Request", "DELETE_DAYS_OFF_REQUEST": "Delete Time Off Request"}, "POLICY": {"HEADER": "Time Off Policy", "POLICY": "Policy", "ADD_POLICY": "Add Policy", "EDIT_POLICY": "Edit Policy", "NAME": "שם", "REQUIRES_APPROVAL": "Requires Approval", "PAID": "Paid", "NAME_IS_REQUIRED": "Policy name is required!"}, "NOTIFICATIONS": {"NO_CHANGES": "No changes", "STATUS_SET_APPROVED": "You successfully set the time off request status to approved.", "ERR_SET_STATUS": "Unable to set time off request status.", "APPROVED_NO_CHANGES": "The time off request status is already set to approved", "RECORD_CREATED": "Time off record was saved", "REQUEST_DENIED": "You successfully set the time off request status to denied", "DENIED_NO_CHANGES": "The time off request status is already set to denied", "REQUEST_DELETED": "Time off request was removed", "ERR_LOAD_RECORDS": "Unable to load time off records", "ERR_DELETE_REQUEST": "Unable to delete Time off request", "ERR_CREATE_RECORD": "Unable to create Time off record", "REQUEST_UPDATED": "Time Off request successfully updated", "ERR_UPDATE_RECORD": "Unable to update Time off record"}}, "TAGS_PAGE": {"HEADER": "Tags", "ADD_TAGS": "Add Tags", "EDIT_TAGS": "Edit Tags", "TAGS_NAME": "שם", "TAGS_DESCRIPTION": "Description", "TAGS_COLOR": "Color", "TAGS_ADD_TAG": "Tag '{{ name }}' was added", "TAGS_EDIT_TAG": "Tag '{{ name }}' was changed", "TAGS_DELETE_TAG": "Tag '{{ name }}' was removed", "TAGS_SELECT_NAME": "Tag name", "TAGS_SELECT_COLOR": "Tag color", "TAGS_SELECT_DESCRIPTION": "Tag description", "ADD_NEW_TAG": "Add new Tag", "TENANT_LEVEL": "Tenant level", "TAGS_TYPE": "Tags type", "TAGS_SELECT_TYPE": "בחר סוג תג"}, "SKILLS_PAGE": {"HEADER": "Skills"}, "LANGUAGE_PAGE": {"HEADER": "Languages", "ADD_NEW_LANGUAGE": "Add new Language"}, "LANGUAGE_LEVELS": {"CONVERSATIONAL": "Conversational", "NATIVE": "Native", "FLUENT": "Fluent"}, "EQUIPMENT_PAGE": {"HEADER": "Equipment", "ADD_EQUIPMENT": "Add Equipment", "EDIT_EQUIPMENT": "Edit Equipment", "EQUIPMENT_NAME": "שם", "EQUIPMENT_TYPE": "סוג", "EQUIPMENT_SN": "SN", "EQUIPMENT_MANUFACTURED_YEAR": "Manufactured year", "EQUIPMENT_INITIAL_COST": "Initial cost", "EQUIPMENT_CURRENCY": "מטבע", "EQUIPMENT_MAX_SHARE_PERIOD": "Max share period", "EQUIPMENT_AUTO_APPROVE": "Auto approve", "EQUIPMENT_EDITED": "Equipment edited", "EQUIPMENT_DELETED": "Equipment '{{ name }}' was removed", "EQUIPMENT_ADDED": "Equipment added", "EQUIPMENT_SAVED": "Equipment '{{ name }}' was saved", "CURRENCY": "מטבע"}, "EQUIPMENT_SHARING_PAGE": {"HEADER": "Equipment Sharing", "ADD_EQUIPMENT_REQUEST": "Add Equipment Request", "EDIT_EQUIPMENT_REQUEST": "Edit Equipment Request", "DELETE_EQUIPMENT_REQUEST": "Delete Equipment Request", "REQUEST": "Request", "EQUIPMENT_NAME": "Equipment name", "EQUIPMENT_SHARING_POLICY": "Equipment Sharing Policy", "SHARE_REQUEST_DATE": "Share request date", "SHARE_START_DATE": "Share start date", "SHARE_END_DATE": "Share end date", "CREATED_BY": "Created By", "STATUS": "עבודה", "REQUEST_SAVED": "Request was saved", "REQUEST_DELETED": "Request was removed", "MESSAGES": {"BEFORE_REQUEST_DAY_ERR": "Date must be after request day.", "EXCEED_PERIOD_ERR": "The maximum allowed days for this item are ", "BEFORE_START_DATE_ERR": "End date cannot be before start date. ", "ITEM_RETURNED_BEFORE_ERR": "Item should be returned before "}, "ACTIONS": "Actions", "APPROVE": "Approve", "REFUSE": "Refuse", "APPROVAL_SUCCESS": "Request Approval Success", "REFUSE_SUCCESS": "Request Refuse Success", "APPROVED": "Approved", "REFUSED": "Refused", "REQUESTED": "Requested", "ACTION": "Action"}, "EQUIPMENT_SHARING_POLICY_PAGE": {"HEADER": "Equipment Sharing Policy", "ADD_EQUIPMENT_SHARING_POLICY": "Add Equipment Sharing Policy", "EDIT_EQUIPMENT_SHARING_POLICY": "Edit Equipment Sharing Policy", "DELETE_EQUIPMENT_SHARING_POLICY": "Delete Equipment Sharing Policy", "REQUEST": "Request", "EQUIPMENT_SHARING_POLICY_NAME": "Equipment Sharing Policy name", "EQUIPMENT_SHARING_POLICY_ORG": "Equipment Sharing Policy organization", "EQUIPMENT_SHARING_POLICY_DESCRIPTION": "Equipment Sharing Policy description", "REQUEST_SAVED": "Policy saved", "REQUEST_DELETED": "Policy deleted", "ACTIONS": "Actions", "MESSAGES": {"EQUIPMENT_REQUEST_SAVED": "Request '{{ name }}' was changed", "EQUIPMENT_REQUEST_DELETED": "Request '{{ name }}' was removed"}}, "INVENTORY_PAGE": {"HEADER": "Inventory", "ADD_INVENTORY_ITEM": "Add inventory item", "EDIT_INVENTORY_ITEM": "Edit inventory item", "INVENTORY_ITEM_DELETED": "Inventory '{{ name }}' was removed", "INVENTORY_ITEM_SAVED": "Inventory '{{ name }}' was saved", "EDIT_PRODUCT_VARIANT": "Edit product variant", "PRODUCT_VARIANT_SAVED": "Product variant saved", "NAME": "שם", "ENABLED": "Enabled", "PRODUCT_TYPE": "Product type", "PRODUCT_CATEGORY": "Product category", "IS_SUBSCRIPTION": "Is subscription", "IS_PURCHASE_AUTOMATICALLY": "Is purchased automatically", "CAN_BE_SOLD": "Can be sold", "CAN_BE_PURCHASED": "Can be purchased", "CAN_BE_CHARGED": "Can be charged", "CAN_BE_RENTED": "Can be rented", "IS_EQUIPMENT": "Is equipment", "TRACK_INVENTORY": "Track inventory", "ADD_OPTION": "Add option", "EDIT_OPTION": "Edit option", "INTERNATIONAL_REFERENCE": "International reference", "CODE": "Code", "NOTES": "הערות", "DESCRIPTION": "Description", "UNIT_COST": "Unit Cost", "UNIT_COST_CURRENCY": "Unit cost currency", "RETAIL_PRICE": "Retail price", "RETAIL_PRICE_CURRENCY": "Retail price currency", "QUANTITY": "Quantity", "TAXES": "Taxes", "BILLING_INVOICING_POLICY": "Billing invoicing policy", "PRODUCT_TYPES": "Product types", "PRODUCT_CATEGORIES": "Product categories", "ORGANIZATION": "Organization", "EDIT_PRODUCT_TYPE": "Edit product type", "ADD_PRODUCT_TYPE": "Add product type", "PRODUCT_TYPE_SAVED": "Product type '{{ name }}' was saved", "PRODUCT_TYPE_DELETED": "Product type '{{ name }}' was removed", "EDIT_PRODUCT_CATEGORY": "Edit product category", "ADD_PRODUCT_CATEGORY": "Add product category", "PRODUCT_CATEGORY_SAVED": "Product category '{{ name }}' was saved", "PRODUCT_CATEGORY_DELETED": "Product category '{{ name }}' was removed", "IMAGE": "תמונה", "LANGUAGE": "Language", "PRODUCT_VARIANT_DELETED": "Product variant deleted!", "ICON": "Icon", "ADD_VARIANT": "Add variant", "EDIT_VARIANT": "Edit variant", "NO_OPTIONS_LABEL": "(no options)", "OPTIONS": "Options", "SELECT_OR_UPLOAD_IMAGE": "Select or upload image", "SELECT_IMAGE": "Select image", "NO_IMAGE_SELECTED": "No image selected", "URL": "Url", "DIMENSIONS": "Dimensions", "FEATURED_IMAGE_WAS_SAVED": "Featured image was saved!", "IMAGE_SAVED": "Image was saved!", "ADD_GALLERY_IMAGE": "Add gallery image", "SET_FEATURED_IMAGE": "Set featured image", "VIEW_GALLERY": "View gallery", "EDIT_IMAGE": "Edit gallery image", "DELETE_IMAGE": "Delete image", "IMAGE_ASSET_DELETED": "Image asset deleted", "CATEGORY": "קטגוריה", "TYPE": "סוג", "VIEW_INVENTORY_ITEM": "View inventory item", "TAGS": "Tags", "PRICE": "Price", "SAVE": "לשמור", "CANCEL": "בטל", "WIDTH": "<PERSON><PERSON><PERSON>", "HEIGHT": "Height", "IMAGE_ADDED_TO_GALLERY": "Image was added to the gallery", "IMAGES_ADDED_TO_GALLERY": "The Images were added to the gallery", "IMAGE_ASSET_UPDATED": "The image asset was updated", "EDIT_IMAGE_ASSET": "Edit image asset", "WAREHOUSES": "Warehouses", "EMAIL": "אימייל", "ACTIVE": "פעיל", "INACTIVE": "Inactive", "LOCATION": "Location", "ADDRESS": "Address", "CREATE_WAREHOUSE": "Create Warehouse", "EDIT_WAREHOUSE": "Edit Warehouse", "WAREHOUSE_CREATED": "Warehouse created", "COULD_NOT_CREATE_WAREHOUSE": "Could not create warehouse", "WAREHOUSE_WAS_CREATED": "Warehouse '{{ name }}' was created", "WAREHOUSE_WAS_DELETED": "Warehouse '{{ name }}' was deleted", "WAREHOUSE_WAS_UPDATED": "Warehouse '{{ name }}' was updated", "CITY": "עיר", "LOGO": "Logo", "CONTACT": "Contact", "COUNTRY": "מדינה", "NEW_OPTION_GROUP": "New option group", "OPTION_GROUP_NAME": "Option group name", "OPTION_TRANSLATIONS": "Option translations", "ADD_PRODUCTS": "Add products", "MANAGE_VARIANTS_QUANTITY": "Manage variants", "ADD_PRODUCT": "Add product", "STORES": "Stores", "ADD_STORE": "Add store", "EDIT_STORE": "Edit store", "CREATE_STORE": "Create store", "PHONE": "Phone", "FAX": "Fax", "FISCAL_INFORMATION": "Fiscal information", "WEBSITE": "Website", "MERCHANTS": "Merchants", "CREATE_MERCHANT": "New Merchant", "DELETE_MERCHANT": "Delete Merchant", "EDIT_MERCHANT": "Edit Merchant", "MERCHANT_CREATED_SUCCESSFULLY": "Merchant '{{ name }}' created successfully!", "MERCHANT_DELETED_SUCCESSFULLY": "Merchant '{{ name }}' deleted!", "MERCHANT_UPDATED_SUCCESSFULLY": "Merchant '{{ name }}' updated successfully!", "THIS_FIELD_IS_REQUIRED": "This field is required", "EMAIL_WRONG_FORMAT": "Email is in wrong format", "PHONE_WRONG_FORMAT": "Phone is in wrong format", "SELECTED": "Selected", "SUCCESSFULLY_ADDED_PRODUCTS": "Products were added successfully!", "MAIN": "עיקרי", "INVENTORY": "Inventory", "IMAGE_WAS_DELETED": "Image was deleted"}, "TASKS_PAGE": {"HEADER": "Tasks", "MY_TASK_HEADER": "My Tasks", "TEAM_TASKS_HEADER": "Team's Tasks", "ADD_TASKS": "Add Tasks", "EDIT_TASKS": "Edit Tasks", "EDIT_TASK": "Edit Task", "DELETE_TASK": "Delete Task", "TASKS_TITLE": "Title", "TASKS_DESCRIPTION": "Description", "TASKS_LOADED": "Tasks loaded", "TASK_ADDED": "Task was added", "TASK_UPDATED": "Task was changed", "TASK_DELETED": "Task was removed", "TASKS_PROJECT": "פרויקטים", "TASKS_CREATOR": "Created By", "TASK_MEMBERS": "עובדים", "TASK_ASSIGNED_TO": "Assigned To", "TASK_TEAMS": "Teams", "TASK_ID": "ID", "TASK_NUMBER": "Task Number", "DUE_DATE": "Due Date", "ESTIMATE": "Estimate", "ESTIMATE_DAYS": "Days", "ESTIMATE_HOURS": "Hours", "ESTIMATE_MINUTES": "<PERSON>s", "TASKS_STATUS": "עבודה", "TASK_PRIORITY": "עדיפות", "TASK_SIZE": "גודל", "PARENT_TASK": "משימה ראשית", "TODO": "Todo", "IN_PROGRESS": "In Progress", "FOR_TESTING": "For Testing", "COMPLETED": "Completed", "TASK_VIEW_MODE": "Task View Mode", "PROJECT": "פרויקטים", "COMPLETE_SPRINT": "Complete Sprint", "DATE_START": "Date Start", "DATE_END": "Date End", "BACKLOG": "Backlog", "EDIT_SPRINT": "Edit Sprint", "DELETE_SPRINT": "Delete Sprint", "SELECT": "Select", "SPRINTS_SETTINGS": "Sprints Settings", "ARE_YOU_SURE": "Are you sure you want delete sprint", "SETTINGS": "הגדרות", "MODULE": "מודול", "SELECT_MODULE": "מודולים"}, "JOBS": {"EMPLOYEE": "עובד", "TITLE": "Title", "DESCRIPTION": "Description", "CREATED_DATE": "Created Date", "STATUS": "עבודה", "ACTION": "Action", "APPLY": "החל ידנית", "APPLY_AUTO": "החל אוטומטית", "CLOSED": "Closed", "OPEN": "Open", "APPLIED": "Applied", "COMPLETED": "Completed", "VIEW": "View", "HIDE": "<PERSON>de", "NO_JOBS": "No Jobs found", "JOB_SEARCH": "Job Search", "JOB_DETAILS": "פרטי עבודה", "LOAD_MORE": "Load More", "HIDE_ALL_CONFIRM": "Are you sure you want to Hide All jobs?", "ACTIONS": "Actions", "UPWORK": "Upwork", "WEB": "Web", "HOURLY": "Hourly", "FIXED": "Fixed", "FILTER": {"TITLE": "Advanced Filter", "SOURCE": "Source", "JOB_TYPE": "Job Type", "JOB_STATUS": "Job Status", "BUDGET": "Budget", "LESS_THAN": "Less than"}, "BROWSE": "Browse", "SEARCH": "Search", "HISTORY": "History", "EMPLOYEES": "עובדים", "MATCHINGS": "Matchings", "PROPOSALS_TEMPLATE": "Proposals Template"}, "JOB_MATCHING": {"CONFIGURE_EMPLOYEES_TO_JOBS_MATCHING": "Configure Employees to Jobs Matching", "SOURCE": "Source", "PRESET": "Preset", "KEYWORDS": "Keywords", "CATEGORY": "קטגוריה", "OCCUPATION": "Occupation", "ADD_NEW_CRITERIONS": "Add New", "FIX_PRICE": "Fix Price", "HOURLY": "Hourly", "SAVE": "לשמור", "DELETE": "מחק", "CRITERIONS": "Criterions", "DELETE_CRITERION_MESSAGE": "Are you sure that, you want to delete criterion", "SAVE_PRESET_MESSAGE": "Criterions will save for job preset. Are you sure you want to save?"}, "JOB_EMPLOYEE": {"EMPLOYEE": "עובד", "EMPLOYEES": "עובדים", "AVAILABLE_JOBS": "Available Jobs", "APPLIED_JOBS": "Applied Jobs", "JOB_SEARCH_STATUS": "Job Search Status", "BROWSE": "Browse", "SEARCH": "Search", "HISTORY": "History", "BILLING_RATE": "תעריף חיוב", "MINIMUM_BILLING_RATE": "תעריף חיוב מינימלי"}, "PROPOSAL_TEMPLATE": {"PROPOSAL_TEMPLATE": "Proposal Template", "EDIT_PROPOSAL_TEMPLATE": "Edit Proposal Template", "ADD_PROPOSAL_TEMPLATE": "Add Proposal Template", "SELECT_PROPOSAL_TEMPLATE": "Select Proposal Template", "SELECT_EMPLOYEE": "Select employee", "NAME": "שם", "CONTENT": "Content", "EMPLOYEE": "עובד", "DESCRIPTION": "Description", "IS_DEFAULT": "<PERSON>", "CONFIRM_DELETE": "Are you sure that, you want to delete", "PROPOSAL_CREATE_MESSAGE": "Proposal template '{{ name }}' was added", "PROPOSAL_EDIT_MESSAGE": "Proposal template '{{ name }}' was changed", "PROPOSAL_DELETE_MESSAGE": "Proposal template '{{ name }}' was removed", "PROPOSAL_MAKE_DEFAULT_MESSAGE": "Proposal template '{{ name }}' set as default", "YES": "Yes", "NO": "No", "BROWSE": "Browse", "SEARCH": "Search"}, "SPRINTS_PAGE": {"SPRINT_ADDED": "Sprint added", "SPRINT_UPDATED": "Sprint updated", "SPRINT_DELETED": "Sprint deleted", "SPRINT": "Sprint", "ADD_SPRINT_NAME": "Add sprint name"}, "USERS_PAGE": {"HEADER": "Manage Users", "ADD_USER": "Add User", "ADD_EXISTING_USER": "Add Existing User", "ADD_EXISTING_ORGANIZATION": "Add Existing Organization", "ADD_EXISTING_USER_TOOLTIP": "Add user from other organization", "CONVERT_USER_TO_EMPLOYEE": "המשת<PERSON>ש הומר בהצלחה לעובד.", "ROLE": {"SUPER_ADMIN": "Super Admin", "ADMIN": "מנהל", "MANAGER": "Manager", "DATA_ENTRY": "Data Entry", "VIEWER": "Viewer", "EMPLOYEE": "עובד", "CANDIDATE": "Candidate", "ROLE": "Role"}, "EDIT_USER": {"HEADER": "Manage User", "EDIT_EXISTING_USER": "Edit Existing User", "MAIN": "עיקרי", "USER_ORGANIZATIONS": "ארגונים"}, "REMOVE_USER": "'{{ name }}' was removed", "ACTIVE": "פעיל", "NOT_STARTED": "Not Started"}, "CONTEXT_MENU": {"TIMER": "Стар<PERSON><PERSON><PERSON><PERSON><PERSON> Таймер", "ADD_INCOME": "הכנסה", "ADD_EXPENSE": "הוצאה", "INVOICE": "Фактура", "ESTIMATE": "Estimate", "PAYMENT": "Payment", "TIME_LOG": "Time Log", "CANDIDATE": "Candidate", "PROPOSAL": "Предложение", "CONTRACT": "Договор", "TEAM": "Team", "TASK": "Задача", "CLIENT": "לקוחות", "CONTACT": "Contact", "PROJECT": "פרויקטים", "ADD_EMPLOYEE": "עובד", "CHAT": "Чат с нас", "FAQ": "Въпроси и Отговори", "HELP": "עזרה"}, "QUICK_ACTIONS_MENU": {"CREATE_INVOICE": "יצירת חשבונית", "RECEIVED_INVOICES": "חשבוניות שהתקבלו", "CREATE_INCOME": "יצירת הכנסה", "CREATE_EXPENSE": "יצירת הוצאה", "CREATE_ESTIMATE": "יצירת אומדן", "RECEIVED_ESTIMATES": "הערכות שהתקבלו", "CREATE_PAYMENT": "יצירת תשלום", "ADD_EMPLOYEE": "הוסף עובד", "ADD_INVENTORY": "הוסף מלאי", "ADD_EQUIPMENT": "הוסף ציוד", "ADD_VENDOR": "הוסף ספק", "ADD_DEPARTMENT": "הוסף מחלקה", "CREATE_TEAM": "יצירת צוות", "CREATE_TASK": "יצירת משימה", "CREATE_PROJECT": "יצירת פרויקט", "VIEW_TASKS": "צ<PERSON>י<PERSON>ה במשימות", "VIEW_TEAM_TASKS": "הצג משימות של הקבוצה", "CREATE_CANDIDATE": "יצירת מועמד", "CREATE_PROPOSAL": "יצירת הצעה", "CREATE_CONTRACT": "יצירת חוזה", "CREATE_LEAD": "יצירת ליד", "CREATE_CUSTOMER": "יצירת לקוח", "CREATE_CLIENT": "יצירת לקוח", "VIEW_CLIENTS": "הצג לקוחות", "START_TIMER": "התחל טיימר", "STOP_TIMER": "עצור טיימר", "TIME_LOG": "<PERSON><PERSON><PERSON><PERSON>", "VIEW_APPOINTMENTS": "צפייה בפגישות", "VIEW_TIME_ACTIVITY": "צ<PERSON><PERSON><PERSON>ה בפעילות זמן"}, "QUICK_ACTIONS_GROUP": {"ACCOUNTING": "חשבונאות", "ORGANIZATION": "ארג<PERSON>ן", "PROJECT_MANAGEMENT": "ניהול פרויקטים", "JOBS": "משרות", "CONTACTS": "א<PERSON><PERSON><PERSON> ק<PERSON>ר", "TIME_TRACKING": "<PERSON><PERSON><PERSON><PERSON>"}, "PROFILE_PAGE": {"FIRST_NAME": "שם פרטי", "LAST_NAME": "שם משפחה", "EMAIL": "דוא\"ל", "PASSWORD": "סיסמה", "REPEAT_PASSWORD": "Repeat Password", "ERROR": "Error", "SAVE": "לשמור", "VALIDATION": {"EMAIL_REQUIRED": "Email is required!", "PASSWORDS_DO_NOT_MATCH": "Password Do Not Match!"}}, "INVITE_PAGE": {"USER": {"MANAGE": "Manage User Invites", "HEADER": "Invite Users", "ACTION": "Invite Users"}, "EMPLOYEE": {"MANAGE": "Manage Employee Invites", "HEADER": "Invite Employees", "ACTION": "Invite Employees"}, "CANDIDATE": {"MANAGE": "Manage Candidate In<PERSON>tes", "HEADER": "In<PERSON><PERSON>", "ACTION": "In<PERSON><PERSON>"}, "SENT": "{{total}} In<PERSON><PERSON>.", "IGNORED": "{{total}} In<PERSON><PERSON>. {{ignored}} were already invited and have been ignored.", "STATUS": {"INVITED": "Invited", "EXPIRED": "Expired", "ACCEPTED": "Accepted"}, "INVITATION_EXPIRATION_OPTIONS": {"DAY": "1 Day", "WEEK": "7 Days", "TWO_WEEK": "14 Days", "MONTH": "30 Days", "NEVER": "Never"}}, "INVOICES_PAGE": {"SENDER": "Sender", "BROWSE": "Browse", "COMMENT": "Comment", "COMMENTS": "Comments", "HEADER": "Invoices", "INVOICE_NUMBER": "Invoice Number", "ESTIMATE_NUMBER": "Estimate Number", "INVOICE_DATE": "Invoice Date", "ESTIMATE_DATE": "Estimate Date", "DUE_DATE": "Due Date", "CURRENCY": "מטבע", "DISCOUNT": "Discount", "CONTACT": "Contact", "TOTAL_VALUE": "Total Value", "PAID_STATUS": "Paid Status", "TAX": "Tax", "TAX_2": "Tax 2", "INVOICE_ACCEPTED": "Invoice Accepted", "INVOICE_REJECTED": "Invoice Rejected", "INVOICES_ADD_INVOICE": "Invoice Added", "INVOICES_ADD_ESTIMATE": "Estimated Added", "INVOICES_EDIT_INVOICE": "Invoice Edited", "INVOICES_EDIT_ESTIMATE": "Estimate Edited", "INVOICES_DUPLICATE_INVOICE": "Invoice Duplicated", "INVOICES_DUPLICATE_ESTIMATE": "Estimate Duplicated", "INVOICES_DELETE_INVOICE": "Invoice Deleted", "INVOICES_DELETE_ESTIMATE": "Estimate Deleted", "INVOICES_SELECT_INVOICE_DATE": "Invoice Date", "INVOICES_SELECT_DUE_DATE": "Due Date", "INVOICES_SELECT_CURRENCY": "מטבע", "INVOICES_SELECT_DISCOUNT_VALUE": "Discount Value", "INVOICES_SELECT_DISCOUNT": "Discount", "INVOICES_SELECT_PAID": "Paid", "INVOICES_TAXES": "Taxes", "INVOICES_SELECT_TERMS": "Terms", "ADD_INVOICE": "Add Invoice", "ADD_ESTIMATE": "Add Estimate", "EDIT_INVOICE": "Edit Invoice", "EDIT_ESTIMATE": "Edit Estimate", "VIEW_INVOICE": "View Invoice", "VIEW_ESTIMATE": "View Estimate", "SELECT_EMPLOYEE": "Select Employee", "SELECT_PROJECT": "Select Project", "SELECT_TASK": "Select Task", "SELECT_PRODUCT": "Select Product", "INVALID_DATES": "Invalid Dates", "INVOICE_NUMBER_DUPLICATE": "Invoice Number already exists", "DISCOUNT_TYPE": "Discount Type", "TAX_TYPE": "Tax Type", "TAX_VALUE": "Tax Value", "PERCENT": "Percent", "FLAT": "Flat", "SUBTOTAL": "Subtotal", "TOTAL": "Total", "NAME": "שם", "PAID": "Paid", "NOT_PAID": "Not Paid", "RECEIVED_INVOICES": "Received Invoices", "RECEIVED_ESTIMATES": "Received Estimates", "SEND_INVOICE": "Invoice Sent", "SEND_ESTIMATE": "Estimate Sent", "APPLY_TAX": "Apply Tax", "APPLY_DISCOUNT": "Apply Discount", "APPLIED": "Applied", "NOT_APPLIED": "Not Applied", "STATUS": "עבודה", "SET_STATUS": "SET STATUS", "SHOW_COLUMNS": "Show Columns", "ITEM": "<PERSON><PERSON>", "SETTINGS": "הגדרות", "SHOW_HIDE_COLUMNS": "Show/Hide Columns", "INVOICES_PER_PAGE": "Invoices Per Page", "ESTIMATES_PER_PAGE": "Estimates Per Page", "APPLY_DISCOUNT_AFTER_TAX": "Apply discount after tax", "SELECT_INVOICE_TO_VIEW_HISTORY": "Select an invoice to view its history.", "INVOICE_SENT_TO": "Invoice sent to '{{ name }}'", "ESTIMATE_SENT_TO": "Estimate sent to '{{ name }}'", "INVOICE": "Invoice", "ESTIMATE": "Estimate", "ACTIONS": "Actions", "HISTORY": "History", "SEARCH": "Search", "NUMBER": "Number", "FROM": "FROM", "TO": "TO", "DATE": "תאריך", "YES": "Yes", "NO": "No", "ALREADY_PAID": "Already Paid", "AMOUNT_DUE": "Amount Due", "INVOICED_REMAINING_AMOUNT": "Invoiced Remaining Amount", "TAX_CALCULATION_TYPE": "סוג חישוב מס", "SIMPLE": "פשוט", "COMPOSED": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "INVOICE_TYPE": {"INVOICE_TYPE": "Invoice Type", "ESTIMATE_TYPE": "Estimate Type", "GENERATE_INVOICE_ITEMS": "Generate Invoice Items", "GENERATE_ESTIMATE_ITEMS": "Generate Estimate Items", "GENERATE_FOR_UNINVOICED_EXPENSES": "Generate for all uninvoiced expenses", "BY_EMPLOYEE_HOURS": "By Employee Hours", "BY_PROJECT_HOURS": "By Project Hours", "BY_TASK_HOURS": "By Task Hours", "BY_PRODUCTS": "By Products", "BY_EXPENSES": "By Expenses", "DETAILED_ITEMS": "Detailed Items", "SELECT_INVOICE_TYPE": "Select an invoice type", "SELECT_ESTIMATE_TYPE": "Select an estimate type", "SELECT_PROJECTS": "Select projects", "SELECT_TASKS": "Select tasks", "SELECT_PRODUCTS": "Select products", "SELECT_EXPENSES": "Select expenses"}, "INVOICE_ITEM": {"ITEM_NUMBER": "Item Number", "TASK": "Task", "NAME": "שם", "DESCRIPTION": "Description", "PRICE": "Price", "QUANTITY": "Quantity", "TOTAL_VALUE": "Total Value", "EMPLOYEE": "עובד", "HOURLY_RATE": "Hourly Rate", "HOURS_WORKED": "Hours Worked", "PROJECT": "פרויקטים", "PRODUCT": "Product", "EXPENSE": "הוצאה", "NO_ITEMS": "Please add invoice items", "INVALID_ITEM": "Invalid Invoice Item", "EMPLOYEE_VALUE": "Employee cannot be empty", "PROJECT_VALUE": "Project cannot be empty", "TASK_VALUE": "Task cannot be empty", "ITEM": "<PERSON><PERSON>"}, "SEND": {"CONFIRMATION_INVOICE": "Send this invoice to", "CONFIRMATION_ESTIMATE": "Send this estimate to", "ALREADY_SENT_INVOICE": "This invoice has already been sent to", "ALREADY_SENT_ESTIMATE": "This estimate has already been sent to", "NOT_LINKED": "Client does not have an organization", "SENT": "<PERSON><PERSON>", "NOT_SENT": "Not Sent"}, "VIEW": {"FROM": "FROM", "TO": "TO"}, "DOWNLOAD": {"CONFIRMATION_INVOICE": "Download this invoice ?", "CONFIRMATION_ESTIMATE": "Download this estimate ?", "INVOICE_DOWNLOAD": "Invoice downloaded", "ESTIMATE_DOWNLOAD": "Estimate downloaded"}, "EMAIL": {"EMAIL_INVOICE": "Send this invoice by email ?", "EMAIL_ESTIMATE": "Send this estimate by email ?", "EMAIL_SENT": "<PERSON><PERSON>"}, "ESTIMATES": {"HEADER": "Estimates", "ESTIMATE_NUMBER": "Estimate Number", "ESTIMATE_DATE": "Estimate Date", "ACCEPT": "Accept", "REJECT": "Reject", "ACCEPTED": "Accepted", "REJECTED": "Rejected", "ACCEPTED_STATUS": "Accepted Status", "ESTIMATE_CONVERT": "Estimate Converted", "SELECT_ESTIMATE_TO_VIEW_HISTORY": "Select an estimate to view its history.", "ESTIMATE_ACCEPTED": "Estimate accepted", "ESTIMATE_REJECTED": "Estimate rejected", "ERROR": "An error occurred", "CONVERTED_TO_INVOICE": "Estimate converted to invoice"}, "PAYMENTS": {"HEADER": "Payments for Invoice", "TOTAL_VALUE": "Total Invoice Value", "RECORD_PAYMENT": "Record Payment", "EDIT_PAYMENT": "Edit Payment", "DELETE_PAYMENT": "Delete Payment", "TOTAL_PAID": "Total Paid", "PAID": "Paid", "PAYMENT_DATE": "Payment Date", "AMOUNT": "כמות", "RECORDED_BY": "Recorded By", "NOTE": "Note", "PAYMENT_ADD": "Payment was added", "PAYMENT_EDIT": "Payment was changed", "PAYMENT_DELETE": "Payment was removed", "PAYMENT_DOWNLOAD": "Payment Downloaded", "STATUS": "עבודה", "ON_TIME": "On time", "OVERDUE": "Overdue", "NO_PAYMENTS_RECORDED": "No payments recorded", "LEFT_TO_PAY": "Left to pay", "PAYMENT_METHOD": "Payment Method", "SELECT_INVOICE": "Select Invoice", "PAYMENT_AMOUNT_ADDED": "Payment of {{ amount }} {{ currency }} added", "PAYMENT": "Payment", "BANK_TRANSFER": "Bank Transfer", "CASH": "Cash", "CHEQUE": "Cheque", "CREDIT_CARD": "Credit Card", "DEBIT": "Debit", "ONLINE": "Online", "PAYMENTS_FOR_INVOICE": "Payments for invoice", "RECEIVED_FROM": "Received from", "RECEIVER": "Receiver", "SEND_RECEIPT": "Send this receipt to {{ name }} ?", "CONTACT_GREETING": "Hi {{ name }},", "RECEIPT_FOR": "This is your receipt for Invoice {{ invoiceNumber }} for {{ amount }} {{ currency }}.", "BEST_REGARDS": "Best regards the {{ name }} team."}, "INTERNAL_NOTE": {"NOTE_SAVED": "Internal Note was added", "ADD_INTERNAL_NOTE": "Add Internal Note", "ADD_NOTE": "Add note", "NOTE": "Note", "INTERNAL_NOTE": "Internal Note"}, "STATUSES": {"DRAFT": "Draft", "SENT": "<PERSON><PERSON>", "VIEWED": "Viewed", "FULLY_PAID": "<PERSON>y Paid", "PARTIALLY_PAID": "Partially Paid", "OVERPAID": "Overpaid", "VOID": "Void", "ACCEPTED": "Accepted", "REJECTED": "Rejected"}, "ACTION": {"DUPLICATE": "Duplicate", "SEND": "Send", "CONVERT_TO_INVOICE": "Convert to invoice", "EMAIL": "אימייל", "DELETE": "מחק", "NOTE": "Note", "PAYMENTS": "Payments"}, "PUBLIC_LINK": {"HEADER": "Generate Public Link", "GENERATE": "Generate a link to the {{ text }} that anyone with the link can view.", "ACCESS": "Anyone with access to this link can view the {{ text }}.", "COPY_TO_CLIPBOARD_TOOLTIP": "Copy public link to clipboard"}}, "PAYMENTS_PAGE": {"HEADER": "Payments", "CONTACT": "Contact", "AMOUNT": "כמות", "PAYMENT_DATE": "Payment Date", "RECORDED_BY": "Recorded By", "NOTE": "Note", "STATUS": "עבודה", "ON_TIME": "On time", "OVERDUE": "Overdue", "PAYMENT_METHOD": "Payment Method", "PROJECT": "פרויקטים", "TAGS": "Tags"}, "HEADER": {"SELECT_EMPLOYEE": "Select Employee", "SELECT_A_DATE": "Select A date", "SELECT_AN_ORGANIZATION": "Select An Organization", "SELECT_PROJECT": "Select Project", "SELECT_TEAM": "<PERSON><PERSON><PERSON>ה"}, "HEADER_TITLE": {"FOR": "for", "FROM": "from"}, "PAGE_NOT_FOUND": {"404_PAGE_NOT_FOUND": "Page Not Found", "TAKE_ME_HOME": "Take me home", "THE_PAGE_YOU_WERE_LOOKING_FOR_DOES_NOT_EXIST": "The page you were looking for doesn't exist", "REDIRECT_TO_HOME": "תופנה לדף הבית בקרוב"}, "HELP_PAGE": {"HELP": "עזרה", "KNOWLEDGE_BASE": "Knowledge base", "CHOSE_ICON": "Chose icon to set", "CREATED_AT": "created at", "WRITTEN_BY": "written by", "EMPLOYEES": "עובדים", "ONLY_FOR_EMPLOYEES": "Only for employees", "DRAFT": "draft", "ADD_ARTICLE": "Add article", "CHOOSE_ANY_CATEGORY": "Choose any category", "ARTICLES": "articles", "REMOVE_ARTICLE": "Remove Article", "ARE_YOU_SURE": "Are you sure? This cannot be undone.", "DESCRIPTION": "Description", "ARTICLE_TEXT": "Article text", "EDIT_ARTICLE": "Edit Article", "MANAGE_CATEGORY": "Manage Category", "ADD_CATEGORY": "Add Category", "EDIT_BASE": "Edit Knowledge Base", "DELETE_BASE": "Delete Base", "LANGUAGE": "Language", "PUBLISH_STATUS": "Publish Status", "PRIVATE_STATUS": "Private Status", "COLOR": "Color", "NAME_CATEGORY": "Name of the category", "NAME_ARTICLE": "Name of the Article", "ADD_BASE": "Add Knowledge Base", "MANAGE_BASE": "Manage Knowledge Base", "NAME_OF_THE_BASE": "Name of the base", "REMOVE_CATEGORY": "Remove Category", "REMOVE_BASE": "Remove Base", "CLEAR": "Clear", "SEARCH_BY_NAME": "Search by name", "FILTER_BY_AUTHOR": "Filter by author", "CATEGORY_EDIT_ADDED": "Category was added", "CATEGORY_EDIT_UPDATED": "Category was changed", "CATEGORY_EDIT_DELETED": "Category was removed"}, "PROJECT_MANAGEMENT_PAGE": {"THIS_TAB_WILL_SHOW_PROJECT_MANAGEMENT_CHARTS_AND_AGGREGATED_DATA": "This tab will show project management charts and aggregated data.", "PROJECT_MODULE": {"PROJECT": "פרויקט", "SELECT_PROJECT": "<PERSON><PERSON><PERSON> פרוי<PERSON>ט", "STATUS": "סטטוס", "TEAM_MEMBERS": "<PERSON><PERSON><PERSON><PERSON> הצוות", "TEAMS": "צוותים", "SELECT_TEAMS": "ב<PERSON>ר צוותים", "CHOOSE_TEAMS": "ב<PERSON>ר צוותים", "NAME": "שם", "ENTER_NAME": "<PERSON><PERSON><PERSON><PERSON> שם", "PARENT_MODULE": "מודול הורה", "SELECT_PARENT": "בחר מודול הורה", "START_DATE": "תאריך התחלה", "SELECT_START_DATE": "בחר תאריך התחלה", "END_DATE": "תאריך סיום", "SELECT_END_DATE": "בחר תאריך סיום", "DESCRIPTION": "תיאור", "IS_FAVORITE": "מועדף"}}, "SETTINGS_FEATURES": {"INVOICE": "Invoice", "INCOME": "הכנסה", "EXPENSE": "הוצאה", "PAYMENT": "Payment", "PROPOSAL": "Предложение", "SALES_PIPELINE": "Sales Pipeline", "TASK_DASHBOARD": " Task Dashboard", "JOBS": "Jobs", "EMPLOYEES": "עובדים", "TIME_ACTIVITY": "Time Activity", "TIMESHEET": "Timesheet", "APPOINTMENT_SCHEDULE": "Appointment & Schedule", "CANDIDATE": "Candidate", "MANAGE_ORGANIZATION": "Manage Organization", "PRODUCT_INVENTORY": "Product Inventory", "PROJECT": "פרויקטים", "ORGANIZATION_TEAM": "Organization Team", "ORGANIZATION_DOCUMENT": "Organization Document", "LEAD_CUSTOMER_CLIENT": "Lead, Customer & Client", "GOAL_AND_OBJECTIVE": "Goal and Objective", "ALL_REPORT": "All Report", "USERS": "Users", "ORGANIZATIONS": "ארגונים", "APPS_INTEGRATIONS": "Apps & Integrations", "EMAIL_HISTORY": "Email History", "SETTING": "Setting", "ENTITY_IMPORT_EXPORT": "Entity Import & Export", "CUSTOM_SMTP": "Custom SMTP", "ROLES_PERMISSIONS": "Roles & Permissions", "TIME_TRACKING": "Time Tracking", "ESTIMATE": "Estimate", "DASHBOARD": "לוח מחוונים"}, "SETTINGS_FEATURES_DESCRIPTION": {"INVOICE": {"MANAGE_INVOICE_CREATE_FIRST_INVOICE": "Manage Invoice, Create First Invoice"}, "INCOME": {"CREATE_FIRST_INCOME": "Create First Income"}, "EXPENSE": {"CREATE_FIRST_EXPENSE": "Create First Expense"}, "PAYMENT": {"MANAGE_PAYMENT_CREATE_FIRST_PAYMENT": "Manage Payment, Create First Payment"}, "PROPOSAL": {"MANAGE_PROPOSAL_REGISTER_FIRST_PROPOSAL": "Manage Proposal, Register First Proposal"}, "SALES_PIPELINE": {"CREATE_SALES_PIPELINE": "Create Sales Pipeline"}, "TASK_DASHBOARD": {"TASK_DASHBOARD": "Task Dashboard"}, "JOBS": {"JOB_SEARCH_JOBS_MATCHING": "Job Search & Jobs Matching"}, "EMPLOYEES": {"MANAGE_EMPLOYEES_ADD_OR_INVITE_EMPLOYEES": "Manage Employees, Add or Invite Employees"}, "TIME_ACTIVITY": {"MANAGE_TIME_ACTIVITY_SCREENSHOTS_APP_VISITED_SITES_ACTIVITIES": "Manage Time Activity, Screenshots, App, Visited Sites, Activities"}, "TIMESHEET": {"MANAGE_EMPLOYEE_TIMESHEET_DAILY_WEEKLY_CALENDAR_CREATE_FIRST_TIMESHEET": "Manage Employee Timesheet Daily, Weekly, Calendar, Create First Timesheet"}, "APPOINTMENT_SCHEDULE": {"EMPLOYEE_APPOINTMENT_SCHEDULES_BOOK_PUBLIC_APPOINTMENT": "Employee Appointment, Schedules & Book Public Appointment"}, "CANDIDATE": {"MANAGE_CANDIDATES_INTERVIEWS_INVITES": "Manage Candidates, Interviews & Invites"}, "MANAGE_ORGANIZATION": {"MANAGE_ORGANIZATION_DETAILS_LOCATION_AND_SETTINGS": "Manage Organization Details, Location and Settings"}, "PRODUCT_INVENTORY": {"MANAGE_PRODUCT_INVENTORY_CREATE_FIRST_PRODUCT": "Manage Product Inventory, Create First Product"}, "PROJECT": {"MANAGE_PROJECT_CREATE_FIRST_PROJECT": "Manage Project, Create First Project"}, "ORGANIZATION_TEAM": {"MANAGE_ORGANIZATION_TEAM_CREATE_FIRST_TEAM": "Manage Organization Team, Create First Team"}, "ORGANIZATION_DOCUMENT": {"MANAGE_ORGANIZATION_DOCUMENT_CREATE_FIRST_DOCUMENT": "Manage Organization Document, Create First Document"}, "LEAD_CUSTOMER_CLIENT": {"MANAGE_LEADS_CUSTOMERS_AND_CLIENTS_CREATE_FIRST_CUSTOMER/CLIENTS": "Manage Leads, Customers and Clients, Create First Customer/Clients"}, "GOAL_AND_OBJECTIVE": {"MANAGE_GOALS_AND_OBJECTIVES": "Manage Goals and Objectives"}, "ALL_REPORT": {"MANAGE_EXPENSE_WEEKLY_TIME_ACTIVITY_AND_ETC_REPORTS": "Manage Expense, Weekly, Time & Activity and etc reports"}, "USERS": {"MANAGE_TENANT_USERS": "Manage Tenant Users"}, "ORGANIZATIONS": {"MANAGE_TENANT_ORGANIZATIONS": "Manage Tenant Organizations"}, "APPS_INTEGRATIONS": {"MANAGE_AVAILABLE_APPS_INTEGRATIONS_LIKE_UPWORK_HUBSTAFF": "Manage Available Apps & Integrations Like Upwork & Hubstaff"}, "EMAIL_HISTORY": {"MANAGE_EMAIL_HISTORY": "Manage Email History"}, "SETTING": {"MANAGE_SETTING": "Manage Setting"}, "ENTITY_IMPORT_EXPORT": {"MANAGE_ENTITY_IMPORT_AND_EXPORT": "Manage Entity Import and Export"}, "CUSTOM_SMTP": {"MANAGE_TENANT_ORGANIZATION_CUSTOM_SMTP": "Manage Tenant & Organization Custom SMTP"}, "ROLES_PERMISSIONS": {"MANAGE_ROLES_PERMISSIONS": "Manage Roles & Permissions"}, "TIME_TRACKING": {"DOWNLOAD_DESKTOP_APP_CREATE_FIRST_TIMESHEET": "Download Desktop App, Create First Timesheet"}, "ESTIMATE": {"MANAGE_ESTIMATE_CREATE_FIRST_ESTIMATE": "Manage Estimate, Create First Estimate"}, "DASHBOARD": {"GO_TO_DASHBOARD_MANAGE_EMPLOYEE_STATISTICS_TIME_TRACKING_DASHBOARD": "Go to dashboard, Manage Employee Statistics, Time Tracking Dashboard"}}, "SETTINGS_FEATURES_TEXT": {"INVOICE": {"INVOICE_RECEIVED": "Invoice Received"}, "INCOME": {"": ""}, "EXPENSE": {"EMPLOYEE_RECURRING_EXPENSE": "Employee Recurring Expense", "ORGANIZATION_RECURRING_EXPENSES": "Organization Recurring Expenses"}, "PAYMENT": {"": ""}, "PROPOSAL": {"PROPOSAL_TEMPLATE": "Proposal Template"}, "SALES_PIPELINE": {"SALES_PIPELINE_DEAL": "Sales Pipeline Deal"}, "TASK_DASHBOARD": {"TEAM_TASK_DASHBOARD": "Team Task Dashboard", "MY_TASK_DASHBOARD": "My Task Dashboard"}, "JOBS": {"": ""}, "EMPLOYEES": {"EMPLOYEE_LEVEL": "Employee Level", "EMPLOYEE_POSITION": "Employee Position", "EMPLOYEE_TIME_OFF": "Employee Time Off", "EMPLOYEE_APPROVAL": "Employee Approval", "EMPLOYEE_APPROVAL_POLICY": "Employee Approval Policy"}, "TIME_ACTIVITY": {"": ""}, "TIMESHEET": {"": ""}, "APPOINTMENT_SCHEDULE": {"": ""}, "CANDIDATE": {"MANAGE_INTERVIEW": "Manage Interview", "MANAGE_INVITE": "Manage Invite"}, "MANAGE_ORGANIZATION": {"HELP_CENTER": "Help Center", "ORGANIZATION_TAG": "Organization Tag", "ORGANIZATION_EQUIPMENT": "Organization Equipment", "ORGANIZATION_VENDOR": "Organization Vendor", "ORGANIZATION_DEPARTMENT": "Organization Department", "ORGANIZATION_EMPLOYMENT_TYPE": "Organization Employment Type"}, "PRODUCT_INVENTORY": {"": ""}, "PROJECT": {"": ""}, "ORGANIZATION_TEAM": {"": ""}, "ORGANIZATION_DOCUMENT": {"": ""}, "LEAD_CUSTOMER_CLIENT": {"": ""}, "GOAL_AND_OBJECTIVE": {"GOAL_TIME_FRAME_KPI": "Goal Time Frame & KPI"}, "ALL_REPORT": {"": ""}, "USERS": {"": ""}, "ORGANIZATIONS": {"": ""}, "APPS_INTEGRATIONS": {"": ""}, "EMAIL_HISTORY": {"CUSTOM_EMAIL_TEMPLATE": "Custom Email Template"}, "SETTING": {"FILE_STORAGE": "File Storage", "SMS_GATEWAY": "SMS Gateway"}, "ENTITY_IMPORT_EXPORT": {"": ""}, "CUSTOM_SMTP": {"": ""}, "ROLES_PERMISSIONS": {"": ""}, "TIME_TRACKING": {"": ""}, "ESTIMATE": {"ESTIMATE_RECEIVED": "Estimate Received"}, "DASHBOARD": {"": ""}}, "ABOUT_PAGE": {"ABOUT": "עלינו"}, "FOOTER": {"BY": "על ידי", "RIGHTS_RESERVED": "כל הזכויות שמורות", "PRESENT": "Present", "TERMS_OF_SERVICE": "Terms Of Service", "PRIVACY_POLICY": "Privacy Policy"}, "TOASTR": {"TITLE": {"SUCCESS": "Success", "ERROR": "Error", "INFO": "Info", "WARNING": "Warning", "MAX_LIMIT_REACHED": "Max limit reached"}, "MESSAGE": {"ERRORS": "Please check the form for errors", "PROJECT_LOAD": "Could Not Load Projects", "COPIED": "Link copied to clipboard", "INVITES_LOAD": "Could Not Load Invites", "INVITES_RESEND": "In<PERSON><PERSON> has been resent to '{{ email }}'.", "INVITES_DELETE": "'{{ email }}' was removed", "EMPLOYEE_DEPARTMENT_ADDED": "Employee added to department", "EMPLOYEE_DEPARTMENT_REMOVED": "Employee removed from department", "EMPLOYEE_PROJECT_ADDED": "Employee added to project", "EMPLOYEE_PROJECT_REMOVED": "Employee removed from project", "EMPLOYEE_CLIENT_ADDED": "Employee added to the client", "EMPLOYEE_CLIENT_REMOVED": "Employee removed from the client", "EMPLOYEE_EDIT_ERROR": "Error in editing employee", "EMPLOYEE_PROFILE_UPDATE": "Profile '{{name}}' was changed", "EMPLOYEE_LEVEL_UPDATE": "Employee Level '{{ name }}' was changed", "EMPLOYEE_ADDED": "Employee '{{name}}' added to '{{organization}}'", "EMPLOYEE_INACTIVE": "Employee '{{name}}' set as inactive.", "EMPLOYEE_ACTIVE": "Employee '{{name}}' set as active.", "EMPLOYEE_JOB_STATUS_ACTIVE": "Employee '{{name}}' job status set as active.", "EMPLOYEE_JOB_STATUS_INACTIVE": "Employee '{{name}}' job status set as inactive.", "EMPLOYEE_TIME_TRACKING_ENABLED": "Enabled time tracking for '{{name}}'.", "EMPLOYEE_TIME_TRACKING_DISABLED": "Disabled time tracking for '{{name}}'.", "CONFIRM": "אשר", "ARE_YOU_SURE_YOU_WANT_TO_RESEND_THE_INVITE_TO": "Are you sure you want to resend the invite to", "OK": "OK", "CANCEL": "בטל", "ARE_YOU_SURE_YOU_WANT_TO_CHANGE_THE": "Are you sure you want to change the", "PERMISSION_UPDATED": "{{ permissionName }} permission updated for {{ roleName }}", "ROLE_CREATED": "{{ name }} successfully created", "ROLE_DELETED": "{{ name }} successfully deleted", "ROLE_CREATED_ERROR": "Error while creating {{ name }} role", "ROLE_DELETED_ERROR": "Error while deleting {{ name }} role", "PERMISSION_UPDATE_ERROR": "There was an error in updating the permissions, please refresh & try again", "REGISTER_PROPOSAL_NO_EMPLOYEE_MSG": "Employee is required!", "NEW_ORGANIZATION_PROJECT_INVALID_NAME": "Invalid input", "NEW_ORGANIZATION_TEAM_INVALID_NAME": "Team name and members are required", "NEW_ORGANIZATION_VENDOR_INVALID_NAME": "Vendor name is required", "NEW_ORGANIZATION_EXPENSE_CATEGORY_INVALID_NAME": "Expense category name is required", "NEW_ORGANIZATION_POSITION_INVALID_NAME": "Position name is required", "NEW_ORGANIZATION_EMPLOYEE_LEVEL_INVALID_NAME": "Employee Level is required", "NEW_ORGANIZATION_INVALID_EMPLOYMENT_TYPE": "Employment type name is required", "NEW_ORGANIZATION_DEPARTMENT_INVALID_NAME": "Department name is required", "FAVORITE_ADDED": "{{ name }} נוסף למועדפים", "FAVORITE_REMOVED": "{{ name }} הוסר מהמועד<PERSON>ים", "FAVORITE_ERROR": "שגיאה בעדכון סטטוס המועדפים", "NEW_ORGANIZATION_CLIENT_INVALID_DATA": "Invalid input", "NEW_ORGANIZATION_AWARD_INVALID_NAME": "Vendor name and year are required", "NEW_ORGANIZATION_LANGUAGE_INVALID_NAME": "Language name and level are required", "MAIN_ORGANIZATION_UPDATED": "'{{ name }}' info was changed", "CANDIDATE_SKILL_REQUIRED": "Skill name is required", "CANDIDATE_EDIT_CREATED": "Successfully Created", "CANDIDATE_EDIT_UPDATED": "Successfully updated", "CANDIDATE_EDIT_DELETED": "Successfully deleted", "CANDIDATE_EDUCATION_REQUIRED": "Education information is required", "CANDIDATE_FEEDBACK_REQUIRED": "Feedback information is required", "CANDIDATE_FEEDBACK_ABILITY": "All interviewers have already left feedback", "CANDIDATE_EXPERIENCE_REQUIRED": "Experience information is required", "CANDIDATE_DOCUMENT_REQUIRED": "Document information is required", "CANDIDATE_SKILLS_REQUIRED": "<PERSON><PERSON>'s name is required", "CANDIDATE_PROFILE_UPDATE": "Profile '{{name}}' was changed.", "NAME_REQUIRED": "Name is required!", "CONTACT_TYPE_REQUIRED": "Contact Type is required!", "EMAIL_REQUIRED": "Email is required!", "EMAIL_SHOULD_BE_REAL": "Email should be a real one!", "PHONE_REQUIRED": "Phone is required!", "PHONE_CONTAINS_ONLY_NUMBERS": "Phone should only contain numbers!", "SOMETHING_BAD_HAPPENED": "Something bad happened!", "EMAIL_TEMPLATE_SAVED": "Email template '{{ templateName }}' was saved", "DELETED": "Successfully deleted", "CREATED": "Successfully created", "UPDATED": "Successfully updated", "MOVED_BASE": "Base was moved", "MOVED_CATEGORY": "Category was moved", "CREATED_BASE": "Base '{{ name }}' was added", "EDITED_BASE": "Base '{{ name }}' was changed", "DELETED_BASE": "Base '{{ name }}' was removed", "DELETED_CATEGORY": "Category '{{ name }}' was removed", "CREATED_CATEGORY": "Category was added", "EDIT_ADD_CATEGORY": "Category '{{ name }}' was added", "EDITED_CATEGORY": "Category '{{ name }}' was changed", "HELP_ARTICLE_CREATED": "Article was added", "HELP_ARTICLE_UPDATED": "Article '{{ name }}' was changed", "HELP_ARTICLE_DELETED": "Article '{{ name }}' was removed", "PIPELINE_CREATED": "Pipeline '{{ name }}' was added", "PIPELINE_UPDATED": "Pipeline '{{ name }}' was changed", "PIPELINE_DELETED": "Pipeline '{{ name }}' was removed", "OBJECTIVE_ADDED": "Objective was added", "OBJECTIVE_DELETED": "Objective was removed", "OBJECTIVE_UPDATED": "Objective was changed", "KEY_RESULT_ADDED": "Key Result was added", "KEY_RESULT_DELETED": "Key Result was removed", "KEY_RESULT_UPDATED": "Key Result was changed", "TIME_FRAME_CREATED": "Time frame '{{ name }}' was added", "TIME_FRAME_UPDATED": "Time frame '{{ name }}' was changed", "TIME_FRAME_DELETED": "Time frame '{{ name }}' was removed", "KPI_CREATED": "KPI was added", "KPI_UPDATED": "KPI was changed", "KPI_DELETED": "KPI was removed", "EDIT_PAST_INTERVIEW": "Editing past interviews is prohibited", "ARCHIVE_INTERVIEW": "This interview has already been archived", "DELETE_PAST_INTERVIEW": "Deleting past interviews is prohibited", "GOAL_GENERAL_SETTING_UPDATED": "Goal General settings updated", "MAX_OBJECTIVE_LIMIT": "You cannot create any more Objectives. Please change maximum objective limit to add more objectives.", "MAX_KEY_RESULT_LIMIT": "You cannot create any more Key Results for this Objective. Please change maximum key result limit to add more Key Results.", "CUSTOM_SMTP_ADDED": "Smtp settings successfully created", "CUSTOM_SMTP_UPDATED": "Smtp settings successfully updated", "JOB_MATCHING_SAVED": "Criterion was changed", "JOB_MATCHING_ERROR": "Error while saving criterion, Please try aging", "JOB_MATCHING_DELETED": "Criterion was removed", "APPROVAL_POLICY_CREATED": "Approval policy '{{ name }}' was added", "APPROVAL_POLICY_UPDATED": "Approval policy '{{ name }}' was changed", "APPROVAL_POLICY_DELETED": "Approval policy '{{ name }}' was removed", "APPROVAL_POLICY_ALREADY_EXISTS": "מדיניות האישור '{{ name }}' כ<PERSON>ר קיימת", "CANDIDATE_CREATED": "Candidate '{{ name }}' was added to '{{ organization }}'", "CANDIDATE_ARCHIVED": "Candidate '{{ name }}' set as archived.", "CANDIDATE_REJECTED": "Candidate '{{ name }}' set as rejected.", "CANDIDATE_HIRED": "Candidate '{{ name }}' set as hired.", "CANDIDATE_DELETED": "Candidate '{{ name }}' was removed", "PRESET_SAVED": "Preset successfully saved", "JOB_APPLIED": "Job applied successfully", "JOB_HIDDEN": "Job hidden successfully", "ORGANIZATION_LOCATION_UPDATED": "'{{ name }}' organization location was changed", "ORGANIZATION_INFO_UPDATED": "'{{ name }}' organization main info was changed", "ORGANIZATION_SETTINGS_UPDATED": "'{{ name }}' organization settings was changed", "SETTINGS_SAVED": "Setting<PERSON> saved successfully", "KEY_RESULTS_CREATED": "Key Results Created", "INVITE_EMAIL_DELETED": "{{ name }} has been deleted.", "HOLIDAY_ERROR": "Unable to get holidays", "INTERVAL_ERROR": "Please pick correct dates and try again", "PROFILE_UPDATED": "Your profile was changed", "PERSONAL_QUALITIES_CREATED": "Personal Qualities '{{ name }}' was added", "PERSONAL_QUALITIES_UPDATED": "Personal Qualities '{{ name }}' was changed", "PERSONAL_QUALITIES_DELETED": "Personal Qualities '{{ name }}' was removed", "TECHNOLOGY_STACK_CREATED": "Technology Stack '{{ name }}' was added", "TECHNOLOGY_STACK_UPDATED": "Technology Stack '{{ name }}' was changed", "TECHNOLOGY_STACK_DELETED": "Technology Stack '{{ name }}' was removed", "ARCHIVE_INTERVIEW_SET": "'{{ name }}' set as archived.", "INTERVIEW_UPDATED": "'{{ name }}' was changed", "INTERVIEW_DELETED": "'{{ name }}' was removed", "INTERVIEW_FEEDBACK_CREATED": "Feedback was added for '{{ name }}'.", "CANDIDATE_EDUCATION_CREATED": "Education '{{ name }}' was added", "CANDIDATE_EDUCATION_UPDATED": "Education '{{ name }}' was changed", "CANDIDATE_EDUCATION_DELETED": "Education '{{ name }}' was removed", "CANDIDATE_EXPERIENCE_CREATED": "Experience '{{ name }}' was added", "CANDIDATE_EXPERIENCE_UPDATED": "Experience '{{ name }}' was changed", "CANDIDATE_EXPERIENCE_DELETED": "Experience '{{ name }}' was removed", "CANDIDATE_SKILL_CREATED": "Skill '{{ name }}' was added", "CANDIDATE_SKILL_UPDATED": "Skill '{{ name }}' was changed", "CANDIDATE_SKILL_DELETED": "Skill '{{ name }}' was removed", "CANDIDATE_DOCUMENT_CREATED": "Document '{{ name }}' was added", "CANDIDATE_DOCUMENT_UPDATED": "Document '{{ name }}' was changed", "CANDIDATE_DOCUMENT_DELETED": "Document '{{ name }}' was removed", "CANDIDATE_INTERVIEW_CREATED": "Interview '{{ name }}' was added", "CANDIDATE_INTERVIEW_UPDATED": "Interview '{{ name }}' was changed", "CANDIDATE_INTERVIEW_DELETED": "Interview '{{ name }}' was removed", "CANDIDATE_FEEDBACK_CREATED": "Feedback was added", "CANDIDATE_FEEDBACK_UPDATED": "Feedback was changed", "CANDIDATE_FEEDBACK_DELETED": "Feedback was removed", "RECURRING_EXPENSE_SET": "Recurring expense set for '{{ name }}'", "RECURRING_EXPENSE_UPDATED": "Recurring expense was changed for '{{ name }}'", "RECURRING_EXPENSE_DELETED": "Recurring expense was removed for '{{ name }}'", "IMAGE_UPDATED": "The image has been updated", "ORGANIZATION_PAGE_UPDATED": "Page was changed for '{{ name }}'", "SCREENSHOT_DELETED": "Screenshot for '{{ name }}' removed from '{{ organization }}'", "TIME_LOG_DELETED": "Time log for '{{ name }}' removed from '{{ organization }}'", "TIME_LOGS_DELETED": "Time logs removed from '{{ organization }}'", "BUCKET_CREATED": "'{{ bucket }}' for '{{ region }}' has been created successfully", "AUTHORIZED_TO_WORK": "{{ name }} is authorized to work", "MODULE_CREATED": "המודול נוצר בהצלחה!", "MODULE_UPDATED": "המודול עודכן בהצלחה!", "MODULE_SAVE_ERROR": "שמירת המודול נכשלה. אנא נסה שוב.", "PASSWORD_REQUIRED": "נדרשת סיסמה."}}, "ACCEPT_INVITE": {"ACCEPT_INVITE_FORM": {"FULL_NAME": "שם מלא", "ENTER_YOUR_FULL_NAME": "Enter Your Full Name", "PASSWORD": "סיסמה", "REPEAT_PASSWORD": "Repeat Password", "AGREE_TO": "Agree to", "TERMS_AND_CONDITIONS": "Terms & Conditions", "ADD_ORGANIZATION": "Add Organization", "COMPLETE_REGISTRATION": "Complete Registration", "PASSWORDS_DO_NOT_MATCH": "Password Do Not Match!"}, "INVALID_INVITE": "Either you entered an incorrect URL or the invitation has expired", "HEADING": "Accept Invitation to {{ organizationName }}", "SUB_HEADING": "Complete your registration {{ email }}", "INVITATION_NO_LONGER_VALID": "This invitation is no longer valid", "ACCOUNT_CREATED": "Your account has been created, please login", "COULD_NOT_CREATE_ACCOUNT": "Could not create your account"}, "NOTES": {"INCOME": {"ADD_INCOME": "Income was added for '{{ name }}'", "EDIT_INCOME": "Income was changed for '{{ name }}'", "DELETE_INCOME": "Income was removed for '{{ name }}'", "INCOME_ERROR": "{{ error }}"}, "INVOICE": {"ADD_INVOICE": "Invoice added for '{{ name }}'", "EDIT_INVOICE": "Invoice edited for '{{ name }}'", "DELETE_INVOICE": "Invoice deleted for '{{ name }}'", "INVOICE_ERROR": "{{ error }}"}, "EXPENSES": {"ADD_EXPENSE": "Expense added for '{{ name }}'", "OPEN_EDIT_EXPENSE_DIALOG": "Expense edited for '{{ name }}'", "DELETE_EXPENSE": "Expense deleted for '{{ name }}'", "EXPENSES_ERROR": "{{ error }}"}, "PROPOSALS": {"EDIT_PROPOSAL": "Proposal successfully updated", "REGISTER_PROPOSAL": "New proposal was added", "REGISTER_PROPOSAL_NO_EMPLOYEE_SELECTED": "Please select an employee from the dropdown menu.", "REGISTER_PROPOSAL_ERROR": "{{ error }}", "DELETE_PROPOSAL": "Proposal was removed", "PROPOSAL_ACCEPTED": "Proposal status updated to Accepted", "PROPOSAL_SENT": "Proposal status updated to Sen<PERSON>"}, "POLICY": {"ADD_POLICY": "Time off policy '{{ name }}' was added", "EDIT_POLICY": "Time off policy '{{ name }}' was changed", "DELETE_POLICY": "Time off policy '{{ name }}' was removed", "ERROR": "{{ error }}", "SAVE_ERROR": "Unable to create Policy record", "POLICY_EXISTS": "מדיניות חופשה '{{ name }}' כבר קיימת"}, "USER": {"EDIT_PROFILE": "Your profile has been updated successfully."}, "CANDIDATE": {"INVALID_FORM": "Please fill the form", "INVALID_FEEDBACK_INFO": "Please add feedback information", "EXPERIENCE": {"INVALID_CANDIDATE_NAME": "Please add a Skill name", "INVALID_FORM": "Please fill the form", "INVALID_FIELD": "Please fill the field", "ERROR": "{{ error }}"}}, "EMPLOYEE": {"EDIT_EMPLOYEE_AWARDS": {"ADD_AWARD": "New award '{{ name }}' was added", "INVALID_AWARD_NAME_YEAR": "Please check the Name and Year for Award input", "REMOVE_AWARD": "Award '{{ name }}' was removed"}, "END_WORK": {"DATE_CONFLICT": "תאריך הסיום ({{ endDate }}) לא יכול להיות מוקדם מתאריך ההתחלה ({{ startDate }})."}}, "ORGANIZATIONS": {"ADD_NEW_ORGANIZATION": "Organization '{{ name }}' was added", "DELETE_ORGANIZATION": "Organization '{{ name }}' was removed", "ADD_NEW_USER_TO_ORGANIZATION": "'{{ username }}' was added to '{{ orgname }}'", "DELETE_USER_FROM_ORGANIZATION": "'{{ username }}' set as inactive.", "DATA_ERROR": "{{ error }}", "EDIT_ORGANIZATIONS_PROJECTS": {"ADD_PROJECT": "Project '{{ name }}' was saved", "REMOVE_PROJECT": "Project '{{ name }}' was removed", "INVALID_PROJECT_NAME": "Please fill in the name of your project", "VISIBILITY": "Now project is {{ name }} "}, "EDIT_ORGANIZATIONS_TEAM": {"ADD_NEW_TEAM": "Team '{{ name }}' was added", "EDIT_EXISTING_TEAM": "Team '{{ name }}' was changed", "INVALID_TEAM_NAME": "Please add a Team name and at least one member", "REMOVE_TEAM": "Team '{{ name }}' was removed"}, "EDIT_ORGANIZATIONS_EMPLOYEE_LEVELS": {"ADD_EMPLOYEE_LEVEL": "Employee Level '{{ name }}' was added", "REMOVE_EMPLOYEE_LEVEL": "Employee Level '{{ name }}' was removed", "INVALID_EMPLOYEE_LEVEL": "Please add an Employee Level", "ALREADY_EXISTS": "רמת העובד  כבר קיימת."}, "EDIT_ORGANIZATIONS_VENDOR": {"ADD_VENDOR": "vendor '{{ name }}' was added", "UPDATE_VENDOR": "Vendor '{{ name }}' was changed", "REMOVE_VENDOR": "Vendor '{{ name }}' was removed", "INVALID_VENDOR_NAME": "Please add a <PERSON><PERSON><PERSON> name"}, "EDIT_ORGANIZATIONS_EXPENSE_CATEGORIES": {"ADD_EXPENSE_CATEGORY": "Category '{{ name }}' was added", "UPDATE_EXPENSE_CATEGORY": "Category '{{ name }}' was changed", "REMOVE_EXPENSE_CATEGORY": "Category '{{ name }}' was removed", "INVALID_EXPENSE_CATEGORY_NAME": "Please add a Category name"}, "EDIT_ORGANIZATIONS_POSITIONS": {"ADD_POSITION": "Position '{{ name }}' was added", "UPDATED_POSITION": "Position '{{ name }}' was changed", "REMOVE_POSITION": "Position '{{ name }}' was removed", "INVALID_POSITION_NAME": "Please add a Position name", "ALREADY_EXISTS": "המשרה  כבר קיימת."}, "EDIT_ORGANIZATIONS_DEPARTMENTS": {"ADD_DEPARTMENT": "Department '{{ name }}' was saved", "REMOVE_DEPARTMENT": "Department '{{ name }}' was removed", "INVALID_DEPARTMENT_NAME": "Please add a Department name", "ALREADY_EXISTS": "המחלקה  כבר קיימת."}, "EDIT_ORGANIZATIONS_CLIENTS": {"ADD_CLIENT": "New client '{{ name }}' successfully added!", "REMOVE_CLIENT": "Client '{{ name }}' successfully removed!", "INVALID_CLIENT_DATA": "Please check the Name, Primary Email and Primary Phone of your client", "INVITE_CLIENT": "Invitation email sent to '{{ name }}'.", "INVITE_CLIENT_ERROR": "Some error occurred while trying to invite client.", "EMAIL_EXISTS": "This client email already exists as a user"}, "EDIT_ORGANIZATIONS_CONTACTS": {"ADD_CONTACT": "Contact '{{ name }}' was added", "UPDATE_CONTACT": "Contact '{{ name }}' was changed", "REMOVE_CONTACT": "Contact '{{ name }}' was removed", "INVALID_CONTACT_DATA": "Please check the Name, Primary Email and Primary Phone of your contact", "INVITE_CONTACT": "Invitation email sent to '{{ name }}'.", "INVITE_CONTACT_ERROR": "Some error occurred while trying to invite contact.", "EMAIL_EXISTS": "This contact email already exists as a user"}, "EDIT_ORGANIZATIONS_EMPLOYMENT_TYPES": {"ADD_EMPLOYMENT_TYPE": "Employment type '{{ name }}' was added", "UPDATE_EMPLOYMENT_TYPE": "Employment type '{{ name }}' was changed", "INVALID_EMPLOYMENT_TYPE": "Please check the Name for Employment type input", "DELETE_EMPLOYMENT_TYPE": "Employment type '{{ name }}' was removed", "ALREADY_EXISTS": "סוג התעסוקה הזה כבר קיים."}, "EDIT_ORGANIZATIONS_RECURRING_EXPENSES": {"ADD_RECURRING_EXPENSE": "Recurring expense was added for `{{ name }}`", "UPDATE_RECURRING_EXPENSE": "Recurring expense was changed for '{{ name }}'", "DELETE_RECURRING_EXPENSE": "Recurring expense was removed"}, "EDIT_ORGANIZATIONS_AWARDS": {"ADD_AWARD": "New award '{{ name }}' was added", "INVALID_AWARD_NAME_YEAR": "Please check the Name and Year for Award input", "REMOVE_AWARD": "Award '{{ name }}' was removed"}, "EDIT_ORGANIZATIONS_LANGUAGES": {"ADD_LANGUAGE": "New language '{{ name }}' was added", "INVALID_LANGUAGE_NAME_LEVEL": "Please check the Name and Level for Language input", "REMOVE_LANGUAGE": "Language '{{ name }}' was removed"}, "EDIT_ORGANIZATION_DOCS": {"CREATED": "Document '{{ name }}' was added", "ERR_CREATE": "Unable to create new organization document", "ERR_LOAD": "Unable to load organization documents", "UPDATED": "Document '{{ name }}' was changed", "ERR_UPDATED": "Unable to update the selected document!", "SELECTED_DOC": "selected document", "DELETED": "Document '{{ name }}' was removed", "ERR_DELETED": "Unable to delete the selected document!"}}, "DANGER_ZONE": {"WRONG_INPUT_DATA": "Wrong input! Please try again.", "ACCOUNT_DELETED": "Your account was deleted successfully!", "ALL_DATA_DELETED": "Your data was deleted successfully!", "RECORD_TYPE": "If Yes, please type '{{ type }}' to confirm.", "TITLES": {"ACCOUNT": "REMOVE ACCOUNT", "ALL_DATA": "REMOVE ALL DATA"}}, "EVENT_TYPES": {"ADD_EVENT_TYPE": "Event type '{{ name }}' was added", "EDIT_EVENT_TYPE": "Event type '{{ name }}' was changed", "DELETE_EVENT_TYPE": "Event type '{{ name }}' was removed", "ERROR": "{{ error }}"}, "AVAILABILITY_SLOTS": {"SAVE": "Availability Slots saved", "ERROR": "{{ error }}"}}, "TIMER_TRACKER": {"IS_BILLABLE": "נית<PERSON> לחיוב", "STOP_TIMER": "עצור טיימר", "START_TIMER": "התחל טיימר", "TIMER": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MANUAL": "ידני", "MANUAL_NOT_ALLOW": "אין אפשרות לשימוש בזמן ידני", "SELECT_PROJECT": "<PERSON><PERSON><PERSON> פרוי<PERSON>ט", "SELECT_TASK": "<PERSON><PERSON><PERSON> משימה", "SELECT_CLIENT": "<PERSON><PERSON><PERSON> לקוח", "SELECT_TEAM": "<PERSON><PERSON><PERSON>ה", "DATE": "תאריך", "START_TIME": "שעת התחלה", "END_TIME": "שעת סיום", "DESCRIPTION": "תיאור", "ADD_TIME_SUCCESS": "הזמן נוסף בהצלחה", "VALIDATION": {"CLIENT_REQUIRED": "יש לבחור לקוח", "PROJECT_REQUIRED": "יש לבחור פרויקט", "TASK_REQUIRED": "יש לבחור משימה", "DESCRIPTION_REQUIRED": "יש למלא תיאור"}, "VIEW_TIMESHEET": "צ<PERSON>ה בגליון זמנים", "ADD_TIME": "הוסף זמן", "TODAY": "היום", "STATUS": "ג<PERSON><PERSON> הזמן כבר פועל ב- {{ source }}", "VERSION": "גרסה v{{ version }}", "WAIT_FOR": "ממתין ש-{{ name }} יתחיל...", "SETUP": {"WELCOME": "ברוכים הבאים", "TITLE": "ברוכים הבאים ל־Ever® Gauzy™ - פלטפורמת ניהול העסק (ERP/CRM/HRM) הפתוחה-מקור", "LABEL": "יישום לשולחן העבודה Gauzy מספק את כל הפונקציות המלאות של פלטפורמת Gauzy, הזמינה ישירות במחשב שולחן העבודה או במחשב נייד. בנוסף, התוכנה מאפשרת מעקב אחר זמן העבודה, הקלטת פעילות ואת היכולת לקבל תזכורות/הודעות בנושא מעקב.", "TITLE_SERVER": "אשף התקנת שרת Gauzy", "LABEL_SERVER": "יישום לשולחן העבודה Gauzy מספק את כל הפונקציות המלאות של פלטפורמת Gauzy, הזמינה ישירות במחשב שולחן העבודה או במחשב נייד. בנוסף, התוכנה מאפשרת מעקב אחר זמן העבודה, הקלטת פעילות ואת היכולת לקבל תזכורות/הודעות בנושא מעקב.", "FUNCTIONALITIES": "פונקציות", "WHAT_WOULD_LIKE_USE": "מה תרצה להשתמש בו?", "SELECT_MULTIPLE_OPTIONS": "תוכל לבחור רק אפשרות אחת או שתיים.", "TIME_ACTIVITY_FEATURES": "תכונות מעקב אחר זמן ופעילות", "GP_FEATURES": "תכונות פלטפורמת Gauzy", "HOW_GONNA_USE_GP": "איך תשתמש בפלטפורמה של Gauzy?", "SELECT_ONE_OPTION": "עליך לבחור רק אחת מהאפשרויות הבאות.", "INTEGRATED": "משולב", "INSTALL_GP_LOCAL": "הת<PERSON>ן והפעל את פלטפורמת Gau<PERSON> במחשב המקומי שלך.", "CUSTOM_NETWORK": "מותאם אישית / מרשת", "SEPARATE_SERVER": "התחבר לשרת נפרד ברשת העבודה שלך.", "LIVE": "חיים", "CONNECTED_LIVE_SERVER": "מחובר לשרת החיים שלנו ושימוש בו כשירות", "GONNA_USE_3RD_PARTY": "האם אתה צריך שילובים מצד שלישי?", "SELECT_MULTIPLE_OPTIONS_3RD_PARTY": "זו אפשרות, תו<PERSON><PERSON> לבחור אחת או שתי סוגי שילובים מצד שלישי.", "LEGAL_NOTE": "משפטי: שמות המוצרים, שמות המותגים, הלוגואים, הסימנים המסחריים והסימנים המוגנים בזכות יוצרים הינם רכושם הבלעדי של בעלי הזכויות המתאימים. אין לנו קשר, אישור, שיתוף פעולה או תמיכה מרשתות החברות, מותגים, מוצרים, אתרים או שירותים של צד ג' שהזכויות משמעו מוגנות. קישורים לתוכנה מצד שלישי נמסרים בכדי להסיק נוחות ולמרכז ידע בלבד, ואין בהם המלצה או אישור של כל מוצר, שירות או דעה של החברה או הארגון או היחיד/ה. אנו משתמשים בלוגואים ושמות מותגים המתאימים רק למטרה פרסומתית, על מנת לכבד שימוש ועבודה עם מוצרים תואמים או טכנולוגיות תואמות או השילוב המובנה בין שירותים תואמים.", "SETTING": "הגדרה", "READY_FOR_ADVANCED_SETTINGS": "האם אתה מוכן להגדרות מתקדמות?", "FINAL_STEP_GP": "זהו השלב הסופי בו תוכל להתעמק בפרטים הקטנים של הפלטפורמה Gauzy שלך.", "PORT": "פורט", "PORT_API": "פורט API", "UI": "ממש<PERSON> משתמש", "API_HOST": "מארח API", "HOST": "מאר<PERSON>", "DB_NAME": "שם מסד נתונים", "DB_PORT": "פורט מסד נתונים", "USER": "משת<PERSON>ש", "PASSWORD": "סיסמה", "UI_HOSTNAME": "שם מארח יישום ממשק משתמש / כתובת IP", "TITLE_SERVER_API": "אשף התקנת שרת API של Ever Gauzy", "LABEL_SERVER_API": "Ever Gauzy API Server Desktop App מספקת את הפונקציונליות המלאה של פלטפורמת Gauzy הזמינה ישירות במחשב השולחני או במחשב הנייד שלך. בנוסף, הוא מאפשר מעקב אחר זמן עבודה, רישום פעילות ויכולת לקבל תזכורות/הודעות מעקב.", "UNABLE_TO_CONNECT": "Unable to connect to the server. Please check your API host or internet connection."}, "SETTINGS": {"SCREEN_CAPTURE": "לכידת מסך", "MONITOR": "מסך", "AUTOMATIC_SCREEN_CAPTURE": "לכידת מסך אוטומטית", "NOTIFICATION_SETTINGS": "הגדרות התראות", "DESKTOP_NOTIFICATIONS": "התראות שולחן העבודה", "SHOW_DESKTOP_NOTIF_SCREEN_CAPTURE": "הצג התראה בשולחן העבודה על ידי לכידת מסך", "NOTIFICATION": "התראה", "SIMPLE_NOTIF": "הודעה פשוטה", "DETAILED_NOTIF": "הודעה מפורטת", "SHOW_NOTIF_CAPTURED_IMG": "הצג התראה עם תמונה שנלכדה", "SHOW_NATIVE_OS_NOTIF": "הצג התראה מערכת הפעלה טבעית", "SOUND_NOTIF": "התראות קול", "PLAY_SOUND": "השמע קול בלכידת מסך", "SOUND_ENABLED": "קול מופעל", "SOUND_DISABLED": "קול מנוטרל", "UPDATE_ACTIVITIES": "עדכון פעילויות או לכידת מסך כל", "RANDOM_SCREENSHOT_TIME": "ז<PERSON><PERSON> לכידת מסך אקראית", "TRACK_TIME_PC_LOCKED": "מעקב א<PERSON>ר ז<PERSON>ן כאשר המחשב נעול", "KEEP_SYSTEM_ACTIVE": "שימור פעילות המערכת והמסך", "PREVENT_DISPLAY_GOING_SLEEP": "מניעת כבוי מסך", "PREVENT_DISPLAY_SLEEP": "מניעת שינה של המסך", "UPDATE": "עדכון", "AUTOMATIC_UPDATE_CHECK": "בדיקת עדכון אוטומטית", "ENABLE_AUTOMATIC_UPDATE_LABEL": "אפ<PERSON>ר בדיקת עדכונים אוטומטית, כדי להפעיל בקשה לבדיקת קיום גרסה חדשה ולהתריע", "SET_UPDATE_INTERVAL_DURATION": "קביעת תדירות בדיקת עדכון", "SELECT_DELAY": "בחירת השהייה", "ENABLE_AUTOMATIC_UPDATE": "א<PERSON><PERSON><PERSON> עד<PERSON><PERSON><PERSON> אוטומטי", "UPDATE_SERVER": "שרת עדכון", "SELECT_DEFAULT_CDN": "בחירת שרת CDN בררת מחדל לעדכון", "TOGGLE_UPDATE_LOCALLY": "החלפה לתהליך עדכון מקומי", "LOCAL_SERVER": "שרת מקומי", "LOCAL_SERVER_NOTE": "הספרייה שנבחרה צריכה להכיל לפחות את המדף <strong>latest.yml</strong> ו/או <strong>latest-mac.yml</strong> כדי לבצע אימות עדכונים", "OTHER_SETTINGS": "הגדרות נוספות", "ALLOW_PRERELEASE_VERSIONS": "אפשר גרסאות גרסה חשמלית", "CHECK_UPDATE_APP_VERSION": "בדיקת ועדכון גרסת האפליקציה", "UPDATE_DOWNLOADED_NOTE": "עדכון חדש כבר הורד! לחץ על כפתור עדכון עכשיו למטה.", "UPDATE_AVAILABLE_NOTE": "עדכון חדש זמין! לחץ על כפתור הורד עכשיו למטה.", "CHECK_UPDATE_NOTE": "ניתן לבדוק לעדכונים על ידי לחיצה על כפתור בדיקת עדכון למטה.", "UPDATE_LOGS": "יומני עדכונים", "ADVANCED_SETTINGS": "הגדרות מתקדמות", "WARNING_STOP_TIMER": "אנא עצור את השעון אם ברצונך לשנות הגדרות", "GENERAL": "כללי", "API_CONFIG": "תצורת API", "SERVER_ACCESS_CONFIG": "תצורת גישה לשרת", "API_SERVER_PORT": "יציאת שרת API", "UI_SERVER_PORT": "יציאת שרת ממשק משתמש", "SERVER_HOSTNAME": "שם מארח/כתובת IP של השרת", "AUTO_START_STARTUP": "הפעלה אוטומטית בהתחלה", "SERVER_TYPE": "סוג שרת", "LOCAL_API_PORT": "יציאת API מקומית", "UI_PORT": "יציאת ממשק משתמש", "SERVER_URL": "כתובת URL של השרת", "DB_CONFIG": "תצורת מסד נתונים", "DB_DRIVER": "נהג מסד הנתונים", "STARTUP_CONFIG": "תצורת הפעלה", "AUTOMATIC_LAUNCH": "הפעלה אוטומטית", "MIN_ON_STARTUP": "מזער על התחלתה של התוכנה", "3RD_PARTY": "צ<PERSON> שלישי", "3RD_PARTY_CONFIG": "תצורת צד שלישי", "AW_PORT": "יציאת Activity Watch", "VISIBLE_AW": "אפשרות Activity Watch גלויה בחלון", "VISIBLE_WAKATIME": "אפשרות Wakatime גלויה בחלון", "SIGN_IN_AS": "מ<PERSON><PERSON><PERSON><PERSON> כ{{ name }} ({{ email }})", "SIGN_OUT": "התנתק", "DB_PASSWORD": "סיסמת מסד הנתונים", "DB_USERNAME": "שם משתמש במסד הנתונים", "DB_HOST": "מארח מסד הנתונים", "CAPTURE_ALL_MONITORS": "לכידת כל המסכים", "ALL_CONNECTED_MONITORS": "כל המסכים המחוברים", "MONITOR_CURRENT_POSITION": "מקום הסמן הנוכחי במסך", "CAPTURE_ACTIVE_MONITOR": "לכידת מסך פעיל", "MESSAGES": {"APP_UPDATE": "עדכ<PERSON>ן אפליקציה", "UPDATE_NOT_AVAILABLE": "ע<PERSON><PERSON><PERSON><PERSON> אינו זמין", "UPDATE_ERROR": "שגי<PERSON><PERSON> עדכון", "UPDATE_AVAILABLE": "ע<PERSON><PERSON><PERSON><PERSON> זמין", "UPDATE_DOWNLOAD_COMPLETED": "הורדת עדכון הושלמה", "UPDATE_DOWNLOADING": "הורדת עדכון בתהליך", "SERVER_CONFIG_UPDATED": "קונפיגורצית השרת עודכנה, אנא המתן לאיחוד השרת", "SERVER_RESTARTED": "השרת הופעל מחדש בהצלחה", "CONNECTION_SUCCEEDS": "התחברות לשרת {{ url }} הצליחה"}, "TIMEZONE": "א<PERSON><PERSON><PERSON>", "TIMEZONE_LABEL": "בחירת 'מקומי' מציגה את השעה באזור הזמן המקומי שלך, בעוד בחירת 'UTC' מציגה את השעה ב-UTC האוניברסלי, שמשמש את מערכות החיוב והמערכות האחרות.", "TIMEZONE_PLACEHOLDER": "בחר בין 'מקומי' או 'UTC'.", "TIMEZONE_LOCAL": "מק<PERSON><PERSON>י", "TIMEZONE_UTC": "UTC", "WIDGET": "ווידג'ט", "PLUGINS": "תוספים", "PLUGIN_DESCRIPTION": "תוספים", "VERSION": "גרסה", "ADD_PLUGIN_INSTALLATION_DESCRIPTION": "התקן תוספים מקבצים מקומיים או CDN. מקומי עבור תוספים מותאמים אישית/פרטיים, CDN עבור ציבוריים. בחר לפי הצרכים שלך.", "ADD_PLUGIN_CDN_DESCRIPTION": "התקנה מ-CDN מאפשרת גישה קלה לתוספים זמינים לציבור.", "PLUGIN": "תוסף", "WIDGET_LABEL": "החלון משקף את מצב הטיימר, מציין האם הוא פועל או עצור, ופועל ככפתור.", "SSL": {"SSL_CONFIG": "תצורת SSL", "USE_SSL": "השתמש ב-SSL", "USE_SSL_INFO": "מפעיל הצפנה SSL.", "SSL_KEY_FILE": "קובץ מפתח SSL", "SSL_KEY_FILE_INFO": "נתיב לקובץ המפתח של הלקוח עבור SSL.", "SSL_CERT_FILE": "קובץ תעודת SSL", "SSL_CERT_FILE_INFO": "נתיב לקובץ תעודת הלקוח עבור SSL.", "SECURE": "מאובטח", "SECURE_INFO": "נכון, אם ברצונך לאמת את תעודות ה-SSL."}, "ADD_PLUGIN_NPM_DESCRIPTION": "התקנת NPM מאפשרת גישה לתוספים זמינים לציבור ופרטיים.", "PACKAGE_NAME": "שם חבילת NPM", "PACKAGE_VERSION": "גרסת חבילת NPM", "REGISTRY": "מרשם NPM", "AUTH_TOKEN": "אסימון אימות", "PRIVATE_REGISTRY_URL": "כתובת URL של מרשם פרטי", "PLUGIN_INSTALL_CDN_ERROR": "כתובת ה-<PERSON><PERSON> של השרת לא יכולה להיות ריקה.", "TRACK_KEYBOARD_MOUSE": "מעק<PERSON> קלט מדויק"}, "WEEKLY_LIMIT_EXCEEDED": "הגעת למגבלת השבוע", "OF_HRS": "מתוך {{ limit }} שעות", "TIME_TRACKER_DISABLED": "מעקב הז<PERSON>ן כבוי", "STOP_TIMER_CHANGE_CLIENT": "אנא עצור את הטיימר לפני שתשנה את הלקוח", "ADD_CONTACT": "הוסף איש קשר", "ADD_TASK": "הוסף משימה", "STOP_TIMER_CHANGE_PROJECT": "אנא עצור את הטיימר לפני שתשנה את הפרויקט", "STOP_TIMER_CHANGE_TASK": "אנא עצור את הטיימר לפני שתשנה את המשימה", "STOP_TIMER_CHANGE_DESCRIPTION": "אנא עצור את הטיימר לפני שתשנה את התיאור", "STOP_TIMER_CHANGE_TEAM": "אנא עצור את הטיימר לפני שאתה משנה את הצוות", "ACTIVITY_WATCH_INTEGRATION": "שליחת פעילות ל־ActivityWatch", "WAKATIME_INTEGRATION": "אינטגרציה עם Wakatime", "LAST_CAPTURE_TAKEN": "נתפסה לכם צילום מסך אחרון", "REQUEST_TASK_PERMISSION": "בק<PERSON> הרשאה ממנהל הארגון", "RETURN_ONLINE": "ח<PERSON><PERSON><PERSON> למצב מקוון", "SEARCH": "חיפוש", "OPEN_SETTINGS": "פתח הגדרות", "REFRESH": "רענון", "SWITCHED_OFFLINE": "החלפת למצב לא מקוון", "SWITCHED_ONLINE": "החלפת למצב מקוון", "ONLINE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "OFFLINE": "לא מקוון", "SYNCED": "מסו<PERSON><PERSON><PERSON>ן", "SYNCED_PROGRESS": "מתבצע סינכרון", "WAIT_SYNCED": "ממתין לסנכרון", "DIALOG": {"WARNING": "אזהרה", "REMOVE_SCREENSHOT": "האם אתה בטוח שברצונך למחוק את התמונה האחרונה ויומן הפעילות?", "CHANGE_CLIENT": "האם אתה בטוח שברצונך לשנות את הלקוח?", "RESUME_TIMER": "הטיימר שלך התמשך בזמן לנעימת המחשב. האם להמשיך את הטיימר?", "EXIT": "האם אתה בטוח שתרצה לצאת?", "STOPPED_DU_INACTIVITY": "הטיימר נעצר עקב פרק החוסר פעילות שעבר את {{ inactivity }}. אנא וודא שתפעיל את הטיימר שוב לאחר שתמשיך לעבוד.", "EXIT_CONFIRM": "לחץ על יציאה כדי לעצור את הטיימר ולצאת מהיישום.", "EXIT_SERVER_CONFIRM": "לחץ על יציאה כדי לעצור את השרת ולצאת מהיישום.", "LOGOUT_CONFIRM": "לחץ על התנתקות כדי לעצור את הטיימר ולהתנתק מהיישום.", "SELECT_UPDATE_FILES": "אנא בחר תיקייה עם קבצי עדכון.", "UPDATE_READY": "העדכון מוכן להורדה", "NEW_VERSION_AVAILABLE": "גרסה חדשה v{{ next }} זמינה. עדכן את היישום על ידי הורדת העדכונים עבור v{{ current }}", "READY_INSTALL": "העדכון מוכן להתקנה", "HAS_BEEN_DOWNLOADED": "גרסה חדשה v{{ version }} כבר הורדה. אנא הפעל מחדש את היישום כדי להחיל את העדכונים.", "CONNECTION_DRIVER": "התחברות למסד הנתונים {{ driver }} הצליחה", "WANT_LOGOUT": "האם אתה בטוח שברצונך להתנתק?", "DOWNLOADING_UPDATE": "הורדת העדכון {{ current }} מ״ב מתוך {{ total }} מ״ב ->> {{ bandwidth }} ק״ב/ש", "STILL_WORKING": "האם אתה עדיין עובד?", "INACTIVITY_HANDLER": "מטפל בחוסר פעילות", "ERROR_HANDLER": "מט<PERSON>ל בשגיאות", "ERROR_OCCURRED": "איר<PERSON>ה שגיאה"}, "NO_LIMIT": "∞ אין <PERSON>ה", "TASK": "משימה", "DUE": "מועדו", "AW_CONNECTED": "קיימת חיבור ל־ActivityWatch", "AW_DISCONNECTED": "אין חיבור ל־ActivityWatch", "TOASTR": {"REMOVE_SCREENSHOT": "הסרת התמונה האחרונה ויומן הפעילות בוצעה בהצלחה", "CANT_RUN_TIMER": "אינך יכול להפעיל את הטיימר כרגע", "NOT_AUTHORIZED": "אין לך הרשאות עבודה", "ACCOUNT_DELETED": "החשבון שלך כבר נמחק", "PROJECT_ADDED": "הפרויקט נוסף בהצלחה", "TASK_ADDED": "המשימה נוספה בהצלחה", "CLIENT_ADDED": "הלקוח נוסף בהצלחה"}, "NATIVE_NOTIFICATION": {"STOPPED_DU_INACTIVITY": "המעקב נעצר בשל ביטול הפעילות!", "SCREENSHOT_TAKEN": "תפסתי צילום מסך", "SCREENSHOT_REMOVED": "הצלחתי להסיר את הצילום האחרון והפעילות", "NEW_VERSION_AVAILABLE": "עד<PERSON><PERSON><PERSON> חדש זמין עבור {{ name }} (גר<PERSON><PERSON> {{ version }})"}, "MENU": {"ZOOM_IN": "התקרבות", "ZOOM_OUT": "התרחקות", "SETTING_DEV_MODE": "הגדרת מצב מפתח", "SERVER_DEV_MODE": "מצב מפתח לוח המחוונים של השרת", "TIMER_DEV_MODE": "מצב מפתח פוקוס זמן", "NOW_TRACKING": "מעק<PERSON> א<PERSON><PERSON> ז<PERSON> נוכחי - {{ time }}", "START_TRACKING": "התחל מעקב אחר הזמן", "STOP_TRACKING": "הפסק מעקב אחר הזמן", "OPEN_TIMER": "פתח פוקוס זמן", "WINDOW": "<PERSON><PERSON><PERSON><PERSON>", "HELP": "עזרה", "LEARN_MORE": "למד עוד", "OPEN_MAIN_WINDOW": "פתח חלון ראשי", "DAILY_RECAP": "סיכום יומי", "WEEKLY_RECAP": "סיכום שבועי", "MONTHLY_RECAP": "סיכום חודשי", "INSTALL_PLUGIN": "התקנת תוסף"}, "RECAP": {"HOURLY_TIME_TRACKING": "מעק<PERSON> זמן לפי שעות", "HOURLY_TIME_TRACKING_DATA": "נתוני מעקב זמן לפי שעות", "NO_MONTHLY_ACTIVITY": "לא עקבת אחרי ז<PERSON>ן ופעילות החודש.", "WORKED_FOR_MONTH": "עבדת החודש", "ACTIVITY_FOR_MONTH": "פעילות חודשית"}, "LOADING": {"PLEASE_HOLD": "אנא המתן...", "SHUTTING_DOWN": "היישום נסגר", "LOGOUT_IN_PROGRESS": "התנתקות בתהליך"}}, "SERVER": "שרת", "SERVER_LOG": "יו<PERSON><PERSON> השרת", "TIMESHEET": {"TODAY": "היום", "DAILY": "יומי", "WEEKLY": "שבועי", "MONTHLY": "חו<PERSON><PERSON>י", "CALENDAR": "לוח שנה", "APPROVALS": "אישורים", "APPROVE_SUCCESS": "ה- Timesheet אושר בהצלחה", "DENIED_SUCCESS": "ה- Timesheet נדחה בהצלחה", "SUBMIT_SUCCESS": "ה- Timesheet נשלח בהצלחה", "UNSUBMIT_SUCCESS": "ה- Timesheet בוטל בהצלחה", "DELETE_TIMELOG": "האם אתה בטוח שברצונך למחוק את רשומת הזמן?", "SELECT_EMPLOYEE": "<PERSON><PERSON><PERSON> עובד", "ALL_EMPLOYEE": "כל העובדים", "SELECT_SOURCE": "<PERSON><PERSON><PERSON>", "SELECT_ACTIVITY_LEVEL": "בחר רמת פעילות", "SELECT_LOG_TYPE": "בחר סוג רישום", "ADD_TIME_LOGS": "הוסף רשומות זמן", "EDIT_TIME_LOGS": "ערוך רשומות זמן", "VIEW_TIME_LOGS": "רשומות זמן", "ADD_TIME": "הוסף זמן", "UPDATE_TIME": "<PERSON><PERSON><PERSON><PERSON>", "TILL_NOW": "עד כה", "VIEW": "הצג", "EDIT": "ערוך", "CLOSE": "סגור", "DELETE": "מחק", "IMMUTABLE_TIME": "<PERSON><PERSON><PERSON> קבוע", "BULK_ACTION": "פעולת גורם", "LOG_TYPE": "סוג רישום", "SOURCE": "<PERSON><PERSON><PERSON><PERSON>", "TOTAL_TIME": "סך זמן", "ACTIVITIES": "פעילויות", "APPROVED_AT": "אושר ב", "SUBMITTED_AT": "הוזן ב", "STATUS": "סטטוס", "SUBMIT_TIMESHEET": "שלח Timesheet", "UNSUBMIT_TIMESHEET": "בט<PERSON> Timesheet", "APPROVE": "אשר", "DENY": "ד<PERSON><PERSON>", "TIME_SPAN": "<PERSON><PERSON><PERSON><PERSON>", "ACTION": "פעולה", "EMPLOYEE": "עובד", "DURATION": "משך", "ORGANIZATION_CONTACT": "לקוחות", "NO_ORGANIZATION_CONTACT": "<PERSON><PERSON><PERSON> ל<PERSON>", "PROJECT": "פרויקטים", "NO_PROJECT": "<PERSON>י<PERSON> פרוי<PERSON>", "NO_TIMELOG": "לא נמצאו רשומות זמן", "TODO": "לעשות", "NO_TODO": "אין לעשות", "OVERLAP_MESSAGE": "רישום זמן זה מתנגש עם פעילות בפרויקטים / משימות אחרות, שייעוף:", "REASON": "סיבה", "NOTES": "הערות", "APPS": "אפליקציות", "TIME_OVERLAPS": "התמוט<PERSON>ות זמן", "TIME_TRACKING": "<PERSON><PERSON><PERSON><PERSON>", "MEMBERS_WORKED": "חב<PERSON>ים עבדו", "PROJECTS_WORKED": "עבודה בפרויקטים", "ACTIVITY_OVER_PERIOD": "פעילות במהלך התקופה", "ACTIVITY_FOR_DAY": "פעילות ליום", "ACTIVITY_FOR_WEEK": "פעילות שבועית", "WORKED_THIS_WEEK": "עבודה השבוע", "WORKED_OVER_PERIOD": "עבודה במהלך התקופה", "WORKED_FOR_DAY": "עבודה לצורך היום", "WORKED_FOR_WEEK": "עבודה למהלך השבוע", "TODAY_ACTIVITY": "פעילות של היום", "WORKED_TODAY": "עבודה היום", "RECENT_ACTIVITIES": "פעילויות אחרונות", "NO_SCREENSHOT_DAY": "לא קיימת תמונת מסך ליום זה", "NO_SCREENSHOT_WEEK": "אין תמונת מסך עבור השבוע", "NO_SCREENSHOT_PERIOD": "אין תמונת מסך עבור התקופה", "TASKS": "משימות", "NO_TASK_ACTIVITY_DAY": "אין פעילות במשימות ליום", "NO_TASK_ACTIVITY_WEEK": "אין פעילות במשימות למהלך השבוע", "NO_TASK_ACTIVITY_PERIOD": "אין פעילות במשימות למהלך התקופה", "MANUAL_TIME": "<PERSON><PERSON><PERSON> ידני", "NO_MANUAL_TIME_DAY": "אין זמן ידני ליום זה", "NO_MANUAL_TIME_WEEK": "אין זמן ידני למהלך השבוע", "NO_MANUAL_TIME_PERIOD": "אין זמן ידני למהלך התקופה", "PROJECTS": "פרויקטים", "NO_PROJECT_ACTIVITY_DAY": "אין פעילות בפרויקטים ליום", "NO_PROJECT_ACTIVITY_WEEK": "אין פעילות בפרויקטים למהלך השבוע", "NO_PROJECT_ACTIVITY_PERIOD": "אין פעילות בפרויקטים למהלך התקופה", "APPS_URLS": "אפליקציות ו- <PERSON>rls", "NO_APP_URL_ACTIVITY_DAY": "אין פעילות באפליקציות ו- url ליום זה", "NO_APP_URL_ACTIVITY_WEEK": "אין פעילות באפליקציות ו- url למהלך השבוע", "NO_APP_URL_ACTIVITY_PERIOD": "אין פעילות באפליקציות ו- url למהלך התקופה", "DATE": "תאריך", "MEMBERS": "חברים", "MEMBER": "<PERSON><PERSON><PERSON>", "MEMBER_INFO": "פרטי הח<PERSON>ר", "THIS_WEEK": "השבוע", "WEEK": "שבוע", "OVER_PERIOD": "למשך התקופה", "NO_MEMBER_ACTIVITY_DAY": "אין פעילות לחבר ליום זה", "NO_MEMBER_ACTIVITY_WEEK": "אין פעילות לחבר למהלך השבוע", "NO_MEMBER_ACTIVITY_PERIOD": "אין פעילות לחבר למשך התקופה", "TITLE": "כותרת", "URL": "כתובת", "TIME_SPENT": "ז<PERSON>ן שנותר (שעות)", "ACTIVITY_LEVEL": "רמת פעילות", "VALIDATION": {"DESCRIPTION": "תיא<PERSON><PERSON> חובה", "TASK": "יש לבחור משימה", "PROJECT": "יש לבחור פרויקט", "EMPLOYEE": "יש לבחור עובד", "REASON": "יש לבחור סיבה"}, "SCREENSHOTS": {"SCREENSHOTS": "תמונות מסך", "OF": "של", "TIME_LOG": "רשו<PERSON><PERSON> ז<PERSON>ן"}, "RUNNING_TIMER_WARNING": "אזהרה. טיי<PERSON><PERSON> פועל, אתה יכול להסיר רק תמונות מסך אם תעצור את הטיימר או באמצעות אפליקציית Inside Timer", "DELETE_CONFIRM": "האם אתה בטוח שברצונך למחוק את התמונות האלה ואת רשומת הזמן?", "VIEW_LOG": "הצ<PERSON> יומן", "NO_DATA": {"DAILY_TIMESHEET": "אין לך רשומות זמן ליום זה עדיין.", "WEEKLY_TIMESHEET": "אין לך רש"}, "VIEW_WINDOWS": "צ<PERSON>ה בחלונות", "VIEW_WIDGETS": "צפה בווידג'טים", "SOURCES": {"WEB_TIMER": "טיי<PERSON><PERSON> אינטרנטי", "DESKTOP": "אפליקציה לטיימר לשולחן העבודה", "MOBILE": "אפליק<PERSON><PERSON>ה לטיימר לנייד", "UPWORK": "Upwork", "HUBSTAFF": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "BROWSER_EXTENSION": "הרח<PERSON><PERSON> דפדפן", "TEAMS": "קבוצות", "CLOC": "קלוק"}, "LAST_WORKED": "עבד לאחרונה:", "TIME_ZONE": "א<PERSON><PERSON><PERSON>", "TIME_FORMAT": "תבנית זמן", "TIME_ZONE_OPTION": {"UTC": "UTC", "ORG_TIMEZONE": "אזור הזמן של הארגון", "MY_TIMEZONE": "אזור הז<PERSON>ן שלי"}}, "ACTIVITY": {"SCREENSHOTS": "Screenshots", "PERCENT_USED": "Percent used", "TIME_SPENT": "Time spent (hours)", "APPS": "Apps", "VISITED_SITES": "Visited Sites", "NO_ACTIVITIES": "No records found. Please select date range, employee or project.", "NO_ACTIVITY": "No Activity", "TIME_AND_ACTIVITIES": "Time & Activities", "VIEW_SCREEN": "View Screen", "VIEW_INFO": "View Info", "MINUTES": "Minutes", "NO_EMPLOYEES_SELECTED": "Please select Employee", "DELETE_CONFIRM": "Are you sure you want to delete this screenshots and activities?", "VISITED_DATES": "Visited Dates", "NO_SCREENSHOT": "No Screenshot", "NO_SCREENSHOTS": "No Screenshots", "NO_RECORD_FOUND": "No records found. Please select date range, employee or project."}, "ONBOARDING": {"FIRST_ORGANIZATION": "בואו ניצור את הארגון הראשון שלכם", "COMPLETE_TITLE": "אתם מוכנים!", "COMPLETE_SUB_TITLE": "לאן תרצו ללכת עכשיו?"}, "VALIDATION": {"FIELD_REQUIRED": "This field is required!", "ENTER_POSITIVE_NUMBER": "Please enter positive number!"}, "APPOINTMENTS_PAGE": {"ADD_APPOINTMENT": "Add Appointment", "EDIT_APPOINTMENT": "Edit Appointment", "SAVE_SUCCESS": "Appointment was saved", "SAVE_FAILED": "Failed to save appointment", "CANCEL_APPOINTMENT": "<PERSON>cel Appointment", "CANCEL_FAIL": "Failed to cancel appointment", "CANCEL_SUCCESS": "Appointment was cancelled", "DURATION_ERROR": "Selected Duration is not valid", "SELECT_EMPLOYEE": "Select Employee", "EMPLOYEE": "עובד", "BUFFER_TIME": "Buffer Time", "BUFFER_AT_START": "Buffer at Start", "BUFFER_AT_END": "Buffer at End", "BREAK_TIME": "Break Time", "ARE_YOU_SURE": "Are you sure? This action is irreversible."}, "EMPLOYEE_SCHEDULES_MODAL": {"EMPLOYEE": "עובד", "SLOTS_AVAILABLE": "Employee is available only for the below time slots, Do you still want to continue?", "SLOTS_UNAVAILABLE": "Employee unavailable for this time slot"}, "EVENT_TYPE_PAGE": {"EVENT_TYPE": "Event Types", "MANAGE_EVENT_TYPE": "Manage Event Type", "EVENT_NAME": "Event Type Name", "EVENT_DURATION": "Duration", "EVENT_DESCRIPTION": "Description", "ACTIVE": "פעיל", "EMPLOYEE": "עובד", "YES": "Yes", "NO": "No", "DURATION_UNIT": "{{ unit }}"}, "SCHEDULE": {"DATE_SPECIFIC_AVAILABILITY": "Date Specific Availability", "SELECT_EMPLOYEE": "Please select employee from the menu above.", "RECURRING_AVAILABILITY": "Recurring Availability", "DATE_SPECIFIC_AVAILABILITY_TOOLTIP": "You can use Date specific availability to mark availability for a particular date or add exceptions to your Recurring availability pattern. The changes you make will override your Weekly recurring availability on those specific dates only.", "RECURRING_AVAILABILITY_TOOLTIP": "You can use Recurring availability to mark your availability on a weekly basis. To change your availability for specific days only, you must use Date-specific availability.", "MONDAY_FRIDAY": "Monday - Friday", "SUNDAY_THURSDAY": "Sunday - Thursday"}, "PUBLIC_APPOINTMENTS": {"BOOK_APPOINTMENT": "Book Appointment", "BOOK_APPOINTMENTS": "Book an appointment", "SELECT_EVENT_TYPES": "Please select an event type", "PICK_DATETIME": "Pick a date and time", "DURATION": "Duration:", "EVENT_TYPE": "Event Type:", "CONFIRM_APPOINTMENT": "Appointment Confirmed", "APPOINTMENT_INFO": "Appointment Information", "DETAILS": "Booking Details", "PARTICIPANTS": "Participant Emails", "HOST": "Host", "RESCHEDULE": "Reschedule Appointment", "CANCEL": "<PERSON>cel Appointment", "TIMEZONE": "Your time zone:", "CHANGE": "(Change)", "SELECT_EMPLOYEE_ERROR": "Please select an employee.", "EXPIRED_OR_CANCELLED": "**This appointment has expired or has been cancelled.", "EMAIL_SENT": "**An email has been sent to the host as well as to all the participants for this meeting.", "NO_ACTIVE_EVENT_TYPES": "There are no active event types", "CLICK_HERE": "Click Here"}, "EMAIL_TEMPLATES_PAGE": {"HEADER": "Email Templates", "SAVE": "לשמור", "LABELS": {"LANGUAGE": "Language", "TEMPLATE_NAME": "Template Name", "SUBJECT": "Subject", "EMAIL_BODY": "Email Body", "EMAIL_PREVIEW": "Email Preview", "SUBJECT_PREVIEW": "Subject Preview"}, "TEMPLATE_NAMES": {"password": "Password Reset", "multi-tenant-password": "איפוס סיסמת ריבוי דיירים", "welcome-user": "Welcome User", "invite-organization-client": "Invite Organization Client", "email-estimate": "Email Estimate", "email-invoice": "Email Invoice", "invite-employee": "Invite Employee", "invite-user": "Invite User", "appointment-confirmation": "Appointment Confirmation", "appointment-cancellation": "Appointment Cancellation", "equipment": "Equipment Create", "equipment-request": "Equipment Request", "timesheet-overview": "Time sheet overview", "timesheet-submit": "Time Sheet Submit", "timesheet-action": "Time Sheet Actions", "timesheet-delete": "Time Sheet Delete", "time-off-report-action": "Time off policy action", "task-update": "Task Update", "candidate-schedule-interview": "Candidate Interview Schedule", "interviewer-interview-schedule": "Interviewer Schedule", "employee-join": "Employee Join", "email-reset": "<PERSON><PERSON>", "organization-team-join-request": "Organization team join request", "payment-receipt": "קבלת תשלום", "reject-candidate": "לדחות את המועמד"}, "HTML_EDITOR": "HTML Editor"}, "PIPELINES_PAGE": {"HEADER": "Pipelines", "HEADER_STAGES": "Stages", "VIEW_DEALS": "View Deals", "HEADER_FORM_EDIT": "<PERSON>", "HEADER_FORM_CREATE": "Create Pipeline", "RECORD_TYPE": "pipeline \"{{ name }}\"", "ACTIVE": "פעיל", "INACTIVE": "Inactive", "BROWSE": "Browse", "SEARCH": "Search", "SEARCH_PIPELINE": "Search Pipeline", "NAME": "שם", "STAGE": "Stage", "SEARCH_STAGE": "Search Stage", "STATUS": "עבודה", "ALL_STATUS": "All Status", "RESET": "Reset"}, "PIPELINE_DEALS_PAGE": {"HEADER": "Pipeline Deals", "FILTER_BY_STAGE": "Filter by Stage", "RECORD_TYPE": "stage \"{{ title }}\"", "ALL_STAGES": "All Stages", "LOW": "Low", "MEDIUM": "Medium", "HIGH": "High", "UNKNOWN": "לא ידוע", "DEAL_DELETED": "Deal '{{ name }}' was removed", "DEAL_EDITED": "Deal '{{ name }}' was changed", "DEAL_ADDED": "Deal '{{ name }}' was added"}, "PIPELINE_DEAL_EDIT_PAGE": {"HEADER": "Edit Deal | Pipeline: '{{ name }}'"}, "PIPELINE_DEAL_CREATE_PAGE": {"HEADER": "Create Deal | Pipeline: '{{ name }}'", "SELECT_STAGE": "Select Stage", "PROBABILITY": "Probability", "SELECT_CLIENT": "Select Client"}, "DIALOG": {"CONFIRM": "אשר", "ALERT": "<PERSON><PERSON>", "DELETE_CONFIRM": "<PERSON><PERSON>", "QUICK_ACTIONS": "Quick Actions"}, "SETTINGS_FILE_STORAGE": {"FILE_PROVIDER": "<PERSON><PERSON><PERSON> קבצים", "S3": {"HEADER": "S3 Configuration", "LABELS": {"ACCESS_KEY_ID": "Access key id", "SECRET_ACCESS_KEY": "Secret access key", "REGION": "Region", "BUCKET": "Bucket"}, "PLACEHOLDERS": {"ACCESS_KEY_ID": "Access key id", "SECRET_ACCESS_KEY": "Secret access key", "REGION": "Region", "BUCKET": "Bucket"}}, "WASABI": {"HEADER": "Wasabi Configuration", "LABELS": {"ACCESS_KEY_ID": "Access key id", "SECRET_ACCESS_KEY": "Secret access key", "REGION": "Region", "BUCKET": "Bucket", "SERVICE_URL": "Service URL"}, "PLACEHOLDERS": {"ACCESS_KEY_ID": "Access key id", "SECRET_ACCESS_KEY": "Secret access key", "REGION": "Region", "BUCKET": "Bucket", "SERVICE_URL": "Service URL"}}}, "CUSTOM_SMTP_PAGE": {"HEADER": "Manage SMTP for '{{ name }}'", "HOST": "Host", "PORT": "Port", "SECURE": "Secure", "AUTH": {"USERNAME": "שם משתמש", "PASSWORD": "סיסמה"}}, "SMS_GATEWAY_PAGE": {"HEADER": "SMS Provider", "TWILIO": "<PERSON><PERSON><PERSON>"}, "FEATURE_PAGE": {"HEADER": "Manage Features for '{{ name }}'"}, "SETTINGS": {"EMAIL_HISTORY": {"EMAIL_ARCHIVED": "Email Archived", "RESEND": "לשלוח מחדש", "ARCHIVE": "Archive", "HEADER": "Email History", "FROM": "From:", "TO": "To:", "DATE": "תאריך:", "SUBJECT": "Subject:", "LANGUAGE": "Language:", "TEMPLATE": "Template:", "NO_EMAILS_SENT": "No emails sent.", "SYSTEM": "System", "FILTERS": {"TO": "To", "TEMPLATE_LANGUAGE": "Template/Language"}}}, "GAUZY_MAINTENANCE": "{{ companySite }} @ Maintenance", "LEGAL": {"PRIVACY_POLICY": "Privacy Policy", "TERMS_AND_CONDITIONS": "Terms & Conditions"}, "LOADING": "Loading, please hold....", "ACCOUNTING_TEMPLATES_PAGE": {"HEADER": "Accounting Templates", "TEMPLATE_NAMES": {"invoice": "Invoice", "estimate": "Estimate", "receipt": "Receipt"}}, "REGISTER_PAGE": {"TITLE": "הרשמה", "HAVE_AN_ACCOUNT": "כבר יש לך חשבון?", "LABELS": {"FULL_NAME": "שם מלא:", "EMAIL": "כתובת דוא\"ל:", "PASSWORD": "סיסמה", "CONFIRM_PASSWORD": "אשר סיסמה:"}, "PLACEHOLDERS": {"FULL_NAME": "שם מלא", "EMAIL": "כתובת דוא\"ל", "PASSWORD": "סיסמה", "CONFIRM_PASSWORD": "א<PERSON>ר סיסמה"}, "VALIDATIONS": {"FULL_NAME_REQUIRED": "שם מלא נדרש!", "FULL_NAME_SHOULD_CONTAIN": "שם מלא צריך להכיל בין {{ minLength }} ל-{{ maxLength }} תווים", "EMAIL_REQUIRED": "כתובת דוא\"ל נדרשת!", "EMAIL_SHOULD_BE_REAL": "כתובת דוא\"ל צריכה להיות אמיתית!", "PASSWORD_NO_SPACE_EDGES": "סיסמאות לא יכולות להתחיל או להסתיים עם רווחים!", "PASSWORD_REQUIRED": "סיסמה נדרשת!", "PASSWORD_SHOULD_CONTAIN": "סיסמה צריכה להכיל בין {{ minLength }} ל-{{ maxLength }} תווים", "CONFIRM_PASSWORD_REQUIRED": "אישור סיסמה נדרש!", "PASSWORDS_NOT_MATCH": "הסיסמה לא תואמת לאישור הסיסמה!", "CHECK_BOX_TEXTS": {"AGREE_TO": "הסכמה", "TERMS_AND_CONDITIONS": "תנאים והגבלות", "AND": " וגם ", "PRIVACY_POLICY": "מדיניות פרטיות"}}}, "LOGIN_PAGE": {"TITLE": "<PERSON><PERSON>", "SUB_TITLE": "Hello! Log in with your email.", "REMEMBER_ME_TITLE": "Remember me", "DO_NOT_HAVE_ACCOUNT_TITLE": "Don't have an account?", "FORGOT_PASSWORD_TITLE": "Forgot Password?", "OR_SIGN_IN_WITH": "או להתחבר עם", "FORGOT_EMAIL_TITLE": "שכחת כתובת דואר אלקטרוני?", "LOGIN_MAGIC": {"TITLE": "התחברות עם קוד קסם", "DESCRIPTION_TITLE": "נשלח לך קוד קסום להתחברות ללא סיסמה. או ניתן ", "OR_SIGN_IN_WITH_PASSWORD": "להתחבר באמצעות סיסמה במקום זאת.", "RESEND_CODE_TITLE": "שלח שוב קוד", "SUCCESS_SENT_CODE_TITLE": "קוד באורך 6 תווים נשלח אל: ", "SUCCESS_SENT_CODE_SUB_TITLE": "הקוד פג תוקף בקרוב, אז יש להזין אותו מהר.", "REQUEST_NEW_CODE_TITLE": "בק<PERSON><PERSON> קוד חדש בעוד {{ countdown }} שניות."}, "LABELS": {"EMAIL": "Email address:", "PASSWORD": "Password:", "CODE": "קוד:"}, "PLACEHOLDERS": {"EMAIL": "Email address", "PASSWORD": "סיסמה", "CODE": "<PERSON><PERSON><PERSON> קוד"}, "VALIDATION": {"EMAIL_REQUIRED": "Email is required!", "EMAIL_REAL_REQUIRED": "Email should be the real one!", "PASSWORD_REQUIRED": "Password is required!", "PASSWORD_SHOULD_CONTAIN": "Password should contain from {{ minLength }} to {{ maxLength }} characters!", "PASSWORD_NO_SPACE_EDGES": "Passwords must not begin or end with spaces.", "CODE_REQUIRED": "נדרש קוד להתחברות.", "CODE_REQUIRED_LENGTH": "הקוד צריך להכיל {{ requiredLength }} תווים."}, "DEMO": {"TITLE": "Login Automatically into Demo accounts", "SUB_TITLE": "Please select account type below.", "SUPER_ADMIN_TITLE": "מנהל ראשי", "ADMIN_TITLE": "מנהל", "EMPLOYEE_TITLE": "עובד", "DEMO_TITLE": "הדגמה", "CREDENTIALS_TITLE": "פרטי כניסה", "LABELS": {"EMAIL": "אימייל: ", "PASSWORD": "סיסמה: "}}}, "FORGOT_PASSWORD_PAGE": {"TITLE": "Forgot Password", "SUB_TITLE": "Enter your email address and we’ll send a link to reset your password", "ALERT_TITLE": "Oh snap!", "ALERT_SUCCESS_TITLE": "Hooray!", "REQUEST_PASSWORD_TEXT": "Request password", "BACK_TO_LOGIN": "Back to?", "FAQ_TITLE": "FAQ", "FAQ_LEARN_MORE": "Learn more", "LABELS": {"EMAIL": "Enter your email address:"}, "PLACEHOLDERS": {"EMAIL": "Email address"}, "VALIDATION": {"EMAIL_REQUIRED": "Email is required!", "EMAIL_REAL_REQUIRED": "Email should be the real one!"}}, "RESET_PASSWORD_PAGE": {"TITLE": "שנה סיסמה", "SUB_TITLE": "אנא הגדר סיסמה חדשה", "LABELS": {"NEW_PASSWORD": "סיסמה חדשה:", "CONFIRM_PASSWORD": "אימות סיסמה:", "BACK_TO": "חזרה ל"}, "PLACEHOLDERS": {"NEW_PASSWORD": "סיסמה חדשה", "CONFIRM_PASSWORD": "אימות סיסמה"}, "VALIDATION": {"NEW_PASSWORD_REQUIRED": "סיסמה חדשה נדרשת!", "CONFIRM_PASSWORD_REQUIRED": "אימות סיסמה נדרש!", "PASSWORDS_NOT_MATCH": "הסיסמה אינה תואמת לאימות הסיסמה!", "PASSWORD_SHOULD_CONTAIN": "הסיסמה החדשה צריכה לכלול בין {{ minLength }} ל-{{ maxLength }} תווים."}}, "PAGINATION": {"ITEMS": "Items"}, "USER_MENU": {"STATUS": "עבודה", "AVAILABLE": "Available", "UNAVAILABLE": "Unavailable", "PAUSE_NOTIFICATIONS": "Pause notifications", "FOR_1_HOUR": "For 1hour", "FOR_2_HOURS": "For 2hours", "UNTIL_TOMORROW": "Until tomorrow", "CUSTOM": "Custom", "SET_AS_NOTIFICATION_SCHEDULE": "Set as notification schedule", "SET_YOURSELF_AS_AWAY": "Set yourself as away", "HOTKEYS": "Hotkeys", "HELP": "עזרה", "SIGN_OUT": "Sign Out", "PROFILE": "Profile"}, "WORKSPACES": {"MENUS": {"SING_ANOTHER_WORKSPACE": "Sign in another workspace", "CREATE_NEW_WORKSPACE": "Create a new workspace", "FIND_WORKSPACE": "Find workspace"}, "LABELS": {"EMAIL": "הזן את כתובת הדוא\"ל שלך:", "PASSWORD": "הזן את הסיסמה שלך:"}, "PLACEHOLDERS": {"EMAIL": "<EMAIL>", "PASSWORD": "סיסמה"}, "SIGN_IN_TITLE": "התח<PERSON>ר למרחב העבודה", "BACK_TO": "חזרה אל", "SUCCESS_SIGNIN_TITLE": ".מזל טוב! נכנסת למערכת בהצלחה", "SUCCESS_SIGNIN_SUB_TITLE": "...אנא התאזרי בסבלנות בזמן שאנו מכינים את המערכת", "FAIL_SIGNIN_TITLE": ".שגיאה! קוד הקסם או הקישור שסיפקת פג או אינו תקף", "FAIL_SIGNIN_SUB_TITLE": ".אנא התחל/י את התהליך מחדש", "THANKING_TEXT": ".תודה על בחירתך בנו", "UNKNOWN_WORKSPACE": "אין לך מושג מה המרחב שלך?", "FIND_WORKSPACE": "מצא את המרחב שלך", "SELECTION": {"WELCOME_BACK": "ברוך שובך!", "YOU_LOOK_NICE_TODAY": "אתה נראה טוב היום!", "EMAIL_MULTIPLE_WORKSPACE": "האימייל מקושר למספר סביבת עבודה, אנא בחר אחת להמשך", "SELECT_WORKSPACE_FOR": "בחר סביבת עבודה עבור", "OPEN": "פתח"}}, "NO_IMAGE": {"ADD_DROP": "Add or Drop Image", "AVAILABLE": "Image not available"}, "SERVER_API": "API Server", "SERVER_API_LOG": "API Server Log", "AGENT_APP": "<PERSON>ו<PERSON><PERSON>", "AGENT_LOG": "<PERSON><PERSON><PERSON><PERSON>וכן", "COMING_SOON": "דף זה יגיע בקרוב!", "PLUGIN": {"VIDEO": {"SINGLE": "וידאו", "PLURAL": "סרטונים", "DOWNLOAD": "הורדה", "NOT_FOUND": "הוויד<PERSON>ו לא נמצא", "NO_VIDEO": "<PERSON>ין וידאו", "DOWNLOADS": "הורדות", "NO_VIDEO_DOWNLOAD": "אין הורדת וידאו", "LOADING_VIDEOS": "טוען סרטונים...", "EDIT_METADATA": "עריכת מטא-נתונים של וידאו", "STARTING_DOWNLOAD_FOR": "מתחיל הורדה עבור: {{ url }}", "DOWNLOAD_FAILED_FOR": "הורדה נכשלה עבור: {{ url }}, נסה שוב", "DOWNLOAD_COMPLETED_FOR": "ההורדה הושלמה עבור: {{ url }}", "SHARED_SUCCESSFULLY": "הווידאו שותף בהצלחה", "COPIED_SUCCESSFULLY": "הוויד<PERSON>ו הועתק בהצלחה", "CORE_INFO": "מידע ליבה", "RECORDED": "הוקלט", "DURATION": "משך", "SIZE": "גודל", "VIEW_FULL": "צ<PERSON>ה בסרטון המלא", "TECHNICAL_DETAILS": "פרטים טכניים", "RESOLUTION": "רזולוציה", "CODEC": "קוד<PERSON>", "FRAME_RATE": "קצב פריימים", "UPLOADED_BY": "הועלה על ידי", "NO_VIDEO_AVAILABLE": "<PERSON>ין ויד<PERSON>ו זמין", "RETRIED": "ניסיון חוזר של וידאו", "REMOVED_FROM_QUEUE": "הווידאו הוסר מהתור", "ADDED_TO_QUEUE": "הווידאו נוסף לתור", "DELETE_SUCCESSFULLY": "הוויד<PERSON>ו נמ<PERSON>ק בהצלחה", "UPDATE_SUCCESSFULLY": "הוויד<PERSON>ו עוד<PERSON>ן בהצלחה", "ERROR": {"REQUIRED": "הכותרת נדרשת.", "AT_LEAST_3": "הכותרת חייבת להכיל לפחות 3 תווים.", "AT_MOST_255": "הכותרת לא יכולה להכיל יותר מ-255 תווים.", "AT_MOST_1000": "התיאור לא יכול להכיל יותר מ-1000 תווים.", "SHARE_CANCELED": "השיתוף בוטל", "INVALID_SHARE_DATA": "נתוני שיתוף לא תקינים", "SHARE_NOT_ALLOWED": "השיתוף אינו מותר", "UNEXPECTED_SHARING_ERROR": "שגיאה בלתי צפויה בשיתוף"}}, "FORM": {"TITLE": "העלאת תוסף", "BASIC_INFO": "מידע בסיסי", "NAME": "שם התוסף", "NAME_PLACEHOLDER": "הזן את שם התוסף", "VALIDATION": {"NAME_REQUIRED": "שם התוסף נדרש", "NAME_TOO_LONG": "שם התוסף ארוך מדי", "VERSION_REQUIRED": "גרסה נדרשת", "VERSION_FORMAT": "פורמט גרסה לא תקין", "DESCRIPTION_TOO_LONG": "התיאור ארוך מדי", "URL_REQUIRED": "נדרש URL", "INVALID_URL": "פורמט URL לא תקין", "PACKAGE_NAME_REQUIRED": "נדרש שם חבילה", "INVALID_PACKAGE_NAME": "פורמט שם חבילה לא תקין", "INVALID_SCOPE": "פורמט היקף לא תקין", "AUTHOR_TOO_LONG": "שם המחבר ארוך מדי", "LICENSE_TOO_LONG": "שדה הרישיון ארוך מדי", "FAILED": "האימות נכשל", "CHANGELOG_TOO_SHORT": "שדה השינויים ארוך מדי", "CHANGELOG_REQUIRED": "שדה השינויים נדרש", "REQUIRED": "נדרש"}, "TYPE": "סוג התוסף", "TYPE_PLACEHOLDER": "בחר סוג תוסף", "TYPES": {"DESKTOP": "שול<PERSON>ן עבודה", "WEB": "אינטר<PERSON>ט", "MOBILE": "נייד"}, "VERSION": "גרסה", "STATUS": "סטטוס", "STATUSES": {"ACTIVE": "פעיל", "INACTIVE": "לא פעיל", "DEPRECATED": "מי<PERSON><PERSON><PERSON>", "ARCHIVED": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ן"}, "DESCRIPTION": "תיאור", "DESCRIPTION_PLACEHOLDER": "הזן תיאור לתוסף", "SOURCE": "<PERSON><PERSON><PERSON><PERSON>", "SOURCE_TYPE": "סוג המקור", "SOURCE_TYPES": {"CDN": "CDN", "NPM": "NPM", "GAUZY": "Gauzy"}, "CDN": {"URL": "כתובת CDN", "INTEGRITY": "שלמות", "CROSS_ORIGIN": "Cross Origin"}, "NPM": {"PACKAGE_NAME": "שם חבילת NPM", "REGISTRY": "רישום NPM", "SCOPE": "היקף", "AUTH_TOKEN": "אסימון אימות"}, "FILE_UPLOAD": {"DRAG_DROP": "גרור ושחרר את הקובץ שלך כאן", "BROWSE": "עיון", "REMOVE": "הסר קובץ", "RESTRICTIONS": "רק קבצי .zip מותרים", "FILE": "קובץ"}, "METADATA": {"TITLE": "מטא-נתונים", "AUTHOR": "<PERSON><PERSON><PERSON><PERSON>", "AUTHOR_PLACEHOLDER": "הזן את שם המחבר", "LICENSE": "רי<PERSON><PERSON><PERSON>ן", "LICENSE_PLACEHOLDER": "הז<PERSON> רישיון", "HOMEPAGE": "דף הבית", "HOMEPAGE_PLACEHOLDER": "הזן את כתובת דף הבית", "REPOSITORY": "<PERSON><PERSON><PERSON><PERSON>", "REPOSITORY_PLACEHOLDER": "הזן את כתובת המאגר"}, "OR": "או", "RELEASE_DATE": "תאריך הרישיון", "CHANGELOG": "יו<PERSON><PERSON> ה<PERSON>ינויים", "CHANGELOG_PLACEHOLDER": "הזן יומן ה<PERSON>ינויים", "CLICK_TO_SELECT_SOURCE": "לחץ כדי לבחור מקור", "SELECT_SOURCE_TYPE": "<PERSON><PERSON>ר סוג מקור", "ADD_SOURCE": "הוסף מקור חדש", "OPERATING_SYSTEM": "מערכת הפעלה", "ARCHITECTURE": "ארכ<PERSON>ט<PERSON>טורה"}, "DETAILS": {"USAGE_STATS": "סטטיסטיק<PERSON>ת שימוש", "DOWNLOAD_COUNT": "מספר הורדות", "LAST_DOWNLOADED": "הורדה אחרונה", "UPLOADED_BY": "הועלה על ידי", "UPLOADED_AT": "הועלה בתאריך", "SECURITY_INFO": "מידע על אבטחה", "CHECKSUM": "סכום ביקורת", "SIGNATURE": "חתימה", "INSTALLED": "התקנה", "HIDDEN_FOR_SECURITY": "הוסרה מהספרייה לכרטיסי אבטחה"}, "LAYOUT": {"DISCOVER": "לגלות", "INSTALLED": "מות<PERSON>ן", "VERSION_HISTORY": "היסטוריית גרסאות"}, "MARKETPLACE": {"UPLOAD": "העלה תוסף חדש", "OFFLINE": "אתה לא מחובר כרגע ⛓️‍"}, "DIALOG": {"DEACTIVATE": {"TITLE": "לבטל את ההפעלה של התוסף", "DESCRIPTION": "האם אתה בטוח שברצונך לבטל את ההפעלה של התוסף הזה?", "CONFIRM": "בטל הפעלה"}, "UNINSTALL": {"TITLE": "הסר את התוסף", "DESCRIPTION": "האם אתה בטוח שברצונך להסיר את התוסף הזה?", "CONFIRM": "הסר התקנה"}, "DELETE": {"TITLE": "מחיקת תוסף", "DESCRIPTION": "האם אתה בטוח שברצונך למחוק תוסף זה?", "CONFIRM": "מחק"}, "VERSION": {"UPDATE": {"TITLE": "עדכון גרסה v{{number}}", "DESCRIPTION": "האם אתה בטוח שברצונך לעדכן את התוסף הזה?", "CONFIRM": "עד<PERSON><PERSON>"}, "CREATE": {"TITLE": "צור גרסה חדשה עבור {{name}}", "DESCRIPTION": "האם אתה בטוח שברצונך ליצור גרסה זו?", "CONFIRM": "צור"}, "DELETE": {"TITLE": "מחיקת גרסה", "DESCRIPTION": "האם אתה בטוח שברצונך למחוק גרסה זו?", "CONFIRM": "מחק"}, "RESTORE": {"TITLE": "שחזור גר<PERSON>ה", "DESCRIPTION": "האם אתה בטוח שברצונך לשחזר גרסה זו?", "CONFIRM": "שח<PERSON><PERSON>"}}, "SOURCE": {"CREATE": {"TITLE": "צור מקור חדש לגרסה v{{ number }} של התוסף {{ name }}", "DESCRIPTION": "האם אתה בטוח שברצונך ליצור גרסה זו?", "CONFIRM": "צור"}}, "INSTALLATION": {"TITLE": "התקנת תוסף", "DESCRIPTION": "האם אתה בטוח שברצונך להתקין תוסף זה?", "CONFIRM": "הת<PERSON>ן", "VALIDATION": {"REQUIRED": "נדרש אסימון אימות"}}}, "TOASTR": {"SUCCESS": {"VERSION": {"CREATED": "גרסת התוסף v{{number}} נוצרה בהצלחה!", "UPDATED": "גרסת התוסף v{{number}} עודכנה בהצלחה!", "DELETED": "גרסת התוסף נמחקה בהצלחה!", "RESTORED": "גרסת התוסף שוחזרה בהצלחה!"}, "SOURCE": {"CREATED": "מקור התוסף נוצר בהצלחה!", "UPDATED": "מקור התוסף עודכן בהצלחה!", "DELETED": "מקור התוסף נמחק בהצלחה!", "RESTORED": "מקור התוסף שוחזר בהצלחה!"}, "UPLOADED": "התוסף הועלה בהצלחה!", "UPDATED": "התוסף עודכן בהצלחה!", "DELETED": "התוסף נמחק בהצלחה!", "INSTALLED": "התוסף הותקן בהצלחה!", "UNINSTALLED": "התוסף הוסר בהצלחה!"}, "INFO": {"VERSION": {"ADDING": "מוסיף גרסת תוסף חדשה...", "UPDATING": "מעדכן גרסת תוסף...", "DELETING": "מוחק גרסת תוסף...", "RESTORING": "משחזר גרסת תוסף..."}, "SOURCE": {"ADDING": "מוסיף מקור חדש...", "UPDATING": "מעדכן גרסת תוסף...", "DELETING": "מוחק גרסת תוסף...", "RESTORING": "משחזר גרסת תוסף..."}, "UPLOADING": "מעלה תוסף...", "UPDATING": "מעדכן תוסף...", "DELETING": "מוחק תוסף...", "INSTALLING": "מתקין תוסף...", "UNINSTALLING": "מסיר תוסף..."}, "ERROR": {"VERSION": {"RESTORE": "שחזור גרסת התוסף נכשל. נא לנסות שוב!"}, "SOURCE": {"RESTORE": "שחזור מקור התוסף נכשל. אנא נסה שוב!"}, "UPLOAD": "העלאת התוסף נכשלה. נא לנסות שוב!", "NOT_FOUND": "תוסף עם מזהה {{ id }} לא נמצא.", "INSTALL": "התקנת התוסף נכשלה. נא לנסות שוב!", "REVERT_INSTALLATION": "ביטול ההתקנה נכשל!"}}}, "SERVER_DOWN": {"TITLE": "השרת לא זמין", "DESCRIPTION": "שרת {{companySite}} אינו נגיש כרגע. ייתכן שהסיבה לכך היא תחזוקה מתוכננת או השבתה בלתי צפויה.", "CONNECTING": "מנסה להתחבר מחדש אוטומטית...", "HINT": "סטטוס: מנ<PERSON><PERSON> ליצור חיבור לשרתי {{companySite}}. קוד שגיאה: 503."}}