import { Injectable } from '@angular/core';
import { TranslateCompiler } from '@ngx-translate/core';

/**
 * Custom compiler for translations.
 */
@Injectable()
export class CustomCompiler extends TranslateCompiler {
	/**
	 * Compiles a single translation value.
	 *
	 * @param value The translation string to compile.
	 * @param lang The language for which the translation is being compiled.
	 * @returns The compiled translation string.
	 */
	compile(value: string, lang: string): string {
		// Log the details of the translation being compiled for debugging purposes.
		console.log('CustomCompiler.compile: Compiling translation', value, 'for language', lang);
		// Implement your custom compilation logic here.
		// For example, you might want to perform certain transformations on the translation string.
		return value;
	}

	/**
	 * Compiles a set of translations.
	 *
	 * @param translations The translations to compile.
	 * @param lang The language for which the translations are being compiled.
	 * @returns The compiled translations.
	 */
	compileTranslations(translations: any, lang: string): any {
		// Log the details of the translations being compiled for debugging purposes.
		console.log('CustomCompiler.compileTranslations: Compiling translations for language', lang, translations);
		// Implement your custom translation compilation logic here.
		// For example, you might want to perform certain transformations on the entire set of translations.
		return translations;
	}
}
