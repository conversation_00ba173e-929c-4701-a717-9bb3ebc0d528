<ng-select
	[addTag]="((hasEditEmployee$ | async) && addTag) ? addOrganizationContact : null"
	[clearable]="clearable"
	[disabled]="disabled"
	[searchable]="searchable"
	[items]="contacts"
	(change)="selectContact($event)"
	[(ngModel)]="organizationContact"
	[placeholder]="placeholder || 'POP_UPS.ALL_CONTACTS' | translate"
	bindLabel="name"
	[searchFn]="searchContact"
	appendTo="body"
>
	<ng-template
		ng-option-tmp
		let-item="item"
		let-index="index"
	>
		{{ item.name }}
	</ng-template>
	<ng-template ng-label-tmp let-item="item">
		<div class="selector-template">
			<span>{{ item.name }}</span>
		</div>
	</ng-template>
</ng-select>