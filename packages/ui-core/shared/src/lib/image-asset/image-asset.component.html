<nb-card class="main">
	<nb-card-header class="d-flex flex-column">
		<span class="cancel"
			><i class="fas fa-times" (click)="onCancel()"></i>
		</span>
		<h4 class="title">
			{{ 'INVENTORY_PAGE.EDIT_IMAGE_ASSET' | translate }}
		</h4>
	</nb-card-header>
	<nb-card-body class="body">
		<div class="row">
			<div class="col-sm-4">
				<img
					*ngIf="imageAsset && imageAsset.fullUrl"
					[src]="imageAsset.fullUrl"
				/>
				<img
					*ngIf="!imageAsset || !imageAsset.fullUrl"
					src="/assets/images/others/no-image-placeholder.svg"
				/>
			</div>
			<div class="col-sm-8">
				<form [formGroup]="form" >
					<div class="form-group">
						<label class="label" for="nameInput">
							{{ 'INVENTORY_PAGE.NAME' | translate }}
						</label>
						<input
							fullWidth
							id="nameInput"
							type="text"
							nbInput
							formControlName="name"
							[placeholder]="'INVENTORY_PAGE.NAME' | translate"
						/>
					</div>
					<div class="form-group">
						<label class="label" for="urlInput">
							{{ 'INVENTORY_PAGE.URL' | translate }}
						</label>
						<input
							fullWidth
							id="urlInput"
							type="text"
							nbInput
							formControlName="url"
							[attr.disabled]="true"
							[placeholder]="'INVENTORY_PAGE.NAME' | translate"
						/>
					</div>
					<div class="form-group">
						<label class="label" for="widthInput">
							{{ 'INVENTORY_PAGE.WIDTH' | translate }}
						</label>
						<input
							fullWidth
							id="widthInput"
							type="text"
							nbInput
							formControlName="width"
							[attr.disabled]="true"
							[placeholder]="'INVENTORY_PAGE.WIDTH' | translate"
						/>
					</div>
					<div class="form-group">
						<label class="label" for="heightInput">
							{{ 'INVENTORY_PAGE.HEIGHT' | translate }}
						</label>
						<input
							fullWidth
							id="heightInput"
							type="text"
							nbInput
							formControlName="height"
							[attr.disabled]="true"
							[placeholder]="'INVENTORY_PAGE.HEIGHT' | translate"
						/>
					</div>
				</form>
			</div>
		</div>
	</nb-card-body>
	<nb-card-footer>
		<button
			(click)="onCancel()"
			outline
			status="basic"
			class="mr-3"
			nbButton
		>
			{{ 'INVENTORY_PAGE.CANCEL' | translate }}
		</button>
		<button
			(click)="onSaveRequest()"
			status="success"
			nbButton
			[disabled]="!imageAsset"
		>
			{{ 'INVENTORY_PAGE.SAVE' | translate }}
		</button>
	</nb-card-footer>
</nb-card>
