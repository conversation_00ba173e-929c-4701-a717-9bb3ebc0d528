<nb-card style="padding: 0 !important" class="card-body">
	<nb-card-header style="padding: 0.5rem">
		<div class="header-container">
			<ga-notes-with-tags [rowData]="entityWithMembers" [layout]="layout"> </ga-notes-with-tags>
			<div class="members-count">
				{{ 'FORM.PLACEHOLDERS.MEMBERS_COUNT' | translate }}
				{{ entityWithMembers.members ? entityWithMembers.members.length : 0 }}
			</div>
			<ng-content></ng-content>
		</div>
	</nb-card-header>

	<nb-card-body class="team-card">
		<div *ngIf="entityWithMembers.members && entityWithMembers.members.length > 0">
			<div class="name-container" *ngFor="let member of entityWithMembers.members">
				<div class="image-container">
					<a href="/pages/employees/edit/{{ member.id }}/profile" target="_blank">
						<img
							nbTooltip="{{ 'ORGANIZATIONS_PAGE.EDIT.CLICK_EMPLOYEE' | translate }}"
							[src]="member.user.imageUrl"
						/>
					</a>
				</div>
				<span
					>{{ member.user.firstName }}
					{{ member.user.lastName }}
				</span>
			</div>
		</div>

		<div class="button-container">
			<ng-container *ngIf="visibleViewButton">
				<button nbButton class="mr-2" status="info" size="tiny" (click)="navigateContact(entityWithMembers)">
					<nb-icon class="mr-1" icon="eye-outline"></nb-icon>
					{{ 'BUTTONS.VIEW' | translate }}
				</button>
			</ng-container>
			<button nbButton class="mr-2" status="success" size="tiny" (click)="editEntity(entityWithMembers.id)">
				<nb-icon class="mr-1" icon="edit-outline"></nb-icon>{{ 'BUTTONS.EDIT' | translate }}
			</button>
			<button size="tiny" class="mr-2" nbButton (click)="removeEntity(entityWithMembers.id)" status="danger">
				<nb-icon class="mr-2" icon="trash-2-outline"></nb-icon>{{ 'BUTTONS.DELETE' | translate }}
			</button>
		</div>
	</nb-card-body>
</nb-card>
