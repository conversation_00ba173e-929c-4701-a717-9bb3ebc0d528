<div class="group-by-wrapper">
	<div class="label">
		{{ 'REPORT_PAGE.GROUP_BY' | translate }}
	</div>
	<div class="ml-3">
		<nb-select [(ngModel)]="groupBy" (selectedChange)="groupByChange()" optionsListClass="nb-options">
			<nb-option value="date">
				{{ 'REPORT_PAGE.DATE' | translate }}
			</nb-option>
			<nb-option value="employee">
				{{ 'REPORT_PAGE.EMPLOYEE' | translate }}
			</nb-option>
			<nb-option value="project">
				{{ 'REPORT_PAGE.PROJECT' | translate }}
			</nb-option>
		</nb-select>
	</div>
</div>
<div
	class="weekly-logs row-table expense-report-grid"
	[nbSpinner]="loading"
	[nbSpinnerSize]="'giant'"
	[nbSpinnerStatus]="'primary'"
>
	<ng-template #columnsHeader>
		<div class="columns-header" *ngIf="dailyData?.length > 0">
			<div class="table-inner-wrapper font-weight-bold align-items-center">
				<div *ngIf="groupBy !== 'date'" class="responsive-table-row employee-column">
					{{ 'REPORT_PAGE.DATE' | translate }}
				</div>
				<div
					*ngIf="groupBy !== 'employee' && groupBy !== 'project'"
					class="responsive-table-row employee-column"
				>
					{{ 'REPORT_PAGE.EMPLOYEE' | translate }}
				</div>
				<div
					*ngIf="groupBy !== 'employee' && groupBy === 'project'"
					class="responsive-table-row employee-column"
				>
					{{ 'REPORT_PAGE.PROJECT' | translate }}
				</div>
				<div *ngIf="groupBy !== 'project'" class="responsive-table-row project-column">
					{{ 'REPORT_PAGE.PROJECT' | translate }}
				</div>
				<div class="responsive-table-row category-column">
					{{ 'REPORT_PAGE.CATEGORY' | translate }}
				</div>
				<div class="responsive-table-row description-column">
					{{ 'REPORT_PAGE.DESCRIPTION' | translate }}
				</div>
				<div class="responsive-table-row amount-column">
					{{ 'REPORT_PAGE.AMOUNT' | translate }}
				</div>
			</div>
		</div>
	</ng-template>

	<ng-template [ngIf]="dailyData?.length > 0" [ngIfElse]="notFound">
		<nb-card [ngSwitch]="groupBy" class="card" *ngFor="let day of dailyData">
			<ng-container *ngSwitchCase="'employee'">
				<ng-container *ngTemplateOutlet="groupByEmployeeCardEl; context: { $implicit: day }"></ng-container>
			</ng-container>
			<ng-container *ngSwitchCase="'project'">
				<ng-container *ngTemplateOutlet="groupByProjectCardEl; context: { $implicit: day }"></ng-container>
			</ng-container>
			<ng-container *ngSwitchDefault>
				<ng-container *ngTemplateOutlet="groupByDateCardEl; context: { $implicit: day }"></ng-container>
			</ng-container>
		</nb-card>
	</ng-template>
	<ng-template #notFound>
		<ngx-no-data-message [message]="'SM_TABLE.NO_DATA.EXPENSE' | translate"></ngx-no-data-message>
	</ng-template>
</div>

<ng-template let-day #groupByDateCardEl>
	<nb-card-header class="card-title">
		{{ day.date | dateFormat }}
	</nb-card-header>
	<nb-card-body class="expense-report-container">
		<ng-container *ngTemplateOutlet="columnsHeader"></ng-container>
		<div class="cart-body project-row" *ngFor="let employeeRow of day.employees; let employeeIndex = index">
			<div class="table-row" *ngFor="let projectRow of employeeRow.projects; let projectIndex = index">
				<div class="expanse-row" *ngFor="let expanseRow of projectRow.expanse; let expanseIndex = index">
					<div class="table-inner-wrapper">
						<div
							class="responsive-table-row employee-column main-column-responsive responsive-heading-row"
							*ngIf="expanseIndex == 0 && projectIndex == 0; else noEmployee"
						>
							<div class="responsive-table-header">
								{{ 'REPORT_PAGE.EMPLOYEE' | translate }}
							</div>
							<div class="responsive-table-content employee">
								<ng-container
									*ngTemplateOutlet="employeeEl; context: { $implicit: employeeRow?.employee }"
								></ng-container>
							</div>
						</div>
						<ng-template #noEmployee>
							<div class="responsive-table-row employee-column no-height"></div>
						</ng-template>
						<div class="responsive-table-row project-column">
							<div class="responsive-table-header">
								{{ 'REPORT_PAGE.PROJECT' | translate }}
							</div>
							<div class="responsive-table-content project-name">
								<ng-template [ngIf]="expanseIndex == 0">
									<ng-container
										*ngTemplateOutlet="projectEl; context: { $implicit: projectRow?.project }"
									></ng-container>
								</ng-template>
							</div>
						</div>
						<div class="responsive-table-row category-column">
							<div class="responsive-table-header">
								{{ 'REPORT_PAGE.CATEGORY' | translate }}
							</div>
							<div class="responsive-table-content project-name">
								<ng-container *ngIf="expanseRow?.category">
									<span class="category-span">
										{{ expanseRow?.category?.name }}
									</span>
								</ng-container>
							</div>
						</div>
						<div class="responsive-table-row description-column">
							<div class="responsive-table-header">
								{{ 'REPORT_PAGE.DESCRIPTION' | translate }}
							</div>
							<div class="responsive-table-content project-name">
								{{ expanseRow?.purpose ? expanseRow.purpose : '&#8212;' }}
							</div>
						</div>
						<div class="responsive-table-row amount-column">
							<div class="responsive-table-header">
								{{ 'REPORT_PAGE.AMOUNT' | translate }}
							</div>
							<div class="responsive-table-content project-name">
								{{
									expanseRow?.amount
										| currency : expanseRow?.currency
										| position : organization?.currencyPosition
								}}
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</nb-card-body>
</ng-template>

<ng-template let-employee #groupByEmployeeCardEl>
	<nb-card-header class="card-title">
		<ng-container *ngTemplateOutlet="employeeEl; context: { $implicit: employee?.employee }"></ng-container>
	</nb-card-header>
	<nb-card-body>
		<ng-container *ngTemplateOutlet="columnsHeader"></ng-container>
		<div class="cart-body project-row" *ngFor="let dateRow of employee.dates; let dateIndex = index">
			<div class="table-row" *ngFor="let projectRow of dateRow.projects; let projectIndex = index">
				<div
					class="expanse-row date-expanse-row"
					*ngFor="let expanseRow of projectRow.expanse; let expanseIndex = index"
				>
					<div class="table-inner-wrapper">
						<div class="responsive-table-row employee-column responsive-heading-row">
							<div class="responsive-table-header">
								{{ 'REPORT_PAGE.DATE' | translate }}
							</div>
							<div class="responsive-table-content project-name">
								{{ projectIndex == 0 && expanseIndex == 0 ? (dateRow.date | dateFormat) : '' }}
							</div>
						</div>
						<div class="responsive-table-row project-column">
							<div class="responsive-table-header">
								{{ 'REPORT_PAGE.PROJECT' | translate }}
							</div>
							<div class="responsive-table-content project-name">
								<ng-template [ngIf]="expanseIndex == 0">
									<ng-container
										*ngTemplateOutlet="projectEl; context: { $implicit: projectRow?.project }"
									>
									</ng-container>
								</ng-template>
							</div>
						</div>
						<div class="responsive-table-row category-column">
							<div class="responsive-table-header">
								{{ 'REPORT_PAGE.CATEGORY' | translate }}
							</div>
							<div class="responsive-table-content project-name">
								<ng-container *ngIf="expanseRow?.category">
									<span class="category-span">
										{{ expanseRow?.category?.name }}
									</span>
								</ng-container>
							</div>
						</div>
						<div class="responsive-table-row description-column">
							<div class="responsive-table-header">
								{{ 'REPORT_PAGE.DESCRIPTION' | translate }}
							</div>
							<div class="responsive-table-content project-name">
								{{ expanseRow?.purpose ? expanseRow.purpose : '&#8212;' }}
							</div>
						</div>
						<div class="responsive-table-row amount-column">
							<div class="responsive-table-header">
								{{ 'REPORT_PAGE.AMOUNT' | translate }}
							</div>
							<div class="responsive-table-content project-name">
								{{
									expanseRow?.amount
										| currency : expanseRow?.currency
										| position : organization?.currencyPosition
								}}
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</nb-card-body>
</ng-template>

<ng-template let-project #groupByProjectCardEl>
	<nb-card-header class="card-title">
		<ng-container *ngTemplateOutlet="projectEl; context: { $implicit: project?.project }"> </ng-container>
	</nb-card-header>
	<nb-card-body>
		<ng-container *ngTemplateOutlet="columnsHeader"></ng-container>
		<div class="cart-body project-row" *ngFor="let dateRow of project.dates; let dateIndex = index">
			<div class="table-row" *ngFor="let employeeRow of dateRow.employees; let employeeIndex = index">
				<div class="expanse-row" *ngFor="let expanseRow of employeeRow.expanse; let expanseIndex = index">
					<div class="table-inner-wrapper">
						<div
							class="responsive-table-row project-column responsive-heading-row"
							*ngIf="employeeIndex == 0 && expanseIndex == 0; else noDate"
						>
							<div class="responsive-table-header">
								{{ 'REPORT_PAGE.DATE' | translate }}
							</div>
							<div class="responsive-table-content project-name">
								{{ dateRow.date | dateFormat }}
							</div>
						</div>
						<ng-template #noDate>
							<div class="responsive-table-row employee-column no-height"></div>
						</ng-template>
						<div
							class="responsive-table-row employee-column main-column-responsive"
							*ngIf="expanseIndex == 0; else noEmployee"
						>
							<div class="responsive-table-header">
								{{ 'REPORT_PAGE.EMPLOYEE' | translate }}
							</div>
							<div class="responsive-table-content employee">
								<ng-container
									*ngTemplateOutlet="employeeEl; context: { $implicit: employeeRow?.employee }"
								>
								</ng-container>
							</div>
						</div>
						<ng-template #noEmployee>
							<div class="responsive-table-row employee-column no-height"></div>
						</ng-template>
						<div class="responsive-table-row category-column">
							<div class="responsive-table-header">
								{{ 'REPORT_PAGE.CATEGORY' | translate }}
							</div>
							<div class="responsive-table-content project-name">
								<ng-container *ngIf="expanseRow?.category">
									<span class="category-span">
										{{ expanseRow?.category?.name }}
									</span>
								</ng-container>
							</div>
						</div>
						<div class="responsive-table-row description-column">
							<div class="responsive-table-header">
								{{ 'REPORT_PAGE.DESCRIPTION' | translate }}
							</div>
							<div class="responsive-table-content project-name">
								{{ expanseRow?.purpose ? expanseRow.purpose : '&#8212;' }}
							</div>
						</div>
						<div class="responsive-table-row amount-column">
							<div class="responsive-table-header">
								{{ 'REPORT_PAGE.AMOUNT' | translate }}
							</div>
							<div class="responsive-table-content project-name">
								{{
									expanseRow?.amount
										| currency : expanseRow?.currency
										| position : organization?.currencyPosition
								}}
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</nb-card-body>
</ng-template>

<ng-template let-project #projectEl>
	<ga-project-column-view *ngIf="project; else noProjects" [project]="project"></ga-project-column-view>
	<ng-template #noProjects>
		<span>{{ 'REPORT_PAGE.NO_PROJECT' | translate }}</span>
	</ng-template>
</ng-template>

<ng-template let-client #clientEl>
	<span *ngIf="client; else noClient">
		<ngx-contact-links [value]="client"></ngx-contact-links>
	</span>
	<ng-template #noClient>
		<span>
			{{ 'REPORT_PAGE.NO_CLIENT' | translate }}
		</span>
	</ng-template>
</ng-template>

<ng-template let-employee #employeeEl>
	<div class="avatar-wrapper-outer">
		<ngx-avatar
			class="report-table"
			*ngIf="employee"
			[src]="employee?.user?.imageUrl"
			[name]="employee?.user?.name"
			[id]="employee?.id"
			[employee]="employee"
		></ngx-avatar>
	</div>
	<ng-template #noEmployee>
		<span>{{ 'REPORT_PAGE.NO_EMPLOYEE' | translate }}</span>
	</ng-template>
</ng-template>
