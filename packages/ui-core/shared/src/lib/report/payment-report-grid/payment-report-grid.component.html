<div class="d-flex align-items-center mb-3">
	<div class="label">
		{{ 'REPORT_PAGE.GROUP_BY' | translate }}
	</div>
	<div class="ml-3">
		<nb-select
			[(ngModel)]="groupBy"
			(selectedChange)="groupByChange()"
		>
			<nb-option value="date">
				{{ 'REPORT_PAGE.DATE' | translate }}
			</nb-option>
			<nb-option value="client">
				{{ 'REPORT_PAGE.CLIENT' | translate }}
			</nb-option>
			<nb-option value="project">
				{{ 'REPORT_PAGE.PROJECT' | translate }}
			</nb-option>
		</nb-select>
	</div>
</div>
<div
	class="weekly-logs row-table payment-report-grid"
	[nbSpinner]="loading"
	nbSpinnerSize="giant"
	nbSpinnerStatus="primary"
>
	<ng-template [ngIf]="dailyData?.length > 0" [ngIfElse]="notFound">
		<nb-card
			[ngSwitch]="groupBy"
			class="card"
			*ngFor="let day of dailyData"
		>
			<ng-container *ngSwitchCase="'client'">
				<ng-container
					*ngTemplateOutlet="
						groupByClientCardEl;
						context: { $implicit: day }
					"
				></ng-container>
			</ng-container>
			<ng-container *ngSwitchCase="'project'">
				<ng-container
					*ngTemplateOutlet="
						groupByProjectCardEl;
						context: { $implicit: day }
					"
				></ng-container>
			</ng-container>
			<ng-container *ngSwitchDefault>
				<ng-container
					*ngTemplateOutlet="
						groupByDateCardEl;
						context: { $implicit: day }
					"
				></ng-container>
			</ng-container>
		</nb-card>
	</ng-template>
	<ng-template #notFound>
		<ngx-no-data-message
			[message]="'SM_TABLE.NO_DATA.PAYMENT' | translate"
		></ngx-no-data-message>
	</ng-template>
</div>

<ng-template let-day #groupByDateCardEl>
	<nb-card-header class="card-title">
		{{ day.date | dateFormat }}
	</nb-card-header>
	<nb-card-body class="budget-container">
		<ng-container *ngTemplateOutlet="columnsHeader"></ng-container>
		<div class="cart-body project-row" *ngFor="let clientRow of day.clients; let clientIndex = index">
			<div class="project-row" *ngFor="let projectRow of clientRow.projects; let projectIndex = index">
				<div class="table-row" *ngFor="let paymentRow of projectRow.payments; let paymentIndex = index">
					<div class="table-inner-wrapper">
						<div class="responsive-table-row employee-column">
							<div class="responsive-table-header">
								{{ 'REPORT_PAGE.CONTACT' | translate }}
							</div>
							<div class="responsive-table-content project-name">
								<ng-container
										*ngTemplateOutlet="
											clientEl;
											context: { $implicit: paymentRow?.organizationContact }">
								</ng-container>
							</div>
						</div>
						<div class="responsive-table-row project-column">
							<div class="responsive-table-header">
								{{ 'REPORT_PAGE.PROJECT' | translate }}
							</div>
							<div class="responsive-table-content project-name">
								<ng-template [ngIf]="paymentIndex == 0">
									<ng-container
											*ngTemplateOutlet="
												projectEl;
												context: { $implicit: projectRow?.project }">
									</ng-container>
								</ng-template>
							</div>
						</div>
						<div class="responsive-table-row currency-column">
							<div class="responsive-table-header">{{ 'REPORT_PAGE.CURRENCY' | translate }}</div>
							<div class="responsive-table-content project-name">
								{{ paymentRow?.currency }}
							</div>
						</div>
						<div class="responsive-table-row note-column">
							<div class="responsive-table-header">{{ 'REPORT_PAGE.NOTE' | translate }}</div>
							<div class="responsive-table-content project-name">
								{{ paymentRow?.note }}
							</div>
						</div>
						<div class="responsive-table-row amount-column">
							<div class="responsive-table-header">{{ 'REPORT_PAGE.AMOUNT' | translate }}</div>
							<div class="responsive-table-content project-name">
								{{ paymentRow?.amount
									| currency: paymentRow?.currency
									| position: organization?.currencyPosition
								}}
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</nb-card-body>
</ng-template>

<ng-template let-client #groupByClientCardEl>
	<nb-card-header class="card-title">
		<ng-container *ngTemplateOutlet="clientEl; context: { $implicit: client?.client }">
		</ng-container>
	</nb-card-header>
	<nb-card-body>
		<ng-container *ngTemplateOutlet="columnsHeader"></ng-container>
		<div class="cart-body project-row" *ngFor="let dateRow of client.dates; let dateIndex = index">
			<div class="project-row" *ngFor="let projectRow of dateRow.projects;let projectIndex = index">
				<div class="table-row" *ngFor="let paymentRow of projectRow.payments;let paymentIndex = index">
					<div class="table-inner-wrapper">
						<div class="responsive-table-row employee-column">
							<div class="responsive-table-header">
								{{ 'REPORT_PAGE.DATE' | translate }}
							</div>
							<div class="responsive-table-content project-name">
								{{ projectIndex == 0 && paymentIndex == 0
										? (dateRow.date | dateFormat)
										: ''
								}}
							</div>
						</div>
						<div class="responsive-table-row project-column">
							<div class="responsive-table-header">{{ 'REPORT_PAGE.PROJECT' | translate }}</div>
							<div class="responsive-table-content project-name">
								<ng-template [ngIf]="paymentIndex == 0">
									<ng-container
											class="project-span"
											*ngTemplateOutlet="
												projectEl;
												context: { $implicit: projectRow?.project }">
									</ng-container>
								</ng-template>
							</div>
						</div>
						<div class="responsive-table-row currency-column">
							<div class="responsive-table-header">{{ 'REPORT_PAGE.CURRENCY' | translate }}</div>
							<div class="responsive-table-content project-name">
								{{ paymentRow?.currency }}
							</div>
						</div>
						<div class="responsive-table-row note-column">
							<div class="responsive-table-header">{{ 'REPORT_PAGE.NOTE' | translate }}</div>
							<div class="responsive-table-content project-name">
								{{ paymentRow?.note }}
							</div>
						</div>
						<div class="responsive-table-row amount-column">
							<div class="responsive-table-header">{{ 'REPORT_PAGE.AMOUNT' | translate }}</div>
							<div class="responsive-table-content project-name">
								{{ paymentRow?.amount
									| currency: paymentRow?.currency
									| position: organization?.currencyPosition
								}}
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</nb-card-body>
</ng-template>

<ng-template let-project #groupByProjectCardEl>
	<nb-card-header class="card-title">
		<ng-container
			*ngTemplateOutlet="
				projectEl;
				context: { $implicit: project?.project }
			"
		>
		</ng-container>
	</nb-card-header>
	<nb-card-body>
		<ng-container *ngTemplateOutlet="columnsHeader"></ng-container>
		<div class="cart-body project-row" *ngFor="let dateRow of project.dates; let dateIndex = index">
			<div class="project-row" *ngFor="let clientRow of dateRow.clients; let clientIndex = index">
				<div class="table-row" *ngFor="let paymentRow of clientRow.payments; let paymentIndex = index">
					<div class="table-inner-wrapper">
						<div class="responsive-table-row employee-column">
							<div class="responsive-table-header">
								{{ 'REPORT_PAGE.DATE' | translate }}
							</div>
							<div class="responsive-table-content project-name">
								{{ clientIndex == 0 && paymentIndex == 0
									? (dateRow.date | dateFormat)
									: ''
								}}
							</div>
						</div>
						<div class="responsive-table-row employee-column">
							<div class="responsive-table-header">{{ 'REPORT_PAGE.EMPLOYEE' | translate }}</div>
							<div class="responsive-table-content project-name">
								<ng-template [ngIf]="paymentIndex == 0">
									<ng-container
											*ngTemplateOutlet="
											clientEl;
											context: { $implicit: paymentRow?.organizationContact }">
									</ng-container>
								</ng-template>
							</div>
						</div>
						<div class="responsive-table-row currency-column">
							<div class="responsive-table-header">{{ 'REPORT_PAGE.CURRENCY' | translate }}</div>
							<div class="responsive-table-content project-name">
								{{ paymentRow?.currency }}
							</div>
						</div>
						<div class="responsive-table-row note-column">
							<div class="responsive-table-header">{{ 'REPORT_PAGE.NOTE' | translate }}</div>
							<div class="responsive-table-content project-name">
								{{ paymentRow?.note }}
							</div>
						</div>
						<div class="responsive-table-row amount-column">
							<div class="responsive-table-header">{{ 'REPORT_PAGE.AMOUNT' | translate }}</div>
							<div class="responsive-table-content project-name">
								{{ paymentRow?.amount
									| currency: paymentRow?.currency
									| position: organization?.currencyPosition
								}}
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</nb-card-body>
</ng-template>

<ng-template let-project #projectEl>
	<ga-project-column-view
		*ngIf="project; else noProjects"
		[project]="project"
	></ga-project-column-view>
	<ng-template #noProjects>
		<span>
			{{ 'REPORT_PAGE.NO_PROJECT' | translate }}
		</span>
	</ng-template>
</ng-template>

<ng-template let-client #clientEl>
	<span *ngIf="client; else noClient">
		{{ client?.name }}
	</span>
	<ng-template #noClient>
		<span>
			{{ 'REPORT_PAGE.NO_CLIENT' | translate }}
		</span>
	</ng-template>
</ng-template>

<ng-template #columnsHeader>
	<div class="columns-header" *ngIf="dailyData?.length > 0">
		<div class="table-inner-wrapper font-weight-bold align-items-center">
			<div *ngIf="groupBy != 'date'" class="responsive-table-row employee-column">
				{{ 'REPORT_PAGE.DATE' | translate }}
			</div>
			<div *ngIf="groupBy !== 'client' && groupBy !== 'project'" class="responsive-table-row employee-column">
				{{ 'REPORT_PAGE.CONTACT' | translate }}
			</div>
			<div *ngIf="groupBy !== 'client' && groupBy === 'project'" class="responsive-table-row employee-column">
				{{ 'REPORT_PAGE.PROJECT' | translate }}
			</div>
			<div *ngIf="groupBy !== 'project'" class="responsive-table-row project-column">
				{{ 'REPORT_PAGE.PROJECT' | translate }}
			</div>
			<div class="responsive-table-row currency-column">
				{{ 'REPORT_PAGE.CURRENCY' | translate }}
			</div>
			<div class="responsive-table-row note-column">
				{{ 'REPORT_PAGE.NOTE' | translate }}
			</div>
			<div class="responsive-table-row amount-column">
				{{ 'REPORT_PAGE.AMOUNT' | translate }}
			</div>
		</div>
	</div>
</ng-template>
