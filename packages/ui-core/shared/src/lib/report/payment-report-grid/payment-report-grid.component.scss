@forward 'report';

:host {
  display: block;

  .report-row {
    min-height: 56px;
  }
  ::ng-deep .payment-report-grid .no-data-found {
  	height: calc(100vh - 50.65rem) !important;
  }
}

.employee-column {
  width: 20%;
  min-width: 165px;
  max-width: 200px;
}
.project-column {
  width: 35%;
  min-width: 35%;

}
.currency-column {
  width: 15%;
}
.note-column {
  width: 20%;
}
.amount-column {
  width: 10%;
}
.weekly-logs{
	display: flex;
	flex-direction: column;
	gap: 1rem;
}
