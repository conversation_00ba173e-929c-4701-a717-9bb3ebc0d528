@forward 'report';

:host {
  display: block;

  .report-row {
    min-height: 56px;
  }
  ::ng-deep .amount-owed-grid .no-data-found {
	  height: calc(100vh - 45.75rem) !important;
  }
}

.employee-column {
  width: 15%;
  min-width: 165px;
  max-width: 185px;
}
.rate-column {
  width: 30%;
  min-width: 30%;
}
.duration-column {
  width: 30%;
}
.amount-column {
  width: 25%;
}

.weekly-logs {
	display: flex;
	flex-direction: column;
	gap: 1rem;
}

.amounts-owed-container{
	padding-bottom: 6px !important;
}
