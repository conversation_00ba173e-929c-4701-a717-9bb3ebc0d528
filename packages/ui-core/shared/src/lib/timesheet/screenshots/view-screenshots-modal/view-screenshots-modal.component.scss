@use 'themes' as *;
@use 'gauzy/_gauzy-table' as *;

:host {
	display: block;
	position: relative;
	width: 700px;
	margin: auto;
	overflow: auto;
	.close-button {
		position: absolute;
		top: 3px;
		right: 5px;
		z-index: 9;
	}
  	.screenshot {
		position: relative;
		width: 150px;
		object-fit: cover;
		& .select-hidden {
			display: none;
			position: absolute;
			right: 3px;
			top: 3px;
		}
		&:hover, &:focus {
			& .select-hidden {
				display: flex;
			}
		}
		& img {
			width: 100%;
			height: 100%;
			object-fit: cover;
			border-radius: var(--border-radius);
		}
		nb-card {
			background-color: var(--gauzy-card-1);
		}
		.info {
			right: 36px;
		}
  	}
	::ng-deep {
		.progress-value {
			span {
				display: none;
			}
		}
	}
	.card {
		background-color: var(--gauzy-card-2);
		border-radius: var(--border-radius);
		border: unset;
	}
	.caption {
		font-size: 11px;
		font-weight: 400;
		line-height: 11px;
		letter-spacing: 0em;
		color: var(--gauzy-text-color-2);
	}
	.badge {
		color: var(--tag-filled-basic-text-color);
		display: inline-block;
		padding: 6px 4px;
		background-color: var(--tag-filled-basic-background-color);
		border-radius: 4px;
		margin: 5px;
	}
	nb-badge {
		position: relative;
		font-size: 14px;
		font-weight: 600;
		padding: 2px 4px;
		border-radius: calc(var(--border-radius) / 2);
	}
	nb-card-header,
	nb-card-body,
	nb-card-footer {
		padding: 1rem;
	}
	.subtitle {
		font-size: 12px;
		font-weight: 600;
		line-height: 13px;
		letter-spacing: -0.01em;
		text-align: left;
		margin-bottom: 10px;
		color: var(--gauzy-text-color-2);
	}
	.screenshot {
		&.danger-bordered {
			img {
				border: 2px solid nb-theme(color-danger-500) !important;
			}
		}
    }
}
