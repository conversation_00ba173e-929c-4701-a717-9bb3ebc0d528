<div class="card" [ngClass]="{ 'danger-bordered': isShowBorder }">
	<div class="activity" *ngIf="timeSlot; else noActivity">
		<ng-template [ngIf]="employees && employees.length > 1" [ngIfElse]="lastScreenshotTemplate">
			<div
				class="image-container curser-pointer"
				[nbPopover]="workedEmployeeTemplate"
				[nbPopoverTrigger]="'hint'"
			>
				<img draggable="false" [src]="lastScreenshot?.thumbUrl" />
				<span class="screenshot-count">
					<i class="fas fa-users"></i>
					{{ employees.length }}
				</span>
			</div>
		</ng-template>
		<ng-template #lastScreenshotTemplate>
			<div
				class="image-container curser-pointer"
				[ngClass]="{ 'select-mode': selectionMode }"
				(click)="toggleSelect(timeSlot)"
			>
				<div class="hover">
					<div class="actions d-flex align-items-center w-100">
						<ng-container *ngIf="timeSlot?.isAllowDelete">
							<ng-container *ngIf="multiple">
								<nb-checkbox
									size="tiny"
									[(ngModel)]="isSelected"
									(click)="$event.stopPropagation()"
									(ngModelChange)="toggleSelect(timeSlot)"
								></nb-checkbox>
							</ng-container>
							<button
								class="ml-auto select-hidden"
								status="danger"
								nbButton
								size="tiny"
								(click)="$event.stopPropagation()"
								ngxConfirmDialog
								[message]="'ACTIVITY.DELETE_CONFIRM' | translate"
								(confirm)="deleteSlot(timeSlot)"
							>
								<nb-icon icon="trash-2-outline"></nb-icon>
							</button>
						</ng-container>
						<button
							*ngIf="lastScreenshot?.description"
							class="ml-2"
							status="info"
							nbButton
							size="tiny"
							[nbTooltip]="lastScreenshot?.description"
						>
							<nb-icon icon="info-outline" size="small"></nb-icon>
						</button>
					</div>
					<div class="view-button select-hidden">
						<ng-container *ngIf="timeSlot?.screenshots?.length > 0">
							<button
								class="mr-1"
								nbButton
								size="small"
								status="primary"
								(click)="$event.stopPropagation()"
								ngxGallery
								[employeeId]="employeeId"
								[items]="screenshots"
								[item]="lastScreenshot"
							>
								{{ 'ACTIVITY.VIEW_SCREEN' | translate }}
							</button>
						</ng-container>
						<button
							nbButton
							size="small"
							status="secondary"
							(click)="$event.stopPropagation(); viewInfo(timeSlot)"
						>
							{{ 'ACTIVITY.VIEW_INFO' | translate }}
						</button>
					</div>
				</div>
				<ng-template [ngIf]="lastScreenshot" [ngIfElse]="noImageEl">
					<img draggable="false" [src]="lastScreenshot?.thumbUrl" />
				</ng-template>
				<ng-template #noImageEl>
					<img draggable="false" [src]="fallbackSvg" class="default-image" />
					<span class="no-image p-3">
						{{ 'ACTIVITY.NO_SCREENSHOT' | translate }}
					</span>
				</ng-template>
			</div>
		</ng-template>
		<div class="slot-info text-left p-3">
			<div class="time-span mb-4 mt-1 hour-label">
				<div class="inline-time-span">
					{{ timeSlot.startedAt | utcToTimezone : timezone | timeFormat : this.timeFormat }} -
					{{ timeSlot.stoppedAt | utcToTimezone : timezone | timeFormat : this.timeFormat }}
				</div>
				<div class="text-caption caption mt-2">
					{{ timeSlot.startedAt | utcToTimezone : timezone | dateFormat }}
				</div>
			</div>
			<nb-progress-bar
				class="mb-1"
				[value]="timeSlot.percentage"
				[status]="progressStatus(timeSlot.percentage)"
				[displayValue]="true"
				size="tiny"
			></nb-progress-bar>
			<div class="activity-count hour-label mt-2">
				{{ timeSlot.percentage || 0 }}% of
				{{ timeSlot.duration | amFromUnix | amFromUtc | amDateFormat : 'mm' }}
				{{ 'ACTIVITY.MINUTES' | translate }}
			</div>
		</div>
	</div>
	<ng-template #noActivity>
		<div class="no-activity p-3 text-center">
			{{ 'ACTIVITY.NO_ACTIVITY' | translate }}
		</div>
	</ng-template>
</div>
<ng-template #workedEmployeeTemplate>
	<nb-list>
		<nb-list-item *ngFor="let employee of employees" class="row border-top align-items-center">
			<ngx-employee-links [value]="employee?.user" [isNavigation]="false"></ngx-employee-links>
		</nb-list-item>
	</nb-list>
</ng-template>
