import { Component, OnInit, Input, OnDestroy, Output, EventEmitter } from '@angular/core';
import { NbDialogService } from '@nebular/theme';
import { filter, take, tap } from 'rxjs/operators';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { sortBy } from 'underscore';
import { ITimeSlot, IScreenshot, ITimeLog, IOrganization, IEmployee, TimeFormatEnum } from '@gauzy/contracts';
import { DEFAULT_SVG, distinctUntilChange, isNotEmpty, progressStatus } from '@gauzy/ui-core/common';
import { ErrorHandlingService, Store, TimesheetService, ToastrService } from '@gauzy/ui-core/core';
import { GalleryItem } from '../../../gallery/gallery.directive';
import { ViewScreenshotsModalComponent } from '../view-screenshots-modal/view-screenshots-modal.component';
import { GalleryService } from '../../../gallery/gallery.service';
import { TimeZoneService } from '../../gauzy-filters/timezone-filter';

@UntilDestroy({ checkProperties: true })
@Component({
    selector: 'ngx-screenshots-item',
    templateUrl: './screenshots-item.component.html',
    styleUrls: ['./screenshots-item.component.scss'],
    standalone: false
})
export class ScreenshotsItemComponent implements OnInit, OnDestroy {
	public isShowBorder: boolean = false;
	public organization: IOrganization;
	progressStatus = progressStatus;
	fallbackSvg = DEFAULT_SVG;

	/*
	 * Getter & Setter for dynamic enabled/disabled element
	 */
	_employees: IEmployee[] = [];
	get employees(): IEmployee[] {
		return this._employees;
	}
	@Input() set employees(employees: IEmployee[]) {
		this._employees = employees;
	}

	@Input() multiple: boolean = true;
	@Input() selectionMode = false;
	@Input() galleryItems: GalleryItem[] = [];
	@Input() isSelected: boolean;
	@Input() employeeId: IEmployee['id'];

	@Output() delete: EventEmitter<any> = new EventEmitter();
	@Output() toggle: EventEmitter<any> = new EventEmitter();

	/*
	 * Getter & Setter for TimeSlot
	 */
	private _timeSlot: ITimeSlot;
	get timeSlot(): ITimeSlot {
		return this._timeSlot;
	}
	@Input() set timeSlot(timeSlot: ITimeSlot) {
		if (!timeSlot) return; // If timeSlot is falsy, return early

		// Create a deep copy of the screenshots to avoid modifying the original array
		let screenshots = JSON.parse(JSON.stringify(timeSlot.screenshots));

		// Map each screenshot with additional properties and employeeId
		this.screenshots =
			screenshots.map((screenshot: IScreenshot) => ({
				employeeId: timeSlot.employeeId,
				...screenshot
			})) || [];

		if (isNotEmpty(this.screenshots)) {
			// Check if all screenshots have isWorkRelated as false
			this.isShowBorder = this.screenshots.every((screenshot: IScreenshot) => screenshot.isWorkRelated === false);
		}

		// Assign a new object to _timeSlot with modified properties
		this._timeSlot = {
			...timeSlot,
			isAllowDelete: this.isEnableDelete(timeSlot),
			screenshots: this.screenshots
		};

		// Sort screenshots by recordedAt in descending order
		screenshots = sortBy(screenshots, 'recordedAt').reverse();

		// Update lastScreenshot with the first screenshot if available
		this.lastScreenshot = screenshots.length > 0 ? screenshots[0] : null;
	}

	/*
	 * Getter & Setter for Screenshots
	 */
	private _screenshots: IScreenshot[] = [];
	get screenshots(): IScreenshot[] {
		return this._screenshots;
	}
	set screenshots(screenshots: IScreenshot[]) {
		this._screenshots = screenshots;
	}

	/*
	 * Getter & Setter for Screenshot
	 */
	private _lastScreenshot: IScreenshot;
	get lastScreenshot(): IScreenshot {
		return this._lastScreenshot;
	}
	set lastScreenshot(screenshot: IScreenshot) {
		this._lastScreenshot = screenshot;
	}

	@Input() timezone: string = this._timeZoneService.currentTimeZone;
	@Input() timeFormat: TimeFormatEnum = TimeFormatEnum.FORMAT_12_HOURS;

	constructor(
		private readonly _nbDialogService: NbDialogService,
		private readonly _timesheetService: TimesheetService,
		private readonly _galleryService: GalleryService,
		private readonly _toastrService: ToastrService,
		private readonly _errorHandlingService: ErrorHandlingService,
		private readonly _store: Store,
		private readonly _timeZoneService: TimeZoneService
	) {}

	ngOnInit(): void {
		this._store.selectedOrganization$
			.pipe(
				filter((organization: IOrganization) => !!organization),
				distinctUntilChange(),
				tap((organization: IOrganization) => (this.organization = organization)),
				untilDestroyed(this)
			)
			.subscribe();
	}

	/**
	 * Toggles the selection of a time slot.
	 * If the time slot allows deletion, it emits the time slot's ID.
	 *
	 * @param {ITimeSlot} timeSlot - The time slot to toggle.
	 */
	toggleSelect(timeSlot: ITimeSlot): void {
		if (timeSlot.isAllowDelete) {
			const slotId = timeSlot.id;
			this.toggle.emit(slotId);
		}
	}

	/**
	 * Deletes a time slot if deletion is allowed and handles related tasks.
	 *
	 * @param timeSlot The time slot to be deleted.
	 */
	async deleteSlot(timeSlot: ITimeSlot): Promise<void> {
		if (!timeSlot.isAllowDelete) {
			return;
		}

		try {
			// Destructure the organization ID and tenant ID from the organization object
			const { id: organizationId, tenantId } = this.organization;

			// Delete time slots
			await this._timesheetService.deleteTimeSlots({
				ids: [timeSlot.id],
				organizationId,
				tenantId
			});

			// Remove related screenshots from the gallery
			const screenshotsToRemove = timeSlot.screenshots.map((screenshot) => ({
				thumbUrl: screenshot.thumbUrl,
				fullUrl: screenshot.fullUrl,
				...screenshot
			}));
			this._galleryService.removeGalleryItems(screenshotsToRemove);

			// Display success message
			const employeeName = timeSlot.employee?.fullName?.trim() || 'Unknown Employee';

			// Display success message
			this._toastrService.success('TOASTR.MESSAGE.SCREENSHOT_DELETED', {
				name: employeeName,
				organization: this.organization.name
			});

			// Trigger delete event
			this.delete.emit();
		} catch (error) {
			console.log('Error while deleting time slot', error);
			this._errorHandlingService.handleError(error);
		}
	}

	/**
	 * Opens a modal to view information about the provided time slot.
	 *
	 * @param timeSlot - The time slot for which information is to be viewed.
	 */
	viewInfo(timeSlot: ITimeSlot): void {
		const dialog$ = this._nbDialogService.open(ViewScreenshotsModalComponent, {
			context: {
				timeSlot,
				timeLogs: timeSlot.timeLogs
			}
		});
		dialog$.onClose
			.pipe(
				filter((data) => Boolean(data && data['isDelete'])),
				tap(() => this.delete.emit()),
				take(1),
				untilDestroyed(this)
			)
			.subscribe();
	}

	/**
	 * Checks if the provided time slot can be deleted based on certain conditions.
	 *
	 * @param timeSlot - The time slot to be checked for deletion.
	 * @returns True if deletion is allowed, false otherwise.
	 */
	isEnableDelete(timeSlot: ITimeSlot): boolean {
		return timeSlot.timeLogs.every((log: ITimeLog) => !log.isRunning);
	}

	ngOnDestroy(): void {}
}
