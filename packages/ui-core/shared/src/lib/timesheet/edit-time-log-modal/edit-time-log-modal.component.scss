@use 'gauzy/_gauzy-dialogs' as *;
@use 'gauzy/_gauzy-table' as *;

:host {
  nb-card {
    width: 645px;
    background-color: nb-theme(gauzy-card-1);
  }

  ::ng-deep label {
    font-size: 12px;
    font-weight: 600;
    line-height: 15px;
    letter-spacing: 0em;
    color: var(--gauzy-text-color-2);
  }

  .col-12,
  .col-6 {
    padding: 0;
  }

  .custom-card {
    background-color: nb-theme(gauzy-card-2);

    .custom-card-body {
      max-height: unset;
      height: unset;
    }
  }
}

.range-picker-row {
  padding: 0 15px;
  .show-time {
    display: flex;
    justify-content: flex-end;
    flex-direction: column;
    padding: 1.5rem 2rem;
  }

  .show-time div {
    background: var(--color-primary-transparent-100);
    width: fit-content;
    padding: 4px 6px;
    border-radius: var(--border-radius);
    margin-top: 6px;
    font-weight: 600;
    color: var(--text-primary-color);
  }
}
