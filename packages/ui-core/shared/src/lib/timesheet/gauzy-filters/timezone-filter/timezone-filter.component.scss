@use 'gauzy/_gauzy-cards' as *;

:host {
  .popover-button {
    font-size: 12px;
    font-weight: 400;
    &[nbButton] {
      color: var(--gauzy-text-color-1);
      background-color: var(--gauzy-card-1);
    }
    nb-icon {
      height: 11px;
      width: 11px;
    }
  }
}
.popover-body {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 10px;
  gap: 10px;
  min-width: 150px;
  .category {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
    width: 100%;
    .view {
      font-size: 10px;
      font-weight: 600;
      line-height: 12px;
      letter-spacing: 0em;
      color: rgba(126, 126, 143, 0.5);
      align-items: center;
      display: flex;
      justify-content: space-between;
      gap: 1rem;
      width: 100%;
    }
    .title {
      font-size: 12px;
      font-weight: 400;
      line-height: 12px;
      letter-spacing: 0em;
      color: var(--gauzy-text-color-2);
      display: flex;
      align-items: center;
      gap: 10px;
      cursor: pointer;
      width: 100%;
    }
  }
  .line {
    border-bottom: 0.5px solid rgba(126, 126, 143, 0.25);
    width: 100%;
  }
}
