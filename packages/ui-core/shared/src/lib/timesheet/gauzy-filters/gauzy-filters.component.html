<div class="row col-auto main-wrapper">
	<div class="col-auto ml-auto">
		<div class="row filter-item-list align-items-end">
			<ng-container *ngIf="hasTimeZoneFilter">
				<div class="col-auto single-filter-wrapper">
					<ga-timezone-filter
						[isTimeFormat]="isTimeFormat"
						(timeFormatChange)="timeFormatChanged($event)"
						(timeZoneChange)="timeZoneChanged($event)"
					></ga-timezone-filter>
				</div>
			</ng-container>
			<ng-container *ngIf="hasSourceFilter">
				<div class="col-auto filter-item single-filter-wrapper">
					<nb-select
						multiple
						[placeholder]="'TIMESHEET.SELECT_SOURCE' | translate"
						[(selected)]="filters.source"
						(selectedChange)="triggerFilterChange()"
					>
						<nb-option *ngFor="let source of timeLogSourceSelectors" [value]="source.value">
							{{ source.label | replace : '_' : ' ' | titlecase }}
						</nb-option>
					</nb-select>
				</div>
			</ng-container>
			<ng-container *ngIf="hasActivityLevelFilter">
				<div class="col-auto filter-item single-filter-wrapper">
					<button
						class="activity-level-filter text-capitalize"
						nbButton
						status="basic"
						outline
						nbPopoverPlacement="bottom"
						[nbPopover]="activityLevelSliderTemplate"
						nbPopoverTrigger="click"
					>
						<span
							*ngIf="activityLevel?.start > 0 || activityLevel?.end < 100; else selectActivityLevelLabel"
						>
							{{ 'TIMESHEET.ACTIVITY_LEVEL' | translate }} : {{ activityLevel?.start }}% -
							{{ activityLevel?.end }}%
						</span>
						<nb-icon icon="chevron-down-outline"></nb-icon>
					</button>
				</div>
				<ng-template #selectActivityLevelLabel>
					{{ 'TIMESHEET.SELECT_ACTIVITY_LEVEL' | translate }}
				</ng-template>
			</ng-container>
			<ng-container *ngIf="hasLogTypeFilter">
				<div class="col-auto filter-item single-filter-wrapper">
					<nb-select
						multiple
						[placeholder]="'TIMESHEET.SELECT_LOG_TYPE' | translate"
						[(selected)]="filters.logType"
						(selectedChange)="triggerFilterChange()"
					>
						<nb-option *ngFor="let logType of TimeLogType | keyvalue" [value]="logType.key">
							{{ logType.value | titlecase }}
						</nb-option>
					</nb-select>
				</div>
			</ng-container>
			<ng-container *ngIf="hasFilterApplies">
				<div class="col-auto single-filter-wrapper clear-fitlers">
					<button nbButton status="danger" (click)="clearFilters()">
						{{ 'BUTTONS.CLEAR' | translate }}
					</button>
				</div>
			</ng-container>
		</div>
	</div>
</div>

<ng-template #activityLevelSliderTemplate>
	<div class="p-3 slider-dropdown">
		<ngx-slider
			[value]="activityLevel?.start"
			[highValue]="activityLevel?.end"
			(userChange)="setActivityLevel($event)"
			[options]="sliderOptions"
		></ngx-slider>
	</div>
</ng-template>
