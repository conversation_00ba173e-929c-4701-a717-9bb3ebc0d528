@use 'themes';
$status: 'basic';
:host {
  .filters {
    .form-control {
      min-height: 40px;
    }
  }
  .select-box {
    display: block;
    width: 300px;
  }
  .week-date-input {
    position: relative;
    overflow: hidden;
    height: auto;
    border-radius: 4px;
    input {
      opacity: 0;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
    }
  }
  .date-range-input {
    min-width: 300px;
  }
  .filter-item-list {
    .filter-item {
      min-width: 220px;
    }
    .select-box,
    nb-select {
      max-width: 100%;
      width: 100%;
      display: block;
    }
  }
  .activity-level-filter {
    background-color: nb-theme(select-outline-#{$status}-background-color);
    border-color: nb-theme(select-outline-#{$status}-border-color);
    color: nb-theme(select-outline-#{$status}-text-color);

    &.placeholder {
      color: nb-theme(select-outline-#{$status}-placeholder-text-color);
    }
    nb-icon {
      color: nb-theme(select-outline-#{$status}-icon-color);
    }

    &:focus {
      background-color: nb-theme(select-outline-#{$status}-focus-background-color);
      border-color: nb-theme(select-outline-#{$status}-focus-border-color);
    }
    &:hover {
      background-color: nb-theme(select-outline-#{$status}-hover-background-color);
      border-color: nb-theme(select-outline-#{$status}-hover-border-color);
    }

    &[disabled] {
      color: nb-theme(select-outline-#{$status}-disabled-text-color);
      background-color: nb-theme(select-outline-#{$status}-disabled-background-color);
      border-color: nb-theme(select-outline-#{$status}-disabled-border-color);

      nb-icon {
        color: nb-theme(select-outline-#{$status}-disabled-icon-color);
      }
    }

    &.bottom,
    &.top {
      border-color: nb-theme(select-outline-#{$status}-open-border-color);
    }

    &.top {
      border-top-color: nb-theme(select-outline-#{$status}-adjacent-border-color);
    }
    &.bottom {
      border-bottom-color: nb-theme(select-outline-#{$status}-adjacent-border-color);
    }
  }
}
::ng-deep {
  .slider-dropdown {
    width: 300px;
  }
}
button.activity-level-filter.text-capitalize.appearance-outline.size-medium.shape-rectangle.icon-end.status-basic.nb-transition {
  inline-size: -webkit-fill-available;
  overflow: hidden;
}
