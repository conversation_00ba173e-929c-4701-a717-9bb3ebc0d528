<nb-card>
	<nb-card-header *ngxPermissionsOnly="[PermissionsEnum.CHANGE_SELECTED_EMPLOYEE]">
		<span class="cancel">
			<i class="fas fa-times" (click)="close()"></i>
		</span>
		<h5 class="title">
			{{ 'TIMESHEET.VIEW_LOG' | translate }}
		</h5>
		<div class="row mt-5 avatar-log">
			<div class="col-6">
				<ngx-avatar
					[id]="timeLog?.employee?.id"
					[employee]="timeLog?.employee"
					[name]="timeLog?.employee?.user?.name"
					[src]="timeLog?.employee?.user?.imageUrl"
					class="report-table"
				></ngx-avatar>
			</div>
			<div class="col-3 p-0">
				<ngx-badge-label
					[status]="TimeLogsLabel[timeLog?.logType].status"
					[text]="TimeLogsLabel[timeLog?.logType].text"
				></ngx-badge-label>
			</div>
		</div>

		<div class="row date">
			<div class="d-flex flex-column col-12">
				<small>{{ timeLog?.startedAt | dateFormat }}</small>
				<small class="duration">{{
					(timeLog?.startedAt | utcToLocal | timeFormat : true) +
						' - ' +
						(!timeLog?.isRunning
							? (timeLog?.stoppedAt | utcToLocal | timeFormat : true)
							: ('TIMESHEET.TILL_NOW' | translate)) +
						' (' +
						(timeLog?.duration | durationFormat) +
						')'
				}}</small>
			</div>
		</div>
		<div class="row">
			<div class="col-3 source-version">
				<ngx-badge-label
					[text]="timeLog?.source | replace : '_' : ' ' | titlecase"
					class="badge-item"
				></ngx-badge-label>

				<ngx-badge-label
					*ngIf="timeLog?.version"
					[text]="timeLog?.version"
					status="info"
					class="badge-item"
				></ngx-badge-label>
			</div>
		</div>
	</nb-card-header>
	<nb-card-body class="custom-scroll">
		<div class="mt-3">
			<div class="description">
				<span class="subtitle"> {{ 'TIMESHEET.ORGANIZATION_CONTACT' | translate }} : </span>
				<ngx-contact-links
					[value]="
						timeLog?.organizationContact
							? timeLog?.organizationContact
							: {
									name: ('TIMESHEET.NO_ORGANIZATION_CONTACT' | translate)
							  }
					"
				></ngx-contact-links>
			</div>
		</div>
		<div class="mt-3">
			<div class="description">
				<span class="subtitle">{{ 'TIMESHEET.PROJECT' | translate }} : </span>
				{{ timeLog?.project ? '' : ('TIMESHEET.NO_PROJECT' | translate) }}
				<ngx-project *ngIf="timeLog?.project" [rowData]="timeLog"></ngx-project>
			</div>
		</div>
		<div class="mt-3">
			<div class="description">
				<span class="subtitle">{{ 'TIMESHEET.TODO' | translate }} : </span>
				{{ timeLog?.task ? timeLog?.task?.title : ('TIMESHEET.NO_TODO' | translate) }}
			</div>
		</div>
		<div class="mt-3" *ngIf="timeLog?.description">
			<div class="description">
				<span class="subtitle">{{ 'TIMESHEET.NOTES' | translate }} : </span>
				{{ timeLog?.description }}
			</div>
		</div>
		<div class="mt-3" *ngIf="timeLog?.reason">
			<div class="description">
				<span class="subtitle">{{ 'TIMESHEET.REASON' | translate }} : </span>
				{{ timeLog?.reason }}
			</div>
		</div>
	</nb-card-body>
	<nb-card-footer class="d-flex justify-content-end flex-column">
		<ng-container *ngIf="timeLog?.isRunning">
			<nb-alert status="warning" [size]="'tiny'">
				{{ 'TIMESHEET.RUNNING_TIMER_WARNING' | translate }}
			</nb-alert>
		</ng-container>
		<div class="actions">
			<ng-template [ngxPermissionsOnly]="PermissionsEnum.ALLOW_MODIFY_TIME">
				<ng-template ngxTimeTrackingAuthorized [permission]="PermissionsEnum.ALLOW_MODIFY_TIME">
					<button
						[disabled]="timeLog.isRunning"
						size="small"
						class="mr-2 action primary"
						nbButton
						status="basic"
						(click)="openDialog()"
					>
						<nb-icon icon="edit"></nb-icon>
						{{ 'TIMESHEET.EDIT' | translate }}
					</button>
				</ng-template>
			</ng-template>
			<ng-template [ngxPermissionsOnly]="PermissionsEnum.ALLOW_DELETE_TIME">
				<ng-template ngxTimeTrackingAuthorized [permission]="PermissionsEnum.ALLOW_DELETE_TIME">
					<button
						[disabled]="timeLog.isRunning"
						size="small"
						class="mr-2 action"
						nbButton
						status="basic"
						ngxConfirmDialog
						[message]="'TIMESHEET.DELETE_TIMELOG' | translate"
						(confirm)="onDeleteConfirm()"
					>
						<nb-icon status="danger" icon="trash-2-outline"></nb-icon>
					</button>
				</ng-template>
			</ng-template>
		</div>
	</nb-card-footer>
</nb-card>
