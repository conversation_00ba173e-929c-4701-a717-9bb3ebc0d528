@use 'gauzy/_gauzy-table' as *;
@use 'gauzy/_gauzy-dialogs' as *;

:host {
  display: flex;
  justify-content: center;

  .close-button {
    position: absolute;
    top: 3px;
    right: 5px;
    z-index: 9;
  }

  nb-card-header,
  nb-card-body,
  nb-card-footer {
    padding: 1rem;
  }

  .description {
    overflow-wrap: break-word;
    display: flex;
    flex-direction: column;
    flex-direction: flex-start;

    > .subtitle {
      font-size: 11px;
      font-weight: 600;
      line-height: 13px;
      letter-spacing: -0.01em;
      text-align: left;
      margin-bottom: 10px;
    }
  }

  nb-alert {
    font-size: 14px !important;
    padding: 8px !important;
    height: 4.5rem !important;
    line-height: unset !important;
  }

  nb-card {
    background-color: nb-theme(gauzy-card-1);
    width: 328px;
  }

  .title,
  .subtitle,
  .date {
    color: rgba(126, 126, 143, 1);
  }
  .date {
    margin-top: 5%;
  }

  ngx-label {
    display: flex;
    position: relative;

    ::ng-deep nb-badge {
      border-radius: 4px;
      font-size: 12px;
      font-weight: 600;
      line-height: 15px;
      letter-spacing: 0em;
      text-align: left;
      padding: 4px 8px;
    }
  }

  .contact {
    width: fit-content;
    background: rgba(36, 189, 255, 0.1);
    border-radius: nb-theme(button-rectangle-border-radius);
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 5px 14px 5px 5px;
    font-size: 12px;
    font-weight: 400;
    line-height: 15px;
    letter-spacing: 0em;
    text-align: left;

    gap: 8px;
    color: nb-theme(text-primary-color);
    cursor: pointer;

    .initial {
      color: white;
      background-color: rgba(126, 126, 143, 1);
      border-radius: nb-theme(button-rectangle-border-radius);
      padding: 4px 5px;
      width: 18px;
      height: 18px;
      font-size: 8px;
      font-weight: 600;
      line-height: 10px;
      letter-spacing: 0em;
      text-align: center;
    }
  }
}

.actions {
  width: fit-content;
}
.avatar-log {
  display: flex;
  justify-content: space-between;
}
.row {
  .source-version {
    display: flex;
    gap: 10%;
  }
}
