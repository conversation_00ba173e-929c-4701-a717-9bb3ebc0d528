<nb-card class="main">
	<nb-card-header class="d-flex flex-column">
		<span class="cancel"
			><i class="fas fa-times" (click)="dialogRef.close()"></i
		></span>
		<h5 class="title">
			{{ action | titlecase }} {{ 'SPRINTS_PAGE.SPRINT' | translate }}
		</h5>
	</nb-card-header>
	<nb-card-body class="body">
		<form [formGroup]="form">
			<div class="sprint">
				<div class="row">
					<div class="col-sm-12">
						<div class="form-group">
							<label class="label">{{
								'FORM.LABELS.NAME' | translate
							}}</label>
							<input
								fullWidth
								status="basic"
								autocomplete="off"
								class="sprint__name"
								formControlName="name"
								type="text"
								nbInput
								placeholder="{{
									'SPRINTS_PAGE.ADD_SPRINT_NAME' | translate
								}}"
							/>
						</div>
					</div>
				</div>

				<div class="row">
					<div class="col">
						<div class="form-group">
							<label class="label">{{
								'FORM.LABELS.DATE_START' | translate
							}}</label>
							<input
								nbInput
								class="sprint__start-date"
								placeholder="{{
									'FORM.PLACEHOLDERS.SPRINT_START_DATE'
										| translate
								}}"
								[nbDatepicker]="startDate"
								formControlName="startDate"
							/>
							<nb-datepicker #startDate></nb-datepicker>
						</div>
					</div>
					<div class="col">
						<div class="form-group">
							<label class="label">{{
								'FORM.LABELS.END_DATE' | translate
							}}</label>
							<input
								nbInput
								class="sprint__end-date"
								placeholder="{{
									'FORM.PLACEHOLDERS.SPRINT_END_DATE'
										| translate
								}}"
								[nbDatepicker]="endDate"
								formControlName="endDate"
							/>
							<nb-datepicker #endDate></nb-datepicker>
						</div>
					</div>
				</div>

				<div class="row">
					<div class="col-6">
						<div class="form-group">
							<label class="label">{{
								'FORM.LABELS.LENGTH' | translate
							}}</label>
							<input
								nbInput
								class="sprint__length"
								placeholder="{{
									'FORM.PLACEHOLDERS.SPRINT_LENGTH'
										| translate
								}}"
								formControlName="length"
							/>
							<nb-datepicker #startDate></nb-datepicker>
						</div>
					</div>
				</div>

				<div class="row">
					<div class="col-sm-12">
						<div class="form-group">
							<label class="label">{{
								'FORM.LABELS.GOAL' | translate
							}}</label>
							<textarea
								formControlName="goal"
								nbInput
								fullWidth
								class="sprint__goal"
								placeholder="{{
									'FORM.PLACEHOLDERS.SPRINT_GOAL' | translate
								}}"
							></textarea>
						</div>
					</div>
				</div>
			</div>
		</form>
	</nb-card-body>
	<nb-card-footer class="text-left">
		<button
			status="basic"
			class="mr-3"
			outline
			nbButton
			(click)="dialogRef.close()"
		>
			{{ 'BUTTONS.CANCEL' | translate }}
		</button>
		<button
			(click)="save()"
			[disabled]="form.invalid"
			status="success"
			nbButton
		>
			{{ 'BUTTONS.SAVE' | translate }}
		</button>
	</nb-card-footer>
</nb-card>
