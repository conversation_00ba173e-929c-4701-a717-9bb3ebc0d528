<nb-card [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
	<nb-card-header class="d-flex flex-column pb-0">
		<div class="header-title-with-back">
			<ngx-back-navigation></ngx-back-navigation>
			<h4>
				<ngx-header-title [allowEmployee]="false">
					{{ 'INVITE_PAGE.' + invitationType + '.MANAGE' | translate }}
				</ngx-header-title>
			</h4>
		</div>
		<div class="align-self-end">
			<ngx-gauzy-button-action
				[componentName]="viewComponentName"
				[isDisable]="disableButton"
				[buttonTemplate]="actionButtons"
				[buttonTemplateVisible]="visibleButton"
			></ngx-gauzy-button-action>
		</div>
	</nb-card-header>
	<nb-card-body>
		<!-- Check if the user has the 'ORG_INVITE_VIEW' permission -->
		<ng-template [ngxPermissionsOnly]="[PermissionsEnum.ALL_ORG_VIEW, PermissionsEnum.ORG_INVITE_VIEW]">
			<ng-container [ngSwitch]="dataLayoutStyle">
				<!-- Table View -->
				<ng-template [ngSwitchCase]="componentLayoutStyleEnum.TABLE">
					<div class="table-scroll-container">
						<angular2-smart-table
							style="cursor: pointer"
							[settings]="settingsSmartTable"
							[source]="smartTableSource"
							(userRowSelect)="selectInvite($event)"
						></angular2-smart-table>
					</div>
					<div class="pagination-container">
						<ng-container *ngIf="smartTableSource">
							<ngx-pagination [source]="smartTableSource"></ngx-pagination>
						</ng-container>
					</div>
				</ng-template>

				<!-- Card Grid View -->
				<ng-template [ngSwitchCase]="componentLayoutStyleEnum.CARDS_GRID">
					<ga-card-grid
						[totalItems]="pagination?.totalItems"
						[settings]="settingsSmartTable"
						[source]="invites"
						(onSelectedItem)="selectInvite($event)"
						(scroll)="onScroll()"
					></ga-card-grid>
				</ng-template>

				<!-- Optional: Default case if no specific layout matches -->
				<ng-template *ngSwitchDefault>
					<p>{{ 'SETTINGS_MENU.NO_LAYOUT' | translate }}</p>
				</ng-template>
			</ng-container>
		</ng-template>
	</nb-card-body>
</nb-card>

<!-- Actions Buttons -->
<ng-template #actionButtons let-buttonSize="buttonSize" let-selectedItem="selectedItem">
	<div class="btn-group actions">
		<ng-template [ngxPermissionsOnly]="[PermissionsEnum.ALL_ORG_EDIT, PermissionsEnum.ORG_INVITE_EDIT]">
			<ng-template [ngIf]="selectedInvite?.status === InviteStatusEnum.INVITED">
				<button
					nbButton
					ngxClipboard
					[disabled]="disableButton"
					status="basic"
					class="action success"
					(click)="copyToClipboard(selectedItem)"
					size="small"
				>
					<i class="fas fa-link mr-1"></i>
					{{ 'BUTTONS.COPY_LINK' | translate }}
				</button>
				<button
					nbButton
					[disabled]="disableButton"
					status="basic"
					(click)="resendInvite(selectedItem)"
					class="action warning"
					size="small"
				>
					<i class="fas fa-repeat mr-1"></i>
					{{ 'BUTTONS.RESEND' | translate }}
				</button>
			</ng-template>
			<button
				nbButton
				[disabled]="disableButton"
				status="basic"
				(click)="deleteInvite(selectedItem)"
				class="action"
				size="small"
				[nbTooltip]="'BUTTONS.DELETE' | translate"
			>
				<nb-icon icon="trash-2-outline" status="danger"></nb-icon>
			</button>
		</ng-template>
	</div>
</ng-template>

<!-- Visible button -->
<ng-template #visibleButton>
	<ng-template [ngxPermissionsOnly]="[PermissionsEnum.ALL_ORG_EDIT, PermissionsEnum.ORG_INVITE_EDIT]">
		<button nbButton type="button" size="small" status="success" class="invite-button" (click)="invite()">
			<i class="fas fa-envelope mr-1"></i>
			{{ 'BUTTONS.INVITE' | translate }}
		</button>
	</ng-template>
</ng-template>
