<form [formGroup]="form" autocomplete-off>
	<div class="row">
		<div class="col-sm-12">
			<div class="form-group">
				<label for="emails" class="label">
					{{ 'FORM.LABELS.EMAILS' | translate }}
				</label>
				<nb-form-field>
					<nb-tag-list class="custom-tag-list" (tagRemove)="onEmailRemove($event)">
						<nb-tag *ngFor="let email of emails" [text]="email" [size]="'tiny'" removable></nb-tag>
						<input
							id="emails"
							type="text"
							name="emails"
							nbTagInput
							fullWidth
							(tagAdd)="onEmailAdd($event)"
							(focusout)="onFocusOut($event)"
							[status]="FormHelpers.isInvalidControl(form, 'emails') ? 'danger' : 'basic'"
						/>
					</nb-tag-list>
					<nb-icon
						*ngIf="emails.size > 0"
						nbSuffix
						icon="close-outline"
						pack="eva"
						(click)="onResetEmails()"
					></nb-icon>
				</nb-form-field>
			</div>
		</div>
	</div>
	<ng-container *ngIf="invitationType === invitationTypeEnum.CANDIDATE">
		<div class="row">
			<div class="col-sm-12">
				<div class="form-group">
					<label for="appliedDate" class="label">
						{{ 'FORM.LABELS.APPLIED_DATE_LABEL' | translate }}
					</label>
					<input
						id="appliedDate"
						[nbDatepicker]="appliedDatePicker"
						nbInput
						fullWidth
						[placeholder]="'FORM.PLACEHOLDERS.START_DATE' | translate"
						formControlName="appliedDate"
					/>
					<nb-datepicker #appliedDatePicker></nb-datepicker>
				</div>
			</div>
		</div>
	</ng-container>
	<ng-container *ngIf="invitationType === invitationTypeEnum.EMPLOYEE">
		<div class="row">
			<div class="col-sm-6">
				<div class="form-group">
					<label for="projectSelection" class="label label-with-select">
						{{ 'FORM.LABELS.PROJECTS_OPTIONAL' | translate }}
						<button nbButton ghost size="tiny" type="button" (click)="selectAllProjects()">
							{{ 'BUTTONS.SELECT_ALL' | translate }}
						</button>
					</label>
					<ng-select
						id="projectSelection"
						class="adjust-height"
						[hideSelected]="false"
						[multiple]="true"
						bindLabel="name"
						bindValue="id"
						appendTo="body"
						formControlName="projects"
						[placeholder]="'FORM.PLACEHOLDERS.PROJECTS' | translate"
						(change)="detectChanges()"
					>
						<ng-option *ngFor="let project of organizationProjects" [value]="project.id">
							{{ project.name }}
						</ng-option>
					</ng-select>
				</div>
			</div>
			<div class="col-sm-6">
				<div class="form-group">
					<label for="contactSelection" class="label label-with-select">
						{{ 'FORM.LABELS.CONTACTS_OPTIONAL' | translate }}
						<button nbButton ghost size="tiny" type="button" (click)="selectAllOrganizationContacts()">
							{{ 'BUTTONS.SELECT_ALL' | translate }}
						</button>
					</label>
					<ng-select
						id="contactSelection"
						class="adjust-height"
						[hideSelected]="false"
						[multiple]="true"
						bindLabel="name"
						bindValue="id"
						appendTo="body"
						formControlName="organizationContacts"
						[placeholder]="'FORM.PLACEHOLDERS.CONTACTS' | translate"
						(change)="detectChanges()"
					>
						<ng-option *ngFor="let contact of organizationContacts" [value]="contact.id">
							{{ contact.name }}
						</ng-option>
					</ng-select>
				</div>
			</div>
		</div>
	</ng-container>
	<ng-container
		*ngIf="invitationType === invitationTypeEnum.EMPLOYEE || invitationType === invitationTypeEnum.CANDIDATE"
	>
		<div class="row">
			<ng-container *ngIf="invitationType === invitationTypeEnum.EMPLOYEE">
				<div class="col-6">
					<div class="form-group">
						<label for="teamSelection" class="label label-with-select">
							{{ 'FORM.LABELS.TEAMS_OPTIONAL' | translate }}
							<button nbButton ghost size="tiny" type="button" (click)="selectAllTeams()">
								{{ 'BUTTONS.SELECT_ALL' | translate }}
							</button>
						</label>
						<ng-select
							id="teamSelection"
							class="adjust-height"
							[hideSelected]="false"
							[multiple]="true"
							bindLabel="name"
							bindValue="id"
							appendTo="body"
							formControlName="teams"
							[placeholder]="'FORM.PLACEHOLDERS.TEAMS' | translate"
							(change)="detectChanges()"
						>
							<ng-option *ngFor="let team of organizationTeams" [value]="team.id">
								{{ team.name }}
							</ng-option>
						</ng-select>
					</div>
				</div>
			</ng-container>
			<div [ngClass]="invitationType === invitationTypeEnum.EMPLOYEE ? 'col-6' : 'col-12'">
				<div class="form-group">
					<label for="departmentSelection" class="label label-with-select">
						{{ 'FORM.LABELS.DEPARTMENTS_OPTIONAL' | translate }}
						<button nbButton ghost size="tiny" type="button" (click)="selectAllDepartments()">
							{{ 'BUTTONS.SELECT_ALL' | translate }}
						</button>
					</label>
					<ng-select
						id="departmentSelection"
						class="adjust-height"
						[hideSelected]="false"
						[multiple]="true"
						bindLabel="name"
						bindValue="id"
						appendTo="body"
						formControlName="departments"
						[placeholder]="'FORM.PLACEHOLDERS.DEPARTMENTS' | translate"
						(change)="detectChanges()"
					>
						<ng-option *ngFor="let department of organizationDepartments" [value]="department.id">
							{{ department.name }}
						</ng-option>
					</ng-select>
				</div>
			</div>
		</div>
	</ng-container>
	<ng-container *ngIf="invitationType === invitationTypeEnum.USER">
		<div class="row">
			<div class="col-12">
				<ngx-role-form-field
					id="role"
					formControlName="role"
					[placeholder]="'FORM.PLACEHOLDERS.ROLE' | translate"
					[label]="'FORM.LABELS.ROLE' | translate"
					[excludes]="excludes"
					(selectedChange)="onSelectionChange($event)"
				></ngx-role-form-field>
			</div>
		</div>
	</ng-container>
	<ng-container *ngIf="invitationType !== invitationTypeEnum.CANDIDATE">
		<div class="row">
			<div class="col-sm-6">
				<div class="form-group">
					<label for="startedWorkOn" class="label">
						{{ 'FORM.LABELS.START_DATE' | translate }}
						<nb-icon
							[nbTooltip]="'FORM.NOTIFICATIONS.STARTED_WORK_ON' | translate"
							icon="question-mark-circle-outline"
						></nb-icon>
					</label>
					<nb-form-field>
						<input
							id="startedWorkOn"
							[nbDatepicker]="startedWorkDatepicker"
							nbInput
							fullWidth
							[placeholder]="'FORM.PLACEHOLDERS.START_DATE' | translate"
							formControlName="startedWorkOn"
						/>
						<nb-datepicker #startedWorkDatepicker></nb-datepicker>
					</nb-form-field>
				</div>
			</div>
		</div>
	</ng-container>
	<ng-container *ngIf="invitationExpiryOptions.length > 0">
		<div class="row">
			<div class="col-sm-6">
				<div class="form-group">
					<label for="invitationExpirationPeriod" class="label">
						{{ 'FORM.LABELS.INVITATION_EXPIRATION' | translate }}
					</label>
					<nb-select
						id="invitationExpirationPeriod"
						fullWidth
						[placeholder]="'FORM.PLACEHOLDERS.INVITATION_EXPIRATION' | translate"
						formControlName="invitationExpirationPeriod"
					>
						<nb-option *ngFor="let option of invitationExpiryOptions" [value]="option?.value">
							{{ option?.label }}
						</nb-option>
					</nb-select>
				</div>
			</div>
		</div>
	</ng-container>
</form>
