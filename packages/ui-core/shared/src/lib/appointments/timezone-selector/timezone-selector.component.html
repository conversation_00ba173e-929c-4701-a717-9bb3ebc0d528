<nb-card style="height: 450px" class="main">
	<nb-card-header class="d-flex">
		<h4 style="width: 400px">
			{{ 'POP_UPS.SELECT_TIMEZONE' | translate }}
		</h4>
	</nb-card-header>
	<nb-card-body class="body">
		<ng-select
			[(items)]="listOfZones"
			[placeholder]="'FORM.PLACEHOLDERS.CHOOSE_TIME_ZONE' | translate"
			[(ngModel)]="selectedTimezone"
			[searchable]="true"
			appendTo="body"
		>
			<ng-template ng-option-tmp let-item="item" let-index="index">
				{{ getTimeWithOffset(item) }}
			</ng-template>
			<ng-template ng-label-tmp let-item="item">
				{{ getTimeWithOffset(item) }}
			</ng-template>
		</ng-select>
	</nb-card-body>
	<nb-card-footer class="text-right">
		<button (click)="close()" status="danger" class="mr-3" nbButton type="button">
			{{ 'BUTTONS.CANCEL' | translate }}
		</button>
		<button (click)="select()" status="success" nbButton type="button">
			{{ 'BUTTONS.SAVE' | translate }}
		</button>
	</nb-card-footer>
</nb-card>
