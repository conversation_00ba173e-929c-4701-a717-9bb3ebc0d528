<!-- <nb-select
	class="multiple-select"
	[disabled]="disabled"
	[multiple]="multiple"
	[placeholder]="'TIMER_TRACKER.SELECT_CLIENT' | translate"
	[(selected)]="contactId"
>
	<nb-option *ngFor="let contact of contacts" [value]="contact.id">
		{{ contact.name }}
	</nb-option>
</nb-select> -->
<ng-select
	[addTag]="(hasEditContact$ | async) ? createNew : null"
	[disabled]="disabled"
	[clearable]="true"
	[items]="contacts"
	appendTo="body"
	[(ngModel)]="contactId"
	[placeholder]="'TIMER_TRACKER.SELECT_CLIENT' | translate"
	bindValue="id"
	bindLabel="name"
></ng-select>