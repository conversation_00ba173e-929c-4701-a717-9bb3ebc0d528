<div class="form-group">
	<label class="label">
		{{ 'FORM.LABELS.GITHUB_REPOSITORY' | translate }}
	</label>
	<ng-select
		#select
		class="mb-2"
		[items]="repositories$ | async"
		[searchable]="false"
		[clearable]="true"
		[loading]="loading"
		[(ngModel)]="sourceId"
		[placeholder]="placeholder || ('INTEGRATIONS.GITHUB_PAGE.SELECT_REPOSITORY' | translate)"
		bindLabel="full_name"
		bindValue="id"
		appendTo="body"
		dropdownPosition="bottom"
		(change)="selectRepository($event)"
	>
		<ng-template ng-header-tmp>
			<input
				type="search"
				class="form-control"
				(input)="select.filter($event.target.value)"
				[placeholder]="'INTEGRATIONS.GITHUB_PAGE.SEARCH_REPOSITORY' | translate"
			/>
		</ng-template>
		<ng-template ng-label-tmp let-item="item">
			<ng-container *ngIf="item">
				<img src="assets/images/integrations/github.svg" />
				<span class="ml-1">{{ item.full_name }}</span>
			</ng-container>
		</ng-template>
		<ng-template ng-option-tmp let-item="item">
			<ng-container *ngIf="item">
				<img src="assets/images/integrations/github.svg" />
				<span class="ml-1">{{ item.full_name }}</span>
			</ng-container>
		</ng-template>
	</ng-select>
</div>
