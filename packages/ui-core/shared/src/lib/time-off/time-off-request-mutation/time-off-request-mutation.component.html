<nb-card class="main">
	<nb-card-header class="d-flex flex-column">
    <span class="cancel"
			><i class="fas fa-times" (click)="close()"></i
		></span>
		<h5 class="title">
			{{
				(!isEditMode
					? 'TIME_OFF_PAGE.REQUEST_TIME_OFF'
					: 'TIME_OFF_PAGE.ACTIONS.EDIT'
				) | translate
			}}
		</h5>
	</nb-card-header>
	<nb-card-body>
		<form [formGroup]="form">
			<div class="row">
				<div class="col-6">
					<div class="form-group">
						<label class="label">{{
							'FORM.LABELS.EMPLOYEE' | translate
						}}</label>
						<ga-employee-selector
							#employeeSelector
							fullWidth
							required
							[skipGlobalChange]="true"
							class="employees"
						></ga-employee-selector>
					</div>
				</div>
				<div class="col-6">
					<ga-time-off-policy-select
						[id]="'policy'"
						[ctrl]="form.get('policyId')"
						[placeholder]="'TIME_OFF_PAGE.SELECT_TIME_OFF_POLICY' | translate"
						(selectedChange)="onPolicySelected($event)"
						formControlName="policyId"
					></ga-time-off-policy-select>
				</div>
			</div>
			<div class="row">
				<div class="col-6">
					<div class="form-group">
						<label class="label" for="start">{{
							'FORM.LABELS.FROM' | translate
						}}</label>
						<input
							formControlName="start"
							nbInput
							fullWidth
							required
							[placeholder]="'POP_UPS.PICK_DATE' | translate"
							[nbDatepicker]="startDatePicker"
							[status]="
								FormHelpers.isInvalidControl(form, 'start')
									? 'danger'
									: 'basic'
							"
						/>
						<nb-datepicker [min]="minDate" #startDatePicker></nb-datepicker>
					</div>
				</div>
				<div class="col-6">
					<div class="form-group">
						<label class="label" for="end">{{
							'FORM.LABELS.TO' | translate
						}}</label>
						<input
							formControlName="end"
							nbInput
							fullWidth
							[placeholder]="'POP_UPS.PICK_DATE' | translate"
							[nbDatepicker]="endDatePicker"
							[status]="
								FormHelpers.isInvalidControl(form, 'end')
									? 'danger'
									: 'basic'
							"
						/>
						<nb-datepicker [min]="minDate" #endDatePicker></nb-datepicker>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-6">
					<div class="form-group">
						<div>
							<label class="label" for="end">
								{{ 'FORM.LABELS.DOWNLOAD_REQUEST_FORM' | translate }}
							</label>
						</div>
						<button
							nbButton
							status="basic"
							class="download-btn mr-4"
							(click)="getRequestForm('paid')"
						>
							{{ 'BUTTONS.PAID_DAYS_OFF' | translate }}
						</button>
						<button
							nbButton
							status="basic"
							class="download-btn"
							(click)="getRequestForm('unpaid')"
						>
							{{ 'BUTTONS.UNPAID_DAYS_OFF' | translate }}
						</button>
					</div>
				</div>
				<div class="col-6">
					<div class="form-group">
						<div class="label">
							{{ 'TIME_OFF_PAGE.UPLOAD_REQUEST_DOCUMENT' | translate }}
						</div>
						<ngx-file-uploader-input
							[placeholder]=" 'FORM.PLACEHOLDERS.UPLOADER_DOCUMENT_PLACEHOLDER' | translate "
							[fileUrl]="form.get('documentUrl').value"
							(uploadedImgUrl)="form.get('documentUrl').setValue($event)"
							(uploadedImageAsset)="uploadDocumentAsset($event)"
							(uploadedImgUrl)="uploadDocumentAssetUrl($event)"
						></ngx-file-uploader-input>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-12">
					<div class="form-group">
						<div class="form-group">
							<label class="label" for="description">{{
								'FORM.LABELS.DESCRIPTION' | translate
							}}</label>
							<textarea
								fullWidth
								nbInput
								formControlName="description"
								[placeholder]="'TIME_OFF_PAGE.ADD_A_DESCRIPTION' | translate"
							></textarea>
						</div>
					</div>
				</div>
			</div>
		</form>
	</nb-card-body>
	<nb-card-footer class="text-left">
		<button
			(click)="close()"
			status="basic"
      outline
			class="mr-3"
			nbButton
		>
			{{ 'BUTTONS.CANCEL' | translate }}
		</button>
		<button
			(click)="saveRequest()"
			status="success"
			nbButton
			[disabled]="form.invalid"
		>
			{{ 'BUTTONS.SAVE' | translate }}
		</button>
	</nb-card-footer>
</nb-card>
