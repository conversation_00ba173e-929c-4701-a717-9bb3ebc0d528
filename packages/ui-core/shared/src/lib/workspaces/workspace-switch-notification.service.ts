import { Injectable } from '@angular/core';
import { Subject, Observable } from 'rxjs';

/**
 * Service to notify components about workspace switching events
 * This helps components like header to know when they need to reinitialize
 */
@Injectable({
	providedIn: 'root'
})
export class WorkspaceSwitchNotificationService {
	private readonly workspaceSwitched$ = new Subject<void>();
	private readonly workspaceSwitchStarted$ = new Subject<void>();

	/**
	 * Observable that emits when workspace switch is completed
	 */
	public get onWorkspaceSwitched(): Observable<void> {
		return this.workspaceSwitched$.asObservable();
	}

	/**
	 * Observable that emits when workspace switch starts
	 */
	public get onWorkspaceSwitchStarted(): Observable<void> {
		return this.workspaceSwitchStarted$.asObservable();
	}

	/**
	 * Notify that workspace switch has started
	 */
	public notifyWorkspaceSwitchStarted(): void {
		this.workspaceSwitchStarted$.next();
	}

	/**
	 * Notify that workspace switch has completed
	 */
	public notifyWorkspaceSwitched(): void {
		this.workspaceSwitched$.next();
	}
}
