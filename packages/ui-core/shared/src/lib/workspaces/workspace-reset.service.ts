import { Injectable, ApplicationRef } from '@angular/core';
import { Observable, Subject } from 'rxjs';
import { catchError, tap, finalize } from 'rxjs/operators';
import { IAuthResponse } from '@gauzy/contracts';
import {
	Store,
	AuthService,
	ToastrService,
	AppInitService,
	ElectronService,
	TimeTrackerService,
	TimesheetFilterService,
	OrganizationStore,
	OrganizationProjectAkitaStore
} from '@gauzy/ui-core/core';
import { NgxPermissionsService } from 'ngx-permissions';
import { deleteCookie } from '../../../../core/src/lib/auth/cookie-helper';

/**
 * Service for handling workspace switching with complete reset
 */
@Injectable({
	providedIn: 'root'
})
export class WorkspaceResetService {
	private resetComplete$ = new Subject<void>();

	constructor(
		private readonly store: Store,
		private readonly authService: AuthService,
		private readonly toastrService: ToastrService,
		private readonly appInitService: AppInitService,
		private readonly electronService: ElectronService,
		private readonly timeTrackerService: TimeTrackerService,
		private readonly timesheetFilterService: TimesheetFilterService,
		private readonly organizationStore: OrganizationStore,
		private readonly organizationProjectStore: OrganizationProjectAkitaStore,
		private readonly ngxPermissionsService: NgxPermissionsService,
		private readonly applicationRef: ApplicationRef
	) {}

	/**
	 * Switch workspace with complete reset (no page reload)
	 */
	public switchWorkspace(workspaceId: string): Observable<IAuthResponse> {
		const preferredLanguage = this.store.preferredLanguage;
		const themeName = localStorage.getItem('themeName');

		return this.authService.switchWorkspace(workspaceId).pipe(
			tap(async (response: IAuthResponse) => {
				if (!response) {
					throw new Error('Failed to switch workspace');
				}
				await this.performReset(response, preferredLanguage, themeName);
			}),
			catchError((error) => {
				this.toastrService.danger('Failed to switch workspace. Please try again.', 'Error');
				throw error;
			}),
			finalize(() => {
				this.resetComplete$.next();
			})
		);
	}

	/**
	 * Perform complete workspace reset
	 */
	private async performReset(response: IAuthResponse, preferredLanguage: string, themeName: string): Promise<void> {
		this.clearApplicationState();
		this.resetStoresAndServices();
		this.clearCaches();
		this.updateAuthenticationState(response);
		await this.reinitializeApplication();
		this.restoreUserPreferences(preferredLanguage, themeName);
		this.toastrService.success('Workspace switched successfully!', 'Success');
	}

	private clearApplicationState(): void {
		this.clearTimeTrackingData();
		this.clearStorageData();
		this.clearAuthenticationCookies();
	}

	private resetStoresAndServices(): void {
		this.store.clear();
		this.clearAkitaStores();
		this.clearFeaturesAndPermissions();
	}

	private clearCaches(): void {
		this.clearCacheServices();
	}

	private updateAuthenticationState(response: IAuthResponse): void {
		const { user, token, refresh_token } = response;
		this.store.userId = user.id;
		this.store.token = token;
		this.store.refresh_token = refresh_token;
		this.store.organizationId = user?.employee?.organizationId;
		this.store.tenantId = user?.tenantId;
		this.store.user = user;
		this.electronAuthentication(response);
	}

	private async reinitializeApplication(): Promise<void> {
		await this.appInitService;
		this.applicationRef.tick();
	}

	private restoreUserPreferences(preferredLanguage: string, themeName: string): void {
		this.store.preferredLanguage = preferredLanguage;
		if (themeName) {
			localStorage.setItem('themeName', themeName);
		}
	}

	/**
	 * Clear time tracking data before workspace switch
	 */
	private async clearTimeTrackingData(): Promise<void> {
		if (this.store.user && this.store.user.employee) {
			// Stop time tracking if running
			if (this.timeTrackerService.running) {
				if (this.timeTrackerService.timerSynced.isExternalSource) {
					this.timeTrackerService.remoteToggle();
				} else {
					await this.timeTrackerService.toggle();
				}
			}

			// Clear time tracker and timesheet filter
			this.timeTrackerService.clearTimeTracker();
			this.timesheetFilterService.clear();
		}
	}

	/**
	 * Clear authentication cookies
	 */
	private clearAuthenticationCookies(): void {
		deleteCookie('userId', { SameSite: 'None', Secure: true });
		deleteCookie('token', { SameSite: 'None', Secure: true });
		deleteCookie('refresh_token', { SameSite: 'None', Secure: true });
	}

	/**
	 * Clear localStorage/sessionStorage data related to store
	 */
	private clearStorageData(): void {
		// List of keys to preserve
		const keysToPreserve = ['themeName', 'preferredLanguage', 'serverConnection'];

		// Get all keys
		const allKeys = Object.keys(localStorage);

		// Remove all keys except those to preserve
		allKeys.forEach((key) => {
			if (!keysToPreserve.includes(key)) {
				localStorage.removeItem(key);
			}
		});

		// Same for sessionStorage if needed
		const sessionKeys = Object.keys(sessionStorage);
		sessionKeys.forEach((key) => {
			if (!keysToPreserve.includes(key)) {
				sessionStorage.removeItem(key);
			}
		});
	}

	/**
	 * Handle Electron authentication after workspace switch
	 */
	private electronAuthentication(response: IAuthResponse): void {
		try {
			if (this.electronService.isElectron) {
				const { user, token } = response;
				this.electronService.ipcRenderer.send('auth_success', {
					user: user,
					token: token,
					userId: user.id,
					employeeId: user.employee ? user.employee.id : null,
					organizationId: user.employee ? user.employee.organizationId : null,
					tenantId: user.tenantId ? user.tenantId : null
				});
			}
		} catch (error) {
			// Silent error handling
		}
	}

	/**
	 * Clear specialized Akita stores that may persist workspace-specific data
	 */
	private clearAkitaStores(): void {
		try {
			// Reset organization-related stores
			this.organizationStore.reset();
			this.organizationProjectStore.reset();

			// Clear Akita store data from localStorage
			this.clearAkitaLocalStorage();
		} catch (error) {
			// Silent error handling
		}
	}

	/**
	 * Clear Akita-related data from localStorage
	 */
	private clearAkitaLocalStorage(): void {
		try {
			const akitaPatterns = ['AkitaStores', 'akita', '@datorama', 'organization', 'project', 'app', 'persist'];

			const allKeys = Object.keys(localStorage);
			let clearedCount = 0;

			allKeys.forEach((key) => {
				akitaPatterns.forEach((pattern) => {
					if (
						key.toLowerCase().includes(pattern.toLowerCase()) &&
						!['themeName', 'preferredLanguage', 'serverConnection'].includes(key)
					) {
						localStorage.removeItem(key);
						clearedCount++;
					}
				});
			});
		} catch (error) {
			// Silent error handling
		}
	}

	/**
	 * Clear features and permissions that may be workspace-specific
	 */
	private clearFeaturesAndPermissions(): void {
		// Clear feature-related data
		this.store.featureToggles = [];
		this.store.featureOrganizations = [];
		this.store.featureTenant = [];

		// Clear permissions from store
		this.store.userRolePermissions = [];
		this.store.clear();

		// Clear permissions from NgxPermissionsService
		this.ngxPermissionsService.flushPermissions();
	}

	/**
	 * Clear ALL cache services that may hold workspace-specific data
	 */
	private clearCacheServices(): void {
		try {
			// Clear timesheet filter service
			this.timesheetFilterService.clear();

			// Clear time tracker service
			this.timeTrackerService.clearTimeTracker();
		} catch (error) {
			// Silent error handling
		}
	}
}
