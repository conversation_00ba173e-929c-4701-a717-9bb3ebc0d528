import { Injectable, Injector, ApplicationRef } from '@angular/core';
import { Router } from '@angular/router';
import { Observable, Subject, BehaviorSubject } from 'rxjs';
import { catchError, tap, finalize } from 'rxjs/operators';
import { IAuthResponse, PermissionsEnum } from '@gauzy/contracts';
import {
	Store,
	AuthService,
	ToastrService,
	AppInitService,
	ElectronService,
	TimeTrackerService,
	TimesheetFilterService,
	OrganizationStore,
	OrganizationProjectAkitaStore,
	PermissionsService,
	SelectorBuilderService,
	DEFAULT_SELECTOR_VISIBILITY
} from '@gauzy/ui-core/core';
import { NgxPermissionsService } from 'ngx-permissions';
import { deleteCookie } from '../../../../core/src/lib/auth/cookie-helper';
import { resetStores } from '@datorama/akita';
import { NbSidebarService } from '@nebular/theme';

/**
 * Advanced service for handling workspace switching without page reload
 * Uses Angular's native mechanisms to completely reset application state
 */
@Injectable({
	providedIn: 'root'
})
export class WorkspaceAdvancedResetService {
	private readonly resetInProgress$ = new BehaviorSubject<boolean>(false);
	private readonly resetComplete$ = new Subject<void>();

	constructor(
		private readonly store: Store,
		private readonly authService: AuthService,
		private readonly toastrService: ToastrService,
		private readonly appInitService: AppInitService,
		private readonly electronService: ElectronService,
		private readonly timeTrackerService: TimeTrackerService,
		private readonly timesheetFilterService: TimesheetFilterService,
		private readonly organizationStore: OrganizationStore,
		private readonly organizationProjectStore: OrganizationProjectAkitaStore,
		private readonly permissionsService: PermissionsService,
		private readonly ngxPermissionsService: NgxPermissionsService,
		private readonly applicationRef: ApplicationRef,
		private readonly router: Router,
		private readonly injector: Injector,
		private readonly sidebarService: NbSidebarService,
		private readonly selectorBuilderService: SelectorBuilderService
	) {}

	/**
	 * Switch workspace with advanced reset approach (no page reload)
	 *
	 * @param workspaceId The ID of the workspace to switch to
	 * @returns Observable with the auth response
	 */
	public switchWorkspace(workspaceId: string): Observable<IAuthResponse> {
		// Prevent multiple simultaneous resets
		if (this.resetInProgress$.value) {
			throw new Error('Workspace switch already in progress');
		}

		// 1. Save important user preferences before switch
		const preferredLanguage = this.store.preferredLanguage;
		const themeName = localStorage.getItem('themeName');

		// 2. Mark reset as in progress
		this.resetInProgress$.next(true);

		// 3. Call API to switch workspace FIRST (using current token)
		return this.authService.switchWorkspace(workspaceId).pipe(
			tap(async (response: IAuthResponse) => {
				if (!response) {
					throw new Error('Failed to switch workspace');
				}

				// 4. Perform advanced reset and re-initialization
				await this.performAdvancedReset(response, preferredLanguage, themeName);
			}),
			catchError((error) => {
				console.error('Error switching workspace:', error);
				this.toastrService.danger('Failed to switch workspace. Please try again.', 'Error');
				this.resetInProgress$.next(false);
				throw error;
			}),
			finalize(() => {
				this.resetInProgress$.next(false);
			})
		);
	}

	/**
	 * Perform advanced reset without page reload
	 * This method orchestrates the complete reset and re-initialization process
	 */
	private async performAdvancedReset(
		response: IAuthResponse,
		preferredLanguage: string,
		themeName: string
	): Promise<void> {
		try {
			console.log('🔄 Starting advanced workspace reset...');

			// Phase 1: Stop all active processes
			await this.stopActiveProcesses();

			// Phase 2: Clear all application state
			await this.clearApplicationState();

			// Phase 3: Apply new workspace data
			await this.applyNewWorkspaceData(response, preferredLanguage, themeName);

			// Phase 4: Re-initialize application
			await this.reinitializeApplication();

			// Phase 5: Navigate to dashboard
			await this.navigateToWorkspace();

			// Phase 6: Wait for complete application stabilization and force header update
			await this.waitForCompleteStabilizationAndUpdate();

			console.log('✅ Advanced workspace reset completed successfully');
			this.toastrService.success('Workspace switched successfully!', 'Success');

			this.resetComplete$.next();
		} catch (error) {
			console.error('❌ Error during advanced workspace reset:', error);
			this.toastrService.danger('Failed to switch workspace', 'Error');
			throw error;
		}
	}

	/**
	 * Phase 1: Stop all active processes before reset
	 */
	private async stopActiveProcesses(): Promise<void> {
		console.log('🛑 Stopping active processes...');

		// Stop time tracking if running
		if (this.store.user && this.store.user.employee) {
			if (this.timeTrackerService.running) {
				try {
					if (this.timeTrackerService.timerSynced.isExternalSource) {
						this.timeTrackerService.remoteToggle();
					} else {
						await this.timeTrackerService.toggle();
					}
				} catch (error) {
					console.warn('Error stopping time tracker:', error);
				}
			}
		}

		// Clear time tracker and timesheet filter
		this.timeTrackerService.clearTimeTracker();
		this.timesheetFilterService.clear();
	}

	/**
	 * Phase 2: Clear all application state
	 */
	private async clearApplicationState(): Promise<void> {
		console.log('🧹 Clearing application state...');

		// 1. Clear main store
		this.store.clear();

		// 2. Reset all Akita stores globally
		resetStores();

		// 3. Clear specialized stores manually
		this.clearAllAkitaStores();

		// 4. Clear all cache services
		this.clearAllCacheServices();

		// 5. Clear features and permissions
		this.clearFeaturesAndPermissions();

		// 6. Clear localStorage/sessionStorage (preserve preferences)
		this.clearStorageData();

		// 7. Clear authentication cookies
		this.clearAuthenticationCookies();

		// 8. Clear Akita localStorage
		this.clearAkitaLocalStorage();

		// 9. Clear HTTP cache and interceptors
		this.clearHttpCache();

		// 10. Reset header selectors state
		this.resetHeaderSelectorsState();
	}

	/**
	 * Phase 3: Apply new workspace data
	 */
	private async applyNewWorkspaceData(
		response: IAuthResponse,
		preferredLanguage: string,
		themeName: string
	): Promise<void> {
		console.log('📝 Applying new workspace data...');

		const { user, token, refresh_token } = response;

		// Update store with new workspace data in the correct order
		this.store.userId = user.id;
		this.store.token = token;
		this.store.refresh_token = refresh_token;
		this.store.tenantId = user?.tenantId;
		this.store.organizationId = user?.employee?.organizationId;
		this.store.user = user;

		// Important: Set selectedOrganization to null initially to prevent old data usage
		this.store.selectedOrganization = null;

		// Restore user preferences
		localStorage.setItem('preferredLanguage', preferredLanguage);
		if (themeName) {
			localStorage.setItem('themeName', themeName);
		}

		// Handle Electron authentication
		this.electronAuthentication(response);
	}

	/**
	 * Phase 4: Re-initialize application
	 */
	private async reinitializeApplication(): Promise<void> {
		console.log('🚀 Re-initializing application...');

		// Re-initialize app services (this will load the organization)
		await this.appInitService.init();

		// Reload permissions
		await this.permissionsService.loadPermissions();

		// Wait for everything to be properly loaded
		await this.waitForPermissionsToLoad();
	}

	/**
	 * Phase 5: Navigate to workspace dashboard
	 */
	private async navigateToWorkspace(): Promise<void> {
		console.log('🧭 Navigating to workspace...');

		// Navigate to dashboard
		await this.router.navigate(['/']);
	}

	/**
	 * Phase 6: Wait for complete application stabilization and force header update
	 */
	private async waitForCompleteStabilizationAndUpdate(): Promise<void> {
		console.log('🔄 Waiting for complete application stabilization...');

		try {
			// Step 1: Wait for all async operations to complete
			await new Promise((resolve) => setTimeout(resolve, 1000));

			// Step 2: Wait for organization to be loaded
			await this.waitForOrganizationToLoad();

			// Step 3: Wait for permissions to be loaded
			await this.waitForPermissionsToLoad();

			// Step 4: Force all observables to emit
			await this.forceAllObservablesToEmit();

			// Step 5: Force header component to re-evaluate everything
			await this.forceHeaderComponentUpdate();

			console.log('✅ Complete application stabilization completed');
		} catch (error) {
			console.warn('⚠️ Error during application stabilization:', error);
		}
	}

	/**
	 * Wait for organization to be loaded with timeout
	 */
	private async waitForOrganizationToLoad(): Promise<void> {
		console.log('⏳ Waiting for organization to load...');

		return new Promise((resolve) => {
			let attempts = 0;
			const maxAttempts = 20; // 10 seconds max

			const checkOrganization = () => {
				attempts++;
				console.log(`🔍 Checking organization (attempt ${attempts}/${maxAttempts})...`);

				if (this.store.selectedOrganization) {
					console.log('✅ Organization loaded:', this.store.selectedOrganization.name);
					resolve();
				} else if (attempts < maxAttempts) {
					setTimeout(checkOrganization, 500);
				} else {
					console.warn('⚠️ Timeout waiting for organization');
					resolve(); // Continue anyway
				}
			};

			setTimeout(checkOrganization, 100);
		});
	}

	/**
	 * Wait for permissions to be loaded with timeout
	 */
	private async waitForPermissionsToLoad(): Promise<void> {
		console.log('⏳ Waiting for permissions to load...');

		return new Promise((resolve) => {
			let attempts = 0;
			const maxAttempts = 20; // 10 seconds max

			const checkPermissions = () => {
				attempts++;
				console.log(`🔍 Checking permissions (attempt ${attempts}/${maxAttempts})...`);

				const hasPermissions = this.store.userRolePermissions && this.store.userRolePermissions.length > 0;

				if (hasPermissions) {
					console.log('✅ Permissions loaded:', this.store.userRolePermissions.length, 'permissions');
					resolve();
				} else if (attempts < maxAttempts) {
					setTimeout(checkPermissions, 500);
				} else {
					console.warn('⚠️ Timeout waiting for permissions');
					resolve(); // Continue anyway
				}
			};

			setTimeout(checkPermissions, 100);
		});
	}

	/**
	 * Clear features and permissions
	 */
	private clearFeaturesAndPermissions(): void {
		// Clear feature-related data
		this.store.featureToggles = [];
		this.store.featureOrganizations = [];
		this.store.featureTenant = [];

		// Clear permissions from store
		this.store.userRolePermissions = [];

		// Clear permissions from NgxPermissionsService
		this.ngxPermissionsService.flushPermissions();
	}

	/**
	 * Clear localStorage/sessionStorage data (preserve preferences)
	 */
	private clearStorageData(): void {
		// List of keys to preserve
		const keysToPreserve = ['themeName', 'preferredLanguage', 'serverConnection'];

		// Clear localStorage
		const allKeys = Object.keys(localStorage);
		allKeys.forEach((key) => {
			if (!keysToPreserve.includes(key)) {
				localStorage.removeItem(key);
			}
		});

		// Clear sessionStorage
		const sessionKeys = Object.keys(sessionStorage);
		sessionKeys.forEach((key) => {
			if (!keysToPreserve.includes(key)) {
				sessionStorage.removeItem(key);
			}
		});
	}

	/**
	 * Clear authentication cookies
	 */
	private clearAuthenticationCookies(): void {
		deleteCookie('userId', { SameSite: 'None', Secure: true });
		deleteCookie('token', { SameSite: 'None', Secure: true });
		deleteCookie('refresh_token', { SameSite: 'None', Secure: true });
	}

	/**
	 * Clear Akita-related data from localStorage
	 */
	private clearAkitaLocalStorage(): void {
		try {
			const akitaPatterns = ['AkitaStores', 'akita', '@datorama', 'organization', 'project', 'app', 'persist'];

			const allKeys = Object.keys(localStorage);
			let clearedCount = 0;

			allKeys.forEach((key) => {
				akitaPatterns.forEach((pattern) => {
					if (
						key.toLowerCase().includes(pattern.toLowerCase()) &&
						!['themeName', 'preferredLanguage', 'serverConnection'].includes(key)
					) {
						localStorage.removeItem(key);
						clearedCount++;
					}
				});
			});

			console.log(`Cleared ${clearedCount} Akita-related entries from localStorage`);
		} catch (error) {
			console.warn('Error clearing Akita localStorage:', error);
		}
	}

	/**
	 * Handle Electron authentication after workspace switch
	 */
	private electronAuthentication(response: IAuthResponse): void {
		try {
			if (this.electronService.isElectron) {
				const { user, token } = response;
				this.electronService.ipcRenderer.send('auth_success', {
					user: user,
					token: token,
					userId: user.id,
					employeeId: user.employee ? user.employee.id : null,
					organizationId: user.employee ? user.employee.organizationId : null,
					tenantId: user.tenantId ? user.tenantId : null
				});
			}
		} catch (error) {
			console.error('Electron authentication error:', error);
		}
	}

	/**
	 * Check if reset is currently in progress
	 */
	public get isResetInProgress(): boolean {
		return this.resetInProgress$.value;
	}

	/**
	 * Observable to track reset completion
	 */
	public get resetComplete(): Observable<void> {
		return this.resetComplete$.asObservable();
	}

	/**
	 * Clear all Akita stores in the application
	 */
	private clearAllAkitaStores(): void {
		try {
			console.log('🗂️ Clearing all Akita stores...');

			// Reset organization-related stores
			this.organizationStore.reset();
			this.organizationProjectStore.reset();

			// Try to get and reset other Akita stores dynamically
			this.resetDynamicAkitaStores();

			console.log('✅ All Akita stores cleared successfully');
		} catch (error) {
			console.warn('⚠️ Error clearing some Akita stores:', error);
		}
	}

	/**
	 * Reset dynamic Akita stores using injector
	 */
	private resetDynamicAkitaStores(): void {
		try {
			// List of known Akita store services to reset
			const akitaStoreServices = ['OrganizationTeamAkitaStore', 'EmployeeAkitaStore', 'AutoRefreshStore'];

			akitaStoreServices.forEach((serviceName) => {
				try {
					const service = this.injector.get(serviceName as any, null);
					if (service && typeof service.reset === 'function') {
						service.reset();
						console.log(`✅ Reset ${serviceName}`);
					}
				} catch (error) {
					// Service might not be available in current context
					console.log(`ℹ️ ${serviceName} not available or already reset`);
				}
			});
		} catch (error) {
			console.warn('⚠️ Error resetting dynamic Akita stores:', error);
		}
	}

	/**
	 * Clear all cache services
	 */
	private clearAllCacheServices(): void {
		try {
			console.log('🗄️ Clearing all cache services...');

			// Clear timesheet filter service
			this.timesheetFilterService.clear();

			// Clear time tracker service
			this.timeTrackerService.clearTimeTracker();

			// Try to clear other cache services dynamically
			this.clearDynamicCacheServices();

			console.log('✅ All cache services cleared successfully');
		} catch (error) {
			console.warn('⚠️ Error clearing cache services:', error);
		}
	}

	/**
	 * Clear dynamic cache services using injector
	 */
	private clearDynamicCacheServices(): void {
		try {
			// List of known cache services to clear
			const cacheServices = [
				'ClientCacheService',
				'EmployeeCacheService',
				'ImageCacheService',
				'LanguageCacheService',
				'OrganizationsCacheService',
				'ProjectCacheService',
				'TagCacheService'
			];

			cacheServices.forEach((serviceName) => {
				try {
					const service = this.injector.get(serviceName as any, null);
					if (service && typeof service.clear === 'function') {
						service.clear();
						console.log(`✅ Cleared ${serviceName}`);
					}
				} catch (error) {
					// Service might not be available in current context
					console.log(`ℹ️ ${serviceName} not available`);
				}
			});
		} catch (error) {
			console.warn('⚠️ Error clearing dynamic cache services:', error);
		}
	}

	/**
	 * Clear HTTP cache and reset interceptors
	 */
	private clearHttpCache(): void {
		try {
			console.log('🌐 Clearing HTTP cache...');

			// Note: Angular HttpClient doesn't have a direct cache clear method
			// But we can clear any custom HTTP cache implementations

			// Clear any custom HTTP cache services if they exist
			this.clearCustomHttpCaches();

			console.log('✅ HTTP cache cleared successfully');
		} catch (error) {
			console.warn('⚠️ Error clearing HTTP cache:', error);
		}
	}

	/**
	 * Clear custom HTTP cache implementations
	 */
	private clearCustomHttpCaches(): void {
		try {
			// Try to clear any custom HTTP cache services
			const httpCacheServices = ['HttpCacheService', 'ApiCacheService'];

			httpCacheServices.forEach((serviceName) => {
				try {
					const service = this.injector.get(serviceName as any, null);
					if (service && typeof service.clear === 'function') {
						service.clear();
						console.log(`✅ Cleared ${serviceName}`);
					}
				} catch (error) {
					// Service might not be available
					console.log(`ℹ️ ${serviceName} not available`);
				}
			});
		} catch (error) {
			console.warn('⚠️ Error clearing custom HTTP caches:', error);
		}
	}

	/**
	 * Reset header selectors state during workspace switch
	 */
	private resetHeaderSelectorsState(): void {
		try {
			console.log('🎯 Resetting header selectors state...');

			// Reset ALL selectors to null to force complete re-evaluation
			this.store.selectedEmployee = null;
			this.store.selectedProject = null;
			this.store.selectedTeam = null;

			// Also reset organization temporarily to prevent old organization ID usage
			this.store.selectedOrganization = null;

			console.log('✅ Header selectors state reset');
		} catch (error) {
			console.warn('⚠️ Error resetting header selectors state:', error);
		}
	}

	/**
	 * Trigger store observable updates with proper timing
	 * This forces all components listening to store observables to update safely
	 */
	private triggerStoreObservableUpdates(): void {
		try {
			console.log('🔄 Waiting for organization to be loaded by OrganizationSelectorComponent...');

			// The organization is loaded by OrganizationSelectorComponent, not AppInitService
			// We need to wait for it to be loaded before forcing header updates
			this.waitForOrganizationAndTriggerUpdate();

			// Initial change detection
			this.applicationRef.tick();
		} catch (error) {
			console.warn('⚠️ Error triggering store observable updates:', error);
		}
	}

	/**
	 * Capture current sidebar state before workspace switch
	 */
	private captureSidebarState(): any {
		try {
			console.log('📸 Capturing sidebar state...');

			// Since we can't easily get the current state synchronously,
			// we'll use a simple approach: assume expanded by default
			// The visual glitch will be minimal with our timing approach

			const sidebarState = {
				shouldRestore: true,
				defaultState: 'expanded' // Most common state
			};

			console.log('✅ Sidebar state captured (default):', sidebarState);
			return sidebarState;
		} catch (error) {
			console.warn('⚠️ Error capturing sidebar state:', error);
			return { shouldRestore: false };
		}
	}

	/**
	 * Restore sidebar state after workspace switch to prevent visual glitch
	 */
	private restoreSidebarState(sidebarState: any): void {
		try {
			console.log('🔄 Ensuring sidebar stability...');

			if (!sidebarState || !sidebarState.shouldRestore) {
				console.log('ℹ️ No sidebar restoration needed');
				return;
			}

			// Ensure sidebar is in a stable state with a small delay
			setTimeout(() => {
				try {
					// Force sidebar to expanded state to prevent visual glitch
					this.sidebarService.expand('menu-sidebar');

					console.log('✅ Sidebar stabilized successfully');
				} catch (error) {
					console.warn('⚠️ Error stabilizing sidebar:', error);
				}
			}, 100); // Minimal delay for DOM stability
		} catch (error) {
			console.warn('⚠️ Error in restoreSidebarState:', error);
		}
	}

	/**
	 * Wait for organization and permissions to be loaded, then trigger header update
	 */
	private waitForOrganizationAndTriggerUpdate(): void {
		console.log('⏳ Waiting for organization and permissions to be loaded...');

		// Poll for organization and permissions to be loaded with timeout
		let attempts = 0;
		const maxAttempts = 30; // 15 seconds max (500ms * 30)

		const checkReadiness = () => {
			attempts++;
			console.log(`🔍 Checking readiness (attempt ${attempts}/${maxAttempts})...`);

			const hasOrganization = !!this.store.selectedOrganization;
			const hasUser = !!this.store.user;
			const hasUserRolePermissions = this.store.userRolePermissions && this.store.userRolePermissions.length > 0;
			const hasPermissions =
				hasUserRolePermissions && this.store.hasPermission(PermissionsEnum.CHANGE_SELECTED_EMPLOYEE);

			console.log('📊 Current state:', {
				user: hasUser,
				selectedOrganization: hasOrganization,
				organizationId: this.store.organizationId,
				userRolePermissions: this.store.userRolePermissions?.length || 0,
				hasUserRolePermissions: hasUserRolePermissions,
				hasPermissions: hasPermissions
			});

			if (hasOrganization && hasUser && hasUserRolePermissions) {
				console.log('✅ Organization and permissions loaded! Triggering header update...');
				this.forceHeaderUpdate();
			} else if (attempts < maxAttempts) {
				console.log('⏳ Still waiting for organization and permissions...');
				console.log('   - Organization:', hasOrganization ? '✅' : '❌');
				console.log('   - User:', hasUser ? '✅' : '❌');
				console.log('   - Permissions:', hasUserRolePermissions ? '✅' : '❌');
				setTimeout(checkReadiness, 500);
			} else {
				console.warn('⚠️ Timeout waiting for organization and permissions. Forcing header update anyway...');
				this.forceHeaderUpdate();
			}
		};

		// Start checking after a small delay
		setTimeout(checkReadiness, 500);
	}

	/**
	 * Force header update once organization is loaded
	 */
	private forceHeaderUpdate(): void {
		try {
			console.log('🎯 Forcing header update with loaded organization...');

			// Step 1: Force selector visibility reconfiguration
			this.forceSelectorVisibilityReconfiguration();

			if (this.store.selectedOrganization) {
				// Step 2: Force selectedOrganization$ observable to emit
				const currentOrg = this.store.selectedOrganization;
				console.log('🔍 Debug info before forcing observables:', {
					organization: currentOrg.name,
					organizationId: currentOrg.id,
					user: this.store.user?.email,
					userRolePermissions: this.store.userRolePermissions?.length || 0,
					hasChangeEmployeePermission: this.store.hasPermission(PermissionsEnum.CHANGE_SELECTED_EMPLOYEE),
					hasProjectViewPermission: this.store.hasAnyPermission(
						PermissionsEnum.ALL_ORG_VIEW,
						PermissionsEnum.ORG_PROJECT_VIEW
					),
					hasTeamViewPermission: this.store.hasAnyPermission(
						PermissionsEnum.ALL_ORG_VIEW,
						PermissionsEnum.ORG_TEAM_VIEW
					),
					allPermissions: this.store.userRolePermissions?.map((p) => p.permission) || []
				});

				this.store.selectedOrganization = null;
				setTimeout(() => {
					this.store.selectedOrganization = currentOrg;
					console.log('✅ selectedOrganization$ forced - waiting for header debounce (1.3s)...');

					// Wait for header component's debounceTime(1300) to complete
					setTimeout(() => {
						console.log('✅ Header selector checks should be completed now!');
						console.log('🔍 Final debug info:', {
							organization: this.store.selectedOrganization?.name,
							selectedEmployee: this.store.selectedEmployee?.firstName,
							selectedProject: this.store.selectedProject?.name,
							selectedTeam: this.store.selectedTeam?.name
						});
						this.applicationRef.tick();
					}, 1500); // 1.5s to be safe (header has 1.3s debounce)
				}, 50);
			} else {
				console.log('ℹ️ No organization available, header will update when organization loads');
			}

			// Force change detection
			setTimeout(() => {
				this.applicationRef.tick();
			}, 100);
		} catch (error) {
			console.warn('⚠️ Error forcing header update:', error);
		}
	}

	/**
	 * Force selector visibility reconfiguration to ensure selectors are enabled
	 */
	private forceSelectorVisibilityReconfiguration(): void {
		try {
			console.log('🔧 Forcing selector visibility reconfiguration...');

			// Get current route data to respect route-specific selector configuration
			const currentRouteData = this.router.routerState.root.firstChild?.snapshot?.data || {};
			const routeSelectors = currentRouteData.selectors || {};

			console.log('📊 Current route selector config:', routeSelectors);

			// Merge with default visibility, respecting route configuration
			const selectorConfig = Object.assign({}, DEFAULT_SELECTOR_VISIBILITY, routeSelectors);

			console.log('🎯 Final selector config:', selectorConfig);

			// IMPORTANT: Register each selector in the mapper first (this is what app.component.ts does)
			// Without this, selectorsMapper will be empty and getSelectorsVisibility() will emit {}
			Object.entries(selectorConfig).forEach(([id, value]) => {
				console.log(`🔧 Registering selector "${id}" with value:`, value);
				this.selectorBuilderService.setSelectorsVisibility(id, value as boolean);
			});

			// Now trigger the selector visibility update - this will emit the registered selectors
			this.selectorBuilderService.getSelectorsVisibility();

			console.log('✅ Selector visibility reconfiguration completed');
		} catch (error) {
			console.warn('⚠️ Error forcing selector visibility reconfiguration:', error);
		}
	}

	/**
	 * Force all store observables to emit
	 */
	private async forceAllObservablesToEmit(): Promise<void> {
		console.log('🔄 Forcing all observables to emit...');

		try {
			// Force user$ observable
			if (this.store.user) {
				const currentUser = this.store.user;
				this.store.user = null;
				await new Promise((resolve) => setTimeout(resolve, 10));
				this.store.user = currentUser;
			}

			// Force selectedOrganization$ observable
			if (this.store.selectedOrganization) {
				const currentOrg = this.store.selectedOrganization;
				this.store.selectedOrganization = null;
				await new Promise((resolve) => setTimeout(resolve, 10));
				this.store.selectedOrganization = currentOrg;
			}

			// Force selectedEmployee$ observable
			if (this.store.selectedEmployee) {
				const currentEmployee = this.store.selectedEmployee;
				this.store.selectedEmployee = null;
				await new Promise((resolve) => setTimeout(resolve, 10));
				this.store.selectedEmployee = currentEmployee;
			}

			console.log('✅ All observables forced to emit');
		} catch (error) {
			console.warn('⚠️ Error forcing observables to emit:', error);
		}
	}

	/**
	 * Force header component to re-evaluate everything
	 */
	private async forceHeaderComponentUpdate(): Promise<void> {
		console.log('🔄 Forcing header component update...');

		try {
			// Step 1: Force selector visibility reconfiguration
			this.forceSelectorVisibilityReconfiguration();

			// Step 2: Wait for header debounce time
			await new Promise((resolve) => setTimeout(resolve, 1500));

			// Step 3: Force multiple change detections
			for (let i = 0; i < 3; i++) {
				this.applicationRef.tick();
				await new Promise((resolve) => setTimeout(resolve, 100));
			}

			console.log('✅ Header component update completed');
		} catch (error) {
			console.warn('⚠️ Error forcing header component update:', error);
		}
	}
}
