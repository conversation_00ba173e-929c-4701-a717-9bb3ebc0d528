import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NbButtonModule, NbContextMenuModule, NbIconModule, NbSpinnerModule } from '@nebular/theme';
import { WorkspacesComponent } from './workspaces.component';
import { WorkspaceResetService } from './workspace-reset.service';
import { WorkspaceAdvancedResetService } from './workspace-advanced-reset.service';
import { WorkspaceSwitchNotificationService } from './workspace-switch-notification.service';

@NgModule({
	imports: [CommonModule, NbButtonModule, NbContextMenuModule, NbIconModule, NbSpinnerModule],
	declarations: [WorkspacesComponent],
	providers: [WorkspaceResetService, WorkspaceAdvancedResetService],
	exports: [WorkspacesComponent]
})
export class WorkspacesModule {}
