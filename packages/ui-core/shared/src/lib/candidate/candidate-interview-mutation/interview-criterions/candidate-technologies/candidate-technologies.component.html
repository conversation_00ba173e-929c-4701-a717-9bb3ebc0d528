<nb-card class="add-criterion-card mb-0">
	<nb-card-header>
		<h6>
			{{ 'CANDIDATES_PAGE.CRITERIONS.TECHNOLOGY_STACK' | translate }}
		</h6>
		<div class="row m-0 mt-2">
			<form class="col-12 pl-0" [formGroup]="form" *ngIf="form">
				<div formArrayName="technologies">
					<div *ngFor="let technology of technologies?.controls; let i = index">
						<div [formGroupName]="i">
							<input
								formControlName="name"
								class="col-12"
								nbInput
								type="text"
								placeholder="{{ 'FORM.PLACEHOLDERS.ADD_TECHNOLOGY' | translate }}"
								fullWidth
							/>
						</div>
					</div>
				</div>
			</form>
		</div>
		<div class="row m-0 mt-3">
			<span class="col-2 pl-2 pr-1"
				><button class="w-100" nbButton status="success" (click)="save()">
					{{ 'BUTTONS.SAVE' | translate }}
				</button></span
			>
		</div>
	</nb-card-header>
	<nb-card-body class="existedNames" *ngIf="existedTechNames?.length > 0">
		<span>{{ 'CANDIDATES_PAGE.CRITERIONS.ALREADY_EXISTED' | translate }}</span>
		<div class="existedNames-card">
			<div class="existedName" *ngFor="let name of existedTechNames">
				{{ name }}
			</div>
		</div>
	</nb-card-body>
	<nb-card-body class="technologies">
		<div class="technologies-card" *ngFor="let technology of technologiesList; let i = index">
			<div class="technology">
				<p class="technology-text">{{ technology.name }}</p>
				<div class="technology-buttons">
					<nb-icon
						class="technology-buttons-icons pl-1"
						icon="edit"
						(click)="edit(i, technology.id)"
					></nb-icon
					><nb-icon class="technology-buttons-icons pl-1" icon="close" (click)="remove(technology)"></nb-icon>
				</div>
			</div>
		</div>
	</nb-card-body>
</nb-card>
