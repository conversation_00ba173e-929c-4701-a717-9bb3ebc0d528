<nb-card>
	<nb-card-header class="d-flex flex-column">
		<span class="cancel"><i class="fas fa-times" (click)="closeDialog()"></i></span>
		<div class="d-flex flex-row p-0">
			<img
				*ngIf="!isCalendar"
				class="candidate-image"
				[src]="selectedCandidate?.user.imageUrl"
				alt="Candidate Avatar"
			/>
			<div class="d-flex flex-column mr-3 ml-3">
				<h5 class="title" [innerText]="headerTitle"></h5>
				<span class="sub-title" *ngIf="!isCalendar">
					{{ selectedCandidate?.user?.name }}
				</span>
			</div>
		</div>
	</nb-card-header>
	<nb-card-body class="stepper">
		<nb-stepper #stepper disableStepNavigation>
			<nb-step [label]="step1">
				<ng-template #step1>
					{{ 'CANDIDATES_PAGE.EDIT_CANDIDATE.INTERVIEW.STEP_1' | translate }}
				</ng-template>
				<div class="form-group" *ngIf="isCalendar">
					<label for="note" class="label">
						{{ 'FORM.LABELS.CANDIDATE' | translate }}
					</label>
					<ga-candidate-select
						[placeholder]="'FORM.PLACEHOLDERS.ADD_REMOVE_CANDIDATE' | translate"
						(selectedChange)="onCandidateSelected($event)"
						[selectedCandidateId]="selectedCandidateId"
					></ga-candidate-select>
				</div>
				<ga-candidate-interview-form
					[interviews]="interviews"
					#candidateInterviewForm
					(titleExist)="titleExist($event)"
					[isCalendar]="isCalendar"
					[editData]="editData"
				></ga-candidate-interview-form>
				<div class="text-left">
					<button
						status="basic"
						outline
						class="green"
						size="small"
						[disabled]="candidateInterviewForm.form.invalid || isTitleExist"
						nbButton
						nbStepperNext
						(click)="next()"
					>
						{{ 'CANDIDATES_PAGE.EDIT_CANDIDATE.INTERVIEW.NEXT' | translate }}
					</button>
				</div>
			</nb-step>
			<nb-step [label]="step2">
				<ng-template #step2>
					{{ 'CANDIDATES_PAGE.EDIT_CANDIDATE.INTERVIEW.STEP_2' | translate }}
				</ng-template>
				<ga-candidate-criterions-form
					#candidateCriterionsForm
					[editSelectedTechnologies]="editData?.technologies"
					[editSelectedQualities]="editData?.personalQualities"
				></ga-candidate-criterions-form>

				<div class="add-criterion" *ngIf="isCriterionsVisible">
					<ga-candidate-technologies class="add-criterion-card"></ga-candidate-technologies>
					<ga-candidate-personal-qualities class="add-criterion-card"></ga-candidate-personal-qualities>
				</div>

				<div class="text-left">
					<button
						class="gray"
						status="basic"
						size="small"
						outline
						(click)="previous()"
						nbButton
						nbStepperPrevious
					>
						{{ 'CANDIDATES_PAGE.EDIT_CANDIDATE.INTERVIEW.PREVIOUS' | translate }}
					</button>

					<button status="warning" size="small" class="mr-3 ml-2" (click)="showCriterions()" nbButton>
						{{
							isCriterionsVisible
								? ('CANDIDATES_PAGE.EDIT_CANDIDATE.INTERVIEW.CLOSE_CRITERIONS' | translate)
								: ('CANDIDATES_PAGE.EDIT_CANDIDATE.INTERVIEW.CREATE_CRITERIONS' | translate)
						}}
					</button>
					<button class="green" status="basic" size="small" outline nbButton nbStepperNext>
						{{ 'CANDIDATES_PAGE.EDIT_CANDIDATE.INTERVIEW.NEXT' | translate }}
					</button>
				</div>
			</nb-step>
			<nb-step [label]="step3">
				<ng-template #step3>
					{{ 'CANDIDATES_PAGE.EDIT_CANDIDATE.INTERVIEW.STEP_3' | translate }}
				</ng-template>
				<ga-candidate-notification-form
					#candidateNotificationForm
					[interview]="interview"
					[employees]="employees"
					[selectedCandidate]="selectedCandidate"
				></ga-candidate-notification-form>
				<div class="text-left">
					<button class="btn gray" status="basic" outline="" (click)="previous()" nbButton nbStepperPrevious>
						{{ 'CANDIDATES_PAGE.EDIT_CANDIDATE.INTERVIEW.PREVIOUS' | translate }}
					</button>
					<button class="btn mr-3 ml-3" status="success" nbButton nbStepperSave (click)="save()">
						{{ 'CANDIDATES_PAGE.EDIT_CANDIDATE.INTERVIEW.SAVE' | translate }}
					</button>
				</div>
			</nb-step>
		</nb-stepper>
	</nb-card-body>
</nb-card>
