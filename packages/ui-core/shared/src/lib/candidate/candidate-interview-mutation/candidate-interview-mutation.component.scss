@use 'gauzy/_gauzy-dialogs' as *;

.candidate-image {
  width: 70px;
  height: 70px;
  border-radius: nb-theme(border-radius);
  object-fit: cover;
}

.candidate-image-small {
  max-width: 35px;
  max-height: 35px;
  border-radius: 13px;
  margin-right: 4px;
  margin-left: 4px;
}

.button-container {
  justify-content: space-between;
  flex-direction: row;
  display: flex;
}

.summary {
  min-width: 45rem;
}

.employee-name {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}

.title-icon {
  color: #8f9bb3 !important;
  font-size: 16px;
}

.stepper {
  max-height: 46rem;
}

.btn {
  width: 6rem;
}

:host {
  nb-card {
    background-color: nb-theme(gauzy-card-1);
  }
  ::ng-deep {
    ngx-timer-range-picker input.form-control {
      background: nb-theme(input-basic-background-color) !important;
    }
  }
}
.sub-title {
  margin-top: 4px;
  font-size: 12px;
  font-weight: 600;
  line-height: 13px;
  letter-spacing: 0em;
}
