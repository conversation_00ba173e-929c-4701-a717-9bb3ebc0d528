<div>
	<div class="notification-title mt-2">
		<strong>
			<nb-icon icon="calendar-outline" class="title-icon"></nb-icon>
			{{ 'CANDIDATES_PAGE.EDIT_CANDIDATE.INTERVIEW.SUMMARY' | translate }}
		</strong>
	</div>
	<nb-card class="summary">
		<nb-card-body class="summary-content">
			<div class="summary-content-text">
				<strong> {{ interview?.title }} </strong>
				{{ 'CANDIDATES_PAGE.EDIT_CANDIDATE.INTERVIEW.ON' | translate }}
				<strong>{{ interview?.startTime | date : 'fullDate' }} </strong
				>{{ 'CANDIDATES_PAGE.EDIT_CANDIDATE.INTERVIEW.FROM' | translate }}
				<strong>{{ interview?.startTime | date : 'shortTime' }}</strong
				>-
				<strong>{{ interview?.endTime | date : 'shortTime' }}</strong>
				{{ 'CANDIDATES_PAGE.EDIT_CANDIDATE.INTERVIEW.WITH' | translate }}
				<strong> {{ selectedCandidate?.user?.name }}</strong>
			</div>
			<div class="employeeName">
				<span *ngFor="let employee of employees" [nbTooltip]="employee?.user?.name" nbTooltipPlacement="top">
					<img class="candidate-image-small" [src]="employee?.user?.imageUrl" alt="Employee Avatar" />
				</span>
			</div>
		</nb-card-body>
	</nb-card>
	<div class="notification-title mt-2">
		<strong>
			<nb-icon icon="bell-outline" class="title-icon"></nb-icon>
			{{ 'CANDIDATES_PAGE.EDIT_CANDIDATE.INTERVIEW.EMAIL_NOTIFICATION' | translate }}
		</strong>
	</div>

	<div class="notify-checkboxes">
		<nb-card class="notify-checkboxes-card">
			<nb-card-body>
				<nb-checkbox (checkedChange)="checkedCandidate($event)" [checked]="isCandidateNotification">
					<strong>{{ 'CANDIDATES_PAGE.EDIT_CANDIDATE.INTERVIEW.NOTIFY_CANDIDATE' | translate }} </strong>
				</nb-checkbox>
				<div class="label">
					{{ 'CANDIDATES_PAGE.EDIT_CANDIDATE.INTERVIEW.DETAILS' | translate }}
					{{ selectedCandidate?.user?.name }}
				</div>
			</nb-card-body>
		</nb-card>
		<nb-card class="notify-checkboxes-card">
			<nb-card-body>
				<nb-checkbox (checkedChange)="checkedInterviewer($event)" [checked]="isInterviewerNotification"
					><strong>
						{{ 'CANDIDATES_PAGE.EDIT_CANDIDATE.INTERVIEW.NOTIFY_INTERVIEWERS' | translate }}</strong
					></nb-checkbox
				>
				<div class="label">
					{{ 'CANDIDATES_PAGE.EDIT_CANDIDATE.INTERVIEW.DETAILS' | translate }}
					<strong>
						{{ employees.length }}
					</strong>
					{{ 'CANDIDATES_PAGE.EDIT_CANDIDATE.INTERVIEW.INTERVIEWERS' | translate }}
				</div>
			</nb-card-body>
		</nb-card>
	</div>

	<ga-candidate-email
		[isCandidate]="true"
		[templateData]="interview"
		[employees]="employees"
		[selectedCandidate]="selectedCandidate"
		#emailCandidateForm
		*ngIf="isCandidateNotification"
	></ga-candidate-email>
	<ga-candidate-email
		[templateData]="interview"
		[employees]="employees"
		[selectedCandidate]="selectedCandidate"
		#emailInterviewerForm
		*ngIf="isInterviewerNotification"
	></ga-candidate-email>
</div>
