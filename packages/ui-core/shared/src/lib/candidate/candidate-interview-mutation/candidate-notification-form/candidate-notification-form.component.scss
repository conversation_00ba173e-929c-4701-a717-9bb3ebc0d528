@use 'themes' as *;

.summary {
  min-width: 45rem;
}

.employee<PERSON>ame {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}

.summary-content,
.notify-checkboxes {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

:host .summary-content-text {
  @include nb-ltr(margin-right, 5rem);
  @include nb-rtl(margin-left, 5rem);
}

.notify-checkboxes-card {
  width: 48%;
  margin-bottom: 1rem;
}

:host .notification-title {
  color: #8f9bb3;
  font-size: 15px;
  @include nb-ltr(padding, 0 0 5px 5px);
  @include nb-rtl(padding, 0 5px 5px 0);
}

.candidate-image-small {
  max-width: 35px;
  max-height: 35px;
  border-radius: 13px;
  margin-right: 4px;
  margin-left: 4px;
}
