@use 'themes' as *;

:host .form {
  width: 45rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: stretch;
  padding-bottom: 1.5rem;

  &-block {
    width: 48%;
    border: 1px #e4e9f2 solid;
    padding: 1rem;

    .placeholder {
      color: var(--gauzy-text-color-1);
      font-family: Open Sans, sans-serif;
      font-size: 0.8rem;
      font-weight: 600;
      line-height: 1rem;
      text-align: center;
    }
  }

  &-blocks {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: stretch;
  }

  &-title {
    color: #8f9bb3;
    font-size: 15px;
    @include nb-ltr(padding, 0 0 5px 5px);
    @include nb-rtl(padding, 0 5px 5px 0);
  }
}
