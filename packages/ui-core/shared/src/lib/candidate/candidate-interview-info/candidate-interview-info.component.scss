@use 'themes' as *;

.card-wrap {
  min-width: 40rem;
}

.card {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 0 10px;
  border: none;
  background-color: nb-theme(gauzy-card-1);
}

.card-info {
  margin: 0 auto;
  width: 34rem;
}

.header {
  padding: 0 5px;
}

:host .pages {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  @include nb-ltr(padding, 0 15px 10px 0px);
  @include nb-rtl(padding, 0 0px 10px 15px);
}

.disable {
  pointer-events: none;
  color: #ededed !important;
}

.show {
  pointer-events: visible;
  color: grey !important;
}

.arrow-icon {
  font-size: 24px;
}

.arrow-icon:focus {
  font-size: 28px;
}

.icons {
  margin-right: 10px;
  color: grey !important;
  pointer-events: visible;
}

.disabled {
  color: rgba(143, 155, 179, 0.48) !important;
  pointer-events: none;
}

.interview-info {
  display: flex;
  flex-direction: row;
  min-width: 500px;
}

:host .interview-data {
  color: nb-theme(gauzy-text-color-1);
  @include nb-ltr(padding-left, 30px);
  @include nb-rtl(padding-right, 30px);
  margin-top: -15px;
  font-size: 18px;
  font-weight: 600;
}

.interview-text {
  font-size: 14px;
  color: grey !important;
}
