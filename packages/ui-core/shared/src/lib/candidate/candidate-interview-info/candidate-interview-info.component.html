<nb-card class="card-wrap">
	<div class="ml-auto mt-3 mr-3">
		<nb-icon
			icon="edit-outline"
			class="mr-1"
			(click)="edit()"
			[ngClass]="{
				icons: !isPastInterview(currentInterview),
				disabled: isPastInterview(currentInterview)
			}"
		></nb-icon>
		<nb-icon icon="close-outline" class="icons" (click)="closeDialog()"></nb-icon>
	</div>

	<div class="card">
		<nb-icon
			*ngIf="isSlider && interviews.length > 1"
			icon="arrow-ios-back"
			class="arrow-icon"
			(click)="previous()"
			[ngClass]="{ show: isPreviousBtn, disable: !isPreviousBtn }"
		></nb-icon>

		<div class="card-info">
			<nb-card-header class="d-flex header">
				<div>
					<h5>
						{{ currentInterview?.title }}
					</h5>
					<p class="interview-info">
						{{ 'CANDIDATES_PAGE.INTERVIEW_INFO_MODAL.SCHEDULED' | translate }}
						{{ timeUpdate }}
					</p>
				</div>
			</nb-card-header>
			<nb-card-body>
				<div>
					<div class="interview-info">
						<nb-icon icon="calendar-outline" class="icons"></nb-icon>
						<p class="interview-text">
							{{ 'CANDIDATES_PAGE.INTERVIEW_INFO_MODAL.DATE' | translate }}
						</p>
					</div>
					<p class="interview-data">
						{{ currentInterview?.startTime | date : 'fullDate' }}
					</p>
				</div>
				<div>
					<div class="interview-info">
						<nb-icon icon="clock-outline" class="icons"></nb-icon>
						<p class="interview-text">
							{{ 'CANDIDATES_PAGE.INTERVIEW_INFO_MODAL.TIME' | translate }}
						</p>
					</div>
					<p class="interview-data">
						{{ currentInterview?.startTime | date : 'shortTime' }} -
						{{ currentInterview?.endTime | date : 'shortTime' }}
					</p>
				</div>
				<div>
					<div class="interview-info">
						<nb-icon icon="person-outline" class="icons"></nb-icon>
						<p class="interview-text">
							{{ 'CANDIDATES_PAGE.INTERVIEW_INFO_MODAL.CANDIDATE' | translate }}
						</p>
					</div>
					<p class="interview-data">
						{{ selectedCandidate?.user?.firstName }}
						{{ selectedCandidate?.user?.lastName }}
					</p>
				</div>
				<div>
					<div class="interview-info">
						<nb-icon icon="people-outline" class="icons"></nb-icon>
						<p class="interview-text">
							{{ 'CANDIDATES_PAGE.INTERVIEW_INFO_MODAL.INTERVIEWERS' | translate }}
						</p>
					</div>
					<p class="interview-data">
						{{ nameList }}
					</p>
				</div>
				<div *ngIf="currentInterview?.location">
					<div class="interview-info">
						<nb-icon icon="navigation-2-outline" class="icons"></nb-icon>
						<p class="interview-text">
							{{ 'CANDIDATES_PAGE.INTERVIEW_INFO_MODAL.LOCATION' | translate }}
						</p>
					</div>
					<p class="interview-data">
						{{ currentInterview?.location }}
					</p>
				</div>
				<div *ngIf="currentInterview?.note">
					<div class="interview-info">
						<nb-icon icon="edit-outline" class="icons"></nb-icon>
						<p class="interview-text">
							{{ 'CANDIDATES_PAGE.INTERVIEW_INFO_MODAL.NOTE' | translate }}
						</p>
					</div>
					<p class="interview-data">{{ currentInterview?.note }}</p>
				</div>
			</nb-card-body>
		</div>

		<nb-icon
			*ngIf="isSlider && interviews.length > 1"
			icon="arrow-ios-forward"
			class="arrow-icon"
			(click)="next()"
			[ngClass]="{ disable: !isNextBtn, show: isNextBtn }"
		></nb-icon>
	</div>
	<div class="pages" *ngIf="isSlider && interviews.length > 1">
		<div class="interview-text">
			{{ index }}
			{{ 'CANDIDATES_PAGE.INTERVIEW_INFO_MODAL.OF' | translate }}
			{{ interviews.length }}
			{{ 'CANDIDATES_PAGE.INTERVIEW_INFO_MODAL.INTERVIEWS_LOWER_CASE' | translate }}
		</div>
	</div>
</nb-card>
