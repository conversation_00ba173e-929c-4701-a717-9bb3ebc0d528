<nb-card>
	<nb-card-header class="d-flex flex-column">
		<span class="cancel">
			<i class="fas fa-times" (click)="closeDialog()"></i>
		</span>
		<h5 class="title">
			{{ 'CANDIDATES_PAGE.ADD_CANDIDATE' | translate }}
		</h5>
	</nb-card-header>
	<nb-card-body>
		<nb-stepper [disableStepNavigation]="true" #stepper>
			<nb-step [label]="step1">
				<ng-template #step1>
					{{ 'CANDIDATES_PAGE.ADD_CANDIDATES.STEP_1' | translate }}
				</ng-template>

				<ng-template [ngIf]="candidates.length > 0">
					<div class="row">
						<div class="col-auto mt-2 mb-2">
							<nb-tag-list (tagRemove)="onCandidateRemove($event)">
								<nb-tag
									removable
									*ngFor="let candidate of candidates"
									[text]="candidate?.user?.email"
								></nb-tag>
							</nb-tag-list>
						</div>
					</div>
				</ng-template>

				<ga-user-basic-info-form
					#userBasicInfo
					[isShowRole]="false"
					[isCandidate]="true"
				></ga-user-basic-info-form>

				<div class="text-left">
					<button
						class="mr-2"
						status="basic"
						outline
						(click)="gotoStep(3)"
						*ngIf="candidates.length"
						nbButton
					>
						{{ 'BUTTONS.CANCEL' | translate }}
					</button>
					<button
						status="basic"
						[disabled]="userBasicInfo.form.invalid"
						nbButton
						class="green"
						size="small"
						outline
						nbStepperNext
					>
						{{ 'CANDIDATES_PAGE.ADD_CANDIDATES.NEXT' | translate }}
					</button>
				</div>
			</nb-step>
			<nb-step [label]="step2">
				<ng-template #step2>
					{{ 'CANDIDATES_PAGE.ADD_CANDIDATES.STEP_2' | translate }}
				</ng-template>
				<div class="file-uploader">
					<div class="label">
						{{ 'FORM.LABELS.CV_URL' | translate }}
					</div>
					<ga-candidate-cv #candidateCv></ga-candidate-cv>
				</div>
				<div class="text-left">
					<button status="basic" class="gray" size="small" nbButton outline nbStepperPrevious>
						{{ 'CANDIDATES_PAGE.ADD_CANDIDATES.PREVIOUS' | translate }}
					</button>
					<button nbButton outline size="small" class="green mr-3 ml-3" nbStepperNext>
						{{ 'CANDIDATES_PAGE.ADD_CANDIDATES.NEXT' | translate }}
					</button>
				</div>
			</nb-step>
			<nb-step [label]="step3">
				<ng-template #step3>
					{{ 'CANDIDATES_PAGE.ADD_CANDIDATES.STEP_3' | translate }}
				</ng-template>
				<div class="text-left">
					<button nbButton outline class="green" size="small" (click)="addCandidate()">
						{{ 'CANDIDATES_PAGE.ADD_CANDIDATES.ADD_ANOTHER_CANDIDATE' | translate }}
					</button>
					<button status="success" size="small" (click)="add()" class="mr-3 ml-3" nbButton>
						{{ 'CANDIDATES_PAGE.ADD_CANDIDATES.FINISHED_ADDING' | translate }}
					</button>
				</div>
			</nb-step>
		</nb-stepper>
	</nb-card-body>
</nb-card>
