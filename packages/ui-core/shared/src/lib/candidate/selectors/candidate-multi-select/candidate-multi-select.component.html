<nb-select
	multiple
	[selected]="selectedCandidateIds"
	(selectedChange)="onCandidateSelected($event)"
	fullWidth
	[placeholder]="'FORM.PLACEHOLDERS.ADD_REMOVE_CANDIDATES' | translate"
>
	<nb-option *ngFor="let candidate of candidates$ | async" [value]="candidate.id">
		<ngx-avatar
			size="sm"
			[src]="candidate.user?.imageUrl"
			[name]="candidate.user?.name"
			[isOption]="true"
		></ngx-avatar>
	</nb-option>
</nb-select>
