:host {
  ::ng-deep {
    .ng-select.ng-select-multiple .ng-select-container .ng-value-container {
      flex-wrap: nowrap;
      overflow: hidden;
    }
  }
}
ngx-avatar ::ng-deep .inner-wrapper {
  display: flex;
  flex-direction: row;
  .names-wrapper {
    margin: 0 10px;
  }
}
.container {
  padding: inherit;
  display: flex;
  align-items: center;
  gap: 2%;
  input {
    width: inherit;
  }
}
nb-checkbox {
  text-wrap: nowrap;
}
