<nb-card class="card-wrap">
	<nb-card-header class="d-flex flex-column">
		<span class="cancel"><i class="fas fa-times" (click)="closeDialog()"></i></span>
		<h5 class="title">
			{{ 'CANDIDATES_PAGE.EDIT_CANDIDATE.LEAVE_FEEDBACK' | translate }}
			{{ currentInterview?.title }}
		</h5>
	</nb-card-header>
	<nb-card-body>
		<form class="form" [formGroup]="form" *ngIf="form">
			<ga-candidate-interviewer-select
				class="select"
				[interviewers]="currentInterview.employees"
				[placeholder]="'FORM.PLACEHOLDERS.ADD_REMOVE_EMPLOYEE' | translate"
				[disabledIds]="disabledIds"
				(selectedChange)="onMembersSelected($event)"
			></ga-candidate-interviewer-select>
			<div class="label-wrap">
				<ga-star-rating-output
					[rate]="averageRating"
					*ngIf="technologiesList[0] || personalQualitiesList[0]"
				></ga-star-rating-output>
				<ga-star-rating-input
					formControlName="rating"
					*ngIf="!(technologiesList[0] || personalQualitiesList[0])"
				></ga-star-rating-input>

				<nb-radio-group [(ngModel)]="status" [ngModelOptions]="{ standalone: true }" class="radio-group">
					<nb-radio [value]="'HIRED'" [disabled]="isRejected"
						>{{ 'CANDIDATES_PAGE.EDIT_CANDIDATE.INTERVIEW.HIRE' | translate }}
						<span *ngIf="interviewers?.length > 1"> ({{ statusHire }}/{{ interviewers?.length }}) </span
						><i
							class="fas fa-thumbs-up"
							[ngClass]="{
								success: !isRejected,
								disabled: isRejected
							}"
						></i>
					</nb-radio>

					<nb-radio [value]="'REJECTED'"
						>{{ 'CANDIDATES_PAGE.EDIT_CANDIDATE.INTERVIEW.REJECT' | translate }}
						<i class="fas error fa-thumbs-down"></i
					></nb-radio>
				</nb-radio-group>
			</div>
			<div class="form-group">
				<div *ngIf="technologiesList[0] || personalQualitiesList[0]">
					<div class="form-blocks mt-3">
						<div
							[ngStyle]="{
								width: personalQualitiesList.length === 0 ? '100%' : '50%'
							}"
							*ngIf="technologiesList.length > 0"
						>
							<div class="label mb-2">
								{{ 'CANDIDATES_PAGE.CRITERIONS.TECHNOLOGY_STACK' | translate }}
							</div>

							<div
								class="criterion"
								*ngFor="
									let technologyRating of form.get('technologies').controls as controls;
									index as i
								"
							>
								<div *ngIf="technologiesList">
									{{ technologiesList[i]?.name }}
								</div>
								<ga-star-rating-input [formControl]="technologyRating"></ga-star-rating-input>
							</div>
						</div>
						<div
							[ngStyle]="{
								width: technologiesList.length === 0 ? '100%' : '50%'
							}"
							*ngIf="personalQualitiesList.length > 0"
						>
							<div class="label mb-2">
								{{ 'CANDIDATES_PAGE.CRITERIONS.PERSONAL_QUALITIES' | translate }}
							</div>
							<div
								class="criterion"
								*ngFor="
									let personalQualityRating of form.get('personalQualities').controls as controls;
									index as i
								"
							>
								<div *ngIf="personalQualitiesList">
									{{ personalQualitiesList[i]?.name }}
								</div>
								<ga-star-rating-input [formControl]="personalQualityRating"></ga-star-rating-input>
							</div>
						</div>
					</div>
				</div>
			</div>

			<div class="form-group">
				<label for="description" class="label">{{ 'FORM.LABELS.FEEDBACK_DESCRIPTION' | translate }}</label>
				<textarea
					id="description"
					nbInput
					fullWidth
					formControlName="description"
					[placeholder]="'FORM.PLACEHOLDERS.FEEDBACK_DESCRIPTION' | translate"
				></textarea>
			</div>
		</form>
	</nb-card-body>
	<nb-card-footer>
		<button (click)="closeDialog()" size="small" nbButton status="basic" outline>
			{{ 'BUTTONS.CANCEL' | translate }}
		</button>
		<button class="mr-3 ml-3" (click)="createFeedback()" nbButton status="success" size="small">
			{{ 'BUTTONS.SAVE' | translate }}
		</button>
	</nb-card-footer>
</nb-card>
