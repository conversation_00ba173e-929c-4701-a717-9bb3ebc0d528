@use 'gauzy/_gauzy-dialogs' as *;

.card-wrap {
  min-width: 40rem;
  background-color: var(--gauzy-card-1);
}

.buttons {
  flex-direction: row;
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.form-btn {
  width: 10rem;
}

.form {
  flex-direction: column;
  display: flex;
  justify-content: space-around;
  align-items: flex-start;
  width: 100%;
  padding-bottom: 1rem;
}

.label-wrap {
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-top: 0.5rem;
}

#description {
  min-height: 10rem;
  margin-top: 0.5rem;
}

.form-group {
  width: 100%;
}

.radio-group {
  flex-direction: row;
  display: flex;
  justify-content: space-around;
  align-items: center;
}

:host .fas {
  @include nb-ltr(padding-left, 3px);
  @include nb-rtl(padding-right, 3px);
  font-size: 15px;
}

.success {
  color: #00d68f;
}

.error {
  color: #ff3d71;
}

.disabled {
  color: rgba(143, 155, 179, 0.48);
}

.select {
  width: 100%;
}

.form-blocks {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: stretch;
  flex-wrap: wrap;
}

:host .form-title {
  color: #8f9bb3;
  font-size: 15px;
  padding: 0 0 5px 5px;
  @include nb-ltr(padding, 0 0px 5px 5px);
  @include nb-rtl(padding, 0 5px 5px 0px);
}

:host .criterion {
  border: 1px #e4e9f2 solid;
  @include nb-ltr(margin, 0.25rem 0.25rem 0.25rem 0);
  @include nb-rtl(margin, 0.25rem 0 0.25rem 0.25rem);
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 0.2rem 0.5rem;
  border-radius: 0.25rem;
}

.accordion {
  min-width: 47rem;
  margin-top: 0.75rem;
  box-shadow: none;
  border: 1px #e4e9f2 solid;

  &-title {
    background-color: #f7f9fc;
    border-color: #e4e9f2;
    color: #8f9bb3;
    font-weight: 400;
    padding: 7px 18px;
  }
}
