@use 'gauzy/_gauzy-dialogs' as *;

:host nb-card {
  //width: 1145px;
  background-color: nb-theme(gauzy-card-1);
  input {
    background-color: nb-theme(select-outline-basic-background-color);
  }

  ::ng-deep .step-content {
    padding-bottom: 0px;
  }
}

nb-card-body {
  .stepper-buttons {
    display: flex;
    justify-content: flex-start;
    gap: 10px;
    &.two {
      margin-left: 15px;
    }
  }

  .no-image {
    background-color: rgba(126, 126, 143, 0.1);
  }

  .location {
    width: 945px;
  }

  .leaflet {
    padding: 0;
  }

  .organization-container {
    padding-right: 70px;
    padding-top: 35px;
    margin-right: 70px;
    border-right: 1px solid rgba(0, 0, 0, 0.1);
    transition: transform 150ms ease-in-out;

    .organization-photo {
      width: fit-content;
      height: fit-content;
      position: relative;

      .image-overlay {
        pointer-events: none;
        background: black;
        position: absolute;
        height: 100%;
        width: 100%;
        border-radius: 13px;
      }

      img,
      .no-image {
        width: 180px;
        height: 180px;
        border-radius: 13px;
        position: relative;

        i {
          position: absolute;
          top: calc(50% - 16px / 2);
          left: calc(50% - 16px / 2);
        }
      }

      img {
        object-fit: cover;
      }

      svg {
        z-index: 2;
        transition: opacity 0.2s ease-in;
        opacity: 0.3;
        position: absolute;
        top: calc(50% - 48px / 2);
        left: calc(50% - 48px / 2);

        g circle {
          fill: nb-theme(text-primary-color);
        }
      }
    }
  }
}

.help-icon {
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
  margin-left: 0.5rem;
}
