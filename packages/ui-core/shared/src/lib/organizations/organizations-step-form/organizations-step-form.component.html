<nb-card [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large" nbSpinnerMessage="">
	<nb-card-header>
		<span *ngIf="closable" class="cancel">
			<i class="fas fa-times" (click)="close()"></i>
		</span>
		<h5 class="title">
			{{ 'ORGANIZATIONS_PAGE.ORGANIZATION_ADD' | translate }}
		</h5>
	</nb-card-header>
	<nb-card-body class="p-3">
		<nb-stepper #stepper>
			<!-- Step 1 Organization Main Form -->
			<nb-step [stepControl]="orgMainForm" [label]="main">
				<ng-template #main>
					{{ 'ORGANIZATIONS_PAGE.MAIN' | translate }}
				</ng-template>
				<div class="content">
					<div class="organization-container">
						<div class="organization-photo">
							<ngx-image-uploader
								[folder]="'organization_avatars'"
								(changeHoverState)="hoverState = $event"
								(uploadedImageAsset)="updateImageAsset($event)"
								(uploadImageAssetError)="handleImageUploadError($event)"
							></ngx-image-uploader>
							<svg
								xmlns="http://www.w3.org/2000/svg"
								xmlns:xlink="http://www.w3.org/1999/xlink"
								width="48"
								height="48"
								viewBox="0 0 68 68"
								[ngStyle]="{
									opacity: hoverState ? '1' : '0.3'
								}"
								*ngIf="orgMainForm && orgMainForm.get('imageUrl').value"
							>
								<defs>
									<path
										id="a"
										d="M28.667 31.333a2 2 0 1 0-.002-4.001 2 2 0 0 0 .002 4.001m13.333 12H26.748l9.34-7.793c.328-.279.923-.277 1.244-.001l6.001 5.12V42c0 .736-.597 1.333-1.333 1.333M26 24.667h16c.736 0 1.333.597 1.333 1.333v11.152l-4.27-3.643c-1.32-1.122-3.386-1.122-4.694-.008l-9.702 8.096V26c0-.736.597-1.333 1.333-1.333M42 22H26c-2.205 0-4 1.795-4 4v16c0 2.205 1.795 4 4 4h16c2.205 0 4-1.795 4-4V26c0-2.205-1.795-4-4-4"
									/>
								</defs>
								<g fill="none" fill-rule="evenodd">
									<circle cx="34" cy="34" r="34" fill="#0091FF" opacity=".3" />
									<circle cx="34" cy="34" r="26" fill="#0091FF" opacity=".9" />
									<use fill="#FFF" fill-rule="nonzero" xlink:href="#a" />
								</g>
							</svg>
							<div class="image-overlay" [ngStyle]="{ opacity: hoverState ? '0.2' : '0' }"></div>
							<ng-template [ngIf]="orgMainForm.get('imageUrl').value" [ngIfElse]="noImageTemplate">
								<img
									[src]="orgMainForm.get('imageUrl').value"
									alt="Organization Photo"
									(mouseenter)="hoverState = true"
									(mouseleave)="hoverState = false"
								/>
							</ng-template>
							<ng-template #noImageTemplate>
								<div class="no-image">
									<i class="fas fa-image"></i>
								</div>
							</ng-template>
						</div>
					</div>
					<form class="step-container" [formGroup]="orgMainForm" (ngSubmit)="orgMainForm.markAsDirty()">
						<div class="fields">
							<div class="row">
								<div class="col">
									<div class="form-group">
										<label class="label" for="nameInput">
											{{ 'FORM.LABELS.ORGANIZATION_NAME' | translate }}
										</label>
										<input
											fullWidth
											id="nameInput"
											type="text"
											nbInput
											formControlName="name"
											[placeholder]="'FORM.LABELS.ORGANIZATION_NAME' | translate"
											[status]="
												FormHelpers.isInvalidControl(orgMainForm, 'name') ? 'danger' : 'basic'
											"
										/>
									</div>
								</div>
								<div class="col">
									<ga-currency
										[formControl]="orgMainForm.get('currency')"
										(optionChange)="currencyChanged($event)"
									></ga-currency>
								</div>
							</div>
							<div class="row">
								<div class="col-6">
									<div class="form-group">
										<div>
											<label class="label" for="officialNameInput">
												{{ 'FORM.LABELS.OFFICIAL_NAME' | translate }}
											</label>
											<nb-icon
												class="help-icon"
												status="info"
												[nbTooltip]="'POP_UPS.OFFICIAL_NAME' | translate"
												icon="question-mark-circle-outline"
											></nb-icon>
										</div>
										<input
											fullWidth
											id="officialNameInput"
											type="text"
											nbInput
											formControlName="officialName"
											[placeholder]="'FORM.PLACEHOLDERS.OFFICIAL_NAME' | translate"
										/>
									</div>
								</div>
								<div class="col-6">
									<div class="form-group">
										<label class="label" for="taxIdInput">
											{{ 'FORM.LABELS.TAX_ID' | translate }}
										</label>
										<input
											fullWidth
											id="taxIdInput"
											type="text"
											nbInput
											formControlName="taxId"
											[placeholder]="'FORM.PLACEHOLDERS.TAX_ID' | translate"
										/>
									</div>
								</div>
							</div>
						</div>
						<ng-container *ngIf="!isOnboarding">
							<div class="row">
								<div class="col-6">
									<ga-tags-color-input
										[selectedTags]="tags"
										(selectedTagsEvent)="selectedTagsEvent($event)"
										[isTenantLevel]="true"
									></ga-tags-color-input>
								</div>
							</div>
						</ng-container>
						<div class="" style="margin-top: 20px">
							<button
								[disabled]="orgMainForm.invalid"
								nbButton
								nbStepperNext
								status="basic"
								class="green"
								outline
							>
								{{ 'BUTTONS.NEXT' | translate }}
							</button>
						</div>
					</form>
				</div>
			</nb-step>
			<!-- Step 1 Organization Main Form End -->

			<!-- Step 2 Organization Location Form -->
			<nb-step [stepControl]="locationForm" [label]="location">
				<ng-template #location>
					{{ 'ORGANIZATIONS_PAGE.LOCATION' | translate }}
				</ng-template>
				<form
					*ngIf="locationForm"
					class="step-container row location"
					[formGroup]="locationForm"
					(ngSubmit)="locationForm.markAsDirty()"
				>
					<div class="col-5">
						<ga-location-form
							#locationFormDirective
							[form]="locationForm"
							[showAutocompleteSearch]="true"
							(mapCoordinatesEmitter)="onCoordinatesChanges($event)"
							(mapGeometryEmitter)="onGeometrySend($event)"
						></ga-location-form>
					</div>
					<div class="leaflet col-7">
						<ng-container *ngIf="stepper._selectedIndex === 1">
							<ga-leaflet-map #leafletTemplate (mapClicked)="onMapClicked($event)"></ga-leaflet-map>
						</ng-container>
					</div>
					<div class="stepper-buttons two">
						<button nbButton class="gray" status="basic" outline nbStepperPrevious>
							{{ 'BUTTONS.PREVIOUS' | translate }}
						</button>
						<ng-template [ngIf]="locationFormBlank" [ngIfElse]="nextButton">
							<button nbButton class="green" status="basic" outline nbStepperNext>
								{{ 'BUTTONS.SKIP_CONTINUE' | translate : { label: 'address' } }}
							</button>
						</ng-template>
						<ng-template #nextButton>
							<button nbButton class="green" status="basic" outline nbStepperNext>
								{{ 'BUTTONS.NEXT' | translate }}
							</button>
						</ng-template>
					</div>
				</form>
			</nb-step>
			<!-- Step 2 Organization Location Form End -->

			<!-- Step 3 Organization Bonus Form -->
			<nb-step [stepControl]="orgBonusForm" [label]="bonus">
				<ng-template #bonus>
					{{ 'SM_TABLE.BONUS' | translate }}
				</ng-template>
				<form class="step-container" [formGroup]="orgBonusForm" (ngSubmit)="orgBonusForm.markAsDirty()">
					<div class="row justify-content-center">
						<div [ngClass]="isOnboarding ? 'col-3' : 'col-6'">
							<div class="form-group">
								<label class="label">
									{{ 'FORM.LABELS.TYPE_OF_BONUS' | translate }}
								</label>
								<nb-select
									class="d-block"
									size="medium"
									formControlName="bonusType"
									[placeholder]="'FORM.PLACEHOLDERS.TYPE_OF_BONUS' | translate"
									(selectedChange)="loadDefaultBonusPercentage($event)"
									fullWidth
								>
									<nb-option [value]="null">
										{{ 'SM_TABLE.NONE' | translate }}
									</nb-option>
									<nb-option *ngFor="let type of defaultBonusTypes" [value]="type">
										{{ 'SM_TABLE.' + type | translate }}
									</nb-option>
								</nb-select>
							</div>
						</div>
						<div [ngClass]="isOnboarding ? 'col-3' : 'col-6'">
							<div class="form-group">
								<label class="label">
									{{ 'FORM.LABELS.BONUS_PERCENTAGE' | translate }}
								</label>
								<input
									nbInput
									type="number"
									[min]="0"
									formControlName="bonusPercentage"
									[placeholder]="'FORM.PLACEHOLDERS.BONUS_PERCENTAGE' | translate"
									fullWidth
									class="d-block"
									[status]="
										FormHelpers.isInvalidControl(orgBonusForm, 'bonusPercentage')
											? 'danger'
											: 'basic'
									"
								/>
							</div>
						</div>
					</div>
					<div class="stepper-buttons">
						<button nbButton class="gray" status="basic" outline nbStepperPrevious>
							{{ 'BUTTONS.PREVIOUS' | translate }}
						</button>
						<button
							[disabled]="orgBonusForm.invalid"
							nbButton
							class="green"
							status="basic"
							outline
							nbStepperNext
						>
							{{ 'BUTTONS.NEXT' | translate }}
						</button>
					</div>
				</form>
			</nb-step>
			<!-- Step 3 Organization Bonus Form End -->

			<!-- Step 4 Organization Settings Form -->
			<nb-step [stepControl]="orgSettingsForm" [label]="settings">
				<ng-template #settings>
					{{ 'ORGANIZATIONS_PAGE.SETTINGS' | translate }}
				</ng-template>
				<form class="step-container" [formGroup]="orgSettingsForm" (ngSubmit)="orgSettingsForm.markAsDirty()">
					<div class="fields">
						<div class="row">
							<div class="col-6">
								<ga-timezone-selector formControlName="timeZone"></ga-timezone-selector>
							</div>
							<div class="col-6">
								<div class="form-group">
									<label class="label" for="startWeekOnSelect">
										{{ 'FORM.LABELS.START_WEEK_ON' | translate }}
									</label>
									<nb-select
										class="d-block"
										size="medium"
										formControlName="startWeekOn"
										id="startWeekOnSelect"
										[placeholder]="'FORM.PLACEHOLDERS.START_WEEK_ON' | translate"
										fullWidth="true"
									>
										<nb-option *ngFor="let weekday of weekdays" [value]="weekday">
											{{ 'SM_TABLE.' + weekday | translate }}
										</nb-option>
									</nb-select>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="col-6">
								<div class="form-group">
									<label class="label">
										{{ 'FORM.LABELS.DATE_TYPE' | translate }}
									</label>
									<nb-select
										class="d-block"
										size="medium"
										formControlName="defaultValueDateType"
										[placeholder]="'FORM.PLACEHOLDERS.DATE_TYPE' | translate"
										fullWidth
									>
										<nb-option *ngFor="let type of defaultValueDateTypes" [value]="type">
											{{ 'SM_TABLE.' + type | translate }}
										</nb-option>
									</nb-select>
								</div>
							</div>
							<div class="col-6">
								<div class="form-group">
									<label class="label">
										{{ 'ORGANIZATIONS_PAGE.EDIT.REGIONS' | translate }}
									</label>
									<nb-select
										class="d-block"
										size="medium"
										[placeholder]="'FORM.PLACEHOLDERS.REGIONS' | translate"
										formControlName="regionCode"
										fullWidth
									>
										<nb-option *ngFor="let code of regionCodes" [value]="code">
											{{ 'SM_TABLE.REGION.' + code | translate }}
										</nb-option>
									</nb-select>
								</div>
							</div>
							<div class="col-6">
								<div class="form-group">
									<label class="label">
										{{ 'FORM.PLACEHOLDERS.NUMBER_FORMAT' | translate }}
									</label>
									<nb-select
										class="d-block"
										size="medium"
										[placeholder]="'FORM.PLACEHOLDERS.NUMBER_FORMAT' | translate"
										formControlName="numberFormat"
										fullWidth
									>
										<nb-option *ngFor="let numFormat of numberFormats" [value]="numFormat">
											{{ numberFormatPreview(numFormat) }}
										</nb-option>
									</nb-select>
								</div>
							</div>
							<div class="col-6">
								<div class="form-group">
									<label class="label">
										{{ 'FORM.LABELS.DATE_FORMAT' | translate }}
									</label>
									<nb-select
										class="d-block"
										size="medium"
										formControlName="dateFormat"
										[placeholder]="'FORM.PLACEHOLDERS.CHOOSE_FORMAT' | translate"
										fullWidth
									>
										<nb-option *ngFor="let format of listOfDateFormats" [value]="format">
											{{ dateFormatPreview(format) }}
										</nb-option>
									</nb-select>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="col-6">
								<div class="form-group">
									<label class="label">
										{{ 'FORM.LABELS.FISCAL_YEAR_START_DATE' | translate }}
									</label>
									<input
										fullWidth
										id="fiscalStartDate"
										type="date"
										nbInput
										formControlName="fiscalStartDate"
									/>
								</div>
							</div>
							<div class="col-6">
								<div class="form-group">
									<label class="label">
										{{ 'FORM.LABELS.FISCAL_YEAR_END_DATE' | translate }}
									</label>
									<input
										fullWidth
										type="date"
										id="fiscalEndDate"
										nbInput
										formControlName="fiscalEndDate"
									/>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="col-6">
								<div class="form-group invite-toggle">
									<label class="label">
										{{ 'FORM.LABELS.ENABLE_DISABLE_INVITES' | translate }}
									</label>
									<nb-toggle
										class="d-block"
										formControlName="invitesAllowed"
										status="primary"
										labelPosition="start"
										(checkedChange)="toggleExpiry($event)"
									>
										{{ 'FORM.LABELS.ALLOW_USER_INVITES' | translate }}
									</nb-toggle>
								</div>
							</div>
							<div class="col-6">
								<div class="form-group">
									<label class="label">
										{{ 'FORM.LABELS.INVITE_EXPIRY_PERIOD' | translate }}
									</label>
									<input
										nbInput
										type="number"
										[min]="0"
										formControlName="inviteExpiryPeriod"
										[placeholder]="'FORM.PLACEHOLDERS.INVITE_EXPIRY_PERIOD' | translate"
										fullWidth
										class="d-block"
										[status]="
											FormHelpers.isInvalidControl(orgSettingsForm, 'inviteExpiryPeriod')
												? 'danger'
												: 'basic'
										"
									/>
								</div>
							</div>
						</div>
					</div>
					<div class="stepper-buttons">
						<button nbButton class="gray" status="basic" outline nbStepperPrevious>
							{{ 'BUTTONS.PREVIOUS' | translate }}
						</button>
						<ng-template [ngIf]="isOnboarding" [ngIfElse]="addButton">
							<button
								[disabled]="orgSettingsForm.invalid"
								nbButton
								class="green"
								status="basic"
								outline
								nbStepperNext
							>
								{{ 'BUTTONS.NEXT' | translate }}
							</button>
						</ng-template>
						<ng-template #addButton>
							<button
								[disabled]="orgSettingsForm.invalid"
								(click)="addOrganization()"
								nbButton
								status="success"
								nbStepperNext
							>
								{{ 'BUTTONS.ADD' | translate }}
							</button>
						</ng-template>
					</div>
				</form>
			</nb-step>
			<!-- Step 4 Organization Settings Form End -->

			<!-- Step 5 Register as Employee Feature Form -->
			<nb-step [stepControl]="employeeFeatureForm" [label]="employeeFeature" [hidden]="!isOnboarding">
				<ng-template #employeeFeature>
					{{ 'ORGANIZATIONS_PAGE.REGISTER_AS_EMPLOYEE' | translate }}
				</ng-template>
				<form class="step-container" [formGroup]="employeeFeatureForm" (ngSubmit)="submitEmployeeFeature()">
					<div class="fields">
						<div class="row justify-content-center">
							<div class="col-6">
								<div class="form-group">
									<div class="mt-1">
										<label class="label" for="registerAsEmployee">
											{{ 'ORGANIZATIONS_PAGE.REGISTER_AS_EMPLOYEE' | translate }}
										</label>
										<nb-icon
											class="help-icon"
											status="info"
											[nbTooltip]="'POP_UPS.REGISTER_AS_EMPLOYEE_TOOLTIP' | translate"
											icon="question-mark-circle-outline"
										></nb-icon>
									</div>
									<nb-toggle
										class="d-block"
										formControlName="registerAsEmployee"
										status="primary"
										labelPosition="start"
										id="registerAsEmployee"
									>
										{{ 'FORM.LABELS.REGISTER_AS_EMPLOYEE_OF_ORGANIZATION' | translate }}
									</nb-toggle>
								</div>
							</div>
							<div class="col-6">
								<div class="form-group">
									<label for="startedWork" class="label">
										{{ 'FORM.LABELS.START_DATE' | translate }}
									</label>
									<input
										[nbDatepicker]="startWorkOnDatepicker"
										nbInput
										fullWidth
										[placeholder]="'FORM.PLACEHOLDERS.START_DATE' | translate"
										formControlName="startedWorkOn"
									/>
									<nb-datepicker #startWorkOnDatepicker></nb-datepicker>
								</div>
							</div>
						</div>
					</div>
					<div class="stepper-buttons">
						<button nbButton class="gray" status="basic" outline nbStepperPrevious>
							{{ 'BUTTONS.PREVIOUS' | translate }}
						</button>
						<ng-template [ngIf]="isOnboarding">
							<button [disabled]="employeeFeatureForm.invalid" nbButton status="success">
								{{ 'BUTTONS.ADD' | translate }}
							</button>
						</ng-template>
					</div>
				</form>
			</nb-step>
			<!-- Step 5 Register as Employee Feature Form End -->
		</nb-stepper>
	</nb-card-body>
</nb-card>
