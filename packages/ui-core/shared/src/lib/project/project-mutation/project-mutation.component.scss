// Import Gauzy dialogs styles
@use 'gauzy/_gauzy-dialogs' as *;

// Define styles for the .project-toggle class
.project-toggle {
	margin-top: 20px;
}

// Define styles for the .action-buttons class
.action-buttons {
	display: flex;
	justify-content: flex-start;
}

:host ::ng-deep {
	nb-card {
		// Apply margins based on language direction
		@include nb-rtl(margin, -16px -18px -16px -8px);
		@include nb-ltr(margin, -16px -8px -16px -18px);

		nb-card-body {
			padding: 0;
			nb-tab.content-active, .action-buttons {
				background-color: var(--gauzy-card-2);
				margin: 0;
			}

			nb-tab.content-active {
				height: calc(100vh - 22.5rem);
			}

			.action-buttons {
				padding: 1rem 2rem;
				border-radius: 0 0 var(--border-radius) var(--border-radius);
			}

			// Override background color for specific elements
			input, nb-select.appearance-outline.status-basic .select-button, .ng-select .ng-select-container {
				background-color: var(--background-basic-color-1) !important;
			}

			.ng-select .ng-select-container {
				input {
					background-color: unset !important;
				}
			}
		}
	}

	nb-toggle {
		div.checked+span.text {
			color: var(--text-primary-color);
		}
		div+span.text {
			color: var(--gauzy-text-color-2);
		}
	}

	label {
		font-size: 14px;
		font-weight: 600;
		line-height: 11px;
		letter-spacing: 0em;
		text-align: left;
		color: var(--gauzy-text-color-2);
	}
}

// Define styles for the .project-tab-container class
.project-tab-container {
	display: flex;
	gap: 1rem;

	.project-form-container {
		width: 65%;
	}
}

// Define styles for the .project-image-container class
.project-image-container {
	transition: transform 150ms ease-in-out;
	display: flex;
	flex-direction: column;
	position: relative;
	margin-right: 3rem;

	.project-image-photo {
		width: fit-content;
		height: 294px;
		position: relative;

		.image-overlay {
			pointer-events: none;
			background: black;
			position: absolute;
			height: 100%;
			width: 100%;
			border-radius: nb-theme(border-radius);
		}

		img,
		.image {
			width: 299px;
			height: 294px;
			object-fit: cover;
			border-radius: nb-theme(border-radius);
		}

		.image {
			background-color: rgba(126, 126, 143, 0.1);

			i {
				margin: 5px;
			}

			>span {
				color: rgba(126, 126, 143, 1);
				z-index: 2;
				transition: opacity 0.2s ease-in;
				position: absolute;
				top: calc(50% - 16px / 2);
				left: calc(50% - 141px / 2);
			}
		}

		svg {
			z-index: 2;
			transition: opacity 0.2s ease-in;
			opacity: 0.3;
			position: absolute;
			top: calc(50% - 68px / 2);
			left: calc(50% - 68px / 2);

			g circle {
				fill: var(--text-primary-color);
			}
		}
	}
}

:host {
	::ng-deep ngx-image-uploader input {
		height: 100% !important;
	}

	// Apply dialog styles
	@include dialog(transparent, var(--gauzy-card-1));
}
