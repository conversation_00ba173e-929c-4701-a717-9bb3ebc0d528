<nb-card class="main">
	<nb-card-header class="d-flex flex-column">
		<span class="cancel"><i class="fas fa-times" (click)="closeDialog()"></i></span>
		<h5 class="title">
			{{
				(requestApproval
					? 'APPROVAL_REQUEST_PAGE.EDIT_APPROVAL_REQUEST'
					: 'APPROVAL_REQUEST_PAGE.ADD_APPROVAL_REQUEST'
				) | translate
			}}
		</h5>
	</nb-card-header>
	<nb-card-body class="body">
		<form [formGroup]="form" (ngSubmit)="onSubmit()" #formDirective="ngForm">
			<div class="row">
				<div class="col-12 mb-3">
					<label for="name" class="label">
						{{ 'APPROVAL_REQUEST_PAGE.APPROVAL_REQUEST_NAME' | translate }}
					</label>
					<input
						nbInput
						type="text"
						formControlName="name"
						fullWidth
						[placeholder]="'APPROVAL_REQUEST_PAGE.APPROVAL_REQUEST_NAME' | translate"
						[ngClass]="{
							'status-danger': FormHelpers.isInvalidControl(form, 'name'),
							'status-success': FormHelpers.isValidControl(form, 'name')
						}"
					/>
				</div>
			</div>

			<div class="row">
				<div class="col-sm-6 mb-3">
					<label for="min_count" class="label">
						{{ 'APPROVAL_REQUEST_PAGE.APPROVAL_REQUEST_MIN_COUNT' | translate }}
					</label>
					<input
						nbInput
						type="number"
						[min]="0"
						formControlName="min_count"
						fullWidth
						[placeholder]="'APPROVAL_REQUEST_PAGE.APPROVAL_REQUEST_MIN_COUNT' | translate"
					/>
				</div>
				<div class="col-sm-6 mb-3">
					<label for="Approval Policy" class="label">
						{{ 'APPROVAL_REQUEST_PAGE.APPROVAL_REQUEST_APPROVAL_POLICY' | translate }}
					</label>
					<nb-select
						formControlName="approvalPolicyId"
						[selected]="selectedApprovalPolicy"
						(selectedChange)="onApprovalPolicySelected($event)"
						fullWidth
						placeholder="{{ 'APPROVAL_REQUEST_PAGE.CHOOSE_POLICIES' | translate }}"
					>
						<nb-option *ngFor="let approvalPolicy of approvalPolicies" [value]="approvalPolicy.id">
							{{ approvalPolicy.name }}</nb-option
						>
					</nb-select>
				</div>
			</div>
			<div class="row">
				<div class="col-sm-12 mb-3">
					<nb-radio-group (valueChange)="onParticipantsChange($event)" [value]="participants">
						<nb-radio [value]="'employees'">{{ 'APPROVAL_REQUEST_PAGE.EMPLOYEES' | translate }} </nb-radio>
						<nb-radio [value]="'teams'">{{ 'APPROVAL_REQUEST_PAGE.TEAMS' | translate }} </nb-radio>
					</nb-radio-group>
				</div>
			</div>
			<div class="row">
				<div class="col-sm-12 mb-3" *ngIf="participants === 'employees'">
					<label for="employees" class="label">
						{{ 'APPROVAL_REQUEST_PAGE.EMPLOYEES' | translate }}
					</label>
					<ga-employee-multi-select
						[selectedEmployeeIds]="selectedEmployees"
						[allEmployees]="employees"
						[label]="''"
						(selectedChange)="onEmployeesSelected($event)"
					>
					</ga-employee-multi-select>
				</div>
				<div class="col-sm-12 mb-3" *ngIf="participants === 'teams'">
					<label for="teams" class="label">
						{{ 'APPROVAL_REQUEST_PAGE.TEAMS' | translate }}
					</label>
					<nb-select
						formControlName="teams"
						multiple
						[selected]="selectedTeams"
						[(ngModel)]="selectedTeams"
						(selectedChange)="onTeamsSelected($event)"
						fullWidth
						placeholder="{{ 'FORM.PLACEHOLDERS.CHOOSE_TEAMS' | translate }}"
					>
						<nb-option *ngFor="let team of teams" [value]="team.id"> {{ team.name }}</nb-option>
					</nb-select>
				</div>
				<div class="col-sm-12 mb-3">
					<ga-tags-color-input
						[selectedTags]="tags"
						(selectedTagsEvent)="selectedTagsEvent($event)"
						[isOrgLevel]="true"
					>
					</ga-tags-color-input>
				</div>
			</div>

			<input type="hidden" formControlName="id" />
		</form>
	</nb-card-body>

	<nb-card-footer class="text-left">
		<button (click)="dialogRef.close()" status="basic" outline class="mr-3" nbButton>
			{{ 'BUTTONS.CANCEL' | translate }}
		</button>
		<button
			[disabled]="form.invalid || formDirective.submitted"
			(click)="formDirective.onSubmit()"
			status="success"
			nbButton
		>
			{{ 'BUTTONS.SAVE' | translate }}
		</button>
	</nb-card-footer>
</nb-card>
