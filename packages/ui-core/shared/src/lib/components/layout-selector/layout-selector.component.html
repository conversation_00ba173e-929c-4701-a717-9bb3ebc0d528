<div class="layout-switch">
	<button
		(click)="changeLayout(layoutStyles.CARDS_GRID)"
		status="basic"
		[ngClass]="componentLayoutStyle === layoutStyles.CARDS_GRID ? 'primary' : 'basic'"
		class="switch-button ml-1 mr-1"
		nbButton
		size="small"
		[nbTooltip]="'SETTINGS_MENU.CARDS_GRID' | translate"
	>
		<nb-icon icon="grid-outline"></nb-icon>
	</button>
	<button
		(click)="changeLayout(layoutStyles.TABLE)"
		status="basic"
		[ngClass]="componentLayoutStyle === layoutStyles.TABLE ? 'primary' : 'basic'"
		class="switch-button ml-1 mr-1"
		nbButton
		size="small"
		[nbTooltip]="'SETTINGS_MENU.TABLE' | translate"
	>
		<nb-icon icon="list-outline"></nb-icon>
	</button>
</div>
