@use 'themes' as *;

$border-primary: nb-theme(text-primary-color);

:host .main {
  display: inline;
  button {
    @include nb-ltr(margin-right, 1.5rem);
    @include nb-rtl(margin-left, 1.5rem);
    width: 1.5rem;
    height: 1.5rem;
    &.appearance-outline.status-primary {
      border-width: 2px;
      border-color: nb-theme(color-primary-transparent-default);
      padding: 0.1rem 0;
    }
    i {
      font-size: 11px;
    }
  }
}
