@use 'themes' as *;

.button {
  /* Search */

  /* Auto layout */
  padding: 10px 20px;
  width: 62px;
  height: 36px;
  &.info {
    color: nb-theme(text-control-color);
    background-color: #0088fe;
  }
  box-shadow: var(--gauzy-shadow);
  border-radius: 30px;
  margin-top: 22px;
}

nb-card {
  width: 300px;
  height: 404px;
  box-shadow: 0px 6px 30px 0px rgba(0, 0, 0, 0.2);
  border-radius: 12px;
}

img {
  width: 226px;
  height: 172px;
  border-radius: nb-theme(border-radius);
  margin-bottom: 22px;
}

p {
  width: 226px;
  text-align: center;
}

nb-card-body {
  display: flex;
  flex-direction: column;
  align-items: center;
  border-radius: 12px;
  padding: 1rem;
}

.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
}
.close-popup {
  width: 100%;
  display: flex;
  justify-content: flex-end;
}

i {
  cursor: pointer;
}
