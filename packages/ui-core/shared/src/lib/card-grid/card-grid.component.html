<div
	*ngIf="source && (source$ | async).length > 0; else noDataGridLayout"
	class="card-layout grid-scroll-container"
	infinite-scroll
	[scrollWindow]="false"
	(scrolled)="onScroll()"
	#grid
>
	<nb-card *ngFor="let item of source$ | async" class="card-item">
		<nb-card-body
			[ngClass]="selected.isSelected && selected.data.id === item.id ? 'card-body active' : 'card-body'"
			(click)="selectedItem(item)"
		>
			<div class="info-line" *ngFor="let key of getKeys()">
				<div
					class="info-meta"
					[ngClass]="{
						'image-logo': settings.columns[key].title === 'Image',
						active: selected.isSelected && selected.data.id === item.id
					}"
				>
					{{ settings.columns[key].title }}
				</div>
				<div
					class="info-value"
					[ngClass]="{
						'image-logo': settings.columns[key].title === 'Image',
						active: selected.isSelected && selected.data.id === item.id
					}"
					[ngSwitch]="settings.columns[key].type"
				>
					<div *ngSwitchCase="'html'" [innerHTML]="item[key]"></div>
					<div *ngSwitchCase="'date'">{{ item[key] | date }}</div>
					<div *ngSwitchCase="'function'">{{ item[key].name }}</div>
					<div *ngSwitchCase="'custom'">
						<ga-custom-component
							#component
							[renderComponent]="settings.columns[key].renderComponent"
							[value]="item[key]"
							[rowData]="item"
							class="custom"
							(click)="selectCustomViewComponent(component)"
						></ga-custom-component>
					</div>
					<div *ngSwitchDefault>{{ getValue(item, key) }}</div>
				</div>
			</div>
		</nb-card-body>
	</nb-card>
	<div class="show-more-button">
		<button nbButton (click)="onScroll()" status="basic" size="small" *ngIf="showMore" class="d-flex">
			<nb-icon icon="arrowhead-down-outline"></nb-icon>{{ 'BUTTONS.SHOW_MORE' | translate }}
		</button>
	</div>
</div>
<ng-template #noDataGridLayout>
	<div class="no-data">
		<ngx-no-data-message [message]="getNoDataMessage()"></ngx-no-data-message>
	</div>
</ng-template>
