@use 'themes' as *;

:host .card-layout {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(21rem, 1fr));
  -webkit-box-align: start;
  align-items: flex-start;
  -webkit-box-pack: start;
  justify-content: flex-start;
  column-gap: 1rem;
  grid-gap: 1rem;
  color: nb-theme(color-basic-default);
  overflow-y: auto;
  @include nb-ltr(padding-right, 0.5rem);
  @include nb-rtl(padding-left, 0.5rem);
  max-height: 100%;

  .card-item {
    display: flex;
    flex-direction: column;
    margin-bottom: 0;
    border-radius: 0.5rem;
    background-color: nb-theme(background-basic-color-3);
    box-shadow: var(--gauzy-shadow);
    cursor: pointer;

    .card-body {
      background-color: nb-theme(background-basic-color-3);
      border-radius: 0.5rem;
      padding: 12px 10px;
		display: flex;
		flex-direction: column;
		gap: 4px;

      &.active {
        background: var(--color-primary-transparent-100);
      }
    }

    &:hover {
      border-color: nb-theme(color-primary-hover-border);
    }

    &:focus {
      border-color: nb-theme(color-primary-focus-border);
    }

    .info-line {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      font-size: 0.7em;
      width: 100%;
		flex-grow: 2;
		gap: 4px;

      .info-meta {
        background-color: var(--gauzy-card-1);
        padding: 6px 10px;
        border-radius: 4px;
        width: 88%;

        &.image-logo {
          display: none;
        }
        &.active {
          background-color: var(--gauzy-sidebar-background-4);
        }
      }

      .info-value {
        text-align: start;
        font-size: 1em;
        background-color: var(--gauzy-card-1);
        padding: 6px 10px;
        border-radius: 4px;
        width: 100%;
        position: relative;

        &.image-logo {
          background-color: unset;
          padding: 0;
        }

        &.active {
          background-color: var(--gauzy-sidebar-background-4);
        }

        .custom::ng-deep {
          .img-container {
            width: 100%;

            img {
              height: 132px;
              width: 100%;
            }
          }

          ga-notes-with-tags,
          ga-only-tags {
            .tags-right {
              justify-content: start;
            }
          }

          ngx-tags-color {
            div {
				margin: 0;
				position: inherit;

				.color {
					width: 100%;
				}
            }
          }

          ngx-status-view {
            height: 2rem;
            width: 100%;
            position: absolute;
            left: 0;
            bottom: 0;
            margin: 0;

            .badge {
              position: absolute;
              height: 2rem;
              width: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }

          ga-status-badge {
            width: 100%;
            position: absolute;
            left: 0;
            top: 0;
            margin: 0;

            .badge {
              height: 2rem;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: nb-theme(text-button-tiny-font-size);
              font-size: 12px;
              font-weight: 600;
              line-height: 15px;
              letter-spacing: 0em;
              text-align: left;
            }
          }

          .progress-bar-container {
            width: 100%;
            position: absolute;
            top: 0;
            left: 0;
            margin: 0;

            .paid-percent {
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }

          li {
            font-size: 1em;
          }
        }
      }
    }
  }

  .card-footer {
    justify-content: space-around;
    display: flex;
  }
}

:host {
  max-height: 100%;
  height: 100%;
  .no-data {
    @include nb-ltr(padding-right, 0.625rem);
    @include nb-rtl(padding-left, 0.625rem);
    height: 100%;
  }
}

.show-more-button {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  button {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: var(--gauzy-shadow);
  }
}
