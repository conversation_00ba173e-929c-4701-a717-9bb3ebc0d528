@use 'gauzy/_gauzy-dialogs' as *;

.employee-form {
  width: 100%;
}

.card {
  height: 100%;
  background-color: var(--gauzy-card-2);
  border: unset;
}

.container {
  padding: 1rem;
  border-radius: nb-theme(border-radius);
  background-color: nb-theme(card-background-color);
}

.entities {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

:host{
  height: 100%;
  @include dialog(var(--gauzy-card-2), var(--gauzy-card-1));
  nb-card-header,
  nb-card-body {
    padding: 1rem;
  }
}
