@use 'themes' as *;

.employee-form {
  height: 100%;
  width: 100%;
}

:host {
  height: 100%;
}

nb-card-body{
	background-color: var(--gauzy-sidebar-background-2);
	border-radius: var(--border-radius);
}

form{
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 1rem;
}

.content {
  height: 100%;
  display: flex;
  padding: 1rem;
  background-color: var(--gauzy-card-2);
  border-radius: nb-theme(border-radius);
}

.bill-rate {
  display: flex;
}

:host .currency-per-hour {
  @include nb-ltr(margin-left, 1rem);
  @include nb-rtl(margin-right, 1rem);
}
