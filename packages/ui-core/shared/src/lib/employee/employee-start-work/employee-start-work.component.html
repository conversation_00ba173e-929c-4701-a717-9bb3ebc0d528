<nb-card>
	<nb-card-header class="d-flex flex-column">
		<span class="cancel"><i (click)="closeDialog()" class="fas fa-times"></i></span>
		<h5 class="title">
			{{ 'POP_UPS.START_WORK_FOR' | translate }}
			{{ employeeFullName.trim() ? employeeFullName : ('POP_UPS.EMPLOYEE' | translate) }}
		</h5>
	</nb-card-header>
	<nb-card-body>
		<div class="row">
			<div class="col-sm-12">
				<input
					[(ngModel)]="startWorkValue"
					[nbDatepicker]="datepicker"
					fullWidth
					nbInput
					placeholder="{{ 'POP_UPS.PICK_DATE' | translate }}"
				/>
				<nb-datepicker #datepicker></nb-datepicker>
			</div>
		</div>
	</nb-card-body>
	<nb-card-footer>
		<button (click)="closeDialog()" nbButton outline status="basic">
			{{ 'BUTTONS.CANCEL' | translate }}
		</button>
		<button (click)="startWork()" nbButton status="success">
			{{ 'BUTTONS.START_WORK' | translate }}
		</button>
	</nb-card-footer>
</nb-card>
