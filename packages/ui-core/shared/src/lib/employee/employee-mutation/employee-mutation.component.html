<nb-card
	[nbSpinner]="loading"
	nbSpinnerStatus="primary"
	nbSpinnerSize="large"
>
	<nb-card-header class="d-flex flex-column">
		<span class="cancel"
			><i class="fas fa-times" (click)="closeDialog()"></i
		></span>
		<h5 class="title">
			{{ 'EMPLOYEES_PAGE.ADD_EMPLOYEE' | translate }}
		</h5>
	</nb-card-header>
	<nb-card-body>
		<nb-stepper
			[linear]="linear"
			orientation="horizontal"
			disableStepNavigation
			#stepper
		>
			<nb-step [label]="step1">
				<ng-template #step1>
					{{ 'EMPLOYEES_PAGE.ADD_EMPLOYEES.STEP_1' | translate }}
				</ng-template>
				<ng-container *ngIf="employees.length > 0">
					<div class="row">
						<div class="col m-2 p-0">
							<nb-tag-list (tagRemove)="onEmployeeRemove($event)">
								<nb-tag
									removable
									*ngFor="let employee of employees"
									[text]="employee?.user?.email"
								></nb-tag>
							</nb-tag-list>
						</div>
					</div>
				</ng-container>
				<ga-user-basic-info-form
					#userBasicInfo
					[isShowRole]="false"
					[isEmployee]="true"
				></ga-user-basic-info-form>
				<div class="text-left">
					<button
						class="mr-2"
						status="basic"
						outline
						(click)="nextStep()"
						size="small"
						*ngIf="employees.length"
						nbButton
					>
						{{ 'BUTTONS.CANCEL' | translate }}
					</button>
					<button
						status="basic"
						[disabled]="userBasicInfo.form.invalid"
						outline
						class="green"
						nbButton
						size="small"
						nbStepperNext
					>
						{{ 'EMPLOYEES_PAGE.ADD_EMPLOYEES.NEXT' | translate }}
					</button>
				</div>
			</nb-step>
			<nb-step [label]="step2">
				<ng-template #step2>
					{{ 'EMPLOYEES_PAGE.ADD_EMPLOYEES.STEP_2' | translate }}
				</ng-template>
				<div class="text-left">
					<button
						status="basic"
						size="small"
						class="gray"
						outline
						nbButton
						nbStepperPrevious
					>
						{{
							'EMPLOYEES_PAGE.ADD_EMPLOYEES.PREVIOUS' | translate
						}}
					</button>
					<button
						nbButton
						status="success"
						size="small"
						class="mr-3 ml-3"
						(click)="addEmployee()"
					>
						{{
							'EMPLOYEES_PAGE.ADD_EMPLOYEES.ADD_ANOTHER_EMPLOYEE'
								| translate
						}}
					</button>
					<button
						status="basic"
						class="green"
						size="small"
						outline
						nbButton
						nbStepperNext
					>
						{{ 'EMPLOYEES_PAGE.ADD_EMPLOYEES.NEXT' | translate }}
					</button>
				</div>
			</nb-step>
			<nb-step [label]="step3">
				<ng-template #step3>
					{{ 'EMPLOYEES_PAGE.ADD_EMPLOYEES.STEP_3' | translate }}
				</ng-template>
				<div class="text-left">
					<button
						class="gray"
						status="basic"
						outline
						size="small"
						nbButton
						nbStepperPrevious
					>
						{{
							'EMPLOYEES_PAGE.ADD_EMPLOYEES.PREVIOUS' | translate
						}}
					</button>
					<button
						size="small"
						status="success"
						class="mr-3 ml-3"
						(click)="add()"
						nbButton
					>
						{{
							'EMPLOYEES_PAGE.ADD_EMPLOYEES.FINISHED_ADDING'
								| translate
						}}
					</button>
				</div>
			</nb-step>
		</nb-stepper>
	</nb-card-body>
</nb-card>
