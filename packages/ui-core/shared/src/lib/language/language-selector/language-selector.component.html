<ng-container
  	*ngIf="_template === 'ng-select'; then ngSelectTemplate; else nbSelectTemplate">
</ng-container>
<ng-template #ngSelectTemplate>
	<ng-select
		[items]="languages"
		appendTo="body"
		bindLabel="name"
		bindValue="code" 
		[loading]="loading"
		[addTag]="(addTag) ? addLanguage : null"
		(change)="onChangeLanguage($event)"
		[closeOnSelect]="true"
		[size]="size"
		[clearable]="clearable"
		[placeholder]="placeholder"
		[(ngModel)]="selectedLanguageCode"
	>
		<ng-template ng-tag-tmp let-newLanguageName="searchTerm">
				<b>{{ 'LANGUAGE_PAGE.ADD_NEW_LANGUAGE' | translate }}</b
			>: {{ newLanguageName }}
		</ng-template>
	</ng-select>
</ng-template>

<ng-template #nbSelectTemplate>
	<nb-select
		fullWidth
		(selectedChange)="onSelectedChange($event)"
		[size]="size"
		[placeholder]="placeholder"
		[(ngModel)]="selectedLanguageCode"
	>
		<nb-option *ngFor="let language of languages" [value]="language.code">
			{{ language.name }}
		</nb-option >
	</nb-select>
</ng-template>
