<ngx-avatar
	[src]="rowData?.imageUrl"
	[name]="rowData?.fullName ? rowData?.fullName : rowData?.name"
	(click)="navigateToContact()"
	class="report-table"
></ngx-avatar>
<ng-template [ngIf]="rowData?.isDefault">
	<nb-badge
		class="color"
		position="centered"
		[style.background]="background(rowData?.color)"
		[style.color]="backgroundContrast(rowData?.brandColor)"
		text="Default"
	></nb-badge>
</ng-template>
<div class="badges-block" *ngIf="isTags">
	<nb-badge
		*ngFor="let tag of (data | async)?.tags"
		class="color"
		position="centered"
		[style.background]="background(tag?.color)"
		[style.color]="backgroundContrast(tag?.color)"
		[text]="tag?.name"
	></nb-badge>
</div>
