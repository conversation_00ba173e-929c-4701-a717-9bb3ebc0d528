@use 'themes' as *;

$radius: nb-theme(button-rectangle-border-radius);

a {
  cursor: pointer;
}

:host .avatar-container {
  width: 100%;

  .avatar {
    border: 2px solid nb-theme(border-basic-color-1);
    background-color: nb-theme(color-primary-transparent-default);
    color: nb-theme(text-primary-color);
    @include nb-ltr(padding, 5px 14px 5px 7px);
    @include nb-rtl(padding, 5px 7px 5px 14px);
    border-radius: $radius;
    gap: 8px;
    display: flex;
    align-items: center;
    width: fit-content;

    .names-wrapper {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 110px;
    }

    img {
      width: 18px;
      height: 18px;
      border-radius: $radius;
    }
  }
}
