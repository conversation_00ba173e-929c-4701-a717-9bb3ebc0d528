@use 'themes' as *;

$radius: var(--button-rectangle-border-radius);

:host .avatar-container {
  position: relative;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  flex-direction: column;
  height: 2.5rem;

  .avatar-group {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;

    .avatar {
      background-color: var(--gauzy-primary-background);
      object-fit: cover;
      color: nb-theme(text-primary-color);
      @include nb-ltr(padding, 5px 14px 5px 7px);
      @include nb-rtl(padding, 5px 7px 5px 14px);
      border-radius: $radius;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      width: 5rem;
      display: inline-block;
      cursor: pointer;

      img {
        width: 18px;
        height: 18px;
        border-radius: var(--border-radius);
        @include nb-ltr(margin-right, 8px);
        @include nb-rtl(margin-left, 8px);
      }

      &:hover {
        text-overflow: clip;
        width: fit-content;
      }
    }
  }
}

.avatar-group .avatar {
  position: relative;
  z-index: 2;
  border: 2px solid #ffffff;
}

.avatar-group .avatar:hover {
  z-index: 3;
}

:host {
  .avatar-group .avatar+.avatar {
    @include nb-ltr(margin-left, -4rem);
    @include nb-rtl(margin-right, -4rem);
  }
}

.main {
  width: 7rem;
}
