<div class="main">
	<ng-container *ngFor="let groups of employees">
		<div *ngIf="value" class="avatar-container">
			<div class="avatar-group">
				<ng-container *ngFor="let employee of groups; let i = index">
					<a
						class="avatar"
						*ngIf="employee?.user"
						(click)="
							selectEmployee(
								employee,
								employee.user.firstName,
								employee.user.lastName,
								employee.user.imageUrl
							)
						"
					>
						<img
							class="img"
							type="user"
							[src]="employee.user.imageUrl"
						/>
						<span
							>{{ employee.user.firstName }}
							{{ employee.user.lastName }}</span
						>
					</a>
				</ng-container>
			</div>
		</div>
	</ng-container>
</div>
