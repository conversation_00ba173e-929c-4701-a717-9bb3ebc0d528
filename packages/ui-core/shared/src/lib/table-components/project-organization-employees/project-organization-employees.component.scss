@use 'themes' as *;

.container {
    background-color: var(--gauzy-card-1);
    border-radius: 4px;
    padding: 10px;
    margin: 0;
}

img {
    width: 18px;
    height: 18px;
}

.employee-column {
    display: flex;
    flex-direction: column;
    row-gap: 8px;
}

:host a {
    background-color: var(--color-primary-transparent-100);
    border-radius: var(--button-rectangle-border-radius);
    display: inline-block;
    flex-direction: row;
    align-items: center;
    width: fit-content;
    max-width: 100%;
    color: nb-theme(text-primary-color);
    font-size: 12px;
    font-weight: 400;
    line-height: 15px;
    letter-spacing: 0em;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    gap: 8px;
    text-decoration: none;
    @include nb-ltr(padding, 3px 9px 3px 3px);
    @include nb-rtl(padding, 3px 3px 3px 9px);

    img {
        border-radius: var(--button-rectangle-border-radius);
    }
}

.employee-row {
    display: flex;
    gap: 8px;
}

.col,
.row {
    padding: 0;
    margin: 0;

    &.header {
        margin-bottom: 8px;
        font-size: 12px;
        font-weight: 600;
        line-height: 15px;
        letter-spacing: 0em;
        color: var(--gauzy-text-color-2);
    }
}
