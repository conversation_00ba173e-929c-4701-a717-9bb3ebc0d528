@use 'gauzy/_gauzy-table' as *;

.selected-item {
	background: rgba(126, 126, 143, 0.1);
	box-shadow: 12.5px 0px 0px -5px rgba(126, 126, 143, 0.1) inset;
	border-radius: var(--border-radius);
}

:host {
	nb-list-item {
		background-color: var(--gauzy-card-4);
		margin-top: 4px;
		border-radius: var(--border-radius)
	}

	nb-card {
		background-color: unset;
	}

	nb-card.size-medium {
		height: calc(100vh - 30rem);
	}

	ngx-gauzy-button-action ::ng-deep .actions-container {
		padding-top: 0;
	}
}
