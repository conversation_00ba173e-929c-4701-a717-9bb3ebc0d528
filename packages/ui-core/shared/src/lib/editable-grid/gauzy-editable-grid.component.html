<ng-template #dialog let-data let-action="currentAction" let-ref="dialogRef">
	<ng-container *ngIf="currentAction === 'create'">
		<ng-template
			*ngTemplateOutlet="
				addDialogTmpl;
				context: {
					$implicit: selectedItem,
					action: currentAction,
					dialogRef: ref
				}
			"
		>
		</ng-template>
	</ng-container>

	<ng-container *ngIf="currentAction === 'edit'">
		<ng-template
			*ngTemplateOutlet="
				editDialogTmpl;
				context: {
					$implicit: selectedItem,
					action: currentAction,
					dialogRef: ref
				}
			"
		>
		</ng-template>
	</ng-container>

	<ng-container *ngIf="currentAction === 'delete'">
		<ng-template
			*ngTemplateOutlet="
				deleteDialogTmpl;
				context: {
					$implicit: selectedItem,
					action: currentAction,
					dialogRef: ref
				}
			"
		>
		</ng-template>
	</ng-container>
</ng-template>

<ng-container>
	<nb-card size="medium">
		<nb-card-header class="pb-0 pr-0 pl-0">
			<div class="d-flex align-items-center justify-content-between">
				<span>{{ 'TASKS_PAGE.SPRINTS_SETTINGS' | translate }}</span>
				<ngx-gauzy-button-action
					[hasLayoutSelector]="false"
					[buttonTemplate]="actionButtons"
					[buttonTemplateVisible]="visible"
					[isDisable]="!selectedItem"
				></ngx-gauzy-button-action>
			</div>
		</nb-card-header>
		<nb-list>
			<nb-list-item
				*ngFor="let item of items"
				[class.selected-item]="selectedItem?.id === item?.id"
				(click)="toggleItemSelection(item)"
			>
				<ng-container
					*ngTemplateOutlet="itemTmpl; context: { $implicit: item }"
				></ng-container>
			</nb-list-item>
		</nb-list>
	</nb-card>
</ng-container>

<ng-template #actionButtons>
	<div class="actions">
		<button
			nbButton
			status="basic"
			[disabled]="!selectedItem"
			(click)="openDialog('edit', dialog)"
			class="action primary"
			size="small"
		>
			<nb-icon class="mr-1" icon="edit-outline"></nb-icon>
			{{ 'BUTTONS.EDIT' | translate }}
		</button>
		<button
			nbButton
			status="basic"
			[disabled]="!selectedItem"
			(click)="openDialog('delete', dialog)"
			class="action"
			size="small"
		>
			<nb-icon status="danger" icon="trash-2-outline"></nb-icon>
			<!-- {{ 'BUTTONS.DELETE' | translate }} -->
		</button>
	</div>
</ng-template>
<ng-template #visible>
	<button
		nbButton
		status="info"
		(click)="openDialog('create', dialog)"
		class="action"
		size="small"
	>
		<nb-icon icon="plus-outline"></nb-icon>
		{{ 'BUTTONS.CREATE' | translate }}
	</button>
</ng-template>
