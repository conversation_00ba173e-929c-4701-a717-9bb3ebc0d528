import { AutocompleteOffDirective } from './autocomplete-off.directive';
import { DebounceClickDirective } from './debounce-click.directive';
import { ImgDirective } from './img.directive';
import { OutsideDirective } from './outside.directive';
import { UnderConstructionDirective } from './under-construction.directive';
import { ReadMoreDirective } from './read-more';
import { TimeTrackingAuthorizedDirective } from './time-tracking-authorized-directive';
import { NoSpaceEdgesDirective } from './no-space-edges.directive';
import { TextMaskDirective } from './text-mask.directive';
import { TriggerParentClickDirective } from './trigger-parent-click.directive';

export { AutocompleteOffDirective } from './autocomplete-off.directive';
export { DebounceClickDirective } from './debounce-click.directive';
export { ImgDirective } from './img.directive';
export { OutsideDirective } from './outside.directive';
export { UnderConstructionDirective } from './under-construction.directive';
export { ReadMoreDirective } from './read-more';
export { TimeTrackingAuthorizedDirective } from './time-tracking-authorized-directive';
export { NoSpaceEdgesDirective } from './no-space-edges.directive';
export { TextMaskDirective } from './text-mask.directive';
export { TriggerParentClickDirective } from './trigger-parent-click.directive';

export const DIRECTIVES = [
	AutocompleteOffDirective,
	DebounceClickDirective,
	ImgDirective,
	ReadMoreDirective,
	TimeTrackingAuthorizedDirective,
	OutsideDirective,
	UnderConstructionDirective,
	NoSpaceEdgesDirective,
	TextMaskDirective,
	TriggerParentClickDirective
];
