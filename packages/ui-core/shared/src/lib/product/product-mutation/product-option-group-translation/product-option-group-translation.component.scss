@use 'gauzy/_gauzy-dialogs' as *;

.main {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 6px;
  overflow: hidden;

  nb-card-header {
    padding: 1.25rem 1.5rem;
    border-bottom: 1px solid #e9ecef;
    position: relative;

    .cancel {
      position: absolute;
      top: 1.25rem;
      right: 1.5rem;
      cursor: pointer;
      transition: opacity 0.2s ease;

      &:hover {
        opacity: 0.7;
      }
    }

    .title {
      margin: 0;
      font-weight: 500;
    }
  }

  nb-card-body {
    padding: 1.5rem;

    .option-row {
      display: flex;
      flex-wrap: wrap;
      margin: 0 -0.75rem 1.5rem;
      align-items: center;

      &:last-child {
        margin-bottom: 0;
      }

      .col-md-3 {
        padding: 0.75rem;

        &.col-sm-12 {
          @media (max-width: 768px) {
            flex: 0 0 100%;
            max-width: 100%;
          }
        }
      }
    }

    .option-line {
      display: flex;
      align-items: center;
      padding: 0.75rem;
      border-radius: 4px;
      border: 1px solid #e9ecef;
      cursor: pointer;
      transition: background-color 0.2s ease;
      overflow: hidden;

      &:hover {
        background-color: #f8f9fa;
      }

      .language-span {
        flex-shrink: 0;
        font-weight: 600;
        color: #495057;
        margin-right: 0.5rem;
        padding-right: 0.5rem;
        border-right: 1px solid #e9ecef;
        display: inline-block;
      }
    }


    input {
      width: 100%;
      border-radius: 4px;
      padding-left: 3.5rem;


      &[nbInput] {
        padding-left: 0.5rem;
      }
    }


    [formControlName] {
      position: relative;
      display: block;


      &::before {
        content: attr(formcontrolname);
        position: absolute;
        left: 0.75rem;
        top: 50%;
        transform: translateY(-50%);
        font-weight: 600;
        color: #495057;
        z-index: 2;
        padding-right: 0.5rem;
        border-right: 1px solid #e9ecef;
        pointer-events: none;
      }
    }
  }

  nb-card-footer {
    padding: 1.25rem 1.5rem;
    border-top: 1px solid #e9ecef;

    button {
      font-weight: 500;
      padding: 0.5rem 1.25rem;

      &:first-child {
        margin-right: 0.75rem;
      }
    }
  }
}

.input-container {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  overflow: hidden;

  .language-label {
    flex-shrink: 0;
    padding: 0.75rem;
    font-weight: 600;
    color: #495057;
    border-right: 1px solid #e9ecef;
    background-color: #f8f9fa;
  }

  input {
    flex-grow: 1;
    border: none !important;
    border-radius: 0 !important;
    box-shadow: none !important;
  }
}

:host ::ng-deep {
  input.nb-input {
    padding-left: 3.5rem !important;
  }
}
