@use 'gauzy/_gauzy-dialogs' as *;

.d-bottom {
  display: flex;
  align-items: flex-end;
  align-content: flex-end;
}

.options-wrap {
  display: flex;
}

.option {
  padding: 5px 10px;
  font-size: 12px;
  background: #3366ff;
  border-radius: 7px;
  color: #fff;
  margin-right: 10px;
}

form {
  max-width: 500px;
}

.category-img {
  width: fit-content;
  height: fit-content;
  position: relative;
  margin-top: 20px;
  margin-right: 30px;
  min-width: 150px;
  min-height: 150px;
  background: rgb(226, 224, 224);
  border-radius: 13px;

  div {
    pointer-events: none;
    background: black;
    position: absolute;
    height: 100%;
    width: 100%;
    border-radius: 13px;
  }

  img {
    height: auto;
    border-radius: 13px;
    width: 100%;
  }

  input {
    width: 100%;
    height: 100%;
    opacity: 0;
    position: absolute;
    z-index: 3;
    cursor: pointer;
  }

  svg {
    z-index: 2;
    transition: opacity 0.2s ease-in;
    opacity: 0.3;
    position: absolute;
    top: calc(50% - 68px / 2);
    left: calc(50% - 68px / 2);

    g circle {
      fill: var(--text-primary-color)
    }
  }

  .uploadimage {
    min-width: 150px;
    min-height: 150px;
  }
}

:host nb-card {
  background-color: var(--gauzy-card-1);
}
