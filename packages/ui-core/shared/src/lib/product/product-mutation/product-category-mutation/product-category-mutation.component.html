<nb-card class="main">
	<nb-card-header class="d-flex flex-column">
		<span class="cancel"
			><i class="fas fa-times" (click)="dialogRef.close()"s></i
		></span>
		<h5 class="title">
			{{
				(productCategory
					? 'INVENTORY_PAGE.EDIT_PRODUCT_CATEGORY'
					: 'INVENTORY_PAGE.ADD_PRODUCT_CATEGORY'
				) | translate
			}}
		</h5>
	</nb-card-header>
	<nb-card-body>
		<form
			[formGroup]="form"
			#productCategoryForm="ngForm"
			(ngSubmit)="onSubmit()"
		>
			<div class="row mb-2">
				<div class="col-md-4">
					<div class="category-img">
						<ngx-image-uploader
							[folder]="'inventory_product_category_pictures'"
							(changeHoverState)="hoverState = $event"
							(uploadedImageAsset)="updateImageAsset($event)"
							(uploadImageAssetError)="handleImageUploadError($event)"
						></ngx-image-uploader>
						<svg
							xmlns="http://www.w3.org/2000/svg"
							xmlns:xlink="http://www.w3.org/1999/xlink"
							width="68"
							height="68"
							viewBox="0 0 68 68"
							[ngStyle]="{ opacity: hoverState ? '1' : '0.3' }"
						>
							<defs>
								<path
									id="a"
									d="M28.667 31.333a2 2 0 1 0-.002-4.001 2 2 0 0 0 .002 4.001m13.333 12H26.748l9.34-7.793c.328-.279.923-.277 1.244-.001l6.001 5.12V42c0 .736-.597 1.333-1.333 1.333M26 24.667h16c.736 0 1.333.597 1.333 1.333v11.152l-4.27-3.643c-1.32-1.122-3.386-1.122-4.694-.008l-9.702 8.096V26c0-.736.597-1.333 1.333-1.333M42 22H26c-2.205 0-4 1.795-4 4v16c0 2.205 1.795 4 4 4h16c2.205 0 4-1.795 4-4V26c0-2.205-1.795-4-4-4"
								/>
							</defs>
							<g fill="none" fill-rule="evenodd">
								<circle
									cx="34"
									cy="34"
									r="34"
									fill="#0091FF"
									opacity=".3"
								/>
								<circle
									cx="34"
									cy="34"
									r="26"
									fill="#0091FF"
									opacity=".9"
								/>
								<use
									fill="#FFF"
									fill-rule="nonzero"
									xlink:href="#a"
								/>
							</g>
						</svg>
						<div
							class="image-overlay"
							[ngStyle]="{ opacity: hoverState ? '0.2' : '0' }"
						></div>
						<ng-template [ngIf]="form.get('imageUrl').value">
							<img
								[src]="form.get('imageUrl').value"
								alt="Category Image"
								class="uploadimage"
								(mouseenter)="hoverState = true"
								(mouseleave)="hoverState = false"
							/>
						</ng-template>
					</div>
				</div>
				<div class="col-sm-8 mb-2 pr-4 col-left">
					<div class="form-group mb-3">
						<label class="label" for="lang">
							{{ 'INVENTORY_PAGE.LANGUAGE' | translate }}
						</label>
						<ngx-language-selector
							[placeholder]="
								'INVENTORY_PAGE.LANGUAGE' | translate
							"
							class="d-block"
							size="medium"
							[template]="'ng-select'"
							[selectedLanguageCode]="selectedLanguage"
							(selectedLanguageEvent)="onLangChange($event)"
						></ngx-language-selector>
					</div>
					<div class="form-group">
						<label class="label" for="name">
							{{ 'INVENTORY_PAGE.NAME' | translate }}
						</label>
						<input
							fullWidth
							id="name"
							type="text"
							nbInput
							formControlName="name"
							[placeholder]="'INVENTORY_PAGE.NAME' | translate"
						/>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-sm-12">
					<label class="label" for="description">{{
						'INVENTORY_PAGE.DESCRIPTION' | translate
					}}</label>
					<textarea
						fullWidth
						id="description"
						nbInput
						formControlName="description"
						[placeholder]="'INVENTORY_PAGE.DESCRIPTION' | translate"
					></textarea>
				</div>
			</div>
		</form>
	</nb-card-body>
	<nb-card-footer class="text-left">
		<button
			(click)="dialogRef.close()"
			status="basic"
			class="mr-2"
			outline
			nbButton
		>
			{{ 'BUTTONS.CANCEL' | translate }}
		</button>
		<button
			[disabled]="form.invalid"
			(click)="productCategoryForm.ngSubmit.emit()"
			status="success"
			nbButton
		>
			{{ 'BUTTONS.SAVE' | translate }}
		</button>
	</nb-card-footer>
</nb-card>
