<div class="masonry-boundary" cdkDropListGroup>
	<div
		class="window"
		cdkDropList
		cdkDropListOrientation="horizontal"
		[cdkDropListData]="index"
		(cdkDropListDropped)="drop($event)"
		*ngFor="let window of windows; let index = index"
	>
		<ga-window
			cdkDrag
			(cdkDragEnded)="onDragEnded($event)"
			[windowDragEnded]="event"
			[templateRef]="window"
			[position]="index"
		></ga-window>
	</div>
</div>
