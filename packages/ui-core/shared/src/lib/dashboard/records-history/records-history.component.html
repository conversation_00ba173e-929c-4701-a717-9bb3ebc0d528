<nb-card class="records" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
	<nb-card-header>
		<span class="cancel"><i class="fas fa-times close" (click)="close()"></i></span>
		<h5 class="title">{{ translatedType | titlecase }}</h5>
	</nb-card-header>
	<nb-card-body>
		<div class="table-scroll-container">
			<angular2-smart-table
				style="cursor: pointer"
				[settings]="smartTableSettings"
				[source]="smartTableSource"
			></angular2-smart-table>
		</div>
		<div class="pagination-container">
			<ng-container *ngIf="smartTableSource">
				<ngx-pagination [source]="smartTableSource"></ngx-pagination>
			</ng-container>
		</div>
	</nb-card-body>
</nb-card>
