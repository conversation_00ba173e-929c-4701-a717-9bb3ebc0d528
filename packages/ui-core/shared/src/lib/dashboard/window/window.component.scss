@use 'themes' as *;

#window {
  position: relative;
  color: var(--gauzy-text-color-2);
  display: inline-block;
  width: 100%;
  -webkit-transition: 1s ease all;
  transition: 1s ease all;
  box-sizing: border-box;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  margin-bottom: 1rem;
}

:host nb-icon {
  font-size: 11px;
  position: absolute;
  @include nb-rtl(left, 1rem);
  @include nb-ltr(right, 1rem);
  top: 1.125rem;
  z-index: 2;
}

:host {
  ::ng-deep {
    .collapsed nb-card-body {
      display: none;
    }
    .expanded nb-card-body {
      display: block;
    }

    .moved nb-card {
      cursor: move;
    }
  }
}

.setting {
  display: flex;
  flex-direction: column;
  padding: 10px 12px;
  gap: 10px;
  .action {
    display: flex;
    gap: 10px;
    align-items: center;
    cursor: pointer;
    i {
      font-size: 12px;
      color: var(--gauzy-text-color-2);
    }
    span {
      color: rgba(126, 126, 143, 0.5);
    }
  }
}
