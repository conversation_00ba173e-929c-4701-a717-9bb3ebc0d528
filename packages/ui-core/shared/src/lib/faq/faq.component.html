<div class="faq">
	<div>
		<h5 class="faq-heading">
			{{ 'FORGOT_PASSWORD_PAGE.FAQ_TITLE' | translate }}
		</h5>
		<div *ngFor="let item of faqs$ | async" class="single-faq">
			<div class="heading-wrapper">
				<nb-icon [icon]="item.icon"></nb-icon>
				<h6 class="side-small-heading">{{ item.title }}</h6>
			</div>
			<p class="side-description">{{ item.content }}</p>
			<div class="hr-div-soft"></div>
		</div>
	</div>
	<span>
		<button nbButton outline status="primary" size="small" class="learn-more">
			{{ 'FORGOT_PASSWORD_PAGE.FAQ_LEARN_MORE' | translate }}
		</button>
	</span>
</div>
