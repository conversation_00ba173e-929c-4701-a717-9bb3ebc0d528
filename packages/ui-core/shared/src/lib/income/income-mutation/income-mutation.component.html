<nb-card class="main">
	<nb-card-header class="d-flex flex-column">
		<div class="cancel">
			<i class="fas fa-times" (click)="close()"></i>
		</div>
		<h5 class="title">
			{{
				(income ? 'POP_UPS.EDIT_INCOME' : 'POP_UPS.ADD_INCOME')
					| translate
			}}
		</h5>
	</nb-card-header>
	<nb-card-body class="body">
		<form
			[formGroup]="form"
			#formDirective="ngForm"
			(ngSubmit)="addOrEditIncome()"
		>
			<div class="row employees">
				<div [hidden]="income" class="col-sm-7">
					<label class="label" for="employee" [hidden]="income">
						{{ 'INCOME_PAGE.EMPLOYEES_GENERATE_INCOME' | translate }}
					</label>
					<ga-employee-selector
						#employeeSelector
						[placeholder]="'INCOME_PAGE.EMPLOYEES_GENERATE_INCOME' | translate"
						[defaultSelected]="true"
						[showAllEmployeesOption]="true"
						[hidden]="income"
						[skipGlobalChange]="true"
						(selectionChanged)="selectionEmployee($event)"
						id="employee"
					></ga-employee-selector>
				</div>
				<div [ngClass]="income ? 'col-sm-12' : 'col-sm-5'">
					<div class="form-group">
						<label class="label">
							{{ 'POP_UPS.CONTACT' | translate }}
						</label>
						<ga-contact-select
							[addTag]="true"
							[clearable]="true"
							[searchable]="true"
							[placeholder]="'POP_UPS.ALL_CONTACTS' | translate"
							formControlName="organizationContact"
						></ga-contact-select>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-sm-4">
					<label class="label">
						{{ 'POP_UPS.PICK_DATE' | translate }}
					</label>
					<input
						formControlName="valueDate"
						nbInput
						[placeholder]="'POP_UPS.PICK_DATE' | translate"
						[nbDatepicker]="valueDatePicker"
						[status]="
							FormHelpers.isInvalidControl(form, 'valueDate')
								? 'danger'
								: 'basic'
						"
					/>
					<nb-datepicker #valueDatePicker></nb-datepicker>
				</div>
			</div>
			<div class="row mt-3">
				<div class="col-sm-6">
					<ga-currency
						formControlName="currency"
						[formControl]="form.get('currency')"
						(optionChange)="currencyChanged($event)"
					></ga-currency>
				</div>
				<div class="col-sm-6">
					<div class="form-group">
						<label class="label">
							{{ 'POP_UPS.AMOUNT' | translate }}
						</label>
						<input
							nbInput
							type="number"
							step="1"
							[min]="0"
							fullWidth
							[placeholder]="'POP_UPS.AMOUNT' | translate"
							formControlName="amount"
							[status]="
								FormHelpers.isInvalidControl(form, 'amount')
									? 'danger'
									: 'basic'
							"
							autocomplete="off"
						/>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-sm-12">
					<div class="form-group">
						<ga-tags-color-input
							[selectedTags]="form.get('tags').value"
							(selectedTagsEvent)="selectedTagsHandler($event)"
							[isOrgLevel]="true"
						></ga-tags-color-input>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col">
					<div class="form-group flex-container">
						<nb-checkbox formControlName="isBonus">
							<span class="label">
								{{ 'DASHBOARD_PAGE.DEVELOPER.BONUS' | translate }}
							</span>
						</nb-checkbox>
						<span
							class="label info-font"
							[nbTooltip]="'INCOME_PAGE.BONUS_HELP' | translate"
						>
							&nbsp;<nb-icon icon="info-outline"></nb-icon>
						</span>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col">
					<label class="label">
						{{ 'TIMER_TRACKER.DESCRIPTION' | translate }}
					</label>
					<div class="form-group">
						<textarea
							nbInput
							fullWidth
							[placeholder]="'POP_UPS.NOTES' | translate"
							formControlName="notes"
							class="notes"
						></textarea>
					</div>
				</div>
			</div>
		</form>
	</nb-card-body>
	<nb-card-footer class="text-left">
		<button
			(click)="close()"
			status="basic"
			outline
			class="mr-3"
			nbButton
		>
			{{ 'BUTTONS.CANCEL' | translate }}
		</button>
		<button
			[disabled]="form.invalid"
			(click)="formDirective.ngSubmit.emit()"
			status="success"
			nbButton
		>
			{{ 'BUTTONS.SAVE' | translate }}
		</button>
	</nb-card-footer>
</nb-card>
