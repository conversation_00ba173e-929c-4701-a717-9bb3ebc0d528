<nb-card class="main">
	<nb-card-header class="d-flex flex-column">
		<span class="cancel">
			<i class="fas fa-times" (click)="closeDialog()"></i>
		</span>
		<h5 class="title">
			{{ (tag ? 'TAGS_PAGE.EDIT_TAGS' : 'TAGS_PAGE.ADD_TAGS') | translate }}
		</h5>
	</nb-card-header>
	<nb-card-body class="body">
		<form [formGroup]="form">
			<div class="row">
				<div class="col-sm-6">
					<div class="form-group">
						<label for="inputName" class="label">
							{{ 'TAGS_PAGE.TAGS_SELECT_NAME' | translate }}
						</label>
						<input
							formControlName="name"
							class="select-name"
							type="text"
							nbInput
							[placeholder]="'TAGS_PAGE.TAGS_SELECT_NAME' | translate"
							id="inputName"
							fullWidth
						/>
					</div>
				</div>
				<div class="col-sm-6">
					<div class="form-group">
						<label for="inputColor" class="label">
							{{ 'TAGS_PAGE.TAGS_SELECT_COLOR' | translate }}
						</label>
						<input
							class="select-color"
							nbInput
							[placeholder]="'TAGS_PAGE.TAGS_SELECT_COLOR' | translate"
							formControlName="color"
							[colorPicker]="color"
							[value]="color"
							[style.background]="color + ' !important'"
							[style.color]="backgroundContrast(color)"
							(colorPickerChange)="onChangeColor($event)"
							id="inputColor"
							fullWidth
						/>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col">
					<div class="form-group">
						<nb-checkbox formControlName="isTenantLevel">
							{{ 'TAGS_PAGE.TENANT_LEVEL' | translate }}
						</nb-checkbox>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-sm-12">
					<div class="form-group">
						<label for="inputDescription" class="label">
							{{ 'TAGS_PAGE.TAGS_SELECT_DESCRIPTION' | translate }}
						</label>
						<textarea
							formControlName="description"
							nbInput
							fullWidth
							class="description"
							[placeholder]="'TAGS_PAGE.TAGS_SELECT_DESCRIPTION' | translate"
							id="inputDescription"
							fullWidth
						></textarea>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-sm-12">
					<div class="form-group">
						<label for="tagTypeId" class="label">
							{{ 'TAGS_PAGE.TAGS_SELECT_TYPE' | translate }}
						</label>
						<nb-select
							formControlName="tagTypeId"
							fullWidth
							id="tagTypeId"
							[placeholder]="'TAGS_PAGE.TAGS_SELECT_TYPE' | translate"
						>
							<nb-option *ngFor="let tagType of tagTypes" [value]="tagType.id">
								{{ tagType.type }}
							</nb-option>
						</nb-select>
					</div>
				</div>
			</div>
		</form>
	</nb-card-body>
	<nb-card-footer class="text-left">
		<button (click)="closeDialog()" status="basic" class="mr-3" outline nbButton>
			{{ 'BUTTONS.CANCEL' | translate }}
		</button>
		<button [disabled]="form.invalid" (click)="tag ? editTag() : addTag()" status="success" nbButton>
			{{ 'BUTTONS.SAVE' | translate }}
		</button>
	</nb-card-footer>
</nb-card>
