@use 'var' as *;

$radius: nb-theme(button-rectangle-border-radius);

ng-select {
  .selector-template {
    display: flex;
    align-items: center;
    height: 100%;
  }
  img {
    margin-right: 0.25rem;
    margin-left: 0.25rem;
    height: 20px;
    width: 20px;
    box-shadow: var(--gauzy-shadow);
    object-fit: cover;
    border-radius: $radius;
  }
}

::ng-deep .organization {
  &.close {
    opacity: 1;
  }
  &.ng-select {
    .ng-select-container {
      .ng-value-container {
        padding-left: 0;
      }
    }
    .ng-arrow-wrapper {
      display: flex;
      align-items: baseline;
      justify-content: center;
    }
    &.close.ng-select .ng-select-container .ng-value-container {
      span {
        display: none;
      }
    }
  }
}
