@use 'var' as *;

:host .calendar-picker {
  position: relative;
  max-height: 40px;
  input {
    width: 100%;
    max-width: 100% !important;
    border-radius: nb-theme(button-rectangle-border-radius);
    height: 32px;
    box-shadow: var(--gauzy-shadow);
    background-color: rgba(126, 126, 143, 0.05);
  }
  .calendar {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: absolute;
    z-index: 9999;
    background-color: nb-theme(background-basic-color-1);
    nb-calendar-year-picker,
    nb-calendar-month-picker {
      z-index: 1;
    }
    margin-top: 10px;
  }
  .date-reset {
    position: absolute;
    top: 18%;
    cursor: pointer;
    @include nb-ltr(right, 20px);
    @include nb-rtl(left, 16px);
  }
}
