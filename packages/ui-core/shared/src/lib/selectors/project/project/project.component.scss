@use 'var' as *;

:host {
	min-width: 200px;
	display: block;

	.multiple-select {
		width: 100%;
	}

	ng-select {
		.selector-template {
			display: flex;
			align-items: center;
			height: 100%;
		}

		img {
			margin-right: 5px;
			border-radius: nb-theme(button-rectangle-border-radius);
		}
	}

	::ng-deep {
		.ng-select .ng-select-container .ng-value-container {
			align-items: center;
			padding-left: 5px;
		}
	}
}

img{
	object-fit: cover;
}
