<div (click)="$event.stopPropagation()">
	<ng-select
		[disabled]="disabled"
		[clearable]="true"
		[items]="timeSlots"
		[(ngModel)]="selectedTime"
		bindValue="value"
		appendTo="body"
		bindLabel="label"
		[placeholder]="'00:00' | timeFormat"
	>
		<ng-template ng-option-tmp let-item="item" let-index="index">
			{{ item.value | timeFormat }}
		</ng-template>
		<ng-template ng-label-tmp let-item="item">
			<span>{{ item.value | timeFormat }}</span>
		</ng-template>
	</ng-select>
</div>
