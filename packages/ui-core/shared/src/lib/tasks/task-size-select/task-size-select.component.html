<ng-select
	#select
	(change)="selectSize($event); select.blur()"
	(clear)="select.blur()"
	[(ngModel)]="size"
	[addTag]="addTag ? createNew : null"
	[clearable]="true"
	[items]="sizes$ | async"
	[placeholder]="placeholder || 'TASKS_PAGE.TASK_SIZE' | translate"
	bindLabel="name"
>
	<ng-template let-index="index" let-item="item" ng-option-tmp>
		<gauzy-task-badge-view [taskBadge]="item"></gauzy-task-badge-view>
	</ng-template>
	<ng-template let-item="item" ng-label-tmp>
		<gauzy-task-badge-view [taskBadge]="item"></gauzy-task-badge-view>
	</ng-template>
</ng-select>
