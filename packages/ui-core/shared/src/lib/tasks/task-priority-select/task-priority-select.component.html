<ng-select
	#select
	(change)="selectPriority($event); select.blur()"
	(clear)="select.blur()"
	[(ngModel)]="priority"
	[addTag]="addTag ? createNew : null"
	[clearable]="true"
	[items]="priorities$ | async"
	[placeholder]="placeholder || 'TASKS_PAGE.TASK_PRIORITY' | translate"
	appendTo="body"
	bindLabel="name"
>
	<ng-template let-index="index" let-item="item" ng-option-tmp>
		<gauzy-task-badge-view [taskBadge]="item"></gauzy-task-badge-view>
	</ng-template>
	<ng-template let-item="item" ng-label-tmp>
		<gauzy-task-badge-view [taskBadge]="item"></gauzy-task-badge-view>
	</ng-template>
</ng-select>
