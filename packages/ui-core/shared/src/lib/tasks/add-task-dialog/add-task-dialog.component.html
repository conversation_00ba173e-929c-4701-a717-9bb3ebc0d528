<nb-card class="main">
	<nb-card-header class="d-flex flex-column">
		<div class="cancel">
			<i class="fas fa-times" (click)="dialogRef.close()"></i>
		</div>
		<h5 class="title">
			{{ (selectedTask && selectedTask.id ? 'TASKS_PAGE.EDIT_TASKS' : 'TASKS_PAGE.ADD_TASKS') | translate }}
		</h5>
	</nb-card-header>
	<nb-card-body class="body">
		<form [formGroup]="form">
			<ng-template [ngIf]="!(selectedTask && selectedTask.id)">
				<div class="row">
					<div class="col-sm-12">
						<ngx-task-number-field
							formControlName="number"
							[formControl]="form.get('number')"
							[projectId]="getControlValue('projectId')"
							[placeholder]="'TASKS_PAGE.TASK_NUMBER' | translate"
						></ngx-task-number-field>
					</div>
				</div>
			</ng-template>
			<div class="row">
				<div class="col-sm-6">
					<div class="form-group">
						<label class="label">{{ 'CONTEXT_MENU.PROJECT' | translate }}</label>
						<ga-project-selector
							formControlName="projectId"
							[placeholder]="'CONTEXT_MENU.PROJECT' | translate"
							[skipGlobalChange]="true"
							[defaultSelected]="false"
							[showAllOption]="false"
							(onChanged)="selectedProject($event)"
						></ga-project-selector>
					</div>
				</div>
				<div class="col-sm-6">
					<div class="form-group">
						<label class="label">
							{{ 'TASKS_PAGE.TASKS_STATUS' | translate }}
						</label>
						<ga-task-status-select
							[projectId]="getControlValue('projectId')"
							formControlName="taskStatus"
							[placeholder]="'TASKS_PAGE.TASKS_STATUS' | translate"
						></ga-task-status-select>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-sm-12">
					<div class="form-group">
						<label class="label">{{ 'TASKS_PAGE.MODULE' | translate }}</label>
						<nb-select
							formControlName="modules"
							[placeholder]="'TASKS_PAGE.SELECT_MODULE' | translate"
							[selected]="selectedModules"
							(selectedChange)="onModulesSelected($event)"
							fullWidth
							multiple
						>
							<nb-option *ngFor="let module of availableModules" [value]="module.id">
								{{ module.name }}
							</nb-option>
						</nb-select>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-sm-12">
					<div class="form-group">
						<label class="label">{{ 'TASKS_PAGE.PARENT_TASK' | translate }}</label>
						<ga-task-selector
							name="parentId"
							[projectId]="getControlValue('projectId')"
							formControlName="parentId"
							[required]="organization?.requireTask"
						></ga-task-selector>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-sm-12">
					<nb-radio-group (valueChange)="onParticipantsChange($event)" [value]="participants">
						<nb-radio class="nb-radio" [value]="taskParticipantEnum.EMPLOYEES"
							>{{ 'TASKS_PAGE.TASK_MEMBERS' | translate }}
						</nb-radio>
						<nb-radio class="nb-radio" [value]="taskParticipantEnum.TEAMS"
							>{{ 'TASKS_PAGE.TASK_TEAMS' | translate }}
						</nb-radio>
					</nb-radio-group>
				</div>
				<div class="col-sm-12">
					<div class="form-group">
						<ng-container *ngIf="participants === taskParticipantEnum.EMPLOYEES">
							<ga-employee-multi-select
								[selectedEmployeeIds]="selectedMembers"
								[allEmployees]="employees"
								(selectedChange)="onMembersSelected($event)"
							>
							</ga-employee-multi-select>
						</ng-container>
						<ng-container *ngIf="participants === taskParticipantEnum.TEAMS">
							<label class="label">{{ 'TASKS_PAGE.TASK_TEAMS' | translate }}</label>
							<nb-select
								formControlName="teams"
								multiple
								[selected]="selectedTeams"
								(selectedChange)="onTeamsSelected($event)"
								fullWidth
								[placeholder]="'FORM.PLACEHOLDERS.CHOOSE_TEAMS' | translate"
							>
								<nb-option *ngFor="let team of teams" [value]="team.id"> {{ team.name }}</nb-option>
							</nb-select>
						</ng-container>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-sm-12">
					<div class="form-group">
						<label class="label">{{ 'TASKS_PAGE.TASKS_TITLE' | translate }}</label>
						<input
							class="name-input"
							formControlName="title"
							type="text"
							nbInput
							[placeholder]="'FORM.PLACEHOLDERS.ADD_TITLE' | translate"
						/>
					</div>
				</div>
				<div class="col-sm-6">
					<div class="form-group">
						<label class="label">
							{{ 'TASKS_PAGE.TASK_PRIORITY' | translate }}
						</label>
						<ga-task-priority-select
							[projectId]="form.get('projectId').value"
							formControlName="taskPriority"
							[placeholder]="'TASKS_PAGE.TASK_PRIORITY' | translate"
						></ga-task-priority-select>
					</div>
				</div>
				<div class="col-sm-6">
					<div class="form-group">
						<label class="label">
							{{ 'TASKS_PAGE.TASK_SIZE' | translate }}
						</label>
						<ga-task-size-select
							[projectId]="form.get('projectId').value"
							formControlName="taskSize"
							[placeholder]="'TASKS_PAGE.TASK_SIZE' | translate"
						></ga-task-size-select>
					</div>
				</div>
				<div class="col-sm-12">
					<div class="form-group">
						<ga-tags-color-input
							[selectedTags]="form.get('tags').value"
							(selectedTagsEvent)="selectedTagsHandler($event)"
							[isOrgLevel]="true"
						></ga-tags-color-input>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-sm-6">
					<div class="form-group">
						<label for="dueDate" class="label">{{ 'TASKS_PAGE.DUE_DATE' | translate }}</label>
						<input
							formControlName="dueDate"
							type="text"
							nbInput
							[placeholder]="'TASKS_PAGE.DUE_DATE' | translate"
							[nbDatepicker]="taskDueDatePicker"
							id="dueDate"
							fullWidth
						/>
						<nb-datepicker #taskDueDatePicker></nb-datepicker>
					</div>
				</div>
				<div class="col-sm-6">
					<div class="form-group">
						<label class="label">{{ 'TASKS_PAGE.ESTIMATE' | translate }}</label>
						<div class="estimate-inputs">
							<input
								formControlName="estimateDays"
								type="number"
								[min]="0"
								nbInput
								[placeholder]="'TASKS_PAGE.ESTIMATE_DAYS' | translate"
							/>
							<input
								formControlName="estimateHours"
								type="number"
								[min]="0"
								[status]="form.get('estimateHours').errors ? 'danger' : 'basic'"
								min="0"
								max="23"
								nbInput
								[placeholder]="'TASKS_PAGE.ESTIMATE_HOURS' | translate"
							/>
							<input
								formControlName="estimateMinutes"
								type="number"
								[min]="0"
								[status]="form.get('estimateMinutes').errors ? 'danger' : 'basic'"
								min="0"
								max="59"
								nbInput
								[placeholder]="'TASKS_PAGE.ESTIMATE_MINUTES' | translate"
							/>
						</div>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-sm-12">
					<div class="form-group">
						<label class="label">{{ 'TASKS_PAGE.TASKS_DESCRIPTION' | translate }}</label>
						<ckeditor class="description" formControlName="description" [config]="ckConfig"></ckeditor>
					</div>
				</div>
			</div>
		</form>
	</nb-card-body>
	<nb-card-footer class="text-left">
		<button (click)="dialogRef.close()" status="basic" outline class="mr-3" nbButton>
			{{ 'BUTTONS.CANCEL' | translate }}
		</button>
		<button (click)="onSave()" [disabled]="form.invalid" status="success" nbButton>
			{{ 'BUTTONS.SAVE' | translate }}
		</button>
	</nb-card-footer>
</nb-card>
