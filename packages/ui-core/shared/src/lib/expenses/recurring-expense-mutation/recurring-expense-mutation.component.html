<nb-card class="main">
	<nb-card-header class="d-flex flex-column">
		<div class="cancel">
			<i (click)="close()" class="fas fa-times"></i>
		</div>
		<h5 class="title">
			{{
				(componentType === ComponentTypeEnum.EMPLOYEE
					? 'EMPLOYEES_PAGE.RECURRING_EXPENSE'
					: 'ORGANIZATIONS_PAGE.RECURRING_EXPENSE'
				) | translate
			}}
		</h5>
	</nb-card-header>
	<nb-card-body>
		<form [formGroup]="form">
			<ng-container *ngIf="componentType === ComponentTypeEnum.EMPLOYEE">
				<div class="row" [hidden]="recurringExpense">
					<div class="col-sm-12 mb-3">
						<ga-employee-selector
							#employeeSelector
							placeholder="Employee"
							[defaultSelected]="true"
							[showAllEmployeesOption]="true"
							[skipGlobalChange]="true"
							class="employees"
						></ga-employee-selector>
					</div>
				</div>
			</ng-container>
			<div class="row">
				<div class="col">
					<div class="form-group">
						<label class="label" for="categoryInput">{{ 'POP_UPS.CATEGORY_NAME' | translate }}</label>
						<ng-select
							[items]="defaultFilteredCategories"
							[addTag]="addNewCustomCategory"
							id="categoryInput"
							[searchable]="true"
							fullWidth
							[placeholder]="'POP_UPS.CATEGORY_NAME' | translate"
							formControlName="categoryName"
							bindValue="value"
							appendTo="body"
						></ng-select>
					</div>
				</div>
			</div>
			<div class="row" *ngIf="componentType === ComponentTypeEnum.ORGANIZATION">
				<div class="col">
					<div class="form-group">
						<nb-checkbox formControlName="splitExpense">
							<span nbTooltip="{{ 'EXPENSES_PAGE.SPLIT_HELP' | translate }}">{{
								'EXPENSES_PAGE.SPLIT_EXPENSE' | translate
							}}</span>
						</nb-checkbox>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-6">
					<div class="form-group">
						<label class="label" for="valueInput">{{ 'SM_TABLE.VALUE' | translate }}</label>
						<input
							[placeholder]="'SM_TABLE.VALUE' | translate"
							fullWidth
							id="valueInput"
							type="number"
							[min]="0"
							step="0.1"
							nbInput
							formControlName="value"
						/>
					</div>
				</div>
				<div class="col-sm-6">
					<ga-currency formControlName="currency" [formControl]="form.get('currency')"></ga-currency>
				</div>
			</div>
			<!-- TODO: translate -->
			<div class="row" *ngIf="recurringExpense">
				<div class="col">
					<div class="form-group">
						<label> Starts On </label>
						<input
							[nbDatepicker]="datepicker"
							nbInput
							fullWidth
							[placeholder]="'POP_UPS.PICK_DATE' | translate"
							formControlName="startDate"
							(ngModelChange)="datePickerChanged($event)"
						/>
						<nb-datepicker #datepicker></nb-datepicker>

						<div
							[ngSwitch]="startDateUpdateType"
							style="margin-top: 10px"
							[nbSpinner]="startDateChangeLoading"
							nbSpinnerStatus="danger"
							nbSpinnerSize="large"
							nbSpinnerMessage=""
						>
							<nb-alert status="warning" *ngSwitchCase="'REDUCE_CONFLICT'">
								{{ 'EXPENSES_PAGE.RECURRING_EXPENSES.WARNING' | translate }}
								<span *ngFor="let conflict of conflicts">
									{{ conflict.value | currency : conflict.currency }}:
									{{ 'EXPENSES_PAGE.RECURRING_EXPENSES.FROM' | translate }}
									{{ formatToOrganizationDate(conflict.startDate) }}
									{{ 'EXPENSES_PAGE.RECURRING_EXPENSES.TO' | translate }}
									{{ formatToOrganizationDate(conflict.endDate) }}</span
								>{{ 'EXPENSES_PAGE.RECURRING_EXPENSES.VALUE_OVERWRITTEN' | translate }}
								{{ month(startDate) }}!
							</nb-alert>
							<nb-alert status="danger" *ngSwitchCase="'INCREASE_CONFLICT'">
								{{ 'EXPENSES_PAGE.RECURRING_EXPENSES.ERROR' | translate }}
								<span *ngFor="let conflict of conflicts">
									{{ conflict.value | currency : conflict.currency }}:
									{{ 'EXPENSES_PAGE.RECURRING_EXPENSES.FROM' | translate }}
									{{ formatToOrganizationDate(conflict.startDate) }}
									{{ 'EXPENSES_PAGE.RECURRING_EXPENSES.TO' | translate }}
									{{ formatToOrganizationDate(conflict.endDate) }}</span
								>
								{{ 'EXPENSES_PAGE.RECURRING_EXPENSES.NOT_SUPPORTED' | translate }}
							</nb-alert>
							<nb-alert status="warning" *ngSwitchCase="'INCREASE_SAFE_WITHIN_LIMIT'">
								{{ 'EXPENSES_PAGE.RECURRING_EXPENSES.EDIT_FUTURE_VALUE' | translate }}
								{{ formatToOrganizationDate(startDate) }},
								{{ 'EXPENSES_PAGE.RECURRING_EXPENSES.EXISTING_VALUE' | translate }}
								{{ recurringExpense.value | currency : recurringExpense.currency }}
								{{ 'EXPENSES_PAGE.RECURRING_EXPENSES.STARTED_ON' | translate }}
								{{ formatToOrganizationDate(recurringExpense.startDate) }}
								{{ 'EXPENSES_PAGE.RECURRING_EXPENSES.AFFECTED' | translate }}
								{{ previousMonth(startDate) }}.
							</nb-alert>
							<nb-alert status="warning" *ngSwitchCase="'INCREASE_SAFE_OUTSIDE_LIMIT'">
								{{ 'EXPENSES_PAGE.RECURRING_EXPENSES.SET_EXPENSE_VALUE' | translate }}
								{{ value | currency : recurringExpense.currency }}
								{{ 'EXPENSES_PAGE.RECURRING_EXPENSES.FROM' | translate }}
								{{ formatToOrganizationDate(startDate) }}
								{{ 'EXPENSES_PAGE.RECURRING_EXPENSES.ONWARDS' | translate }}
								{{ recurringExpense.value | currency : recurringExpense.currency }}
								({{ 'EXPENSES_PAGE.RECURRING_EXPENSES.STARTED_ON' | translate }}
								{{ formatToOrganizationDate(recurringExpense.startDate) }}
								{{ 'EXPENSES_PAGE.RECURRING_EXPENSES.ENDING_ON' | translate }}
								{{ formatToOrganizationDate(recurringExpense.endDate) }})
								{{ 'EXPENSES_PAGE.RECURRING_EXPENSES.SET_UNTIL' | translate }}
								{{ previousMonth(startDate) }}.
							</nb-alert>
							<nb-alert status="warning" *ngSwitchCase="'REDUCE_SAFE'">
								{{ 'EXPENSES_PAGE.RECURRING_EXPENSES.SET_UNTIL' | translate }}
								{{ formatToOrganizationDate(startDate) }}
								{{ 'EXPENSES_PAGE.RECURRING_EXPENSES.FOR_EXPENSE_VALUE' | translate }}
								{{ value | currency : currencyValue }}
							</nb-alert>
							<nb-alert status="primary" *ngSwitchCase="'WITHIN_MONTH'">
								{{ 'EXPENSES_PAGE.RECURRING_EXPENSES.CHANGE_START_DATE' | translate }}
								{{ formatToOrganizationDate(startDate) }}
							</nb-alert>
						</div>
					</div>
				</div>
			</div>
		</form>
	</nb-card-body>
	<nb-card-footer>
		<button (click)="close()" nbButton size="small" outline class="mr-2" status="basic">
			{{ 'BUTTONS.CANCEL' | translate }}
		</button>
		<button
			(click)="submitForm()"
			[disabled]="form.invalid"
			type="submit"
			nbButton
			status="success"
			*ngIf="!recurringExpense"
			[nbTooltip]="'EMPLOYEES_PAGE.RECURRING_EXPENSE_ADD' | translate"
		>
			{{ 'BUTTONS.SAVE' | translate }}
		</button>
		<button
			(click)="submitForm()"
			size="small"
			[disabled]="form.invalid || startDateUpdateType === 'INCREASE_CONFLICT'"
			type="submit"
			nbButton
			status="success"
			*ngIf="recurringExpense"
		>
			{{ 'BUTTONS.EDIT' | translate }}
		</button>
	</nb-card-footer>
</nb-card>
