@use 'var' as *;

:host .setting-block {

  border-radius: nb-theme(border-radius);
  margin-bottom: 8px;
  background-color: nb-theme(gauzy-card-1) !important;

  &.block {
    box-shadow: -6px 0px 0px 0px rgba(0, 0, 0, 0.15);
  }

  .setting-row {
    display: flex;
    justify-content: space-between;
    width: 100%;
    height: 84px;
    background-color: nb-theme(gauzy-card-1);
    border-radius: nb-theme(border-radius);
    @include nb-ltr(padding, 0.75rem 0 0.75rem 0.75rem);
    @include nb-rtl(padding, 0.75rem 0.75rem 0.75rem 0);

    &:hover,
    &.active {
      background-color: nb-theme(gauzy-sidebar-background-3);
    }
  }

  .setting-table {
    background-color: transparent;
  }

  .block-settings {
    width: 150px;
    display: flex;
    height: 84px;
    align-items: center;
    background-color: #333;
    justify-content: space-evenly;
    overflow: hidden;
    transition: width 0.2s ease-in;
    border-radius: 0 nb-theme(border-radius) nb-theme(border-radius) 0;

    .single-setting {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 30px;
      cursor: pointer;

      ::ng-deep svg {
        fill: rgb(127, 127, 127);
      }

      span {
        color: #d7d9de;
        font-size: 10px;
        opacity: 0.6;
      }
    }
  }
}

:host .block-content {
  display: flex;
  justify-content: space-between;
  width: 100%;

  .block-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: fit-content;
    @include nb-ltr(padding-left, 42px);
    @include nb-rtl(padding-right, 42px);

    .expense {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;

      .block-item {
        width: 11rem;
        display: flex;
        justify-content: flex-start;
        align-items: center;
      }

      .block-item-big {
        width: 17.5rem;
        display: flex;
        justify-content: flex-start;
        align-items: center;
      }
    }

    span {
      color: nb-theme(gauzy-text-color-1);
      font-size: 14px;
    }

    .block-amount {
      width: 130px;
      display: flex;
      align-items: center;
      @include nb-ltr(padding-right, 24px);
      @include nb-rtl(padding-left, 24px);
      font-size: 14px;
      font-weight: 400;
      line-height: 17px;
      letter-spacing: 0em;
      cursor: pointer;
    }
  }

  .block-panel {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 240px;
    position: relative;
    visibility: hidden;
    display: none;

    .block-value {
      display: flex;
      align-items: center;
      @include nb-ltr(right, 30px);
      @include nb-rtl(left, 30px);

      .hide-menu {
        cursor: pointer;
      }

      .block-actions.open {
        cursor: pointer;
        position: absolute;
        @include nb-ltr(right, 35px);
        @include nb-rtl(left, 35px);
      }

      small {
        cursor: pointer;
      }

      svg {
        width: 24px;
        height: fit-content;
      }
    }
  }
}

@media screen and (max-width: 1438px) {
  .setting-block {
    width: fit-content;
  }
}
