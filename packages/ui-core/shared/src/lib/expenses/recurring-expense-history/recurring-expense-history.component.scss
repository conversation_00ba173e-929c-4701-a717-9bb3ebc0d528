@use 'var' as *;

.recurring-expense-history {
  background-color: transparent;
  padding: 10px 30px 0px 30px;
  border-radius: 0 0 nb-theme(border-radius) nb-theme(border-radius);
  display: flex;
  flex-direction: row;
  justify-content: space-between;

  .history {
    max-width: 560px;
    flex: 1;

    row.head {
      b {
        font-weight: 600;
        color: nb-theme(gauzy-text-color-2);
      }
    }

    h6 {
      font-size: 16px;
      font-weight: 600;
      line-height: 17px;
      letter-spacing: 0em;
      text-align: left;
      color: nb-theme(gauzy-text-color-1);
    }
  }

  .close-icon {
    cursor: pointer;
    justify-content: space-between;
  }
}
