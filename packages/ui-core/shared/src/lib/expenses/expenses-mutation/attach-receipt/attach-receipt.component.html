<nb-card>
	<nb-card-body class="receipt-image p-0">
		<ngx-image-uploader
			[folder]="'attach_receipts'"
			(changeHoverState)="hoverState = $event"
			(uploadedImageAsset)="updateImageAsset($event)"
		></ngx-image-uploader>

		<svg
			xmlns="http://www.w3.org/2000/svg"
			xmlns:xlink="http://www.w3.org/1999/xlink"
			width="68"
			height="68"
			viewBox="0 0 68 68"
			[ngStyle]="{ opacity: hoverState ? '1' : '0.3' }"
		>
			<defs>
				<path
					id="a"
					d="M28.667 31.333a2 2 0 1 0-.002-4.001 2 2 0 0 0 .002 4.001m13.333 12H26.748l9.34-7.793c.328-.279.923-.277 1.244-.001l6.001 5.12V42c0 .736-.597 1.333-1.333 1.333M26 24.667h16c.736 0 1.333.597 1.333 1.333v11.152l-4.27-3.643c-1.32-1.122-3.386-1.122-4.694-.008l-9.702 8.096V26c0-.736.597-1.333 1.333-1.333M42 22H26c-2.205 0-4 1.795-4 4v16c0 2.205 1.795 4 4 4h16c2.205 0 4-1.795 4-4V26c0-2.205-1.795-4-4-4"
				/>
			</defs>
			<g fill="none" fill-rule="evenodd">
				<circle cx="34" cy="34" r="34" fill="#0091FF" opacity=".3" />
				<circle cx="34" cy="34" r="26" fill="#0091FF" opacity=".9" />
				<use fill="#FFF" fill-rule="nonzero" xlink:href="#a" />
			</g>
		</svg>

		<div
			class="image-overlay"
			[ngStyle]="{ opacity: hoverState ? '0.2' : '0' }"
		></div>

		<img
			*ngIf="imageUrl"
			[src]="imageUrl"
			alt="Receipt Image"
			(mouseenter)="hoverState = true"
			(mouseleave)="hoverState = false"
		/>
	</nb-card-body>
	<nb-card-footer class="text-left">
		<button (click)="cancelReceipt()" status="basic" outline nbButton>
			{{ 'BUTTONS.CANCEL' | translate }}
		</button>
		<button
			[disabled]="disable"
			(click)="saveReceipt()"
			status="success"
			nbButton
      		class="mr-3 ml-3"
		>
			{{ 'BUTTONS.SAVE' | translate }}
		</button>
	</nb-card-footer>
</nb-card>
