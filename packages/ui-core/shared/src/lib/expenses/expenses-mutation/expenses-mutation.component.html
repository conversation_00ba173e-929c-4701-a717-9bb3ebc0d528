<nb-card class="main">
	<nb-card-header class="d-flex flex-column">
		<div class="cancel">
			<i class="fas fa-times" (click)="close()"></i>
		</div>
		<div>
			<h4 class="title" *ngIf="expense; else headerAdd">
				{{ (duplicate ? 'POP_UPS.DUPLICATE' : 'POP_UPS.EDIT_EXPENSE') | translate }}
			</h4>
			<ng-template #headerAdd>
				<h4 class="title">
					{{ 'POP_UPS.ADD_EXPENSE' | translate }}
				</h4>
			</ng-template>
		</div>
	</nb-card-header>
	<nb-card-body class="body">
		<form [formGroup]="form">
			<div class="text-center">
				<nb-radio-group formControlName="typeOfExpense">
					<nb-radio
						*ngFor="let expenseType of expenseTypes"
						[value]="expenseType"
						(valueChange)="setExpenseStatuses($event)"
					>
						{{ 'EXPENSES_PAGE.MUTATION.' + expenseType | translate }}
					</nb-radio>
				</nb-radio-group>
			</div>
			<div class="row employees">
				<div class="col-sm-6">
					<label class="label" for="status" [hidden]="expense && !duplicate">
						{{ 'EXPENSES_PAGE.MUTATION.EMPLOYEES_GENERATE_EXPENSE' | translate }}
					</label>
					<ga-employee-selector
						fullWidth
						[hidden]="expense && !duplicate"
						[skipGlobalChange]="true"
						class="employees"
						[placeholder]="'EXPENSES_PAGE.MUTATION.EMPLOYEES_GENERATE_EXPENSE' | translate"
						[defaultSelected]="true"
						(selectionChanged)="onEmployeeChange($event)"
					></ga-employee-selector>
				</div>
				<div class="col-sm-6 categories" [ngClass]="{ 'col-sm-12': expense && !duplicate }">
					<div class="form-group">
						<label class="label">{{ 'POP_UPS.CATEGORIES' | translate }}</label>
						<ga-expense-category-select
							[clearable]="true"
							formControlName="category"
							[addTag]="true"
							[placeholder]="'POP_UPS.ALL_CATEGORIES' | translate"
						></ga-expense-category-select>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-sm-6">
					<label class="label">{{ 'POP_UPS.DATE' | translate }}</label>
					<input
						formControlName="valueDate"
						nbInput
						class="datepicker"
						[placeholder]="'POP_UPS.PICK_DATE' | translate"
						[nbDatepicker]="valueDatePicker"
						[status]="FormHelpers.isInvalidControl(form, 'valueDate') ? 'danger' : 'basic'"
					/>
					<nb-datepicker #valueDatePicker></nb-datepicker>
				</div>
				<div class="col-sm-6">
					<div class="form-group">
						<label class="label">{{ 'POP_UPS.ALL_VENDORS' | translate }}</label>
						<ga-vendor-select
							formControlName="vendor"
							[searchable]="true"
							[addTag]="true"
							[placeholder]="'POP_UPS.ALL_VENDORS' | translate"
							[clearable]="true"
						></ga-vendor-select>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-sm-6">
					<div class="form-group">
						<label class="label d-flex align-items-center">
							{{ 'POP_UPS.AMOUNT' | translate }}
							<ng-container *ngIf="showTooltip">
								<span class="help-text ml-2" [nbTooltip]="'EXPENSES_PAGE.SPLIT_HELP' | translate">
									<nb-icon size="small" status="danger" icon="alert-triangle-outline"></nb-icon>
								</span>
							</ng-container>
						</label>
						<input
							nbInput
							[min]="0"
							type="number"
							step="1"
							fullWidth
							[placeholder]="'POP_UPS.AMOUNT' | translate"
							formControlName="amount"
							[status]="FormHelpers.isInvalidControl(form, 'amount') ? 'danger' : 'basic'"
							autocomplete="off"
						/>
					</div>
				</div>
				<div class="col-sm-6">
					<ga-currency formControlName="currency" (optionChange)="currencyChanged($event)"></ga-currency>
				</div>
			</div>
			<div class="row">
				<div class="col-sm-12">
					<label class="label">{{ 'POP_UPS.PURPOSE' | translate }}</label>
					<div class="form-group">
						<textarea
							nbInput
							formControlName="purpose"
							fullWidth
							[placeholder]="'POP_UPS.PURPOSE' | translate"
						></textarea>
					</div>
				</div>
			</div>
			<nb-card accent="warning" class="p-3" *ngIf="showWarning">
				<nb-icon class="ml-auto close" (click)="closeWarning()" icon="close-outline"></nb-icon>
				<p>
					<b>{{ 'EXPENSES_PAGE.MUTATION.CONTACT_IS_REQUIRED' | translate }}</b>
				</p>
				<p>
					{{ 'EXPENSES_PAGE.MUTATION.PLEASE_SELECT_A_CONTACT_OR_CHANGE_EXPENSE_TYPE' | translate }}
				</p>
			</nb-card>
			<div>
				<h6 class="title mb-3">
					{{ 'EXPENSES_PAGE.MUTATION.ASSIGN_TO' | translate }}
				</h6>
				<div class="row">
					<div class="col-sm-6">
						<div class="form-group">
							<label class="label">
								{{ 'CONTEXT_MENU.CONTACT' | translate }}
							</label>
							<ga-contact-select
								[addTag]="true"
								[clearable]="true"
								[searchable]="true"
								[placeholder]="'CONTEXT_MENU.CONTACT' | translate"
								formControlName="organizationContact"
								(onChanged)="selectedOrganizationContact($event)"
							></ga-contact-select>
						</div>
					</div>
					<div class="col-sm-6">
						<div class="form-group">
							<label class="label">{{ 'CONTEXT_MENU.PROJECT' | translate }}</label>
							<ga-project-selector
								formControlName="projectId"
								[placeholder]="'CONTEXT_MENU.PROJECT' | translate"
								[skipGlobalChange]="true"
								[defaultSelected]="false"
								[showAllOption]="false"
								(onChanged)="selectedProject($event)"
							></ga-project-selector>
						</div>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-sm-12">
					<div class="form-group">
						<ga-tags-color-input
							[selectedTags]="form.get('tags').value"
							(selectedTagsEvent)="selectedTagsHandler($event)"
							[isOrgLevel]="true"
						></ga-tags-color-input>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-4">
					<div class="form-group">
						<label class="label" for="status">
							{{ 'FORM.LABELS.STATUS' | translate }}
						</label>
						<div>
							<nb-select
								id="status"
								[placeholder]="'FORM.PLACEHOLDERS.SELECT_STATUS' | translate"
								fullWidth
								formControlName="status"
							>
								<nb-option *ngFor="let status of statuses" [value]="status">
									{{ 'EXPENSES_PAGE.MUTATION.' + status | translate }}
								</nb-option>
							</nb-select>
						</div>
					</div>
				</div>
				<div class="col-8">
					<div class="mt-2 text-right">
						<button
							nbButton
							size="tiny"
							status="basic"
							outline
							(click)="showNotesInput()"
							*ngIf="!showNotes"
							class="gray ml-2 mt-3"
						>
							<i class="fas fa-plus mr-1"></i>
							{{ 'BUTTONS.ADD_NOTE' | translate }}
						</button>
						<button
							nbButton
							size="tiny"
							status="basic"
							outline
							(click)="includeTaxes()"
							*ngIf="!showTaxesInput"
							class="gray ml-2 mt-3"
						>
							<i class="fas fa-percentage mr-1"></i>
							{{ 'EXPENSES_PAGE.MUTATION.INCLUDE_TAXES' | translate }}
						</button>
						<button
							nbButton
							size="tiny"
							status="basic"
							outline
							(click)="attachReceipt()"
							class="gray ml-2 mt-3"
						>
							<i class="fas fa-paperclip mr-1"></i>
							{{ 'EXPENSES_PAGE.MUTATION.ATTACH_A_RECEIPT' | translate }}
						</button>
					</div>
				</div>
			</div>
			<div *ngIf="showNotes">
				<h6 class="title mb-3">
					{{ 'POP_UPS.NOTES' | translate }}
				</h6>
				<div class="row">
					<div class="col">
						<div class="form-group">
							<textarea
								nbInput
								fullWidth
								[placeholder]="'POP_UPS.NOTES' | translate"
								formControlName="notes"
								[status]="FormHelpers.isInvalidControl(form, 'notes') ? 'danger' : 'basic'"
							>
							</textarea>
						</div>
					</div>
				</div>
			</div>
			<div *ngIf="showTaxesInput">
				<h6 class="title mb-3">
					{{ 'EXPENSES_PAGE.MUTATION.INCLUDE_TAXES' | translate }}
				</h6>
				<div class="row">
					<div class="col-sm-6">
						<div class="form-group">
							<input
								nbInput
								type="text"
								formControlName="taxLabel"
								fullWidth
								[placeholder]="'POP_UPS.TAX_LABEL' | translate"
							/>
						</div>
					</div>
					<div class="col-sm-6">
						<div class="form-group">
							<nb-select
								formControlName="taxType"
								[placeholder]="'POP_UPS.TAX_TYPE' | translate"
								fullWidth
							>
								<nb-option *ngFor="let taxType of taxTypes" [value]="taxType">
									{{ 'EXPENSES_PAGE.MUTATION.' + taxType | translate }}
								</nb-option>
							</nb-select>
						</div>
					</div>
					<div class="col-sm-6">
						<div class="form-group">
							<input
								nbInput
								[min]="0"
								type="number"
								step="1"
								fullWidth
								formControlName="rateValue"
								[placeholder]="'POP_UPS.TAX_RATE' | translate"
							/>
						</div>
					</div>
					<div class="col-sm-6">
						<input nbInput type="text" fullWidth [placeholder]="calculatedValue" disabled />
					</div>
				</div>
			</div>
		</form>
	</nb-card-body>
	<nb-card-footer>
		<button (click)="close()" status="basic" outline class="mr-3" nbButton>
			{{ 'BUTTONS.CANCEL' | translate }}
		</button>
		<button [disabled]="form.invalid" (click)="addOrEditExpense()" status="success" nbButton>
			{{ 'BUTTONS.SAVE' | translate }}
		</button>
	</nb-card-footer>
</nb-card>
