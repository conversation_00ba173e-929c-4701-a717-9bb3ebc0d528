<form [formGroup]="form" *ngIf="form">
	<label class="label" for="addSkills">
		{{ 'SKILLS_PAGE.HEADER' | translate }}
	</label>
	<ng-select
		bindLabel="name"
		[addTag]="true"
		formControlName="skills"
		[items]="skills"
		[(ngModel)]="selectedSkills"
		multiple="true"
		(change)="onChange()"
		placeholder=" {{ 'SKILLS_PAGE.HEADER' | translate }} "
		appendTo="body"
	>
		<ng-template ng-label-tmp let-item="item" let-clear="clear">
			<span class="ng-value-label"
				><nb-badge
					width="20px"
					height="20px"
					[style.background]="item.color"
					text="{{ item.name }}"
				></nb-badge
			></span>
			<span
				class="ng-value-icon right"
				(click)="clear(item)"
				aria-hidden="true"
				>×</span
			>
		</ng-template>
		<ng-template ng-option-tmp let-item="item">
			<nb-badge
				width="20px"
				height="20px"
				[style.background]="item.color"
				text="{{ item.name }}"
			></nb-badge>
		</ng-template>
	</ng-select>
</form>

<form *ngIf="!form">
	<label class="label" for="addSkills">
		{{ 'SKILLS_PAGE.HEADER' | translate }}
	</label>
	<ng-select
		[items]="skills"
		[(ngModel)]="selectedSkills"
		bindLabel="id"
		multiple="true"
		(change)="onChange()"
		[ngModelOptions]="{ standalone: true }"
		placeholder=" {{ 'SKILLS_PAGE.HEADER' | translate }} "
		appendTo="body"
	>
		<ng-template ng-label-tmp let-item="item" let-clear="clear">
			<span class="ng-value-label"
				><nb-badge
					width="20px"
					height="20px"
					[style.background]="item.color"
					text="{{ item.name }}"
				></nb-badge
			></span>
			<span
				class="ng-value-icon right"
				(click)="clear(item)"
				aria-hidden="true"
				>×</span
			>
		</ng-template>
		<ng-template ng-option-tmp let-item="item">
			<nb-badge
				width="20px"
				height="20px"
				[style.background]="item.color"
				text="{{ item.name }}"
			></nb-badge>
		</ng-template>
	</ng-select>
</form>
