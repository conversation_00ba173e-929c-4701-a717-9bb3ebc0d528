<nb-card>
	<nb-card-header class="d-flex flex-column">
		<span class="cancel"
			><i class="fas fa-times" (click)="closeDialog()"></i
		></span>
		<h5 class="title">
			{{
				(approvalPolicy
					? 'APPROVAL_POLICY_PAGE.EDIT_APPROVAL_POLICY'
					: 'APPROVAL_POLICY_PAGE.ADD_APPROVAL_POLICY'
				) | translate
			}}
		</h5>
	</nb-card-header>
	<nb-card-body class="body">
		<form
			[formGroup]="form"
			(ngSubmit)="onSubmit()"
			#formDirective="ngForm"
		>
			<div class="row">
				<div class="col-sm-12 mb-3">
					<label for="name" class="label">
						{{
							'APPROVAL_POLICY_PAGE.APPROVAL_POLICY_NAME'
								| translate
						}}
					</label>
					<input
						nbInput
						type="text"
						formControlName="name"
						fullWidth
						[placeholder]="
							'APPROVAL_POLICY_PAGE.APPROVAL_POLICY_NAME'
								| translate
						"
						[ngClass]="{
							'status-danger': FormHelpers.isInvalidControl(
								form,
								'name'
							),
							'status-success': FormHelpers.isValidControl(
								form,
								'name'
							)
						}"
					/>
				</div>
				<div class="col-sm-12 mb-3">
					<label for="Description" class="label">
						{{
							'APPROVAL_POLICY_PAGE.APPROVAL_POLICY_DESCRIPTION'
								| translate
						}}
					</label>
					<textarea
						nbInput
						formControlName="description"
						fullWidth
						rows="4"
						[placeholder]="
							'APPROVAL_POLICY_PAGE.APPROVAL_POLICY_DESCRIPTION'
								| translate
						"
					></textarea>
				</div>
			</div>
		</form>
	</nb-card-body>
	<nb-card-footer>
		<button (click)="closeDialog()" status="basic" outline class="mr-3" nbButton>
			{{ 'BUTTONS.CANCEL' | translate }}
		</button>
		<button
			[disabled]="form.invalid || formDirective.submitted"
			(click)="formDirective.onSubmit()"
			status="success"
			nbButton
		>
			{{ 'BUTTONS.SAVE' | translate }}
		</button>
	</nb-card-footer>
</nb-card>
