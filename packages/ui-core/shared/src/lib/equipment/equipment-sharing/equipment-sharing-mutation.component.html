<nb-card class="main">
	<nb-card-header class="d-flex flex-column">
		<span class="cancel"
			><i class="fas fa-times" (click)="closeDialog()"></i
		></span>
		<h5 class="title">
			{{
				(equipmentSharing
					? 'EQUIPMENT_SHARING_PAGE.EDIT_EQUIPMENT_REQUEST'
					: 'EQUIPMENT_SHARING_PAGE.ADD_EQUIPMENT_REQUEST'
				) | translate
			}}
		</h5>
	</nb-card-header>
	<nb-card-body>
		<form [formGroup]="form" *ngIf="form">
			<div class="row">
				<div class="col-sm-5 mb-4">
					<input
						class="d-block"
						nbInput
						type="text"
						formControlName="name"
						fullWidth
						[placeholder]="'FORM.PLACEHOLDERS.NAME' | translate"
					/>
				</div>

				<div class="col-sm-5 mb-4">
					<nb-select
						class="d-block"
						formControlName="equipment"
						(selectedChange)="setRequestStatus($event)"
						[placeholder]="
							'FORM.LABELS.SELECT_EQUIPMENT' | translate
						"
					>
						<nb-option
							*ngFor="let item of equipmentItems"
							[value]="item.id"
						>
							{{ item.name }}
						</nb-option>
					</nb-select>
				</div>
				<div class="col-sm-2 mb-4">
					<div
						class="status-label"
						[ngClass]="{
							active: requestStatus === requestStatuses[2],
							requested: requestStatus === requestStatuses[0],
							approved: requestStatus === requestStatuses[1]
						}"
					>
						<h4>{{ getStatus(requestStatus) }}</h4>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-sm-12 mb-3">
					<nb-select
						formControlName="equipmentSharingPolicyId"
						[selected]="selectedEquipmentSharingPolicy"
						(selectedChange)="
							onEquipmentSharingPolicySelected($event)
						"
						fullWidth
						placeholder="{{
							'FORM.PLACEHOLDERS.CHOOSE_APPROVAL_POLICY'
								| translate
						}}"
					>
						<nb-option
							*ngFor="let policy of equipmentSharingPolicies"
							[value]="policy.id"
						>
							{{ policy.name }}</nb-option
						>
					</nb-select>
				</div>
			</div>
			<div class="row">
				<div class="col-sm-12 mb-3">
					<nb-radio-group
						(valueChange)="onParticipantsChange($event)"
						[value]="participants"
					>
						<nb-radio
							[value]="equipmentSharingParticipantEnum.EMPLOYEE"
							>{{ 'APPROVAL_REQUEST_PAGE.EMPLOYEES' | translate }}
						</nb-radio>
						<nb-radio [value]="equipmentSharingParticipantEnum.TEAM"
							>{{ 'APPROVAL_REQUEST_PAGE.TEAMS' | translate }}
						</nb-radio>
					</nb-radio-group>
				</div>
			</div>
			<div class="row">
				<ng-container
					*ngIf="
						participants ===
						equipmentSharingParticipantEnum.EMPLOYEE
					"
				>
					<div class="col-sm-12 mb-3">
						<ga-employee-multi-select
							[selectedEmployeeIds]="form.get('employees').value"
							[placeholder]="
								'FORM.PLACEHOLDERS.CHOOSE_EMPLOYEES' | translate
							"
							(selectedChange)="onEmployeesSelected($event)"
						></ga-employee-multi-select>
					</div>
				</ng-container>
				<ng-container
					*ngIf="
						participants === equipmentSharingParticipantEnum.TEAM
					"
				>
					<div class="col-sm-12 mb-3">
						<nb-select
							formControlName="teams"
							multiple
							[selected]="selectedTeams"
							(selectedChange)="onTeamsSelected($event)"
							fullWidth
							[placeholder]="
								'FORM.PLACEHOLDERS.CHOOSE_TEAMS' | translate
							"
						>
							<nb-option
								*ngFor="let team of teams"
								[value]="team.id"
							>
								{{ team.name }}
							</nb-option>
						</nb-select>
					</div>
				</ng-container>
			</div>
			<div class="row">
				<div class="col-sm-6">
					<div class="form-group">
						<input
							fullWidth
							formControlName="shareRequestDay"
							nbInput
							[nbDatepicker]="shareRequestDatePicker"
							placeholder="{{
								'FORM.LABELS.SELECT_SHARE_REQUEST_DATE'
									| translate
							}}"
						/>
						<nb-datepicker #shareRequestDatePicker></nb-datepicker>
					</div>
				</div>
				<div class="col-sm-6">
					<div class="form-group">
						<input
							fullWidth
							formControlName="shareStartDay"
							nbInput
							[nbDatepicker]="shareStartDatePicker"
							placeholder="{{
								'FORM.LABELS.SELECT_SHARE_START_DATE'
									| translate
							}}"
							[ngClass]="{
								'status-danger':
									shareStartDay.invalid &&
									shareStartDay.touched
							}"
						/>
						<nb-datepicker
							#shareStartDatePicker
							[(date)]="date1"
							[filter]="filter"
						></nb-datepicker>
						<div
							class="text-danger"
							*ngIf="
								shareStartDay.dirty &&
								shareStartDay.errors &&
								shareStartDay.errors.beforeRequestDay
							"
						>
							{{ shareStartDay.errors.beforeRequestDayMsg }}
						</div>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-sm-6">
					<div class="form-group">
						<input
							fullWidth
							formControlName="shareEndDay"
							nbInput
							[nbDatepicker]="shareEndDatePicker"
							placeholder="{{ 'POP_UPS.PICK_DATE' | translate }}"
							placeholder="{{
								'FORM.LABELS.SELECT_SHARE_END_DATE' | translate
							}}"
							[ngClass]="{
								'status-danger':
									shareEndDay.invalid && shareEndDay.touched
							}"
						/>
						<nb-datepicker
							#shareEndDatePicker
							[(date)]="date2"
							[filter]="filter"
						></nb-datepicker>
						<div *ngIf="shareEndDay.dirty && shareEndDay.errors">
							<div
								class="text-danger"
								*ngIf="shareEndDay.errors.exceedAllowedDays"
							>
								{{ shareEndDay.errors.exceedAllowedDaysMsg }}
							</div>
							<div
								class="text-danger"
								*ngIf="shareEndDay.errors.beforeStartDate"
							>
								{{ shareEndDay.errors.beforeStartDateMsg }}
							</div>
							<div
								class="text-danger"
								*ngIf="shareEndDay.errors.itemInUse"
							>
								{{ shareEndDay.errors.itemInUseMsg }}
							</div>
						</div>
					</div>
				</div>
			</div>
		</form>
	</nb-card-body>

	<nb-card-footer class="text-left">
		<button
			class="delete mr-3"
			(click)="dialogRef.close()"
			nbButton
			status="basic"
			outline
		>
			{{ 'BUTTONS.CANCEL' | translate }}
		</button>
		<button
			[disabled]="form.invalid"
			status="success"
			nbButton
			(click)="onSaveRequest()"
		>
			{{ 'BUTTONS.SAVE' | translate }}
		</button>
	</nb-card-footer>
</nb-card>
