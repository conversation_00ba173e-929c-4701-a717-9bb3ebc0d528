@use 'gauzy/_gauzy-dialogs' as *;

.card {
	display: flex;
	flex-direction: column;
	width: 500px;
	background-color: var(--gauzy-card-1);
	border-radius: var(--border-radius);

	nb-card-body {
		&.card-body {
			nb-toggle {
				span {
					font-size: 0.75rem;
					font-weight: 700;
					line-height: 1rem;
					margin: 0;
				}
			}
		}
	}
}

:host {

	input,
	::ng-deep nb-select.appearance-outline.status-basic .select-button,
	::ng-deep .ng-select .ng-select-container {
		background-color: var(--gauzy-sidebar-background-4) !important;
		border: unset;
	}

	::ng-deep .ng-select .ng-select-container {
		input {
			background-color: unset !important;
		}
	}

	::ng-deep .toggle {
		border: 1px solid #7E7E8F !important;
		background-color: #7E7E8F !important;

		&.checked {
			background-color: var(--text-primary-color) !important;
			border: 1px solid var(--text-primary-color) !important;

			&+span {
				color: var(--text-primary-color);
			}
		}
	}
}
