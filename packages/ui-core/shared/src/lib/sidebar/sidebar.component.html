<div class="sidemenu-wrap">
	<nb-card class="sidemenu">
		<nb-card-body class="sidebar">
			<div class="add-icon-field">
				<button status="success" class="base-button" (click)="addEditBase(actionEnum.ADD)" nbButton>
					{{ 'HELP_PAGE.KNOWLEDGE_BASE' | translate }}
					<nb-icon class="mr-1" icon="plus-outline"></nb-icon>
				</button>
			</div>
			<div class="edit-node-field">
				<tree-root #tree [focused]="false" [nodes]="nodes" [options]="options" (moveNode)="onMoveNode($event)">
					<ng-template #loadingTemplate>{{ 'LOADING' | translate }}</ng-template>
					<ng-template #treeNodeTemplate let-node let-index="index" class="tree">
						<div
							class="base"
							[ngClass]="setClasses(node)"
							[ngStyle]="{ color: node.data.color }"
							(click)="onNodeClicked(node.data)"
						>
							<div>
								<nb-icon icon="{{ node.data.icon }}" class="icons" (click)="addIcon(node)"></nb-icon>
								<span class="text">{{ node.data.name }}</span>
							</div>
							<div class="d-flex">
								<div class="d-flex">
									<nb-icon
										class="icons privacy"
										icon="{{ node.data.privacy }}"
										(click)="changePrivacy(node.data)"
										[ngClass]="{
											'privacy-base': node.data.flag === 'base',
											'privacy-category': node.data.flag === 'category'
										}"
									></nb-icon>
									<nb-action
										*ngIf="node.data.flag === 'base'"
										icon="settings-2-outline"
										class="icons"
										[nbContextMenu]="settingsContextMenu"
									></nb-action>
								</div>
								<nb-icon
									*ngIf="node.data.flag === 'category'"
									[ngStyle]="{
										color: 'var(--text-primary-color)'
									}"
									class="edit-icons"
									icon="edit-outline"
									(click)="addEditCategory(actionEnum.EDIT, node.data)"
								></nb-icon>
								<nb-icon
									*ngIf="node.data.flag === 'category'"
									[ngStyle]="{
										color: 'var(--color-danger-default)'
									}"
									class="edit-icons"
									icon="trash-2-outline"
									(click)="deleteCategory(node.data)"
								></nb-icon>
							</div>
						</div>
					</ng-template>
				</tree-root>
			</div>
		</nb-card-body>
	</nb-card>
</div>
