@use 'var' as *;
@use 'gauzy/_gauzy-table' as *;

:host .actions-container {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  @include nb-rtl(left, 0);
  @include nb-ltr(right, 0);
  padding: 1rem 0;
}

:host .transition-container {
  overflow-x: hidden;
  border-radius: nb-theme(button-rectangle-border-radius);
  span {
    background: var(--gauzy-card-2);
    border-radius: nb-theme(button-rectangle-border-radius);
    padding: 2px 4px;
    @include nb-ltr(margin-right, 10px);
    @include nb-rtl(margin-left, 10px);
  }
}

:host .transition {
  display: flex;
  &.hide {
    @include nb-rtl(transform, translateX(-100%));
    @include nb-ltr(transform, translateX(100%));
    transition: all 0.1s ease-in;
  }
  &.show {
    opacity: 1;
    transform: translateX(0%);
    transition: all 0.2s ease-out;
  }
  &.show-button{
    @include nb-rtl(transform, translateX(100%));
    @include nb-ltr(transform, translateX(-100%));
    transition: all 0.15s ease-out;
  }
}
:host ga-layout-selector{
  @include nb-ltr(margin-left, 20px);
  @include nb-rtl(margin-right, 20px);
}
