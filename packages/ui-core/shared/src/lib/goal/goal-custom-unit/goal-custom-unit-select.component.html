<div [formGroup]="parentFormGroup">
	<ng-template
		[ngIf]="parentFormGroup.value.type == keyResultTypeEnum.CURRENCY"
		[ngIfElse]="unitTemplate"
	>
		<ga-currency
			*ngIf="parentFormGroup.value.type == keyResultTypeEnum.CURRENCY"
			formControlName="unit"
			[placeholder]="'Unit'"
		></ga-currency>
	</ng-template>
	<ng-template #unitTemplate>
		<label for="unit" class="label">
			{{ 'FORM.LABELS.UNIT' | translate }}
		</label>
		<nb-select
			fullWidth
			formControlName="unit"
			[selected]="numberUnits[0]"
			(selectedChange)="
				parentFormGroup.value.unit == 'create-new'
					? (createNew = !createNew)
					: true
			"
			*ngIf="
				parentFormGroup.value.type == keyResultTypeEnum.NUMERICAL &&
				!createNew
			"
			id="unit"
			nbSuffix
		>
			<nb-option [value]="unit" *ngFor="let unit of numberUnits">
				{{ unit }}
			</nb-option>
			<nb-option value="create-new" class="bg-secondary text-light">
				{{ 'GOALS_PAGE.CREATE_NEW' | translate }}
			</nb-option>
		</nb-select>
	</ng-template>
	<nb-form-field *ngIf="createNew">
		<input type="text" formControlName="unit" nbInput fullWidth />
		<nb-icon
			nbSuffix
			icon="checkmark-outline"
			status="success"
			(click)="createNewUnit()"
		>
		</nb-icon>
		<nb-icon
			nbSuffix
			icon="close-outline"
			status="danger"
			(click)="createNew = !createNew"
		>
		</nb-icon>
	</nb-form-field>
</div>
