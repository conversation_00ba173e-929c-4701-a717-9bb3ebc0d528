<nav class="d-flex justify-between align-items-center w-100 mt-3">
	<ul class="pagination mb-0">
		<li (click)="onChangePage(1)">
			<a href="#" aria-label="First">
				<span class="icon" aria-hidden="true">
					<nb-icon status="primary" icon="arrowhead-left-outline"></nb-icon>
				</span>
				<span class="sr-only">First</span>
			</a>
		</li>
		<li (click)="onPrevPageClick()" [ngClass]="{ disabled: activePage == 1 }">
			<a href="#" aria-label="Prev">
				<span class="icon" aria-hidden="true">
					<nb-icon status="primary" icon="arrow-ios-back-outline"></nb-icon>
				</span>
				<span class="sr-only">Prev</span>
			</a>
		</li>
		<li *ngFor="let page of getPages()" (click)="onChangePage(page)" [ngClass]="{ active: activePage == page }">
			<span>{{ page }} <span class="sr-only">(current)</span></span>
		</li>
		<li (click)="onNextPageClick()" [ngClass]="{ disabled: activePage == getPages().length }">
			<a href="#" aria-label="Next">
				<span class="icon" aria-hidden="true">
					<nb-icon status="primary" icon="arrow-ios-forward-outline"></nb-icon>
				</span>
				<span class="sr-only">Next</span>
			</a>
		</li>
		<li (click)="onChangePage(getPagesCount())">
			<a href="#" aria-label="Last">
				<span class="icon" aria-hidden="true">
					<nb-icon status="primary" icon="arrowhead-right-outline"></nb-icon>
				</span>
				<span class="sr-only">Last</span>
			</a>
		</li>
	</ul>
	<div class="d-flex justify-between align-items-center">
		<nb-select size="small" [(selected)]="itemsPerPage">
			<nb-option [value]="5">5</nb-option>
			<nb-option [value]="10">10</nb-option>
			<nb-option [value]="25">25</nb-option>
			<nb-option [value]="50">50</nb-option>
			<nb-option [value]="100">100</nb-option>
		</nb-select>
		<span>
			{{ getStartPagesCount() }} - {{ getEndPagesCount() }} of {{ totalItems }}
			{{ 'PAGINATION.ITEMS' | translate }}
		</span>
	</div>
</nav>
