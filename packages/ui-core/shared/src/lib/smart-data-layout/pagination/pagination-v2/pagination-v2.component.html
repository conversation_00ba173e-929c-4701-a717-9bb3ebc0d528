<nav *ngIf="isShouldShow" class="d-flex justify-between align-items-center w-100">
	<ul class="pagination mb-0">
		<li (click)="page === 1 ? false : paginate(1)">
			<a href="#" aria-label="First">
				<span class="icon" aria-hidden="true">
					<nb-icon status="primary" icon="arrowhead-left-outline"></nb-icon>
				</span>
				<span class="sr-only">First</span>
			</a>
		</li>
		<li (click)="page === 1 ? false : prev()" [ngClass]="{ disabled: page === 1 }">
			<a href="#" aria-label="Prev">
				<span class="icon" aria-hidden="true">
					<nb-icon status="primary" icon="arrow-ios-back-outline"></nb-icon>
				</span>
				<span class="sr-only">Prev</span>
			</a>
		</li>
		<li *ngFor="let sheet of pages" [ngClass]="{ active: sheet === page }">
			<span *ngIf="sheet === page">{{ sheet }} <span class="sr-only">(current)</span></span>
			<span (click)="paginate(sheet)" *ngIf="page != sheet">{{ sheet }}</span>
		</li>
		<li (click)="page === last ? false : next()" [ngClass]="{ disabled: page === last }">
			<a href="#" aria-label="Next">
				<span class="icon" aria-hidden="true">
					<nb-icon status="primary" icon="arrow-ios-forward-outline"></nb-icon>
				</span>
				<span class="sr-only">Next</span>
			</a>
		</li>
		<li (click)="page === last ? false : paginate(last)">
			<a href="#" aria-label="Last">
				<span class="icon" aria-hidden="true">
					<nb-icon status="primary" icon="arrowhead-right-outline"></nb-icon>
				</span>
				<span class="sr-only">Last</span>
			</a>
		</li>
	</ul>
	<div *ngIf="perPageSelect && perPageSelect.length > 0" class="d-flex justify-between align-items-center">
		<nb-select size="small" (selectedChange)="onChangePerPage($event)" [(selected)]="currentPerPage">
			<nb-option *ngFor="let item of perPageSelect" [value]="item">{{ item }}</nb-option>
		</nb-select>
		<span> {{ startCount }} - {{ endCount }} of {{ count }} {{ 'PAGINATION.ITEMS' | translate }} </span>
	</div>
</nav>
