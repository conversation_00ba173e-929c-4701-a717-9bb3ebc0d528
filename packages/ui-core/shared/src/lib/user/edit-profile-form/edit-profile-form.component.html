<nb-card class="card-scroll">
	<nb-card-body>
		<div class="content">
			<div class="employee-container">
				<div class="employee-photo">
					<ng-container *ngTemplateOutlet="imageUploaderTemplate"></ng-container>
				</div>
				<ng-template [ngIf]="role">
					<gauzy-role class="badge" [value]="role"></gauzy-role>
				</ng-template>
			</div>
			<div class="employee-form">
				<form [formGroup]="form" autocomplete-off>
					<div class="row">
						<div class="col">
							<div class="form-group">
								<label
									class="label"
									for="firstName"
									[innerText]="'PROFILE_PAGE.FIRST_NAME' | translate"
								></label>
								<input fullWidth id="firstName" type="text" nbInput formControlName="firstName" />
							</div>
						</div>
						<div class="col">
							<div class="form-group">
								<label
									for="lastName"
									class="label"
									[innerText]="'PROFILE_PAGE.LAST_NAME' | translate"
								></label>
								<input fullWidth id="lastName" type="text" nbInput formControlName="lastName" />
							</div>
						</div>
					</div>
					<div class="row">
						<div class="col">
							<ngx-password-form-field
								id="'password'"
								[placeholder]="'PROFILE_PAGE.PASSWORD' | translate"
								[label]="'PROFILE_PAGE.PASSWORD' | translate"
								[ctrl]="form.get('password')"
								formControlName="password"
								[autocomplete]="'new-password'"
							></ngx-password-form-field>
						</div>
						<div class="col">
							<ngx-password-form-field
								[id]="'reset-password'"
								[placeholder]="'PROFILE_PAGE.REPEAT_PASSWORD' | translate"
								[label]="'PROFILE_PAGE.REPEAT_PASSWORD' | translate"
								[ctrl]="form.get('repeatPassword')"
								formControlName="repeatPassword"
								[autocomplete]="'new-password'"
							>
								<div
									*ngIf="FormHelpers.isInvalidControl(form, 'repeatPassword')"
									class="invalid-feedback d-block"
								>
									<div *ngIf="form.get('repeatPassword').errors.mustMatch">
										{{ 'PROFILE_PAGE.VALIDATION.PASSWORDS_DO_NOT_MATCH' | translate }}
									</div>
								</div>
							</ngx-password-form-field>
						</div>
					</div>
					<div class="row">
						<div class="col-6">
							<div class="form-group">
								<label class="label" for="email" [innerText]="'PROFILE_PAGE.EMAIL' | translate"></label>
								<input
									fullWidth
									id="email"
									type="email"
									nbInput
									formControlName="email"
									[status]="FormHelpers.isInvalidControl(form, 'email') ? 'danger' : 'basic'"
								/>
								<div
									*ngIf="FormHelpers.isInvalidControl(form, 'email')"
									class="invalid-feedback d-block"
								>
									<div *ngIf="form.get('email').errors.required">
										{{ 'PROFILE_PAGE.VALIDATION.EMAIL_REQUIRED' | translate }}
									</div>
								</div>
							</div>
						</div>
						<div class="col-6">
							<div class="form-group">
								<ga-tags-color-input
									[selectedTags]="form.get('tags').value"
									(selectedTagsEvent)="selectedTagsHandler($event)"
									[isTenantLevel]="true"
								></ga-tags-color-input>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="col-sm-6" *ngIf="allowRoleChange">
							<ngx-role-form-field
								[id]="'role'"
								[placeholder]="'FORM.PLACEHOLDERS.ROLE' | translate"
								[label]="'FORM.LABELS.ROLE' | translate"
								formControlName="role"
								(selectedChange)="onSelectionChange($event)"
								[excludes]="excludes"
							></ngx-role-form-field>
						</div>
						<div class="col-sm-6">
							<div class="form-group">
								<label
									for="preferredLanguage"
									class="label"
									[innerText]="'FORM.LABELS.PREFERRED_LANGUAGE' | translate"
								></label>
								<ngx-language-selector
									[placeholder]="'FORM.PLACEHOLDERS.PREFERRED_LANGUAGE' | translate"
									class="d-block"
									[template]="'ng-select'"
									formControlName="preferredLanguage"
								></ngx-language-selector>
							</div>
						</div>
						<div class="col-sm-6">
							<ga-timezone-selector formControlName="timeZone"></ga-timezone-selector>
						</div>
						<div class="col-sm-6">
							<ngx-phone-form-input formControlName="phoneNumber"></ngx-phone-form-input>
						</div>
						<div class="col-6">
							<div class="form-group">
								<label class="label" for="timeZone">
									{{ 'FORM.LABELS.TIME_FORMAT' | translate }}
								</label>
								<ng-select
									id="timeFormat"
									appendTo="body"
									[(items)]="listOfTimeFormats"
									[placeholder]="'FORM.PLACEHOLDERS.TIME_FORMAT' | translate"
									[searchable]="false"
									[clearable]="false"
									formControlName="timeFormat"
								></ng-select>
							</div>
						</div>
					</div>
					<div class="actions">
						<button [disabled]="form.invalid" (click)="submitForm()" nbButton status="success">
							{{ 'PROFILE_PAGE.SAVE' | translate }}
						</button>
					</div>
				</form>
			</div>
		</div>
	</nb-card-body>
</nb-card>

<ng-template #imageUploaderTemplate>
	<ngx-image-uploader
		(changeHoverState)="hoverState = $event"
		(uploadedImageAsset)="updateImageAsset($event)"
		(uploadImageAssetError)="handleImageUploadError($event)"
	></ngx-image-uploader>
	<svg
		xmlns="http://www.w3.org/2000/svg"
		xmlns:xlink="http://www.w3.org/1999/xlink"
		width="68"
		height="68"
		viewBox="0 0 68 68"
		[ngStyle]="{ opacity: hoverState ? '1' : '0.3' }"
	>
		<defs>
			<path
				id="a"
				d="M28.667 31.333a2 2 0 1 0-.002-4.001 2 2 0 0 0 .002 4.001m13.333 12H26.748l9.34-7.793c.328-.279.923-.277 1.244-.001l6.001 5.12V42c0 .736-.597 1.333-1.333 1.333M26 24.667h16c.736 0 1.333.597 1.333 1.333v11.152l-4.27-3.643c-1.32-1.122-3.386-1.122-4.694-.008l-9.702 8.096V26c0-.736.597-1.333 1.333-1.333M42 22H26c-2.205 0-4 1.795-4 4v16c0 2.205 1.795 4 4 4h16c2.205 0 4-1.795 4-4V26c0-2.205-1.795-4-4-4"
			/>
		</defs>
		<g fill="none" fill-rule="evenodd">
			<circle cx="34" cy="34" r="34" fill="#0091FF" opacity=".3" />
			<circle cx="34" cy="34" r="26" fill="#0091FF" opacity=".9" />
			<use fill="#FFF" fill-rule="nonzero" xlink:href="#a" />
		</g>
	</svg>
	<div class="image-overlay" [ngStyle]="{ opacity: hoverState ? '0.2' : '0' }"></div>
	<img
		*ngIf="!!form"
		[src]="form.get('imageUrl').value"
		alt="Profile Photo"
		(mouseenter)="hoverState = true"
		(mouseleave)="hoverState = false"
	/>
</ng-template>
