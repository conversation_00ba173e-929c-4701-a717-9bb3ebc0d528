<form [formGroup]="form" autocomplete-off no-validate>
	<div class="row">
		<div class="col-3">
			<div class="form-group row" [hidden]="form.get('imageUrl').invalid">
				<div class="col-sm-12">
					<div class="row preview-img" [hidden]="!showImageMeta">
						<div class="image">
							<img
								#imagePreview
								[src]="form.get('imageUrl').value"
								alt="Invalid image"
								class="img-rounded"
							/>
							<button class="trash-icon" nbButton status="basic" size="small" (click)="deleteImageUrl()">
								<nb-icon
									status="danger"
									[title]="'FORM.PLACEHOLDERS.REMOVE_IMAGE' | translate"
									icon="trash-2-outline"
									[title]="'FORM.PLACEHOLDERS.REMOVE_IMAGE' | translate"
								></nb-icon>
							</button>
						</div>
					</div>
					<div class="row preview-img" [hidden]="showImageMeta">
						<div class="image">
							<i class="far fa-image"></i>
							<button class="trash-icon" nbButton status="basic" size="small">
								<nb-icon
									status="danger"
									[title]="'FORM.PLACEHOLDERS.REMOVE_IMAGE' | translate"
									icon="trash-2-outline"
								></nb-icon>
							</button>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="col-9">
			<div class="row">
				<div class="col-sm-12">
					<div class="form-group">
						<label for="inputImageUrl" class="label">
							{{ 'FORM.LABELS.IMAGE_URL' | translate }}
						</label>
						<ngx-file-uploader-input
							id="inputImageUrl"
							[placeholder]="'FORM.PLACEHOLDERS.UPLOADER_PLACEHOLDER' | translate"
							[fileUrl]="form.get('imageUrl').value"
							(uploadedImageAsset)="updateImageAsset($event)"
							(uploadedImgUrl)="updateImageUrl($event)"
						></ngx-file-uploader-input>
					</div>
					<ng-container *ngIf="form.get('imageUrl')?.value !== null && FormHelpers.isInvalidControl(form, 'imageUrl')"
					>
						<p class="caption status-danger mt-1">
							{{ 'FORM.ERROR.INVALID_IMAGE_URL' | translate }}
						</p>
					</ng-container>

				</div>
			</div>
		</div>
	</div>
	<div class="row">
		<div class="col">
			<div class="form-group">
				<label for="firstName" class="label">
					{{ 'FORM.LABELS.FIRST_NAME' | translate }}
				</label>
				<input
					nbInput
					type="text"
					id="firstName"
					fullWidth
					[placeholder]="'FORM.PLACEHOLDERS.FIRST_NAME' | translate"
					formControlName="firstName"
					[status]="FormHelpers.isInvalidControl(form, 'firstName') ? 'danger' : 'basic'"
				/>
				<ng-container *ngIf="FormHelpers.isInvalidControl(form, 'firstName')">
					<p class="caption status-danger mt-1">
						{{ 'TOASTR.MESSAGE.NAME_REQUIRED' | translate }}
					</p>
				</ng-container>
			</div>
		</div>
		<div class="col">
			<div class="form-group">
				<label for="lastName" class="label">{{ 'FORM.LABELS.LAST_NAME' | translate }}</label>
				<input
					nbInput
					type="text"
					id="lastName"
					fullWidth
					[placeholder]="'FORM.PLACEHOLDERS.LAST_NAME' | translate"
					formControlName="lastName"
					[status]="FormHelpers.isInvalidControl(form, 'lastName') ? 'danger' : 'basic'"
				/>
			</div>
		</div>
	</div>
	<div class="row">
		<div class="col-4">
			<div class="form-group">
				<label for="username" class="label">{{ 'FORM.USERNAME' | translate }}</label>
				<input
					nbInput
					type="text"
					id="username"
					fullWidth
					[placeholder]="'FORM.USERNAME' | translate"
					formControlName="username"
					[status]="FormHelpers.isInvalidControl(form, 'username') ? 'danger' : 'basic'"
				/>
			</div>
			<ng-container *ngIf="FormHelpers.isInvalidControl(form, 'username')">
				<p class="caption status-danger mt-1">
					{{ 'TOASTR.MESSAGE.NAME_REQUIRED' | translate }}
				</p>
			</ng-container>
		</div>
		<div class="col-8">
			<div class="form-group">
				<label for="email" class="label">{{ 'FORM.EMAIL' | translate }}</label>
				<input
					autofocus
					pattern=".+@.+\..+"
					type="email"
					#email
					nbInput
					type="email"
					id="email"
					fullWidth
					[placeholder]="'FORM.EMAIL' | translate"
					formControlName="email"
					[status]="FormHelpers.isInvalidControl(form, 'email') ? 'danger' : 'basic'"
				/>
				<ng-container *ngIf="FormHelpers.isInvalidControl(form, 'email')">
					<p class="caption status-danger mt-1" *ngIf="form.controls.email.errors?.pattern">
						{{ 'TOASTR.MESSAGE.EMAIL_SHOULD_BE_REAL' | translate }}
					</p>
				</ng-container>
			</div>
		</div>
	</div>
	<div class="row">
		<div class="col-8">
			<ngx-password-form-field
				id="password"
				[placeholder]="'FORM.PASSWORD' | translate"
				[label]="'FORM.PASSWORD' | translate"
				[ctrl]="form.controls.password"
				formControlName="password"
			></ngx-password-form-field>
			<ng-container *ngIf="FormHelpers.isInvalidControl(form, 'password')">
				<p class="caption status-danger mt-1">
					{{ 'TOASTR.MESSAGE.PASSWORD_REQUIRED' | translate }}
				</p>
			</ng-container>
		</div>
	</div>
	<div class="row">
		<ng-container *ngIf="isShowRole">
			<div class="col">
				<ngx-role-form-field
					id="role"
					formControlName="role"
					[placeholder]="'FORM.PLACEHOLDERS.ROLE' | translate"
					[label]="'FORM.LABELS.ROLE' | translate"
					[excludes]="excludes"
					(selectedChange)="onSelectionChange($event)"
				></ngx-role-form-field>
			</div>
		</ng-container>
		<div class="col" *ngIf="isEmployee">
			<div class="form-group">
				<label for="startedWork" class="label">{{ 'FORM.LABELS.START_DATE' | translate }}</label>
				<input
					[nbDatepicker]="startWorkOnDatepicker"
					nbInput
					fullWidth
					[placeholder]="'FORM.PLACEHOLDERS.START_DATE' | translate"
					formControlName="startedWorkOn"
				/>
				<nb-datepicker #startWorkOnDatepicker></nb-datepicker>
				<div class="notes" *ngIf="form.get('startedWorkOn').touched && form.get('startedWorkOn').value == null">
					<p>
						{{ 'FORM.NOTIFICATIONS.STARTED_WORK_ON' | translate }}
					</p>
				</div>
			</div>
		</div>
		<div class="col" *ngIf="isCandidate">
			<div class="form-group">
				<label for="appliedDate" class="label">{{ 'FORM.LABELS.APPLIED_DATE' | translate }}</label>
				<input
					fullWidth
					id="appliedDate"
					formControlName="appliedDate"
					nbInput
					[nbDatepicker]="appliedDatePicker"
					[placeholder]="'POP_UPS.PICK_DATE' | translate"
				/>
				<nb-datepicker #appliedDatePicker></nb-datepicker>
			</div>
		</div>
		<div class="col" *ngIf="isEmployee || isCandidate">
			<div class="form-group">
				<label for="rejectDate" class="label">{{ 'FORM.LABELS.REJECT_DATE' | translate }}</label>
				<input
					fullWidth
					id="rejectDate"
					formControlName="rejectDate"
					nbInput
					[nbDatepicker]="rejectDatePicker"
					[placeholder]="'POP_UPS.PICK_DATE' | translate"
					[status]="FormHelpers.isInvalidControl(form, 'rejectDate') ? 'danger' : 'basic'"
				/>
				<nb-datepicker #rejectDatePicker></nb-datepicker>
			</div>
		</div>
	</div>
	<div class="row" *ngIf="isEmployee">
		<div class="col">
			<div class="form-group">
				<label for="offerDate" class="label">{{ 'FORM.LABELS.OFFER_DATE' | translate }}</label>
				<input
					fullWidth
					id="offerDate"
					formControlName="offerDate"
					nbInput
					[nbDatepicker]="offerDatePicker"
					[placeholder]="'POP_UPS.PICK_DATE' | translate"
				/>
				<nb-datepicker #offerDatePicker></nb-datepicker>
			</div>
		</div>
		<div class="col">
			<div class="form-group">
				<label for="acceptDate" class="label">{{ 'FORM.LABELS.ACCEPT_DATE' | translate }}</label>
				<input
					fullWidth
					id="acceptDate"
					formControlName="acceptDate"
					nbInput
					[nbDatepicker]="acceptDatePicker"
					[placeholder]="'POP_UPS.PICK_DATE' | translate"
					[status]="FormHelpers.isInvalidControl(form, 'acceptDate') ? 'danger' : 'basic'"
				/>
				<nb-datepicker #acceptDatePicker></nb-datepicker>
			</div>
		</div>
	</div>
	<div class="row">
		<div class="col">
			<div class="form-group">
				<ga-tags-color-input
					[selectedTags]="form.get('tags').value"
					(selectedTagsEvent)="selectedTagsHandler($event)"
					[isOrgLevel]="true"
				>
				</ga-tags-color-input>
			</div>
		</div>
	</div>
	<div class="row" *ngIf="isCandidate">
		<div class="col">
			<div class="form-group">
				<label for="source" class="label">{{ 'FORM.LABELS.SOURCE' | translate }}</label>
				<input
					fullWidth
					id="source"
					formControlName="source"
					nbInput
					[placeholder]="'POP_UPS.SOURCE' | translate"
				/>
			</div>
		</div>
	</div>
	<ng-container *ngIf="isShowRole">
		<div class="row" *ngIf="enableEmployee()">
			<div class="col-sm-12">
				<div class="form-group">
					<nb-checkbox formControlName="featureAsEmployee">
						{{ 'FORM.LABELS.ENABLE_EMPLOYEE_FEATURES' | translate }}
					</nb-checkbox>
				</div>
			</div>
		</div>
	</ng-container>
</form>
