@use 'themes' as *;

.preview-img {
  padding-left: 14px;
  padding-right: 16px;
}

.remove-icon {
  div {
    cursor: pointer;
  }

  padding-left: 7px;
  padding-right: 7px;
  padding-top: 2px;
}
.notes {
  text-indent: 1em;
  max-width: 360px;
  p {
    margin: 0;
    color: #eac72d;
    font-size: 0.75rem;
    font-weight: 300;
    line-height: initial;
  }
}

.image {
  position: relative;
  height: 98px;
  width: 130px;
  border-radius: nb-theme(border-radius) !important;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(126, 126, 143, 0.1);
  .img-rounded {
    object-fit: cover;
    max-height: 98px;
    max-width: 130px;
    border-radius: nb-theme(border-radius) !important;
  }
  .trash-icon {
    position: absolute;
    top: 10px;
    right: 10px;
  }
}
