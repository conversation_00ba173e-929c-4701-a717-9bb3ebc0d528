<div class="form-group">
	<label class="label" [attr.for]="id" [innerText]="label"></label>
	<nb-form-field>
		<input
			#input
			fullWidth
			nbInput
			[fieldSize]="fieldSize"
			[formControl]="ctrl"
			(blur)="onInputChange($event, input.value)"
			[status]="ctrl.invalid && (ctrl.touched || ctrl.dirty) ? 'danger' : 'basic'"
			[attr.type]="showPassword ? 'text' : 'password'"
			[attr.placeholder]="placeholder"
			[attr.id]="id"
			[ngClass]="ngClass"
			[autocomplete]="autocomplete"
		/>
		<button *ngIf="icon" nbSuffix nbButton ghost type="button" (click)="showPassword = !showPassword">
			<nb-icon
				[icon]="showPassword ? 'eye-outline' : 'eye-off-outline'"
				pack="eva"
				[attr.aria-label]="showPassword ? 'hide password' : 'show password'"
			>
			</nb-icon>
		</button>
	</nb-form-field>
	<ng-content select=".invalid-feedback"></ng-content>
</div>
