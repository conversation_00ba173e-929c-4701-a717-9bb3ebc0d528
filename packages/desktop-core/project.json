{"name": "desktop-core", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/desktop-core/src", "projectType": "library", "release": {"version": {"generatorOptions": {"packageRoot": "dist/{projectRoot}", "currentVersionResolver": "git-tag"}}}, "tags": [], "implicitDependencies": [], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/desktop-core", "main": "packages/desktop-core/src/index.ts", "tsConfig": "packages/desktop-core/tsconfig.lib.json", "assets": ["packages/desktop-core/*.md"]}}, "nx-release-publish": {"options": {"packageRoot": "dist/{projectRoot}"}}, "lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/desktop-core/jest.config.ts"}}}}