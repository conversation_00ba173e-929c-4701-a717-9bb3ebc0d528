# @gauzy/desktop-core

This library was generated with [Nx](https://nx.dev).

## Building

Run `yarn nx build desktop-core` to build the library.

## Running unit tests

Run `yarn nx test desktop-core` to execute the unit tests via [Jest](https://jestjs.io).

## Publishing

After building your library with `yarn nx build desktop-core`, go to the dist folder `dist/packages/desktop-core` and run `npm publish`.

## Installation

Install the Desktop Libs Library using your preferred package manager:

```bash
npm install @gauzy/desktop-core
# or
yarn add @gauzy/desktop-core
```
