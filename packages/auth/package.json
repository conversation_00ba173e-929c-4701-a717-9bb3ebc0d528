{"name": "@gauzy/auth", "version": "0.1.0", "description": "Authentication module for the Ever Gauzy Platform, providing various authentication strategies and integrations.", "author": {"name": "Ever Co. LTD", "email": "<EMAIL>", "url": "https://ever.co"}, "repository": {"type": "git", "url": "https://github.com/ever-co/ever-gauzy", "directory": "packages/auth"}, "bugs": {"url": "https://github.com/ever-co/ever-gauzy/issues"}, "homepage": "https://ever.co", "license": "AGPL-3.0", "private": true, "type": "commonjs", "main": "./src/index.js", "typings": "./src/index.d.ts", "scripts": {"lib:build": "yarn nx build auth", "lib:build:prod": "yarn nx build auth", "lib:watch": "yarn nx build auth --watch"}, "dependencies": {"@gauzy/config": "^0.1.0", "@gauzy/contracts": "^0.1.0", "@gauzy/utils": "^0.1.0", "@nestjs/axios": "github:ever-co/nestjs-axios#master", "@nestjs/common": "^11.1.0", "@nestjs/config": "^4.0.2", "@nestjs/passport": "^11.0.5", "axios": "^1.9.0", "bcrypt": "^5.1.1", "express": "^5.1.0", "passport": "^0.7.0", "passport-auth0": "^1.3.3", "passport-facebook": "^3.0.0", "passport-github2": "^0.1.12", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "passport-keycloak-oauth2-oidc": "^1.0.5", "passport-linkedin-oauth2": "^2.0.0", "passport-microsoft": "^2.1.0", "passport-oauth2": "^1.8.0", "passport-twitter": "^1.0.4", "rxjs": "^7.8.2", "tslib": "^2.6.2"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/jest": "29.5.14", "@types/node": "^20.14.9", "@types/passport": "^1.0.17", "@types/passport-facebook": "^3.0.3", "@types/passport-github2": "^1.2.9", "@types/passport-google-oauth20": "^2.0.16", "@types/passport-jwt": "^4.0.1", "typescript": "^5.8.3"}, "keywords": ["auth", "authentication", "authorization", "gauzy", "<PERSON><PERSON><PERSON>", "o<PERSON>h", "jwt", "passport"], "engines": {"node": ">=20.18.1", "yarn": ">=1.22.19"}, "sideEffects": false}