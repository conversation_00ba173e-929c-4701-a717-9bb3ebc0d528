# @gauzy/auth

This library was generated with [Nx](https://nx.dev). It contains the authentication for the Gauzy API platform.

## Overview

This library provides a set of services and utilities for authentication and authorization in the Gauzy API platform.

## Building

Run `nx build auth` to build the library.

## Running unit tests

Run `nx test auth` to execute the unit tests via [Jest](https://jestjs.io).

## Publishing

After building your library with `yarn nx build auth`, go to the dist folder `dist/packages/auth` and run `npm publish`.

## Installation

To install the API auth Library, simply run the following command in your terminal:

```bash
npm install @gauzy/auth
# or
yarn add @gauzy/auth
```
