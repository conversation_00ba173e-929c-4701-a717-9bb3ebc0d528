{"name": "auth", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/auth/src", "projectType": "library", "release": {"version": {"generatorOptions": {"packageRoot": "dist/{projectRoot}", "currentVersionResolver": "git-tag"}}}, "tags": [], "implicitDependencies": [], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/auth", "tsConfig": "packages/auth/tsconfig.lib.json", "packageJson": "packages/auth/package.json", "main": "packages/auth/src/index.ts", "assets": ["packages/auth/*.md"]}}, "nx-release-publish": {"options": {"packageRoot": "dist/{projectRoot}"}}, "lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/auth/jest.config.ts"}}}}