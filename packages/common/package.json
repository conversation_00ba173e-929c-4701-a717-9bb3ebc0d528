{"name": "@gauzy/common", "version": "0.1.0", "description": "Ever Gauzy Platform Common module", "author": {"name": "Ever Co. LTD", "email": "<EMAIL>", "url": "https://ever.co"}, "repository": {"type": "git", "url": "https://github.com/ever-co/ever-gauzy", "directory": "packages/common"}, "bugs": {"url": "https://github.com/ever-co/ever-gauzy/issues"}, "homepage": "https://ever.co", "license": "AGPL-3.0", "private": true, "type": "commonjs", "main": "./src/index.js", "typings": "./src/index.d.ts", "scripts": {"lib:build": "yarn nx build common", "lib:build:prod": "yarn nx build common", "lib:watch": "yarn nx build common --watch"}, "dependencies": {"@apollo/server": "^4.11.3", "@gauzy/constants": "^0.1.0", "@gauzy/contracts": "^0.1.0", "@mikro-orm/nestjs": "^6.1.1", "@nestjs/common": "^11.1.0", "@nestjs/core": "^11.1.0", "@nestjs/typeorm": "^11.0.0", "graphql": "^16.11.0", "nest-knexjs": "^0.0.26", "slugify": "^1.6.6", "typeorm": "^0.3.24", "tslib": "^2.6.2"}, "devDependencies": {"@types/jest": "29.5.14", "@types/node": "^20.14.9", "typescript": "^5.8.3"}, "keywords": ["<PERSON><PERSON><PERSON>", "common-module", "shared-module", "ever-gauzy", "platform", "typescript", "graphql", "nestjs-common", "nestjs-core", "mikro-orm", "typeorm", "apollo-server", "slugify", "open-source", "enterprise", "agpl"], "engines": {"node": ">=20.18.1", "yarn": ">=1.22.19"}, "sideEffects": false}