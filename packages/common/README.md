# @gauzy/common

This library was generated with [Nx](https://nx.dev). It contains the common code used internally in different Gauzy API packages.

## Building

Run `yarn nx build common` to build the library.

## Running unit tests

Run `yarn nx test common` to execute the unit tests via [Jest](https://jestjs.io).

## Publishing

After building your library with `yarn nx build common`, go to the dist folder `dist/packages/common` and run `npm publish`.

## Installation

Install the Plugin Module using your preferred package manager:

```bash
npm install @gauzy/common
# or
yarn add @gauzy/common
```
