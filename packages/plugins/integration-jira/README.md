# @gauzy/plugin-integration-jira

This library was generated with [Nx](https://nx.dev).

## Building

Run `yarn nx build plugin-integration-jira` to build the library.

## Running unit tests

Run `yarn nx test plugin-integration-jira` to execute the unit tests via [Jest](https://jestjs.io).

## Publishing

After building your library with `yarn nx build plugin-integration-jira`, go to the dist folder `dist/packages/plugins/integration-jira` and run `npm publish`.

## Installation

Install the Integration Jira Plugin using your preferred package manager:

```bash
npm install @gauzy/plugin-integration-jira
# or
yarn add @gauzy/plugin-integration-jira
```
