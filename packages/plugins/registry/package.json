{"name": "@gauzy/plugin-registry", "version": "0.1.0", "description": "This plugin provides plugins registry functionality", "author": {"name": "Ever Co. LTD", "email": "<EMAIL>", "url": "https://ever.co"}, "repository": {"type": "git", "url": "https://github.com/ever-co/ever-gauzy", "directory": "packages/plugins/videos"}, "bugs": {"url": "https://github.com/ever-co/ever-gauzy/issues"}, "homepage": "https://ever.co", "license": "AGPL-3.0", "private": true, "type": "commonjs", "main": "./src/index.js", "typings": "./src/index.d.ts", "scripts": {"lib:build": "yarn nx build plugin-registry", "lib:build:prod": "yarn nx build plugin-registry", "lib:watch": "yarn nx build plugin-registry --watch"}, "dependencies": {"tslib": "^2.6.2"}, "devDependencies": {"typescript": "^5.8.3"}, "engines": {"node": ">=20.18.1", "yarn": ">=1.22.19"}, "sideEffects": false}