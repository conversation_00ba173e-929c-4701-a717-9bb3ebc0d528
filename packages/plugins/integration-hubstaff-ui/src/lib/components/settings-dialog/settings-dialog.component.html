<nb-card>
	<nb-card-header>
		{{ 'INTEGRATIONS.SETTINGS' | translate }}
	</nb-card-header>
	<nb-card-body>
		<div class="switcher-wrapper" *ngIf="entitiesToSync$ | async as entitiesToSync">
			<ng-container *ngFor="let entity of entitiesToSync.currentValue">
				<nb-toggle [(checked)]="entity.sync" labelPosition="start" status="basic">
					{{ entity.entity }}
				</nb-toggle>
				<div class="tied-entities-wrapper">
					<ng-container *ngFor="let tiedEntity of entity.tiedEntities">
						<div class="tied-entity">
							<div
								*ngIf="tiedEntity.entity === IntegrationEntity.ACTIVITY; else notActivityTemp"
								class="entity-more-options"
							>
								<nb-icon
									[icon]="expandOptions ? 'chevron-down-outline' : 'chevron-right-outline'"
									(click)="expandOptions = !expandOptions"
								></nb-icon>
								<label class="label">
									{{ tiedEntity.entity }}
								</label>
								<nb-icon
									icon="info-outline"
									[nbTooltip]="'INTEGRATIONS.HUBSTAFF_PAGE.TOOLTIP_ACTIVITY_INFO' | translate"
								></nb-icon>
							</div>
							<ng-template #notActivityTemp>
								<label class="label">
									{{ tiedEntity.entity }}
								</label>
							</ng-template>
							<nb-toggle
								[(checked)]="tiedEntity.sync"
								[disabled]="!entity.sync"
								labelPosition="start"
								status="basic"
							></nb-toggle>
						</div>
						<div [ngClass]="expandOptions && tiedEntity.entity === IntegrationEntity.ACTIVITY ? 'visible' : 'not-visible'">
							<input
								nbInput
								[placeholder]="'INTEGRATIONS.HUBSTAFF_PAGE.DATE_RANGE_PLACEHOLDER' | translate"
								[nbDatepicker]="rangePicker"
								[value]="displayDate"
							/>
							<nb-rangepicker
								#rangePicker
								[range]="defaultRange$ | async"
								[max]="maxDate"
								[min]="minDate"
								(rangeChange)="onDateChange($event)"
							></nb-rangepicker>
						</div>
					</ng-container>
				</div>
			</ng-container>
		</div>
	</nb-card-body>
	<nb-card-footer>
		<div class="dialog-footer">
			<button nbButton (click)="closeDialog(true)" status="primary" class="mr-2">
				{{ 'BUTTONS.SAVE' | translate }}
			</button>
		</div>
	</nb-card-footer>
</nb-card>
