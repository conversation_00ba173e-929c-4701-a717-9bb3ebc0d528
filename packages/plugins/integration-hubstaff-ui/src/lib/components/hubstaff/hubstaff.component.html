<nb-card [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large" class="card-scroll">
	<nb-card-header class="card-header-title">
		<div class="card-header-title">
			<h5>
				<ngx-back-navigation
					class="float-left"
					[haveLink]="true"
					(click)="navigateToIntegrations()"
				></ngx-back-navigation>
				{{ 'INTEGRATIONS.HUBSTAFF_PAGE.TITLE' | translate }}
			</h5>
		</div>
		<div>
			<div>
				<ng-template [ngxPermissionsOnly]="['INTEGRATION_EDIT']">
					<button nbButton status="primary" class="mr-2" size="small" (click)="navigateToResetIntegration()">
						<div class="sync-container">
							<nb-icon class="sync" icon="sync-outline"></nb-icon>
							{{ 'BUTTONS.RESET_INTEGRATION' | translate }}
						</div>
					</button>
				</ng-template>
				<button nbButton size="small" (click)="openSettingModal()">
					<nb-icon icon="settings-2-outline"></nb-icon>
				</button>
			</div>
		</div>
	</nb-card-header>
	<nb-card-body>
		<div class="mb-3">
			<button
				nbButton
				[disabled]="selectedProjects.length === 0"
				(click)="syncProjects()"
				status="primary"
				class="mr-2"
			>
				<nb-icon class="mr-1" icon="edit-outline"></nb-icon>
				{{ 'BUTTONS.SYNC' | translate }}
			</button>
			<button nbButton (click)="autoSync()" status="primary" class="mr-2">
				<nb-icon class="mr-1" icon="edit-outline"></nb-icon>
				{{ 'BUTTONS.AUTO_SYNC' | translate }}
			</button>
		</div>
		<div class="hubstaff-container">
			<ng-select
				class="mb-2"
				bindLabel="name"
				[items]="organizations$ | async"
				[searchable]="true"
				[placeholder]="'INTEGRATIONS.HUBSTAFF_PAGE.SELECT_ORGANIZATION' | translate"
				(change)="selectOrganization($event)"
				appendTo="body"
			></ng-select>
			<angular2-smart-table
				[settings]="settingsSmartTable"
				[source]="projects$ | async"
				(userRowSelect)="selectProject($event)"
				style="cursor: pointer"
				#projectsTable
			></angular2-smart-table>
		</div>
	</nb-card-body>
</nb-card>
