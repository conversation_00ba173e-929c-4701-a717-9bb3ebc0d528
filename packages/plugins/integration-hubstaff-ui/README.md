# @gauzy/plugin-integration-hubstaff-ui

This library was generated with [Nx](https://nx.dev).

## Building

Run `yarn nx build plugin-integration-hubstaff-ui` to build the library.

## Running unit tests

Run `yarn nx test plugin-integration-hubstaff-ui` to execute the unit tests.

## Publishing

After building your library with `yarn nx build plugin-integration-hubstaff-ui`, go to the dist folder `dist/packages/plugins/integration-hubstaff-ui` and run `npm publish`.

## Installation

Install the Integration Hubstaff UI Plugin using your preferred package manager:

```bash
npm install @gauzy/plugin-integration-hubstaff-ui
# or
yarn add @gauzy/plugin-integration-hubstaff-ui
```
