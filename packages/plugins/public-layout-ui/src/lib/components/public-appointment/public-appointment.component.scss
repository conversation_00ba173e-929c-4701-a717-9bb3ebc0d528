.employee-info,
.org-info {
  display: flex;

  .employee-image,
  .org-image {
    max-width: 70px;
    max-height: 70px;
    border-radius: 13px;
    margin-right: 24px;
    margin-left: 24px;
  }

  .employee-details,
  .org-details {
    display: flex;
    flex-direction: column;
    justify-content: center;

    .employee-name,
    .org-name {
      font-weight: bold;
      font-size: 18px;
    }

    .employee-position,
    .org-position {
      font-size: 14px;
    }
  }
}

.transparent {
  opacity: 0.7;
}

.body-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  margin-top: 25px;
}

.block-content {
  display: flex;
  justify-content: space-between;
  width: 100%;

  .block-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 560px;
    padding-left: 30px;

    .row {
      width: 100%;
    }
  }
}

.calendar {
  margin-top: 20px;
}

.row {
  margin-bottom: 20px;
}
