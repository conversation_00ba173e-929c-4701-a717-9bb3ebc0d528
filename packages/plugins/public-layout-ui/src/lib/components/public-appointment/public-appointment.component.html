<nb-card [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
	<nb-card-header>
		<div class="main-header">
			<h4>{{ 'PUBLIC_APPOINTMENTS.BOOK_APPOINTMENTS' | translate }}</h4>
		</div>
	</nb-card-header>
	<nb-card-body *ngIf="organization$ | async as organization">
		<ng-container *ngIf="employee$ | async as employee">
			<div class="employee-info">
				<img class="employee-image" [src]="employee.user?.imageUrl" alt="Employee Avatar" />
				<div class="employee-details">
					<span class="employee-name">{{ employee?.fullName }}</span>
					<div class="employee-position">
						{{ employee.organizationPosition?.name }}
					</div>
					<div class="transparent">
						<strong>{{ employee.user?.email }}</strong>
					</div>
				</div>
			</div>
		</ng-container>
		<div class="body-header">
			<h5>
				{{
					(eventTypesExist
						? 'PUBLIC_APPOINTMENTS.SELECT_EVENT_TYPES'
						: 'PUBLIC_APPOINTMENTS.NO_ACTIVE_EVENT_TYPES'
					) | translate
				}}
			</h5>
		</div>
		<div *ngIf="eventTypesExist">
			<div class="row block-content" *ngFor="let eventType of eventTypes">
				<div class="col-3">
					<div class="block-info">
						<strong>{{ eventType.title }}</strong>
					</div>
				</div>
				<div class="col-3">
					{{ 'PUBLIC_APPOINTMENTS.DURATION' | translate }}
					<strong>{{ eventType.duration }} {{ eventType.durationUnit }}</strong>
				</div>
				<div class="col-3 text-right">
					<button (click)="selectEventType(eventType.id)" status="success" nbButton>
						{{ 'BUTTONS.SELECT' | translate }}
					</button>
				</div>
			</div>
		</div>
	</nb-card-body>
</nb-card>
