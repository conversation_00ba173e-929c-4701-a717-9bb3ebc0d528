<ng-container *ngIf="appointment$ | async as appointment">
	<nb-card [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
		<nb-card-header>
			<div class="main-header">
				<h4>
					{{
						(appointment.status
							? 'PUBLIC_APPOINTMENTS.APPOINTMENT_INFO'
							: 'PUBLIC_APPOINTMENTS.CONFIRM_APPOINTMENT'
						) | translate
					}}
					<nb-icon *ngIf="!appointment.status" icon="checkmark-circle-2-outline" class="icons"></nb-icon>
				</h4>
			</div>
		</nb-card-header>
		<nb-card-body>
			<nb-card class="p-3">
				<h6>
					{{ 'PUBLIC_APPOINTMENTS.DETAILS' | translate }}
					<nb-icon icon="calendar-outline" class="icons"></nb-icon>
				</h6>
				<div class="p-2">
					<strong>{{ 'PUBLIC_APPOINTMENTS.HOST' | translate }}: </strong
					><span>{{ appointment?.employee?.user?.name }} ({{ appointment?.employee?.user?.email }})</span>
				</div>
				<div class="p-2">
					<strong>{{ 'FORM.LABELS.MEETING_AGENDA' | translate }}: </strong
					><span>{{ appointment.agenda }}</span>
				</div>
				<div class="p-2" *ngIf="appointment.description">
					<strong>{{ 'FORM.LABELS.MEETING_DESCRIPTION' | translate }}: </strong>
					<span>{{ appointment.description }}</span>
				</div>
				<div class="p-2">
					<strong>{{ 'PUBLIC_APPOINTMENTS.PARTICIPANTS' | translate }}: </strong>
					<span>{{ appointment.emails }}</span>
				</div>
				<div class="p-2">
					<strong>{{ 'FORM.LABELS.MEETING_LOCATION' | translate }}: </strong>
					<span>{{ appointment.location || 'N/A' }}</span>
				</div>
				<div class="p-2">
					<strong>{{ 'FORM.LABELS.DURATION' | translate }}: </strong>
					<span>{{ formatDuration(appointment?.startDateTime, appointment?.endDateTime) }}</span>
				</div>
			</nb-card>
			<nb-card *ngIf="!appointment.status">
				<div class="p-3 caption-2">
					<span>{{ 'PUBLIC_APPOINTMENTS.EMAIL_SENT' | translate }} </span>
				</div>
			</nb-card>
			<nb-card *ngIf="rescheduleLink">
				<div class="p-3" style="overflow-wrap: break-word">
					<strong>{{ 'PUBLIC_APPOINTMENTS.RESCHEDULE' | translate }}: </strong>
					<a [href]="rescheduleLink" target="_blank" rel="noopener noreferrer">
						{{ 'PUBLIC_APPOINTMENTS.CLICK_HERE' | translate }}
					</a>
				</div>
			</nb-card>
			<nb-card *ngIf="appointment.status">
				<div class="p-3">
					<span style="color: red">{{ 'PUBLIC_APPOINTMENTS.EXPIRED_OR_CANCELLED' | translate }} </span>
				</div>
			</nb-card>
			<div *ngIf="!appointment.status" class="col-3" style="padding-left: 0%">
				<button (click)="cancelAppointment(appointment.id)" status="warning" nbButton>
					{{ 'PUBLIC_APPOINTMENTS.CANCEL' | translate }}
				</button>
			</div>
		</nb-card-body>
	</nb-card>
</ng-container>
