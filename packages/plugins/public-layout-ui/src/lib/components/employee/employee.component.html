<nb-card>
	<nb-card-body *ngIf="employee$ | async as employee" class="employee-card">
		<div class="employee-info-wrapper">
			<div class="employee-photo-wrapper">
				<ng-container *ngIf="hasEditPermission$ | async; else noPermissionTemp">
					<div class="employee-photo">
						<ngx-image-uploader
							(changeHoverState)="hoverState = $event"
							(uploadedImageAsset)="updateImageAsset($event)"
							(uploadImageAssetError)="handleImageUploadError($event)"
						></ngx-image-uploader>
						<svg
							xmlns="http://www.w3.org/2000/svg"
							xmlns:xlink="http://www.w3.org/1999/xlink"
							width="68"
							height="68"
							viewBox="0 0 68 68"
							[ngStyle]="{
								opacity: hoverState ? '1' : '0.3'
							}"
						>
							<defs>
								<path
									id="a"
									d="M28.667 31.333a2 2 0 1 0-.002-4.001 2 2 0 0 0 .002 4.001m13.333 12H26.748l9.34-7.793c.328-.279.923-.277 1.244-.001l6.001 5.12V42c0 .736-.597 1.333-1.333 1.333M26 24.667h16c.736 0 1.333.597 1.333 1.333v11.152l-4.27-3.643c-1.32-1.122-3.386-1.122-4.694-.008l-9.702 8.096V26c0-.736.597-1.333 1.333-1.333M42 22H26c-2.205 0-4 1.795-4 4v16c0 2.205 1.795 4 4 4h16c2.205 0 4-1.795 4-4V26c0-2.205-1.795-4-4-4"
								/>
							</defs>
							<g fill="none" fill-rule="evenodd">
								<circle cx="34" cy="34" r="34" fill="#0091FF" opacity=".3" />
								<circle cx="34" cy="34" r="26" fill="#0091FF" opacity=".9" />
								<use fill="#FFF" fill-rule="nonzero" xlink:href="#a" />
							</g>
						</svg>
						<div
							class="image-overlay"
							[ngStyle]="{
								opacity: hoverState ? '0.2' : '0'
							}"
						></div>
						<img
							*ngIf="imageUrl"
							[src]="imageUrl"
							[alt]="'PUBLIC_PAGE.COMPANY_PROFILE' | translate"
							(mouseenter)="hoverState = true"
							(mouseleave)="hoverState = false"
						/>
						<button
							*ngIf="imageUpdateButton"
							class="save-image"
							(click)="
								saveImage({
									userId: employee.user.id,
									imageUrl: imageUrl
								})
							"
							status="primary"
							nbButton
						>
							{{ 'BUTTONS.UPDATE' | translate }}
						</button>
					</div>
				</ng-container>
				<ng-template #noPermissionTemp>
					<img class="employee-image" [src]="employee.user.imageUrl" alt="User Avatar" />
				</ng-template>
				<div class="employee-info">
					<h4 class="employee-name">
						{{ employee.user.name }}
					</h4>
					<span class="employee-banner">
						<gauzy-email [rowData]="employee.user"></gauzy-email>
					</span>
					<span class="employee-banner">
						{{ employee.short_description }}
					</span>
				</div>
			</div>
			<div class="right-side">
				<div class="employee-org-info">
					<ng-container *ngIf="organization$ | async as organization">
						<div class="employee-profile-item">
							<div class="employee-profile-item-keys">
								<h5 class="employee-profile-item-title">{{ 'MENU.ORGANIZATION' | translate }}:</h5>
								<h5 class="employee-profile-item-title">{{ 'PUBLIC_PAGE.ACTIVE' | translate }}:</h5>
								<h5 *ngIf="employee.show_start_work_on" class="employee-profile-item-title">
									{{ 'PUBLIC_PAGE.STARTED_WORK_ON' | translate }}:
								</h5>
							</div>
							<div class="employee-profile-item-value">
								<span>
									{{ organization.name }}
								</span>
								<span>
									{{ employee.isActive ? 'Yes' : 'No' }}
								</span>
								<span *ngIf="employee.show_start_work_on">
									{{
										employee.startedWorkOn
											| dateFormat : organization.regionCode : organization.dateFormat
									}}
								</span>
							</div>
						</div>
					</ng-container>
				</div>
				<div (click)="openEditEmployeeDialog(employee)" *ngIf="hasEditPermission$ | async">
					<button status="basic" class="action primary" size="small" nbButton>
						<nb-icon icon="edit-outline"> </nb-icon>
						{{ 'BUTTONS.EDIT' | translate }}
					</button>
				</div>
			</div>
		</div>
		<nb-tabset class="o__p__s">
			<nb-tab [tabTitle]="'PUBLIC_PAGE.PROFILE' | translate" class="o__p__s__t">
				<ng-container
					*ngTemplateOutlet="
						profileTemplate;
						context: {
							employee: employee
						}
					"
				></ng-container>
			</nb-tab>
			<nb-tab [tabTitle]="'PUBLIC_PAGE.SKILLS' | translate" class="o__p__s__t">
				<ng-container
					*ngTemplateOutlet="
						skillTemplate;
						context: {
							skills: employee.skills
						}
					"
				></ng-container>
			</nb-tab>
			<nb-tab [tabTitle]="'PUBLIC_PAGE.PORTFOLIO' | translate" class="o__p__s__t">
				<ng-container *ngTemplateOutlet="portfolioTemplate"></ng-container>
			</nb-tab>
		</nb-tabset>
	</nb-card-body>
</nb-card>

<!-- Profile HTML Template -->
<ng-template let-employee="employee" #profileTemplate>
	<div class="tab-employee-info">
		<ng-template [ngIf]="employee.show_billrate">
			<div class="employee-profile-item">
				<h5 class="employee-profile-item-title">{{ 'PUBLIC_PAGE.RATE' | translate }}:</h5>
				<div class="bill-rate">
					{{ employee.billRateValue }}
					{{ employee.billRateCurrency }}/hr
				</div>
			</div>
		</ng-template>
		<ng-template [ngIf]="employee.show_payperiod">
			<div class="employee-profile-item">
				<h5 class="employee-profile-item-title">{{ 'PUBLIC_PAGE.PAY_PERIOD' | translate }}:</h5>
				<div class="pay-period">
					{{ employee.payPeriod | replace : '_' : ' ' | titlecase }}
				</div>
			</div>
		</ng-template>
		<ng-template [ngIf]="employee.short_description">
			<div class="employee-profile-item">
				<h5 class="employee-profile-item-title">{{ 'PUBLIC_PAGE.DESCRIPTION' | translate }}:</h5>
				<div class="org-banner" [textContent]="employee.short_description"></div>
			</div>
		</ng-template>
		<ng-template [ngIf]="employee.show_average_income">
			<div class="employee-profile-item">
				<h5 class="employee-profile-item-title">{{ 'PUBLIC_PAGE.AVERAGE_INCOME' | translate }}:</h5>
				<span>{{ employee.averageIncome }}</span>
			</div>
		</ng-template>
		<ng-template [ngIf]="employee.show_average_expenses">
			<div class="employee-profile-item">
				<h5 class="employee-profile-item-title">{{ 'PUBLIC_PAGE.AVERAGE_EXPENSES' | translate }}:</h5>
				<span>{{ employee.averageExpenses }}</span>
			</div>
		</ng-template>
		<ng-template [ngIf]="employee.show_average_bonus">
			<div class="employee-profile-item">
				<h5 class="employee-profile-item-title">{{ 'PUBLIC_PAGE.AVERAGE_BONUS' | translate }}:</h5>
				<span>{{ employee.averageBonus }}</span>
			</div>
		</ng-template>
	</div>
</ng-template>

<!-- Skill HTML Template -->
<ng-template let-skills="skills" #skillTemplate>
	<nb-tag-list>
		<nb-tag *ngFor="let skill of skills" [text]="skill.name" [removable]="false"></nb-tag>
	</nb-tag-list>
</ng-template>

<!-- Portfolio HTML Template -->
<ng-template #portfolioTemplate>
	<ga-wip></ga-wip>
</ng-template>
