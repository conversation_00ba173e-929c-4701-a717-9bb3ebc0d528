@use 'gauzy/_gauzy-table' as *;

.employee-info-wrapper,
.right-side {
  position: relative;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  flex-wrap: wrap;
}
.right-side {
  gap: 1rem;
  padding: 1rem;
  .employee-org-info {
    padding: 10px;
    background-color: var(--gauzy-card-2);
    border-radius: var(--border-radius);
    color: var(--gauzy-text-color-1);
    height: 100%;
    .employee-profile-item-title {
      color: var(--gauzy-text-color-2);
      font-size: 12px;
      font-weight: 600;
      line-height: 15px;
      letter-spacing: 0em;
    }
    .employee-profile-item-keys,
    .employee-profile-item-value {
      display: flex;
      flex-direction: column;
      height: 100%;
      gap: 6px;
    }
    .employee-profile-item-value {
      font-size: 14px;
      font-weight: 400;
      line-height: 17px;
      letter-spacing: 0em;
      gap: 4px;
    }
  }
}

.employee-photo-wrapper {
  display: flex;
  gap: 17px;
  align-items: center;
  padding: 1rem;
  .employee-info {
    .employee-name {
      font-size: 14px;
      font-weight: 600;
      line-height: 17px;
      letter-spacing: 0em;
      color: var(--gauzy-text-color-1);
    }
    color: var(--gauzy-text-color-2);
    font-size: 12px;
    font-weight: 400;
    line-height: 15px;
    letter-spacing: 0em;
    display: flex;
    flex-flow: column;
  }
}

img.employee-image {
  object-fit: cover;
  object-position: center;
  width: 72px;
  height: 72px;
  border-radius: var(--border-radius);
  box-shadow: var(--gauzy-shadow);
}

.employee-photo {
  position: relative;

  div {
    pointer-events: none;
    background: black;
    position: absolute;
    height: 100%;
    width: 100%;
    border-radius: var(--border-radius);
  }

  img {
    width: 72px;
    height: 72px;
    border-radius: var(--border-radius);
    box-shadow: var(--gauzy-shadow);
    object-fit: cover;
  }

  input {
    width: 100%;
    height: 100%;
    opacity: 0;
    position: absolute;
    z-index: 3;
    cursor: pointer;
  }

  svg {
    z-index: 2;
    transition: opacity 0.2s ease-in;
    opacity: 0.3;
    position: absolute;
    width: 48px;
    height: 48px;
    top: calc(50% - 48px / 2);
    left: calc(50% - 48px / 2);
    g circle {
      fill: var(--text-primary-color);
    }
  }
}

button.save-image {
  bottom: -24px;
  position: absolute;
  left: -4px;
  z-index: 9999;
  font-size: 12px;
  border: none;
  box-shadow: var(--gauzy-shadow);
}

.employee-profile-item-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 5px 0;
  color: #222b45;
}

.employee-profile-item {
  display: flex;
  align-items: flex-start;
}

.tag {
  border-radius: 25px;
  border: 1px solid #ced9ee;
  color: #3c495a;
  display: inline-block;
  padding: 6px 12px 5px;
  font-size: 12px;

  &:hover {
    cursor: pointer;
    color: #fff;
    border-color: #007bff;
    background-color: #007bff;
    -webkit-box-shadow: 0 2px 4px #82bfff;
    box-shadow: 0 2px 4px #82bfff;
  }
}

.tag.tag-sm {
  font-weight: normal;
  font-size: 12px;
  margin-bottom: 15px;
}

.o__p__s__t {
  background-color: var(--gauzy-card-2);
  border-radius: 0 var(--border-radius) var(--border-radius)
    var(--border-radius);
  overflow: auto;
  height: calc(100vh - 16rem);
}

nb-card {
  margin: 0;
  nb-card-body {
    background-color: var(--gauzy-card-2);
    border-radius: var(--border-radius);
    padding: 0;
  }
}

.skill-items {
  display: flex;
  list-style: none;
  padding: 0;
  > li {
    margin-right: 5px;
  }
}

.tab-employee-info {
  .employee-profile-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    .employee-profile-item-title {
      font-size: 12px;
      font-weight: 600;
      line-height: 15px;
      letter-spacing: 0em;
      width: 8rem;
      color: var(--gauzy-text-color-2);
    }
    .bill-rate {
      background-color: var(--color-primary-transparent-100);
      font-weight: 600;
      border-radius: 4px;
      padding: 4px 8px;
      color: var(--text-primary-color);
    }
    .pay-period {
      padding: 4px 8px;
      background-color: var(--color-success-transparent-300);
      font-weight: 600;
      border-radius: 4px;
      color: var(--color-success-600);
    }
    color: var(--gauzy-text-color-1);
    font-size: 14px;
    font-weight: 400;
    line-height: 17px;
    letter-spacing: 0em;
  }
}
