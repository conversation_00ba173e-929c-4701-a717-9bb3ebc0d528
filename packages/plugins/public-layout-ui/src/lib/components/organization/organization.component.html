<nb-card>
	<ng-container *ngIf="organization$ | async as organization">
		<nb-card-body>
			<div class="org-info">
				<div class="org-head">
					<div class="row">
						<div class="col-md-6 org-head-left-sec">
							<img
								class="org-image"
								*ngIf="!(hasEditPublicPage$ | async)"
								[src]="organization.imageUrl"
								alt="Organization Avatar"
							/>
							<div class="organization-photo" *ngIf="hasEditPublicPage$ | async">
								<ngx-image-uploader
									(changeHoverState)="hoverState = $event"
									(uploadedImageAsset)="updateImageUrl($event)"
									(uploadImageAssetError)="handleImageUploadError($event)"
								></ngx-image-uploader>
								<svg
									xmlns="http://www.w3.org/2000/svg"
									xmlns:xlink="http://www.w3.org/1999/xlink"
									width="24"
									height="24"
									viewBox="0 0 68 68"
									[ngStyle]="{
										opacity: hoverState ? '1' : '0.3'
									}"
								>
									<defs>
										<path
											id="a"
											d="M28.667 31.333a2 2 0 1 0-.002-4.001 2 2 0 0 0 .002 4.001m13.333 12H26.748l9.34-7.793c.328-.279.923-.277 1.244-.001l6.001 5.12V42c0 .736-.597 1.333-1.333 1.333M26 24.667h16c.736 0 1.333.597 1.333 1.333v11.152l-4.27-3.643c-1.32-1.122-3.386-1.122-4.694-.008l-9.702 8.096V26c0-.736.597-1.333 1.333-1.333M42 22H26c-2.205 0-4 1.795-4 4v16c0 2.205 1.795 4 4 4h16c2.205 0 4-1.795 4-4V26c0-2.205-1.795-4-4-4"
										/>
									</defs>
									<g fill="none" fill-rule="evenodd">
										<circle cx="34" cy="34" r="34" fill="#0091FF" opacity=".3" />
										<circle cx="34" cy="34" r="26" fill="#0091FF" opacity=".9" />
										<use fill="#FFF" fill-rule="nonzero" xlink:href="#a" />
									</g>
								</svg>
								<div
									class="image-overlay"
									[ngStyle]="{
										opacity: hoverState ? '0.2' : '0'
									}"
								></div>

								<img
									*ngIf="imageUrl"
									[src]="imageUrl"
									alt="{{ 'PUBLIC_PAGE.COMPANY_PROFILE' | translate }}"
									(mouseenter)="hoverState = true"
									(mouseleave)="hoverState = false"
								/>
								<button
									*ngIf="imageUpdateButton"
									class="save_image"
									(click)="saveImage({ imageUrl: imageUrl })"
									status="success"
									nbButton
								>
									{{ 'BUTTONS.UPDATE' | translate }}
								</button>
							</div>
							<h4 class="org-name" [textContent]="organization.name"></h4>
							<span class="org-banner" [textContent]="organization.banner"></span>
						</div>
						<div class="col-md-6 org-head-right-sec">
							<div class="org-head-details">
								<div class="org-year-found">
									<h5 *ngIf="organization.registrationDate" class="org-title label-text">
										<span class="org-key"> {{ 'PUBLIC_PAGE.YEAR_FOUNDED' | translate }}: </span>
										<span class="org-value">
											{{ organization.registrationDate | dateFormat : null : 'Y' }}
										</span>
									</h5>
								</div>
								<div class="org-size">
									<h5 *ngIf="employeeCounts$ | async as employeeCounts" class="org-title label-text">
										<span class="org-key"> {{ 'PUBLIC_PAGE.COMPANY_SIZE' | translate }}: </span>
										<span class="org-value">
											{{ employeeCounts }}
										</span>
									</h5>
								</div>
								<div class="org-size" *ngIf="organization.show_clients_count">
									<h5 class="org-title label-text">
										<span class="org-key"> {{ 'PUBLIC_PAGE.TOTAL_CLIENTS' | translate }}: </span>
										<span class="org-value">
											{{ clientCounts$ | async }}
										</span>
									</h5>
								</div>
								<div class="org-client-focus" *ngIf="organization.client_focus">
									<h5 class="org-title label-text">
										<span class="org-key"> {{ 'PUBLIC_PAGE.CLIENT_FOCUS' | translate }}: </span>
										<span
											class="client-focus"
											[textContent]="organization.client_focus.replace(',', ', ')"
										></span>
									</h5>
								</div>
								<div class="row" *ngIf="organization.show_profits">
									<div class="col">
										<h5 class="org-title label-text">
											<span class="org-key">
												{{ 'PUBLIC_PAGE.PROFITS' | translate }}
												:
											</span>
											<span
												class="org-value"
												[textContent]="
													profits || 0
														| currency : organization?.currency
														| position : organization?.currencyPosition
												"
											></span>
										</h5>
									</div>
								</div>
							</div>
							<ng-container *ngIf="hasEditPublicPage$ | async">
								<button
									(click)="editPublicPage()"
									class="action primary"
									status="basic"
									size="small"
									nbButton
								>
									<nb-icon class="open" icon="edit-outline"></nb-icon>
									{{ 'BUTTONS.EDIT' | translate }}
								</button>
							</ng-container>
						</div>
					</div>
				</div>
				<div class="section_block"></div>
				<nb-tabset class="o__p__s" #tabset>
					<nb-tab #profileTab [tabTitle]="'PUBLIC_PAGE.PROFILE' | translate" class="o__p__s__t">
						<ng-container *ngTemplateOutlet="profileTemplate"></ng-container>
					</nb-tab>
					<nb-tab #employeeTab [tabTitle]="'ORGANIZATIONS_PAGE.EMPLOYEES' | translate" class="o__p__s__t">
						<ng-container *ngTemplateOutlet="employeeTemplate"></ng-container>
					</nb-tab>
					<nb-tab #portfolioTab [tabTitle]="'PUBLIC_PAGE.PORTFOLIO' | translate" class="o__p__s__t">
						<ng-container *ngTemplateOutlet="portfolioTemplate"></ng-container>
					</nb-tab>
					<nb-tab
						#clientTab
						class="o__p__s__t"
						[tabTitle]="'ORGANIZATIONS_PAGE.CLIENTS' | translate"
						*ngIf="organization.show_clients"
					>
						<ng-container *ngTemplateOutlet="clientTemplate"></ng-container>
					</nb-tab>
				</nb-tabset>
			</div>
		</nb-card-body>
	</ng-container>
</nb-card>

<!-- Profile HTML Template -->
<ng-template #profileTemplate>
	<ng-container *ngIf="organization">
		<ng-template [ngIf]="organization?.show_projects_count">
			<div class="row mb-3">
				<div class="col">
					<div class="org-year-found row">
						<h5 class="org-title org-key label-text col-xl-1 col-md-2">
							{{ 'PUBLIC_PAGE.TOTAL_PROJECTS' | translate }}:
						</h5>
						<span class="org-value col text-left" [textContent]="projectCounts$ | async"></span>
					</div>
				</div>
			</div>
		</ng-template>

		<ng-template [ngIf]="organization?.show_income">
			<div class="row mb-3">
				<div class="col">
					<div class="org-year-found row">
						<h5 class="org-title org-key col-xl-1 col-md-2 label-text">
							{{ 'PUBLIC_PAGE.TOTAL_INCOME' | translate }}:
						</h5>
						<span
							class="org-value text-left col"
							[textContent]="
								totalIncome || 0
									| currency : organization?.currency
									| position : organization?.currencyPosition
							"
						></span>
					</div>
				</div>
			</div>
		</ng-template>

		<ng-template [ngIf]="organization?.show_bonuses_paid">
			<div class="row mb-3">
				<div class="col">
					<div class="org-year-found row">
						<h5 class="org-title org-key col-xl-1 col-md-2 label-text">
							{{ 'PUBLIC_PAGE.TOTAL_BONUSES_PAID' | translate }}:
						</h5>
						<span
							class="org-value col text-left"
							[textContent]="
								bonusesPaid || 0
									| currency : organization?.currency
									| position : organization?.currencyPosition
							"
						></span>
					</div>
				</div>
			</div>
		</ng-template>

		<ng-container *ngTemplateOutlet="languagesTemplate"></ng-container>

		<ng-container *ngTemplateOutlet="awardsTemplate"></ng-container>

		<ng-template [ngIf]="organization?.show_employees_count">
			<div class="row mb-3">
				<div class="col">
					<div class="org-year-found row">
						<h5 class="org-title org-key col-xl-1 col-md-2 label-text">
							{{ 'PUBLIC_PAGE.EMPLOYEES' | translate }}:
						</h5>
						<span class="col org-value text-left" [textContent]="employeeCounts$ | async"></span>
					</div>
				</div>
			</div>
		</ng-template>

		<ng-template [ngIf]="organization?.short_description">
			<div class="row wrapper mb-3">
				<div class="col">
					<div class="row">
						<h5 class="org-title org-key col-xl-1 col-md-2 label-text">
							{{ 'PUBLIC_PAGE.DESCRIPTION' | translate }}:
						</h5>
						<div class="org-value col text-left" [textContent]="organization.short_description"></div>
					</div>
				</div>
			</div>
		</ng-template>

		<ng-template [ngIf]="organization?.overview">
			<div class="row">
				<div class="col">
					<div class="row">
						<h5 class="org-title org-key col-xl-1 col-md-2 label-text">
							{{ 'PUBLIC_PAGE.OVERVIEW' | translate }}:
						</h5>
						<div class="org-value col text-left">
							<div [innerHtml]="organization.overview"></div>
						</div>
					</div>
				</div>
			</div>
		</ng-template>

		<ng-template [ngIf]="organization?.show_minimum_project_size">
			<div class="row mb-3">
				<div class="col">
					<div class="row">
						<h5 class="org-title org-key col-xl-1 col-md-2 label-text">
							{{ 'POP_UPS.MINIMUM_PROJECT_SIZE' | translate }}:
						</h5>
						<div class="col org-value text-left">
							<div>
								{{ organization.currency }}
								{{ organization.minimumProjectSize }}
							</div>
						</div>
					</div>
				</div>
			</div>
		</ng-template>

		<ng-container *ngIf="organization?.skills?.length > 0">
			<div class="row mb-3">
				<div class="col">
					<div class="org-skills row">
						<h5 class="org-title org-key col-xl-1 col-md-2 label-text">
							{{ 'PUBLIC_PAGE.SKILLS' | translate }}:
						</h5>
						<div class="col">
							<nb-tag-list size="small" class="mt-1">
								<nb-tag
									size="small"
									*ngFor="let skill of organization.skills"
									[text]="skill.name"
									[removable]="false"
								></nb-tag>
							</nb-tag-list>
						</div>
					</div>
				</div>
			</div>
		</ng-container>

		<!-- Languages HTML Template -->
		<ng-template #languagesTemplate>
			<ng-container *ngIf="organization.languages as languages">
				<ng-container *ngIf="languages?.length > 0">
					<div class="row mb-3">
						<div class="col">
							<div class="org-year-found row">
								<h5 class="org-title org-key col-xl-1 col-md-2 label-text">
									{{ 'PUBLIC_PAGE.LANGUAGES' | translate }}:
								</h5>
								<div class="col org-value">
									<nb-tag-list size="small" class="mt-1">
										<nb-tag
											*ngFor="let language of languages"
											[text]="language.name + ' (' + language.level + ')'"
											[removable]="false"
											size="small"
										></nb-tag>
									</nb-tag-list>
								</div>
							</div>
						</div>
					</div>
				</ng-container>
			</ng-container>
		</ng-template>

		<!-- Awards HTML Template -->
		<ng-template #awardsTemplate>
			<ng-container *ngIf="organization.awards as awards">
				<ng-container *ngIf="awards?.length > 0">
					<div class="row mb-3">
						<div class="col">
							<div class="org-year-found row">
								<h5 class="org-title org-key col-xl-1 col-md-2 label-text">
									{{ 'PUBLIC_PAGE.AWARDS' | translate }}:
								</h5>
								<div class="col org-value">
									<nb-tag-list size="small" class="mt-1">
										<nb-tag
											*ngFor="let award of awards"
											[text]="award.name + ' (' + award.year + ')'"
											[removable]="false"
											size="small"
										></nb-tag>
									</nb-tag-list>
								</div>
							</div>
						</div>
					</div>
				</ng-container>
			</ng-container>
		</ng-template>
	</ng-container>
</ng-template>

<!-- Portfolio HTML Template -->
<ng-template #portfolioTemplate>
	<ga-wip></ga-wip>
</ng-template>

<!-- Employee HTML Template -->
<ng-template #employeeTemplate>
	<ng-container *ngIf="employees$ | async as employees">
		<div class="row" *ngIf="employees?.length > 0">
			<div class="col">
				<div class="employee-list">
					<nb-list class="nb_list">
						<nb-list-item
							class="search-result"
							*ngFor="let employee of employees; let last = last"
							[ngClass]="{ 'border-bottom': !last }"
						>
							<ng-container *ngIf="!employee.anonymousBonus; else anonymousTemplate">
								<div class="main-details">
									<div class="row align-items-center">
										<div>
											<div class="avatar has-aura">
												<a>
													<img [src]="employee?.user?.imageUrl" />
												</a>
											</div>
										</div>
										<div class="col-2">
											<div>
												<a
													class="name margin-right-10"
													[routerLink]="[employee.profile_link, employee.id]"
												>
													{{ employee?.fullName }}
												</a>
											</div>
											<div class="position">
												{{ employee?.organizationPosition?.name }}
											</div>
											<div class="specialty">
												{{ employee?.short_description }}
											</div>
										</div>
										<div class="col">
											<ng-template [ngIf]="employee.payPeriod">
												<div class="label label-hourly margin-right-10">
													{{ employee.payPeriod | replace : '_' : ' ' | titlecase }}
												</div>
											</ng-template>
										</div>
										<div class="col-2">
											<div>
												<a
													class="works-at"
													[routerLink]="['/share/organization', organization.profile_link]"
												>
													<nb-icon
														size="tiny"
														icon="globe-2-outline"
														[options]="{
															color: 'var(--gauzy-text-color-2)'
														}"
													>
													</nb-icon>
													{{ organization.name }}
												</a>
											</div>
											<div>
												<span class="location text-secondary">
													<nb-icon
														size="tiny"
														icon="pin-outline"
														[options]="{
															color: 'var(--gauzy-text-color-2)'
														}"
													>
													</nb-icon>
													Sofia, Sofia, Bulgaria
												</span>
											</div>
											<div class="profile-bio">
												<span class="average-bonus">
													{{ 'PUBLIC_PAGE.AVERAGE_BONUS' | translate }}:
												</span>
												<span class="text-secondary">
													{{ employee.billRateCurrency }}
													{{ employee.averageBonus === null ? 0 : employee.averageBonus }}
												</span>
											</div>
										</div>
										<div class="col-3">
											<div
												class="profile-bio text"
												[innerHtml]="employee.description | truncate : 180 | safeHtml"
											></div>
										</div>
										<div class="col-1">
											<div class="list-inline">
												<li *ngFor="let skill of employee.skills">
													<span class="tag tag-sm">
														{{ skill.name }}
													</span>
												</li>
											</div>
										</div>
										<div class="col-2 text-right">
											<div class="pay-label">
												<ng-template [ngIf]="employee.billRateValue">
													<div class="pay-rate">
														{{
															employee.billRateValue
																| currency : employee.billRateCurrency : 'symbol'
																| position : organization?.currencyPosition
														}}/hr
													</div>
												</ng-template>
												<ng-template [ngIf]="employee.totalWorkHours">
													<div class="caption label-total-hours mt-2">
														{{ employee.totalWorkHours }}
														{{ 'ORGANIZATIONS_PAGE.HOURS_WORKED' | translate }}
													</div>
												</ng-template>
											</div>
										</div>
									</div>
								</div>
							</ng-container>
							<ng-template #anonymousTemplate>
								<div class="main-details">
									<div class="row align-items-center">
										<div>
											<div class="anonymous-avatar">A</div>
										</div>
										<div class="col-2">
											<div class="align-items-center">
												{{ 'PUBLIC_PAGE.ANONYMOUS' | translate }}
											</div>
											<div class="specialty">Full Stack Web Developer</div>
										</div>
										<div class="col-2">
											<ng-template [ngIf]="employee.payPeriod">
												<div class="label label-hourly margin-right-10">
													{{ employee.payPeriod | replace : '_' : ' ' | titlecase }}
												</div>
											</ng-template>
										</div>
										<div class="col-2">
											<div>
												<a
													class="works-at"
													[routerLink]="['/share/organization', organization.profile_link]"
												>
													<nb-icon
														size="tiny"
														icon="globe-2-outline"
														[options]="{
															color: 'var(--gauzy-text-color-2)'
														}"
													>
													</nb-icon>
													{{ organization.name }}
												</a>
											</div>
											<div>
												<span class="location text-secondary">
													<nb-icon
														size="tiny"
														icon="pin-outline"
														[options]="{
															color: 'var(--gauzy-text-color-2)'
														}"
													>
													</nb-icon>
													Sofia, Sofia, Bulgaria
												</span>
											</div>
											<div class="profile-bio">
												<span class="average-bonus">
													{{ 'PUBLIC_PAGE.AVERAGE_BONUS' | translate }}:
												</span>
												<span class="text-secondary">
													{{ employee.billRateCurrency }}
													{{ employee.averageBonus === null ? 0 : employee.averageBonus }}
												</span>
											</div>
										</div>
										<div class="col-1">
											<div class="list-inline">
												<li *ngFor="let skill of employee.skills">
													<span class="tag tag-sm">
														{{ skill.name }}
													</span>
												</li>
											</div>
										</div>
									</div>
								</div>
							</ng-template>
						</nb-list-item>
					</nb-list>
				</div>
			</div>
		</div>
	</ng-container>
</ng-template>

<!-- Client HTML Template -->
<ng-template #clientTemplate>
	<ng-container *ngIf="clients$ | async as clients">
		<nb-list class="nb_list">
			<nb-list-item
				class="search-result"
				*ngFor="let client of clients; let last = last"
				[ngClass]="{ 'border-bottom': !last }"
			>
				<div class="avatar has-aura">
					<img [src]="client?.imageUrl" />
				</div>
				<div class="main-details">
					{{ client?.name }}
				</div>
				<div *ngIf="hasEditPublicPage$ | async" class="specialty">
					<gauzy-email [rowData]="{ email: client?.primaryEmail }"></gauzy-email>
				</div>
			</nb-list-item>
		</nb-list>
	</ng-container>
</ng-template>
