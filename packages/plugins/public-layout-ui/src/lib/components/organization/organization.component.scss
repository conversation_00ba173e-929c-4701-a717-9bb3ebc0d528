@use 'gauzy/_gauzy-table' as *;

.org-info {
  border-radius: var(--border-radius);

  .edit-icon {
    margin-left: 30px;
    position: absolute;
    width: 36px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    right: 45px;
    top: 35px;

    svg {
      position: absolute;
    }

    nb-icon {
      position: absolute;
      cursor: pointer;
      z-index: 999;
    }
  }

  .org-head {
    padding: 1rem;
    border-radius: var(--border-radius) var(--border-radius) 0 0;

    .org-head-left-sec {
      h4.org-name {
        font-size: 14px;
        font-weight: 600;
        line-height: 17px;
        letter-spacing: 0em;
        text-align: left;
        color: var(--gauzy-text-color-1);
        margin: 0;
      }

      img.org-image {
        object-fit: cover;
        object-position: center;
        width: 48px;
        height: 48px;
        border-radius: var(--border-radius);
        box-shadow: 0 2px 4px rgba(0, 0, 1, 0.2);
      }

      .organization-photo {
        float: left;
        margin-right: 17px;
        width: fit-content;
        height: fit-content;
        position: relative;

        div {
          pointer-events: none;
          background: black;
          position: absolute;
          height: 100%;
          width: 100%;
          border-radius: var(--border-radius);
        }

        img {
          width: 48px;
          height: 48px;
          border-radius: var(--border-radius);
          box-shadow: 0px 1px 1px 0px rgba(0, 0, 0, 0.25);
        }

        input {
          width: 100%;
          height: 100%;
          opacity: 0;
          position: absolute;
          z-index: 3;
          cursor: pointer;
        }

        svg {
          z-index: 2;
          transition: opacity 0.2s ease-in;
          opacity: 0.3;
          position: absolute;
          top: calc(50% - 24px / 2);
          left: calc(50% - 24px / 2);
        }
      }

      button.save_image {
        top: 125px;
        position: absolute;
        left: 50px;
        z-index: 9999;
        padding: 5px 15px;
        font-size: 12px;
      }
    }

    .org-head-right-sec {
      display: flex;
      align-items: flex-start;
      justify-content: flex-end;
      gap: 17px;

      .org-head-details {
        background-color: var(--gauzy-card-2);
        border-radius: var(--border-radius);
        padding: 10px;
        width: fit-content;
      }

      .org-banner {
        color: var(--gauzy-text-color-2);
        margin: 5px 0;
        font-size: 12px;
        font-weight: 400;
        line-height: 15px;
        letter-spacing: 0em;
        text-align: left;

      }
    }
  }

  .org-banner {
    color: var(--gauzy-text-color-2);
    font-size: 12px;
    font-weight: 400;
    line-height: 15px;
    letter-spacing: 0em;
    text-align: left;
  }

  .short_description {
    font-size: 15px;
    line-height: 26px;
    font-weight: 600;
  }

  .org-details {
    width: 100%;
    display: block;
    clear: both;
    padding: 20px 0;
  }

  ul.org-value li.org_list_data {
    display: inline-block;
    padding-right: 5px;
    padding-left: 5px;
  }

  ul.org-value li.org_list_data span {
    border-radius: var(--border-radius);
    border: 1px solid #ced9ee;
    color: #3c495a;
    display: inline-block;
    padding: 6px 12px 5px;
    font-weight: normal;
    font-size: 12px;
  }

  ul.org-value li.org_list_data span:hover,
  .list-inline li span.tag:hover {
    cursor: pointer;
    color: #fff;
    border-color: #007bff;
    background-color: #007bff;
    -webkit-box-shadow: 0 2px 4px #82bfff;
    box-shadow: 0 2px 4px #82bfff;
  }
}

h5.org-title,
.org-title {
  font-size: 12px;
  font-weight: 600;
  line-height: 15px;
  letter-spacing: 0em;
  text-align: left;
  color: var(--gauzy-text-color-1);
}

h5.org-title .org-key {
  padding-right: 1rem;
}

.org-skills {
  display: flex;
  align-items: center;
}

.org-skills p.org-title.org-key {
  color: #222b45;
  line-height: 1.3rem;
  font-size: 16px;
  font-weight: 600;
  clear: both;
}

ul.org-value {
  clear: both;
  line-height: 1.3rem;
  margin: 0;
  padding: 0;
}

h5.org-title span.client-focus,
span.org-value {
  font-size: 14px;
  font-weight: 400;
  line-height: 17px;
  letter-spacing: 0em;
  text-align: right;
}

.section_block {
  padding: 20px 0;
  clear: both;
  display: block;
}

.nb_list {
  overflow: unset;
}

.nb_list.block_style {
  display: block;
}

.nb_list_item.item {
  border: none !important;
  float: left;
  padding-left: 0;
}

.nb_list_employee {
  border: none;
  padding: 15px 0;
  float: left;
  width: 100%;
}

.nb_list_employee:first-child {
  border: none;
}

h5.org-title.title-employees {
  text-align: center;
  padding: 9px;
  margin: 0 auto;
  max-width: 180px;
  margin-bottom: 25px;
  font-size: 14px;
  font-weight: 400;
  line-height: 17px;
  letter-spacing: 0em;
  text-align: left;
}

p,
.paragraph,
.org-overview p {
  color: #617083;
}

.nb-theme-default p,
.nb-theme-default .paragraph,
.nb-theme-default li,
.col ul li {
  color: #617083;
  line-height: 1.35rem !important;
}

.search-result {
  display: block;
  color: #617083;

  a {
    color: #55a8fd;
    text-decoration: none;
    background-color: transparent;
  }

  .avatar {
    position: relative;
    float: left;
    margin: 0 13px 0 0;
    width: 36px;

    img {
      display: block;
      width: 36px;
      height: 36px;
      object-fit: cover;
      border-radius: 50%;
      -webkit-box-shadow: var(--gauzy-shadow)(0, 0, 0, 0.25);
      box-shadow: var(--gauzy-shadow)(0, 0, 0, 0.25);
    }
  }

  .avatar:before {
    width: 58px;
    height: 58px;
    content: '';
    position: absolute;
    top: -4px;
    left: -4px;
    border: 2px solid transparent;
    border-radius: 50%;
  }

  .name {
    font-size: 16px;
    font-weight: 500;
    color: var(--gauzy-text-color-1);
  }

  .margin-right-10 {
    margin-right: 10px;
  }

  .label {
    font-size: 12px;
    display: inline;
    padding: 0.2em 0.6em 0.3em;
    font-weight: 700;
    line-height: 1;
    color: #fff;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.25em;
  }

  .label-hourly {
    background-color: var(--color-success-transparent-200);
    color: var(--color-success-default);
    font-size: 12px;
    font-weight: 600;
    line-height: 15px;
    letter-spacing: 0em;
  }

  .location {
    font-size: 12px;
    font-weight: 600;
    line-height: 15px;
    letter-spacing: 0em;
    text-align: left;
  }

  .pay-rate {
    color: var(--gauzy-text-color-1);
    font-size: 16px;
    font-weight: 600;
    line-height: 19px;
    letter-spacing: 0em;
  }

  .pay-label{
    width: fit-content;
    text-align: left;
  }

  .specialty, .position {
    color: var(--gauzy-text-color-2);
    font-size: 12px;
    font-weight: 400;
    line-height: 15px;
    letter-spacing: 0em;
    text-align: left;
  }

  .average-bonus {
    color: var(--gauzy-text-color-2);
    font-size: 12px;
    font-weight: 400;
    line-height: 15px;
    letter-spacing: 0em;
    text-align: left;
  }

  .works-at {
    margin-top: 10px;
    display: inline-block;
    margin-right: 10px;
    color: var(--gauzy-text-color-1);
    font-size: 12px;
    font-weight: 600;
    line-height: 15px;
    letter-spacing: 0em;
    text-align: left;
  }

  .text-success {
    color: #56d48f;
  }

  .profile-bio {
    text-overflow: ellipsis;
    color: var(--gauzy-text-color-1);
    font-size: 12px;
    font-weight: 400;
    line-height: 15px;
    letter-spacing: 0em;
    text-align: left;
    word-break: break-word;
  }

  .push-bottom-10 {
    margin-top: 10px;
  }

  .push-bottom-20 {
    margin-top: 20px;
  }

  .list-inline {
    padding-left: 0;
    list-style: none;
    margin-left: -5px;
  }

  .list-inline>li {
    display: inline-block;
    padding-right: 5px;
    padding-left: 5px;
  }

  .tag {
    border-radius: 25px;
    border: 1px solid #ced9ee;
    color: #3c495a;
    display: inline-block;
    padding: 6px 12px 5px;
    font-size: 12px;
  }

  .tag.tag-sm {
    font-weight: normal;
    font-size: 12px;
    margin-bottom: 15px;
  }
}

.search-result:first-child {
  border-top: none;
}

@media (min-width: 768px) {
  .search-result:after {
    clear: both;
  }
}

@media (min-width: 768px) {

  .search-result:before,
  .search-result:after {
    display: table;
    content: ' ';
  }
}

@media (min-width: 768px) {
  .search-result .main-details {
    float: left;
    width: calc(100% - 63px);
  }
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: ' ';
}

.clearfix:after {
  clear: both;
}

.label-text {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  color: #222b45;
}

.wrapper {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
}

.o__p__s__t {
  background-color: var(--gauzy-card-2);
  border-radius: 0 var(--border-radius) var(--border-radius) var(--border-radius);
  overflow: auto;
  height: calc(100vh - 19rem);
}

.anonymous-avatar {
  display: inline-flex;
  position: relative;
  float: left;
  margin: 0 13px 0 0;
  width: 50px;
  height: 50px;
  border-radius: var(--border-radius);
  background: #cecece;
  color: white;
  justify-content: center;
  align-items: center;
  font-size: 18px;
  font-weight: 700;
}

nb-card {
  nb-card-body {
    background-color: var(--gauzy-card-2);
    border-radius: var(--border-radius);
    margin: 0;
    padding: 0;
  }
}
