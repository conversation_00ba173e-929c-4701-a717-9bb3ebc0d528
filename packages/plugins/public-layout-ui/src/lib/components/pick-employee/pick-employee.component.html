<nb-card [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
	<nb-card-header class="card-header-title">
		<div class="card-header-title">
			<ngx-back-navigation></ngx-back-navigation>
			<h4>
				{{ 'APPOINTMENTS_PAGE.SELECT_EMPLOYEE' | translate }}
			</h4>
		</div>
	</nb-card-header>
	<nb-card-body>
		<ng-container *ngIf="organization$ | async as organization">
			<div class="col-9">
				<div style="height: 300px" class="form-group">
					<label for="description" class="label">
						{{ 'APPOINTMENTS_PAGE.EMPLOYEE' | translate }}
					</label>
					<div class="center-div">
						<ga-employee-selector
							class="col-6"
							style="width: 100%"
							#employeeSelector
							[skipGlobalChange]="true"
							[addTag]="false"
							class="employees"
						></ga-employee-selector>
						<span class="col-1"></span>
						<button class="col-3" (click)="bookPublicEmployeeAppointment()" status="success" nbButton>
							{{ 'PUBLIC_APPOINTMENTS.BOOK_APPOINTMENT' | translate }}
						</button>
					</div>
				</div>
			</div>
		</ng-container>
	</nb-card-body>
</nb-card>
