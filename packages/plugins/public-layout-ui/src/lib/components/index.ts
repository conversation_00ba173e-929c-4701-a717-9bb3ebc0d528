import { AppointmentFormComponent } from './appointment-form/appointment-form.component';
import { ConfirmAppointmentComponent } from './confirm-appointment/confirm-appointment.component';
import { CreateAppointmentComponent } from './create-appointment/create-appointment.component';
import { EditAppointmentComponent } from './edit-appointment/edit-appointment.component';
import { EmployeeComponent } from './employee/employee.component';
import { InvoiceEstimateViewComponent } from './invoice-estimate/invoice-estimate-view.component';
import { OrganizationComponent } from './organization/organization.component';
import { PickEmployeeComponent } from './pick-employee/pick-employee.component';
import { PublicAppointmentComponent } from './public-appointment/public-appointment.component';
import { PublicLayoutComponent } from './public-layout.component';
import { PublicPageEmployeeMutationComponent } from './mutation/public-page-employee-mutation/public-page-employee-mutation.component';
import { PublicPageOrganizationMutationComponent } from './mutation/public-page-organization-mutation/public-page-organization-mutation.component';

export * from './appointment-form/appointment-form.component';
export * from './confirm-appointment/confirm-appointment.component';
export * from './create-appointment/create-appointment.component';
export * from './edit-appointment/edit-appointment.component';
export * from './employee/employee.component';
export * from './invoice-estimate/invoice-estimate-view.component';
export * from './organization/organization.component';
export * from './pick-employee/pick-employee.component';
export * from './public-appointment/public-appointment.component';
export * from './public-layout.component';
export * from './mutation/public-page-employee-mutation/public-page-employee-mutation.component';
export * from './mutation/public-page-organization-mutation/public-page-organization-mutation.component';

export const COMPONENTS = [
	AppointmentFormComponent,
	ConfirmAppointmentComponent,
	CreateAppointmentComponent,
	EditAppointmentComponent,
	EmployeeComponent,
	InvoiceEstimateViewComponent,
	OrganizationComponent,
	PickEmployeeComponent,
	PublicAppointmentComponent,
	PublicLayoutComponent,
	PublicPageEmployeeMutationComponent,
	PublicPageOrganizationMutationComponent
];
