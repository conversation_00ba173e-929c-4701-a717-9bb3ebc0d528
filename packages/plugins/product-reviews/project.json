{"name": "plugin-product-reviews", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/plugins/product-reviews/src", "projectType": "library", "release": {"version": {"generatorOptions": {"packageRoot": "dist/{projectRoot}", "currentVersionResolver": "git-tag"}}}, "tags": [], "implicitDependencies": [], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/plugins/product-reviews", "tsConfig": "packages/plugins/product-reviews/tsconfig.lib.json", "packageJson": "packages/plugins/product-reviews/package.json", "main": "packages/plugins/product-reviews/src/index.ts", "assets": ["packages/plugins/product-reviews/*.md"]}}, "nx-release-publish": {"options": {"packageRoot": "dist/{projectRoot}"}}, "lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/plugins/product-reviews/jest.config.ts"}}}}