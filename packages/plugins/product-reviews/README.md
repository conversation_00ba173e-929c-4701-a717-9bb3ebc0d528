# @gauzy/plugin-product-reviews

This library was generated with [Nx](https://nx.dev). It contains the product reviews plugin for the Gauzy platform.

## Building

Run `yarn nx build plugin-product-reviews` to build the library.

## Running unit tests

Run `yarn run test plugin-product-reviews` to execute the unit tests via [Jest](https://jestjs.io).

## Publishing

After building your library with `yarn nx build plugin-product-reviews`, go to the dist folder `dist/packages/plugins/product-reviews` and run `npm publish`.

## Installation

Install the Product Reviews Plugin using your preferred package manager:

```bash
npm install @gauzy/plugin-product-reviews
# or
yarn add @gauzy/plugin-product-reviews
```
