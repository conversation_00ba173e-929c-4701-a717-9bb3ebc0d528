{"name": "@gauzy/plugin-product-reviews", "version": "0.1.0", "description": "Gauzy Product Reviews Plugin - Integration for managing and displaying product reviews in the Gauzy Platform.", "author": {"name": "Ever Co. LTD", "email": "<EMAIL>", "url": "https://ever.co"}, "repository": {"type": "git", "url": "https://github.com/ever-co/ever-gauzy", "directory": "packages/plugins/product-reviews"}, "bugs": {"url": "https://github.com/ever-co/ever-gauzy/issues"}, "homepage": "https://ever.co", "license": "AGPL-3.0", "private": true, "type": "commonjs", "main": "./src/index.js", "typings": "./src/index.d.ts", "scripts": {"lib:build": "yarn nx build plugin-product-reviews", "lib:build:prod": "yarn nx build plugin-product-reviews", "lib:watch": "yarn nx build plugin-product-reviews --watch"}, "dependencies": {"@gauzy/contracts": "^0.1.0", "@gauzy/core": "^0.1.0", "@gauzy/plugin": "^0.1.0", "@nestjs/common": "^11.1.0", "@nestjs/swagger": "^11.1.5", "@nestjs/typeorm": "^11.0.0", "chalk": "^4.1.0", "class-validator": "^0.14.2", "graphql-tag": "^2.12.6", "tslib": "^2.6.2", "typeorm": "^0.3.24"}, "devDependencies": {"@types/jest": "29.5.14", "@types/node": "^20.14.9", "typescript": "^5.8.3"}, "keywords": ["gauzy", "product-reviews", "plugin", "<PERSON><PERSON><PERSON>", "typescript", "reviews", "feedback", "e-commerce"], "engines": {"node": ">=20.18.1", "yarn": ">=1.22.19"}, "sideEffects": false}