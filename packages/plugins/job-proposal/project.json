{"name": "plugin-job-proposal", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/plugins/job-proposal/src", "projectType": "library", "release": {"version": {"generatorOptions": {"packageRoot": "dist/{projectRoot}", "currentVersionResolver": "git-tag"}}}, "tags": [], "implicitDependencies": [], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/plugins/job-proposal", "tsConfig": "packages/plugins/job-proposal/tsconfig.lib.json", "packageJson": "packages/plugins/job-proposal/package.json", "main": "packages/plugins/job-proposal/src/index.ts", "assets": ["packages/plugins/job-proposal/*.md"]}}, "nx-release-publish": {"options": {"packageRoot": "dist/{projectRoot}"}}, "lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/plugins/job-proposal/jest.config.ts"}}}}