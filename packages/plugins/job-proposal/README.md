# @gauzy/plugin-job-proposal

## Overview

The Job Proposal Plugin is a powerful tool designed to simplify and optimize the process of creating, managing, and tracking job proposals.

## Features

- **Proposal Creation:** Easily create job proposals with customizable fields and templates.
- **Proposal Management:** Manage and track job proposals, including status updates, notes, and attachments.
- **Proposal Approval:** Ensure job proposals are approved before they are posted on the job board.

## Building

Run `yarn nx build plugin-job-proposal` to build the library.

## Running unit tests

Run `yarn nx test plugin-job-proposal` to execute the unit tests via [Jest](https://jestjs.io).

## Publishing

After building your library with `yarn nx build plugin-job-proposal`, go to the dist folder `dist/packages/plugins/job-proposal` and run `npm publish`.

## Installation

To install the Job Proposal Plugin, simply run the following command in your terminal:

```bash
npm install @gauzy/plugin-job-proposal
# or
yarn add @gauzy/plugin-job-proposal
```
