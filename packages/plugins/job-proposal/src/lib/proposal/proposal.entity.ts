import { Join<PERSON><PERSON>um<PERSON>, <PERSON><PERSON>Id, JoinTable } from 'typeorm';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional, IsUUID } from 'class-validator';
import { IProposal, IEmployee, IOrganizationContact, ProposalStatusEnum, ID } from '@gauzy/contracts';
import {
	ColumnIndex,
	Employee,
	MultiORMColumn,
	MultiORMEntity,
	MultiORMManyToMany,
	MultiORMManyToOne,
	OrganizationContact,
	Tag,
	Taggable,
	TenantOrganizationBaseEntity
} from '@gauzy/core';
import { MikroOrmProposalRepository } from './repository/mikro-orm-proposal.repository';

@MultiORMEntity('proposal', { mikroOrmRepository: () => MikroOrmProposalRepository })
export class Proposal extends TenantOrganizationBaseEntity implements IProposal, Taggable {
	/**
	 * The URL of the posted job associated with this proposal.
	 * This property is optional and can be null if there is no direct job post link.
	 */
	@ApiPropertyOptional({ type: () => String })
	@IsOptional()
	@ColumnIndex()
	@MultiORMColumn({ nullable: true })
	jobPostUrl?: string;

	/**
	 * Represents an optional date related to the value or pricing of the proposal.
	 * Can be used to track when the value or estimate was determined.
	 */
	@ApiPropertyOptional({ type: () => Date })
	@IsOptional()
	@MultiORMColumn({ nullable: true })
	valueDate?: Date;

	/**
	 * The content or description of the job posting to which this proposal relates.
	 * This field is required, as proposals typically reference a specific job post.
	 */
	@ApiProperty({ type: () => String })
	@IsNotEmpty()
	@MultiORMColumn()
	jobPostContent: string;

	/**
	 * The main body or content of the proposal being submitted.
	 * This field is required to ensure the proposal includes some meaningful text.
	 */
	@ApiProperty({ type: () => String })
	@IsNotEmpty()
	@MultiORMColumn()
	proposalContent: string;

	/**
	* The status of the proposal (e.g., 'SENT', 'ACCEPTED').
	* Enum-based property to enforce valid statuses defined by ProposalStatusEnum.
	*/
	@ApiProperty({ type: () => String, enum: ProposalStatusEnum })
	@IsEnum(ProposalStatusEnum)
	@MultiORMColumn()
	status?: ProposalStatusEnum;

	/*
	|--------------------------------------------------------------------------
	| @ManyToOne
	|--------------------------------------------------------------------------
	*/
	/**
	 * Represents a many-to-one relationship to the `Employee` entity.
	 *
	 * The `employeeId` field is the relation ID:
	 * - Decorated with `@IsUUID()` to enforce UUID format
	 * - Uses `@RelationId(...)` to automatically set `employeeId` from `employee`
	 * - Marked as nullable in the database
	 */
	@MultiORMManyToOne(() => Employee, {
		/** Indicates if relation column value can be nullable or not. */
		nullable: true,

		/** Database cascade action on delete. */
		onDelete: 'CASCADE'
	})
	@JoinColumn()
	employee?: IEmployee;

	/**
	 * The UUID representing the linked `Employee` record.
	 */
	@ApiProperty({ type: () => String })
	@IsUUID()
	@RelationId((it: Proposal) => it.employee)
	@MultiORMColumn({ nullable: true, relationId: true })
	employeeId?: ID;

	/**
	 * Defines a many-to-one relationship with the `OrganizationContact` entity.
	 *
	 * - The `organizationContactId` field is generated by the `@RelationId` decorator,
	 *   ensuring the ID is populated automatically based on the associated entity.
	 * - Decorators such as `@IsOptional()` and `@IsUUID()` ensure optional or UUID
	 *   validation where appropriate.
	 */

	@MultiORMManyToOne(() => OrganizationContact, {
		/** Indicates if relation column value can be nullable or not. */
		nullable: true,

		/** Database cascade action on delete. */
		onDelete: 'CASCADE'
	})
	@JoinColumn()
	organizationContact?: IOrganizationContact;

	@ApiPropertyOptional({ type: () => String })
	@IsOptional()
	@IsUUID()
	@RelationId((it: Proposal) => it.organizationContact)
	@MultiORMColumn({ nullable: true, relationId: true })
	organizationContactId?: ID;

	/*
	|--------------------------------------------------------------------------
	| @ManyToMany
	|--------------------------------------------------------------------------
	*/
	/**
	 * Tags
	 */
	@MultiORMManyToMany(() => Tag, {
		/**  Database cascade action on update. */
		onUpdate: 'CASCADE',
		/** Database cascade action on delete. */
		onDelete: 'CASCADE',
		/** This column is a boolean flag indicating whether the current entity is the 'owning' side of a relationship.  */
		owner: true,
		/** Pivot table for many-to-many relationship. */
		pivotTable: 'tag_proposal',
		/** Column in pivot table referencing 'proposal' primary key. */
		joinColumn: 'proposalId',
		/** Column in pivot table referencing 'tag' primary key. */
		inverseJoinColumn: 'tagId'
	})
	@JoinTable({ name: 'tag_proposal' })
	tags?: Tag[];
}
