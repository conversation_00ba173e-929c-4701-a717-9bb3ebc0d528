{"name": "plugin-knowledge-base", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/plugins/knowledge-base/src", "projectType": "library", "release": {"version": {"generatorOptions": {"packageRoot": "dist/{projectRoot}", "currentVersionResolver": "git-tag"}}}, "tags": [], "implicitDependencies": [], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/plugins/knowledge-base", "tsConfig": "packages/plugins/knowledge-base/tsconfig.lib.json", "packageJson": "packages/plugins/knowledge-base/package.json", "main": "packages/plugins/knowledge-base/src/index.ts", "assets": ["packages/plugins/knowledge-base/*.md"]}}, "nx-release-publish": {"options": {"packageRoot": "dist/{projectRoot}"}}, "lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/plugins/knowledge-base/jest.config.ts"}}}}