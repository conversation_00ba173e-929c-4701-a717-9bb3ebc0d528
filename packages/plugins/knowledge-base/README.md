# @gauzy/plugin-knowledge-base

This library was generated with [Nx](https://nx.dev). It contains the knowledge base for the Gauzy API platform.

## Building

Run `yarn nx build plugin-knowledge-base` to build the library.

## Running unit tests

Run `yarn nx test plugin-knowledge-base` to execute the unit tests via [Jest](https://jestjs.io).

## Publishing

After building your library with `yarn nx build plugin-knowledge-base`, go to the dist folder `dist/packages/plugins/knowledge-base` and run `npm publish`.

## Installation

Install the Knowledge Base Plugin using your preferred package manager:

```bash
npm install @gauzy/plugin-knowledge-base
# or
yarn add @gauzy/plugin-knowledge-base
```
