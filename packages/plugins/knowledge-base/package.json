{"name": "@gauzy/plugin-knowledge-base", "version": "0.1.0", "description": "Ever Gauzy Platform Knowledge Base plugin", "author": {"name": "Ever Co. LTD", "email": "<EMAIL>", "url": "https://ever.co"}, "license": "AGPL-3.0", "private": true, "type": "commonjs", "main": "./src/index.js", "typings": "./src/index.d.ts", "scripts": {"lib:build": "yarn nx build plugin-knowledge-base", "lib:build:prod": "yarn nx build plugin-knowledge-base", "lib:watch": "yarn nx build plugin-knowledge-base --watch"}, "dependencies": {"@faker-js/faker": "^9.8.0", "@gauzy/contracts": "^0.1.0", "@gauzy/core": "^0.1.0", "@gauzy/plugin": "^0.1.0", "@gauzy/utils": "^0.1.0", "@mikro-orm/nestjs": "^6.1.1", "@nestjs/common": "^11.1.0", "@nestjs/core": "^11.1.0", "@nestjs/cqrs": "^11.0.3", "@nestjs/passport": "^11.0.5", "@nestjs/swagger": "^11.1.5", "@nestjs/typeorm": "^11.0.0", "chalk": "^4.1.0", "class-validator": "^0.14.2", "tslib": "^2.6.2", "typeorm": "^0.3.24"}, "devDependencies": {"@types/jest": "29.5.14", "@types/node": "^20.14.9", "typescript": "^5.8.3"}, "keywords": ["knowledge-base", "plugin", "<PERSON>", "NestJS", "typescript", "microservices", "documentation", "content-management", "enterprise", "platform"], "engines": {"node": ">=20.18.1", "yarn": ">=1.22.19"}, "sideEffects": false}