# @gauzy/plugin-integration-wakatime

See more information about [WakaTime](https://wakatime.com).

This library was generated with [Nx](https://nx.dev).

## Building

Run `yarn nx build plugin-integration-wakatime` to build the library.

## Running unit tests

Run `yarn nx test plugin-integration-wakatime` to execute the unit tests via [Jest](https://jestjs.io).

## Publishing

After building your library with `yarn nx build plugin-integration-wakatime`, go to the dist folder `dist/packages/plugins/integration-wakatime` and run `npm publish`.

## Installation

Install the Wakatime plugin using your preferred package manager:

```bash
npm install @gauzy/plugin-integration-wakatime
# or
yarn add @gauzy/plugin-integration-wakatime
```
