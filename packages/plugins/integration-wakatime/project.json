{"name": "plugin-integration-wakatime", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/plugins/integration-wakatime/src", "projectType": "library", "release": {"version": {"generatorOptions": {"packageRoot": "dist/{projectRoot}", "currentVersionResolver": "git-tag"}}}, "tags": [], "implicitDependencies": [], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/plugins/integration-wakatime", "tsConfig": "packages/plugins/integration-wakatime/tsconfig.lib.json", "packageJson": "packages/plugins/integration-wakatime/package.json", "main": "packages/plugins/integration-wakatime/src/index.ts", "assets": ["packages/plugins/integration-wakatime/*.md"]}}, "nx-release-publish": {"options": {"packageRoot": "dist/{projectRoot}"}}, "lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/plugins/integration-wakatime/jest.config.ts"}}}}