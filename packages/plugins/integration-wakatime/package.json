{"name": "@gauzy/plugin-integration-wakatime", "version": "0.1.0", "description": "Ever Gauzy Platform plugin for integration with Wakatime APIs", "author": {"name": "Ever Co. LTD", "email": "<EMAIL>", "url": "https://ever.co"}, "repository": {"type": "git", "url": "https://github.com/ever-co/ever-gauzy", "directory": "packages/plugins/integration-wakatime"}, "bugs": {"url": "https://github.com/ever-co/ever-gauzy/issues"}, "homepage": "https://ever.co", "license": "AGPL-3.0", "private": true, "type": "commonjs", "main": "./src/index.js", "typings": "./src/index.d.ts", "scripts": {"lib:build": "yarn nx build plugin-integration-wakatime", "lib:build:prod": "yarn nx build plugin-integration-wakatime", "lib:watch": "yarn nx build plugin-integration-wakatime --watch"}, "dependencies": {"@gauzy/contracts": "^0.1.0", "@gauzy/core": "^0.1.0", "@mikro-orm/core": "^6.4.13", "@mikro-orm/nestjs": "^6.1.1", "@nestjs/common": "^11.1.0", "@nestjs/core": "^11.1.0", "@nestjs/typeorm": "^11.0.0", "moment": "^2.30.1", "tslib": "^2.6.2", "typeorm": "^0.3.24"}, "devDependencies": {"@nestjs/testing": "^11.0.0", "@types/jest": "29.5.14", "@types/node": "^20.14.9", "typescript": "^5.8.3"}, "keywords": ["<PERSON>", "Wakatime", "API integration", "time tracking", "productivity tool", "<PERSON><PERSON><PERSON>", "mikro-orm", "typeorm"], "engines": {"node": ">=20.18.1", "yarn": ">=1.22.19"}, "sideEffects": false}