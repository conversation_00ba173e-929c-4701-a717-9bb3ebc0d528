{"name": "@gauzy/plugin-job-proposal-ui", "version": "0.1.0", "description": "A UI plugin for managing job proposals within the Ever Gauzy platform.", "author": {"name": "Ever Co. LTD", "email": "<EMAIL>", "url": "https://ever.co"}, "repository": {"type": "git", "url": "https://github.com/ever-co/ever-gauzy", "directory": "packages/plugins/job-proposal-ui"}, "bugs": {"url": "https://github.com/ever-co/ever-gauzy/issues"}, "homepage": "https://ever.co", "license": "AGPL-3.0", "private": true, "scripts": {"lib:build": "yarn nx build plugin-job-proposal-ui --configuration=development", "lib:build:prod": "yarn nx build plugin-job-proposal-ui --configuration=production", "lib:watch": "yarn nx build plugin-job-proposal-ui --watch --configuration=development"}, "peerDependencies": {"@angular/common": "^19.2.0", "@angular/core": "^19.2.0"}, "dependencies": {"@angular/forms": "^19.2.10", "@angular/platform-browser": "^19.2.10", "@angular/router": "^19.2.10", "@gauzy/contracts": "^0.1.0", "@nebular/theme": "^15.0.0", "@ng-select/ng-select": "^14.8.1", "@ngneat/until-destroy": "^10.0.0", "@ngx-translate/core": "^16.0.4", "angular2-smart-table": "^3.6.2", "ckeditor4-angular": "4.0.1", "moment": "^2.30.1", "ngx-infinite-scroll": "^19.0.0", "ng2-charts": "^8.0.0", "ngx-permissions": "^19.0.0", "rxjs": "^7.8.2", "tslib": "^2.6.2"}, "devDependencies": {"@types/jest": "29.5.14", "@types/node": "^20.14.9", "jest-preset-angular": "14.5.5"}, "keywords": ["<PERSON>", "job proposal", "UI plugin", "Angular", "ngx-translate", "smart table", "job management"], "engines": {"node": ">=20.18.1", "yarn": ">=1.22.19"}, "sideEffects": false}