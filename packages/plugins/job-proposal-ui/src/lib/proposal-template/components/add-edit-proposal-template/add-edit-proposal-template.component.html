<nb-card class="main">
	<nb-card-header class="d-flex flex-column">
		<span class="cancel"><i class="fas fa-times" (click)="close()"></i></span>
		<h5 class="title">
			{{
				(proposalTemplate
					? 'PROPOSAL_TEMPLATE.EDIT_PROPOSAL_TEMPLATE'
					: 'PROPOSAL_TEMPLATE.ADD_PROPOSAL_TEMPLATE'
				) | translate
			}}
		</h5>
	</nb-card-header>
	<nb-card-body class="body">
		<form [formGroup]="form">
			<ng-template [ngIf]="!selectedEmployee && !proposalTemplate" [ngIfElse]="employeeInfoEl">
				<div class="form-group">
					<ga-employee-multi-select
						[label]="'PROPOSAL_TEMPLATE.SELECT_EMPLOYEE' | translate"
						[placeholder]="'PROPOSAL_TEMPLATE.SELECT_EMPLOYEE' | translate"
						[multiple]="false"
						formControlName="employeeId"
					></ga-employee-multi-select>
				</div>
			</ng-template>
			<div class="form-group">
				<label class="d-block">
					{{ 'PROPOSAL_TEMPLATE.NAME' | translate }}
				</label>
				<input
					fullWidth
					formControlName="name"
					type="text"
					nbInput
					[placeholder]="'PROPOSAL_TEMPLATE.NAME' | translate"
				/>
			</div>
			<div class="form-group">
				<label class="d-block">
					{{ 'PROPOSAL_TEMPLATE.CONTENT' | translate }}
				</label>
				<ckeditor formControlName="content" [config]="ckConfig"></ckeditor>
			</div>
		</form>
	</nb-card-body>
	<nb-card-footer class="text-left">
		<button (click)="close()" status="basic" outline class="mr-3" nbButton>
			{{ 'BUTTONS.CANCEL' | translate }}
		</button>
		<button (click)="onSave()" [disabled]="form.invalid" status="success" nbButton>
			{{ 'BUTTONS.SAVE' | translate }}
		</button>
	</nb-card-footer>
</nb-card>
<ng-template #employeeInfoEl>
	<div class="mb-2">
		<label>
			{{ 'PROPOSAL_TEMPLATE.EMPLOYEE' | translate }}
		</label>
		<ng-container *ngIf="!proposalTemplate">
			<ngx-avatar [name]="selectedEmployee?.fullName" [src]="selectedEmployee?.imageUrl"></ngx-avatar>
		</ng-container>
		<ng-container *ngIf="proposalTemplate">
			<ngx-avatar
				[name]="proposalTemplate?.employee?.user?.name"
				[src]="proposalTemplate?.employee?.user?.imageUrl"
			></ngx-avatar>
		</ng-container>
	</div>
</ng-template>
