@use 'gauzy/_gauzy-cards' as *;
@use 'gauzy/_gauzy-overrides' as ga-overrides;

.register-btn {
  margin: 20px;
}

.upload-btn {
  margin-top: 20px;
}

.wrapper-top {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  grid-gap: 3em;
  text-align: center;

  .prop-card {
    align-items: center;
    padding: 2em;
  }

  @media (max-width: 1120px) {
    grid-template-columns: 1fr 1fr;
  }

  @media (max-width: 600px) {
    display: block;
  }
}

.wrapper-bottom {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-gap: 2em;
  text-align: left;
  height: fit-content;

  h6 {
    text-align: center;
    padding: 1em;
  }

  p {
    padding: 0 2em;
  }

  @media (max-width: 900px) {
    display: block;
  }
}

:host {
  ::ng-deep .card-header {
    @include nb-rtl(gap, 2rem);
    ngx-back-navigation button {
      @include nb-rtl(margin, 3px 0 0 0 !important);
    }
  }
  @include card_overrides(overlay, calc($default-card-height + 1.5rem), $default-radius);
  .card,
  .card-body {
    background: var(--gauzy-card-2);
  }
  @include ga-overrides.input-appearance(42px, var(--gauzy-card-1));
}
