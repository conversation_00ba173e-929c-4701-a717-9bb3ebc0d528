// Import the components
import { ProposalLayoutComponent } from './proposal-layout.component';
import { ProposalComponent } from './proposal/proposal.component';
import { ProposalDetailsComponent } from './proposal-details/proposal-details.component';
import { ProposalEditComponent } from './proposal-edit/proposal-edit.component';
import { ProposalRegisterComponent } from './proposal-register/proposal-register.component';
import { ProposalPieChartComponent } from './proposal-pie-chart/proposal-pie-chart.component';
import { JobTitleComponent } from './table-components/job-title/job-title.component';
import { ProposalStatusComponent } from './table-components/proposal-status/proposal-status.component';

// Export the components
export { ProposalLayoutComponent } from './proposal-layout.component';
export { ProposalComponent } from './proposal/proposal.component';
export { ProposalDetailsComponent } from './proposal-details/proposal-details.component';
export { ProposalEditComponent } from './proposal-edit/proposal-edit.component';
export { ProposalRegisterComponent } from './proposal-register/proposal-register.component';
export { ProposalPieChartComponent } from './proposal-pie-chart/proposal-pie-chart.component';
export { JobTitleComponent } from './table-components/job-title/job-title.component';
export { ProposalStatusComponent } from './table-components/proposal-status/proposal-status.component';

export const COMPONENTS = [
	ProposalLayoutComponent,
	ProposalComponent,
	ProposalRegisterComponent,
	ProposalDetailsComponent,
	ProposalEditComponent,
	ProposalPieChartComponent,
	ProposalStatusComponent,
	JobTitleComponent
];
