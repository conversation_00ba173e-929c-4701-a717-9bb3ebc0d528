@use 'gauzy/_gauzy-cards' as *;
@use 'gauzy/_gauzy-table' as *;

.tags {
  padding-top: 0.25rem !important;
  padding-bottom: 2rem !important;
}

nb-badge {
  position: relative !important;
  &:not(:first-child) {
    margin-left: 0.3rem !important;
  }
}

.card-container {
  display: flex;
  flex-direction: column;
}

.wrapper-top {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-gap: 2em;
  text-align: center;

  .prop-card {
    align-items: center;
  }

  @media (max-width: 1120px) {
    grid-template-columns: 1fr 1fr;
  }

  @media (max-width: 600px) {
    display: block;
  }
}

.wrapper-bottom {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-gap: 2em;
  text-align: left;
  height: fit-content;

  h6 {
    text-align: center;
    padding: 1em;
  }

  p {
    padding: 0 2em;
  }

  @media (max-width: 900px) {
    display: block;
  }
}
h4 {
  font-size: 24px;
  font-weight: 500;
  line-height: 30px;
  letter-spacing: 0em;
}

:host {
  @include nb-card-overrides(
    auto,
    calc($default-card-height + 7.5rem),
    $default-radius
  );
  nb-card {
    background: var(--gauzy-card-2);
  }
}

.main-content {
  background: var(--gauzy-card-2);
}
.image-container {
  object-fit: cover;
  img {
    width: 48px;
    height: 48px;
    border-radius: 32px;
  }
}
.custom {
  align-items: center;
  background-color: nb-theme(gauzy-card-2);
  margin-bottom: 1rem;
  .role {
    font-size: 16px;
    font-weight: 600;
    line-height: 17px;
    letter-spacing: 0em;
    text-align: left;
  }

  &.author {
    height: 84px;
    .identity {
      align-items: center;
      margin: 0 10px;
      .name {
        font-size: 12px;
        font-weight: 400;
        line-height: 15px;
        letter-spacing: 0em;
        text-align: left;
        color: nb-theme(text-primary-color);
      }
    }
  }

  &.status {
    height: 48px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 10px;
    ::ng-deep ga-proposal-status {
      div > div {
        margin-top: 5px;
        font-size: 12px;
        font-weight: 600;
        line-height: 11px;
        letter-spacing: 0em;
        text-align: left;
        padding: 7px 14px;
        border-radius: 4px;
      }
    }
  }

  &.job {
    height: 84px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding: 10px;
  }

  &.cont {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding: 10px;
  }
}
