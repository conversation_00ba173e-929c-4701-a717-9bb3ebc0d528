<nb-card>
	<nb-card-header class="card-header-title">
		<div class="card-header-title">
			<ngx-back-navigation></ngx-back-navigation>
			<h4>
				{{ 'PROPOSALS_PAGE.PROPOSAL_DETAILS.PROPOSAL_DETAILS' | translate }}
			</h4>
		</div>
		<div>
			<button (click)="edit()" nbButton type="button" status="basic" class="action primary" size="small">
				<nb-icon icon="edit-outline"></nb-icon>
				{{ 'BUTTONS.EDIT' | translate }}
			</button>
		</div>
	</nb-card-header>
	<nb-card-body class="main-content">
		<div class="row">
			<div class="col-xl-3 col-12 d-flex flex-column">
				<ng-container *ngIf="!employee?.id">
					<nb-card class="custom author d-flex flex-row p-3">
						<div class="image-container">
							<img [src]="proposal?.author?.user?.imageUrl" alt="" />
						</div>
						<div class="identity">
							<h6 class="role">
								{{ 'PROPOSALS_PAGE.PROPOSAL_DETAILS.AUTHOR' | translate }}
							</h6>
							<span class="name">
								{{ proposal?.author?.fullName }}
							</span>
						</div>
					</nb-card>
				</ng-container>
				<nb-card class="custom status">
					<div>{{ 'PROPOSALS_PAGE.PROPOSAL_DETAILS.STATUS' | translate }}</div>
					<ga-proposal-status *ngIf="proposal" [rowData]="proposal"></ga-proposal-status>
				</nb-card>
			</div>
			<div class="col-xl-9 col-12">
				<div class="row">
					<div class="col-lg-6 col-12">
						<nb-card class="custom job">
							<h6 class="role">
								{{ 'PROPOSALS_PAGE.PROPOSAL_DETAILS.JOB_POST_URL' | translate }}
							</h6>
							<a *ngIf="jobPostLink" [href]="jobPostLink" target="_blank">
								{{ 'PROPOSALS_PAGE.PROPOSAL_DETAILS.VIEW_JOB_POST' | translate }}
							</a>
						</nb-card>
					</div>
					<div class="col-lg-6 col-12">
						<nb-card class="custom job">
							<h6 class="role">
								{{ 'PROPOSALS_PAGE.PROPOSAL_DETAILS.PROPOSAL_SENT_ON' | translate }}
							</h6>
							{{ proposal?.valueDate | date : 'shortDate' | dateFormat }}
						</nb-card>
					</div>
				</div>
				<div class="row">
					<div class="col-lg-6 col-12">
						<nb-card class="custom cont">
							<h6 class="role">
								{{ 'PROPOSALS_PAGE.PROPOSAL_DETAILS.JOB_POST_CONTENT' | translate }}
							</h6>
							<div [innerHTML]="jobPostContent"></div>
							<div class="tags">
								<ng-container *ngFor="let tag of proposal?.tags">
									<nb-badge class="color" [style.background]="tag.color" [text]="tag.name"></nb-badge>
								</ng-container>
							</div>
						</nb-card>
					</div>
					<div class="col-lg-6 col-12">
						<nb-card class="custom cont">
							<h6 class="role">
								{{ 'PROPOSALS_PAGE.PROPOSAL_DETAILS.PROPOSAL_CONTENT' | translate }}
							</h6>
							<div [innerHTML]="proposalContent"></div>
						</nb-card>
					</div>
				</div>
			</div>
		</div>
	</nb-card-body>
</nb-card>
