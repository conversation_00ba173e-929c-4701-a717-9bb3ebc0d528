# @gauzy/plugin-job-proposal-ui

This library was generated with [Nx](https://nx.dev).

## Building

Run `yarn nx build plugin-job-proposal-ui` to build the library.

## Running unit tests

Run `yarn nx test plugin-job-proposal-ui` to execute the unit tests.

## Publishing

After building your library with `yarn nx build plugin-job-proposal-ui`, go to the dist folder `dist/packages/plugins/job-proposal-ui` and run `npm publish`.

## Installation

Install the Job Proposal UI Plugin using your preferred package manager:

```bash
npm install @gauzy/plugin-job-proposal-ui
# or
yarn add @gauzy/plugin-job-proposal-ui
```
