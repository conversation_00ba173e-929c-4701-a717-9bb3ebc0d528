{"name": "plugin-integration-upwork", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/plugins/integration-upwork/src", "projectType": "library", "release": {"version": {"generatorOptions": {"packageRoot": "dist/{projectRoot}", "currentVersionResolver": "git-tag"}}}, "tags": [], "implicitDependencies": [], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/plugins/integration-upwork", "tsConfig": "packages/plugins/integration-upwork/tsconfig.lib.json", "packageJson": "packages/plugins/integration-upwork/package.json", "main": "packages/plugins/integration-upwork/src/index.ts", "assets": ["packages/plugins/integration-upwork/*.md"]}}, "nx-release-publish": {"options": {"packageRoot": "dist/{projectRoot}"}}, "lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/plugins/integration-upwork/jest.config.ts"}}}}