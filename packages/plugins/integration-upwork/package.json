{"name": "@gauzy/plugin-integration-upwork", "version": "0.1.0", "description": "Ever Gauzy Platform plugin for integration with Upwork APIs", "author": {"name": "Ever Co. LTD", "email": "<EMAIL>", "url": "https://ever.co"}, "license": "AGPL-3.0", "repository": {"type": "git", "url": "https://github.com/ever-co/ever-gauzy", "directory": "packages/plugins/integration-upwork"}, "bugs": {"url": "https://github.com/ever-co/ever-gauzy/issues"}, "homepage": "https://ever.co", "private": true, "type": "commonjs", "main": "./src/index.js", "typings": "./src/index.d.ts", "scripts": {"lib:build": "yarn nx build plugin-integration-upwork", "lib:build:prod": "yarn nx build plugin-integration-upwork", "lib:watch": "yarn nx build plugin-integration-upwork --watch"}, "dependencies": {"@gauzy/config": "^0.1.0", "@gauzy/contracts": "^0.1.0", "@gauzy/core": "^0.1.0", "@gauzy/plugin": "^0.1.0", "@gauzy/plugin-job-proposal": "^0.1.0", "@gauzy/utils": "^0.1.0", "@nestjs/common": "^11.1.0", "@nestjs/cqrs": "^11.0.3", "@nestjs/platform-express": "^11.1.0", "@nestjs/swagger": "^11.1.5", "class-validator": "^0.14.2", "csv-parser": "^2.3.2", "express": "^5.1.0", "fs-extra": "^10.1.0", "moment": "^2.30.1", "tslib": "^2.6.2", "typeorm": "^0.3.24", "underscore": "^1.13.3", "upwork-api": "^1.3.8", "uuid": "^11.1.0"}, "devDependencies": {"@types/express": "^5.0.1", "@types/fs-extra": "5.0.2", "@types/jest": "29.5.14", "@types/node": "^20.14.9", "typescript": "^5.8.3"}, "keywords": ["gauzy", "upwork", "integration", "plugin", "<PERSON><PERSON><PERSON>", "ever-co", "typescript", "api", "cqrs", "express", "nestjs-plugin"], "engines": {"node": ">=20.18.1", "yarn": ">=1.22.19"}, "sideEffects": false}