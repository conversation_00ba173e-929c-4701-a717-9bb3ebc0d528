# @gauzy/plugin-integration-upwork

This library was generated with [Nx](https://nx.dev). It contains the Upwork Integration plugin for the Gauzy platform.

## Overview

This plugin provides integration with Upwork APIs. It allows you to manage freelance projects and job postings, track job applications, and generate job offers.

## Building

Run `yarn nx build plugin-integration-upwork` to build the library.

## Running unit tests

Run `yarn nx test plugin-integration-upwork` to execute the unit tests via [Jest](https://jestjs.io).

## Publishing

After building your library with `yarn nx build plugin-integration-upwork`, go to the dist folder `dist/packages/plugins/integration-upwork` and run `npm publish`.

## Installation

Install the Integration Upwork plugin using your preferred package manager:

```bash
npm install @gauzy/plugin-integration-upwork
# or
yarn add @gauzy/plugin-integration-upwork
```
