# @gauzy/plugin-integration-zapier

This library was generated with [Nx](https://nx.dev). It contains the Zapier Integration plugin for Gauzy platform.

## Overview

This plugin provides integration with Zapier, enabling seamless automation between Gauzy and thousands of other apps. It allows you to create custom workflows, automate tasks, and synchronize data across multiple platforms.

## Building

Run `yarn nx build plugin-integration-zapier` to build the library.

## Running unit tests

Run `yarn nx test plugin-integration-zapier` to execute the unit tests via [Jest](https://jestjs.io).


## Publishing

After building your library with `yarn nx build plugin-integration-zapier`, go to the dist folder `dist/packages/plugins/integration-zapier` and run `npm publish`.

## Installation

Install the Integration Zapier plugin using your preferred package manager:

```bash
npm install @gauzy/plugin-integration-zapier
# or
yarn add @gauzy/plugin-integration-zapier
```
