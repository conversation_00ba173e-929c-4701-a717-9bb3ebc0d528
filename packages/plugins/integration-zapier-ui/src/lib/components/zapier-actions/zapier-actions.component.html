<nb-card>
	<nb-card-header>
		<h6>{{ 'INTEGRATIONS.ZAPIER_PAGE.ACTIONS.TITLE' | translate }}</h6>
	</nb-card-header>
	<nb-card-body>
		<div *ngIf="loading" class="d-flex justify-content-center">
			<nb-spinner></nb-spinner>
		</div>
		<div *ngIf="!loading">
			<div class="endpoints-list">
				<div *ngFor="let action of actions" class="endpoint-item">
					<div class="endpoint-info">
						<h6>{{ action.name }}</h6>
						<p>{{ action.description }}</p>
					</div>
					<div class="endpoint-actions">
						<button nbButton status="primary" size="small" outline (click)="openActionDetails()">
							{{ 'BUTTONS.VIEW' | translate }}
						</button>
					</div>
				</div>
				<div *ngIf="actions.length === 0" class="no-data">
					{{ 'INTEGRATIONS.ZAPIER_PAGE.ACTIONS.NO_ACTIONS' | translate }}
				</div>
			</div>
		</div>
	</nb-card-body>
</nb-card>
