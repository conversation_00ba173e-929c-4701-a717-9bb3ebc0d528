# @gauzy/plugin-integration-zapier-ui

This library was generated with [Nx](https://nx.dev).

## Building

Run `yarn nx build plugin-integration-zapier-ui` to build the library.

## Running unit tests

Run `yarn nx test plugin-integration-zapier-ui` to execute the unit tests via [Jest](https://jestjs.io).

## Publishing

After building your library with `yarn nx build plugin-integration-zapier-ui`,
go to the dist folder `dist/packages/plugins/integration-zapier-ui`
and run `npm publish`.

## Installation

Install the Integration Zapier UI Plugin using your preferred package manager:

```bash
npm install @gauzy/plugin-integration-zapier-ui
# or
yarn add @gauzy/plugin-integration-zapier-ui
```
