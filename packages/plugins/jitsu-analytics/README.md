# @gauzy/plugin-jitsu-analytics

## Overview

The Jitsu Analytics Plugin seamlessly integrates your server with Jitsu Analytics, a powerful analytics platform. This integration empowers you to gather and analyze data from various sources, enabling data-driven decision-making and insights into user behavior.

## Features

- **Comprehensive Analytics**: Collect, process, and analyze data from different sources to gain a comprehensive view of user interactions and system performance.
- **Real-time Data Processing**: Benefit from real-time data processing capabilities, ensuring that you have the most up-to-date information at your fingertips.
- **Customizable Dashboards**: Create customizable dashboards to visualize key metrics and track the performance of your application.
- **Event Tracking**: Monitor and analyze user interactions and events within your application for a deeper understanding of user behavior.

## Building

Run `yarn nx build plugin-jitsu-analytics` to build the library.

## Running unit tests

Run `yarn nx test plugin-jitsu-analytics` to execute the unit tests via [Jest](https://jestjs.io).

## Publishing

After building your library with `yarn nx build plugin-jitsu-analytics`, go to the dist folder `dist/packages/plugins/jitsu-analytics` and run `npm publish`.

## Installation

Install the Jitsu Analytics Plugin using your preferred package manager:

```bash
npm install @gauzy/plugin-jitsu-analytics
# or
yarn add @gauzy/plugin-jitsu-analytics
```
