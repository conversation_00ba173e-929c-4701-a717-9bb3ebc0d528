{"name": "plugin-jitsu-analytics", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/plugins/jitsu-analytics/src", "projectType": "library", "release": {"version": {"generatorOptions": {"packageRoot": "dist/{projectRoot}", "currentVersionResolver": "git-tag"}}}, "tags": [], "implicitDependencies": [], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/plugins/jitsu-analytics", "tsConfig": "packages/plugins/jitsu-analytics/tsconfig.lib.json", "packageJson": "packages/plugins/jitsu-analytics/package.json", "main": "packages/plugins/jitsu-analytics/src/index.ts", "assets": ["packages/plugins/jitsu-analytics/*.md"]}}, "nx-release-publish": {"options": {"packageRoot": "dist/{projectRoot}"}}, "lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/plugins/jitsu-analytics/jest.config.ts"}}}}