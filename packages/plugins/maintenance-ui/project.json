{"name": "plugin-maintenance-ui", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/plugins/maintenance-ui/src", "prefix": "lib", "projectType": "library", "tags": [], "implicitDependencies": [], "targets": {"build": {"executor": "@nx/angular:package", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "packages/plugins/maintenance-ui/ng-package.json", "tsConfig": "packages/plugins/maintenance-ui/tsconfig.json"}, "configurations": {"production": {"tsConfig": "packages/plugins/maintenance-ui/tsconfig.lib.prod.json"}, "development": {"tsConfig": "packages/plugins/maintenance-ui/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/plugins/maintenance-ui/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}