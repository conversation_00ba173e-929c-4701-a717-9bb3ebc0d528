# @gauzy/plugin-job-employee-ui

This library was generated with [Nx](https://nx.dev).

## Overview

The Plugin Job Employee UI is a Gauzy plugin that provides a user-friendly interface for managing job employees within the Gauzy platform.

## Building

Run `yarn nx build plugin-job-employee-ui` to build the library.

## Running unit tests

Run `yarn nx test plugin-job-employee-ui` to execute the unit tests.

## Publishing

After building your library with `yarn nx build plugin-job-employee-ui`, go to the dist folder `dist/packages/plugins/job-employee-ui` and run `npm publish`.

## Installation

To install the Plugin Job Employee UI, simply run the following command in your terminal:

```bash
npm install @gauzy/plugin-job-employee-ui
# or
yarn add @gauzy/plugin-job-employee-ui
```
