# @gauzy/plugin-videos-ui

This library was generated with [Nx](https://nx.dev).

## Overview

The **@gauzy/plugin-videos-ui** is a UI plugin for managing and displaying videos within the Gauzy ecosystem.
It provides a seamless integration for video-related functionality, making it easy to embed, stream, and organize videos.

## Features

✅ **Video Upload & Management** – Easily upload and manage video content.
✅ **Angular & Nebular Integration** – Built with Angular and Nebular UI components.
✅ **Multi-language Support** – Fully compatible with `@ngx-translate/core` for translations.

## Building

Run `yarn nx build plugin-videos-ui` to build the library.

## Running unit tests

Run `yarn nx test plugin-videos-ui` to execute the unit tests.

## Publishing

After building your library with `yarn nx build plugin-videos-ui`, go to the dist folder `dist/packages/plugins/videos-ui` and run `npm publish`.

## Installation

To install the `@gauzy/plugin-videos-ui` package, run the following command:

```bash
npm install @gauzy/plugin-videos-ui
# or using Yarn
yarn add @gauzy/plugin-videos-ui
```
