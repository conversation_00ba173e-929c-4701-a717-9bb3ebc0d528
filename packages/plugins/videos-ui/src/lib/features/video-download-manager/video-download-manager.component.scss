nb-card-body {
  padding: 1rem;
  gap: 1rem;
  display: flex;
  flex-direction: column;
}

.download-container {
  background: var(--gauzy-card-2);
  border-radius: var(--border-radius);
  padding: 1rem;
  background-color: var(--gauzy-card-2);
  width: 100%;
}

.download-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
}

.file-info {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  nb-icon {
    font-size: 32px;
  }
}

.file-details {
  display: flex;
  flex-direction: column;
}

.file-name {
  font-weight: bold;
}

.progress-section {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 1rem;
  width: 100%;
}

.progress-percentage {
  font-size: 12px;
  color: #666;
}

.actions {
  display: flex;
  gap: 10px;
}

nb-progress-bar {
  width: 100%;
}

:host {
  ::ng-deep {
    .progress-container {
      height: 5px !important;
    }
  }
}

.no-downloads,
.main {
  min-width: 400px;
}
