@let timeSlotId = (video$ | async)?.timeSlotId;

<!-- Structure -->

<div class="custom-card">
	<div #detail class="custom-card-body">
		<plug-video class="w-100"></plug-video>
		<nb-tabset>
			@if(showList$ | async) {
			<nb-tab tabTitle="Similar">
				@defer(on viewport) {
				<div nbInfiniteList [threshold]="500" [throttleTime]="300" (bottomThreshold)="fetchMoreVideos()"
					class="list">
					<plug-video-list [vertical]="true"></plug-video-list>
				</div>
				}@placeholder{
				<ngx-no-data-message class="no-data" status="info"
					message="Loading similar videos..."></ngx-no-data-message>
				}
			</nb-tab>
			}
			<nb-tab tabTitle="Camshots">
				@defer(on viewport) {
				<div class="list">
					<plug-camshot-list [timeSlotId]="timeSlotId"></plug-camshot-list>
				</div>
				}@placeholder {
				<ngx-no-data-message class="no-data" status="danger"
					message="Loading camshots..."></ngx-no-data-message>
				}
			</nb-tab>
			<nb-tab tabTitle="Soundshots">
				@defer(on viewport) {
				<div class="list">
					<plug-soundshot-list [timeSlotId]="timeSlotId"></plug-soundshot-list>
				</div>
				}@placeholder {
				<ngx-no-data-message class="no-data" status="info"
					message="Loading soundshots..."></ngx-no-data-message>
				}
			</nb-tab>
		</nb-tabset>
	</div>
</div>
