.custom-card {
  height: calc(100vh - 17.5rem);
  background-color: var(--gauzy-card-2);
  border-radius: 0 0 var(--border-radius) var(--border-radius);
  display: flex;
  flex-direction: column;

  .no-data {
    padding: 1rem;
    height: 100%;
    width: 100%;
  }

  .custom-card-header {
    padding-top: 1rem;
  }

  .custom-card-body {
    flex-grow: 10;
    padding: 1rem;
    overflow-y: auto;
  }

  ::ng-deep ngx-no-data-message {
    nb-spinner {
      border-radius: var(--border-radius);
    }
  }
}
