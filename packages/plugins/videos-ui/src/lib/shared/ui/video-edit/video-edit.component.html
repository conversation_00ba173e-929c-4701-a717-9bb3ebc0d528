<form [formGroup]="form" (ngSubmit)="submit()">
	<nb-card>
		<nb-card-header class="mb-0">
			<span class="cancel"><i (click)="close()" class="fas fa-times"></i></span>
			<h5 class="title">{{ 'PLUGIN.VIDEO.EDIT_METADATA' | translate }}</h5>
		</nb-card-header>
		<nb-card-body class="form">
			<!-- Title Input -->
			<div class="title">
				<input
					nbInput
					fullWidth
					[placeholder]="'FORM.PLACEHOLDERS.TITLE' | translate"
					formControlName="title"
				/>
				<div *ngIf="form.controls.title.invalid && form.controls.title.touched" class="error">
					<span *ngIf="form.controls.title.errors?.required">{{
						'PLUGIN.VIDEO.ERROR.REQUIRED' | translate
					}}</span>
					<span *ngIf="form.controls.title.errors?.minlength">{{
						'PLUGIN.VIDEO.ERROR.AT_LEAST_3' | translate
					}}</span>
					<span *ngIf="form.controls.title.errors?.maxlength">{{
						'PLUGIN.VIDEO.ERROR.AT_MOST_255' | translate
					}}</span>
				</div>
			</div>

			<!-- Description Input -->
			<div class="description">
				<textarea
					nbInput
					fullWidth
					[placeholder]="'FORM.PLACEHOLDERS.DESCRIPTION' | translate"
					formControlName="description"
					rows="5"
				></textarea>
				<div *ngIf="form.controls.description.invalid && form.controls.description.touched" class="error">
					<span *ngIf="form.controls.description.errors?.maxlength">{{
						'PLUGIN.VIDEO.ERROR.AT_MOST_1000' | translate
					}}</span>
				</div>
			</div>
		</nb-card-body>
		<nb-card-footer class="text-right mt-0">
			<button nbButton type="button" (click)="close()" outline status="basic">
				{{ 'BUTTONS.CANCEL' | translate }}
			</button>
			<button nbButton type="submit" [disabled]="form.invalid" status="success">
				{{ 'BUTTONS.SAVE' | translate }}
			</button>
		</nb-card-footer>
	</nb-card>
</form>
