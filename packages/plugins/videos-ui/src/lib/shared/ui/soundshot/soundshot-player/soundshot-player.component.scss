.soundshot-player {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  background: var(--gauzy-card-1, #fff);
  border-radius: calc(var(--border-radius) * 1.5);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  padding: 1rem 1.5rem;
  margin: 0 auto;

  &.deleted {
    background: rgba(255, 0, 0, 0.05);
  }
}

.soundshot-info {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  transition: all 0.3s ease;
}

.soundshot-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--gauzy-text-color-1, #222);
  margin-bottom: 0.1rem;
}

.soundshot-meta {
  font-size: 0.85rem;
  color: var(--gauzy-text-color-2);
  display: flex;
  gap: 0.5rem;
}

.player-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.player-btn {
  background: #f3f4f6;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  font-size: 1.3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background 0.2s;
}

.player-btn:hover {
  background: #e5e7eb;
}

.progress-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.2rem;
  cursor: pointer;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background: #e5e7eb;
  border-radius: 3px;
  position: relative;
  overflow: hidden;
}

.progress {
  height: 100%;
  background: #6366f1;
  border-radius: 3px;
  transition: width 0.2s;
}

.time-info {
  display: flex;
  justify-content: space-between;
  font-size: 0.8rem;
  color: #888;
}

.volume-slider {
  width: 80px;
  accent-color: #6366f1;
  box-shadow: unset;
}

.soundshot-cover {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0;
}

.cover-image {
  max-width: 100%;
  max-height: 48px;
  border-radius: var(--border-radius);
  object-fit: cover;
}

.soundshot-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

@media (max-width: 600px) {
  .soundshot-player {
    padding: 0.75rem 0.5rem;
    max-width: 100%;
  }

  .soundshot-info {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }

  .volume-slider {
    width: 50px;
  }
}

.actions {
  flex-shrink: 0;
  cursor: pointer;
  margin-left: auto;
}
