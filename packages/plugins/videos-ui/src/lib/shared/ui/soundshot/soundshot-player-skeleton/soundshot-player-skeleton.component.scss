.skeleton-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  width: 100%;
  margin: 0 auto;
  padding: 1rem;
  background: var(--gauzy-card-3);
  border-radius: var(--border-radius);
  position: relative;

  &.animate {
    .skeleton-element {
      animation: skeleton-loading 1.5s ease-in-out infinite;
    }
  }
}

.skeleton-info {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 1rem;
}

.skeleton-cover {
  width: 48px;
  height: 48px;
  border-radius: var(--border-radius);
  background: var(--skeleton-color);
  @extend .skeleton-element;
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

.skeleton-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.skeleton-title {
  height: 18px;
  width: 40%;
  border-radius: 4px;
  background: var(--skeleton-color);
  margin-bottom: 0.1rem;
  @extend .skeleton-element;
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

.skeleton-meta {
  display: flex;
  gap: 0.5rem;
}

.skeleton-meta-item {
  height: 12px;
  width: 32px;
  border-radius: 4px;
  background: var(--skeleton-color);
  @extend .skeleton-element;
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

.skeleton-player-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.skeleton-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--skeleton-color);
  flex-shrink: 0;
  @extend .skeleton-element;
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

.skeleton-progress-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.2rem;
}

.skeleton-progress-bar {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: var(--skeleton-color);
  @extend .skeleton-element;
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

.skeleton-time-info {
  display: flex;
  justify-content: space-between;
  font-size: 0.8rem;
  color: #888;
}

.skeleton-time {
  width: 32px;
  height: 12px;
  border-radius: 4px;
  background: var(--skeleton-color);
  @extend .skeleton-element;
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

.skeleton-volume-slider {
  width: 80px;
  height: 6px;
  border-radius: 3px;
  background: var(--skeleton-color);
  @extend .skeleton-element;
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

// Skeleton styling
.skeleton-element {
  background: var(--skeleton-color);
  background-size: 200% 100%;
  background-image: linear-gradient(
    90deg,
    var(--skeleton-color) 25%,
    var(--skeleton-highlight-color) 50%,
    var(--skeleton-color) 75%
  );
}

// CSS Variables for skeleton colors
:root {
  --skeleton-color: #e2e8f0;
  --skeleton-highlight-color: #f1f5f9;
}

// Dark theme skeleton colors
:host-context(.dark-theme) {
  --skeleton-color: #334155;
  --skeleton-highlight-color: #475569;
}

// Animation
@keyframes skeleton-loading {
  0% {
    background-color: var(--gauzy-card-2);
  }

  50% {
    background-color: var(--gauzy-card-1);
  }

  100% {
    background-color: var(--gauzy-card-2);
  }
}

// Responsive design
@media (max-width: 480px) {
  .skeleton-container {
    padding: 1rem;
    max-width: none;
    margin: 0;
  }

  .skeleton-title {
    width: 60%;
  }

  .skeleton-volume-slider {
    width: 50px;
  }

  .skeleton-info {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }
}
