// camshot-item.component.scss
.camshot-item {
  display: flex;
  align-items: flex-start;
  transition: all 0.3s ease;

  &.deleted {
    opacity: 0.7;
    background: rgba(255, 0, 0, 0.05);
  }

  &:hover {
    box-shadow: var(--gauzy-shadow);
    transform: translateY(-2px);
  }

  .thumbnail {
    width: 100%;
    height: auto;
    max-width: 50%;
    margin-right: 1rem;
    border-radius: var(--border-radius);
    overflow: hidden;
    flex-shrink: 0;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .details {
    flex-grow: 1;

    .title {
      margin: 0 0 0.5rem 0;
      font-size: 1rem;
      font-weight: 600;
    }

    .meta {
      display: flex;
      flex-direction: column;
      gap: 0.25rem;
      font-size: 0.875rem;
      color: var(--gauzy-text-color-2);

      nb-icon {
        margin-right: 0.25rem;
      }
    }
  }

  .actions {
    flex-shrink: 0;
    cursor: pointer;
  }
}

@media (max-width: 1435px) {
  .thumbnail {
    max-width: 300px !important;
    max-height: 200px;
  }
}
