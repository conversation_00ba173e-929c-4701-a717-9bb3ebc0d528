{"name": "@gauzy/plugin-videos-ui", "version": "0.1.0", "description": "A UI plugin for managing and displaying videos within the Gauzy ecosystem.", "author": {"name": "Ever Co. LTD", "email": "<EMAIL>", "url": "https://ever.co"}, "license": "AGPL-3.0", "repository": {"type": "git", "url": "https://github.com/ever-co/ever-gauzy", "directory": "packages/plugins/videos-ui"}, "bugs": {"url": "https://github.com/ever-co/ever-gauzy/issues"}, "homepage": "https://ever.co", "private": true, "scripts": {"lib:build": "yarn nx build plugin-videos-ui --configuration=development", "lib:build:prod": "yarn nx build plugin-videos-ui --configuration=production", "lib:watch": "yarn nx build plugin-videos-ui --watch --configuration=development"}, "peerDependencies": {"@angular/common": "^19.2.0", "@angular/core": "^19.2.0"}, "dependencies": {"@angular/forms": "^19.2.10", "@angular/router": "^19.2.10", "@gauzy/contracts": "^0.1.0", "@nebular/theme": "^15.0.0", "@ng-select/ng-select": "^14.8.1", "@ngneat/until-destroy": "^10.0.0", "@ngx-translate/core": "^16.0.4", "ngx-permissions": "^19.0.0", "@ngneat/effects-ng": "3.1.4", "rxjs": "^7.8.2", "tslib": "^2.6.2"}, "devDependencies": {"@types/jest": "29.5.14", "@types/node": "^20.14.9", "jest-preset-angular": "14.5.5"}, "keywords": ["gauzy", "video", "video-management", "video-ui", "angular", "ngx", "nebular", "plugin", "ui-plugin", "media"], "engines": {"node": ">=20.18.1", "yarn": ">=1.22.19"}, "sideEffects": false}