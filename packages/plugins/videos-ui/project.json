{"name": "plugin-videos-ui", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/plugins/videos-ui/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/angular:package", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "packages/plugins/videos-ui/ng-package.json"}, "configurations": {"production": {"tsConfig": "packages/plugins/videos-ui/tsconfig.lib.prod.json"}, "development": {"tsConfig": "packages/plugins/videos-ui/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/plugins/videos-ui/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}