# @gauzy/plugin-camshot

This library was generated with [Nx](https://nx.dev). This plugin provides functionality to upload and manage camera shots.

## Overview

This plugin enables camera shot management capabilities, including:

-   Camera shot upload and storage
-   Camera shot metadata management

## Building

Run `yarn nx build plugin-camshot` to build the library.

## Running unit tests

Run `yarn nx test plugin-camshot` to execute the unit tests via [Jest](https://jestjs.io).

## Publishing

After building your library with `yarn nx build plugin-camshot`, go to the dist folder `dist/packages/plugins/camshot` and run `npm publish`.

## Installation

Install the Camshot plugin using your preferred package manager:

```bash
npm install @gauzy/plugin-camshot
# or
yarn add @gauzy/plugin-camshot
```
