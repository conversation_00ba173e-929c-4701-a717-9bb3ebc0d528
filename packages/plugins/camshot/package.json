{"name": "@gauzy/plugin-camshot", "version": "0.1.0", "description": "This plugin provides functionality to upload and manage camera shots", "author": {"name": "Ever Co. LTD", "email": "<EMAIL>", "url": "https://ever.co"}, "repository": {"type": "git", "url": "https://github.com/ever-co/ever-gauzy", "directory": "packages/plugins/camshot"}, "bugs": {"url": "https://github.com/ever-co/ever-gauzy/issues"}, "homepage": "https://ever.co", "license": "AGPL-3.0", "private": true, "type": "commonjs", "main": "./src/index.js", "typings": "./src/index.d.ts", "scripts": {"lib:build": "yarn nx build plugin-camshot", "lib:build:prod": "yarn nx build plugin-camshot", "lib:watch": "yarn nx build plugin-camshot --watch"}, "dependencies": {"@gauzy/contracts": "^0.1.0", "@gauzy/core": "^0.1.0", "@gauzy/plugin": "^0.1.0", "tslib": "^2.6.2"}, "devDependencies": {"@types/express": "^5.0.1", "@types/jest": "29.5.14", "@types/node": "^20.14.9", "typescript": "^5.8.3"}, "keywords": ["camshot", "camshot-upload", "camshot-management", "media", "media-upload", "camshot-library", "plugin", "gauzy", "camshot-plugin", "camshot-plugins"], "engines": {"node": ">=20.18.1", "yarn": ">=1.22.19"}, "sideEffects": false}