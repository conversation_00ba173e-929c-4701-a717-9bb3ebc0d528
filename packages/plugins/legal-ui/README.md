# @gauzy/plugin-legal-ui

This library was generated with [Nx](https://nx.dev).

## Building

Run `yarn nx build plugin-legal-ui` to build the library.

## Running unit tests

Run `yarn nx test plugin-legal-ui` to execute the unit tests via [Jest](https://jestjs.io).

## Publishing

After building your library with `yarn nx build plugin-legal-ui`, go to the dist folder `dist/packages/plugins/legal-ui` and run `npm publish`.

## Installation

Install the Job Proposal UI Plugin using your preferred package manager:

```bash
npm install @gauzy/plugin-legal-ui
# or
yarn add @gauzy/plugin-legal-ui
```
