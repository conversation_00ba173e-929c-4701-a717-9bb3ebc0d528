{"name": "@gauzy/plugin-legal-ui", "version": "0.1.0", "description": "A plugin for the legal UI in the Ever Gauzy platform, providing components and modules for managing legal documents and agreements.", "author": {"name": "Ever Co. LTD", "email": "<EMAIL>", "url": "https://ever.co"}, "repository": {"type": "git", "url": "https://github.com/ever-co/ever-gauzy", "directory": "packages/plugins/legal-ui"}, "bugs": {"url": "https://github.com/ever-co/ever-gauzy/issues"}, "homepage": "https://ever.co", "license": "AGPL-3.0", "private": true, "scripts": {"lib:build": "yarn nx build plugin-legal-ui --configuration=development", "lib:build:prod": "yarn nx build plugin-legal-ui --configuration=production", "lib:watch": "yarn nx build plugin-legal-ui --watch --configuration=development"}, "peerDependencies": {"@angular/common": "^19.2.0", "@angular/core": "^19.2.0"}, "dependencies": {"@angular/router": "^19.2.10", "@nebular/auth": "^15.0.0", "@ngx-translate/core": "^16.0.4", "tslib": "^2.6.2"}, "devDependencies": {"@types/jest": "29.5.14", "@types/node": "^20.14.9", "jest-preset-angular": "14.5.5"}, "keywords": ["legal", "plugin", "angular", "ui", "gauzy"], "engines": {"node": ">=20.18.1", "yarn": ">=1.22.19"}, "sideEffects": false}