{"compilerOptions": {"target": "es2022", "useDefineForClassFields": false, "forceConsistentCasingInFileNames": true, "strict": false, "noImplicitOverride": false, "noPropertyAccessFromIndexSignature": false, "noImplicitReturns": false, "noFallthroughCasesInSwitch": false, "baseUrl": ".", "paths": {"@gauzy/ui-core/*": ["./../../../dist/packages/ui-core/*/index.d.ts"]}}, "files": [], "include": [], "references": [{"path": "./tsconfig.lib.json"}, {"path": "./tsconfig.spec.json"}], "extends": "../../../tsconfig.base.json", "angularCompilerOptions": {"enableI18nLegacyMessageIdFormat": false, "strictInjectionParameters": true, "strictInputAccessModifiers": true, "strictTemplates": true}}