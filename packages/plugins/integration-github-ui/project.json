{"name": "plugin-integration-github-ui", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/plugins/integration-github-ui/src", "prefix": "lib", "projectType": "library", "tags": [], "implicitDependencies": [], "targets": {"build": {"executor": "@nx/angular:package", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "packages/plugins/integration-github-ui/ng-package.json", "tsConfig": "packages/plugins/integration-github-ui/tsconfig.json"}, "configurations": {"production": {"tsConfig": "packages/plugins/integration-github-ui/tsconfig.lib.prod.json"}, "development": {"tsConfig": "packages/plugins/integration-github-ui/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/plugins/integration-github-ui/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}