<nb-card class="card-scroll" [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
	<nb-card-header class="card-header-title">
		<div class="card-header-title">
			<h5>
				<ngx-back-navigation
					class="float-left"
					[haveLink]="true"
					(click)="navigateToIntegrations()"
				></ngx-back-navigation>
				{{ 'INTEGRATIONS.GITHUB_PAGE.TITLE' | translate }}
			</h5>
		</div>
		<div>
			<div>
				<ng-template [ngxPermissionsOnly]="['INTEGRATION_EDIT']">
					<button nbButton status="primary" class="mr-2" size="small" (click)="navigateToResetIntegration()">
						<div class="sync-container">
							<nb-icon class="sync" icon="sync-outline"></nb-icon>
							{{ 'BUTTONS.RESET_INTEGRATION' | translate }}
						</div>
					</button>
				</ng-template>
				<button
					nbButton
					size="small"
					[nbPopoverPlacement]="'bottom'"
					[nbPopoverTrigger]="'noop'"
					[nbPopover]="settingsPopover"
					(click)="openSettingModalPopover()"
				>
					<nb-icon icon="settings-2-outline"></nb-icon>
				</button>
			</div>
		</div>
	</nb-card-header>
	<nb-card-body class="p-0" *ngIf="integration$ | async">
		<ng-container
			*ngIf="integration?.isActive; then ngxIntegrationSync; else ngxIntegrationSyncExcept"
		></ng-container>
		<ng-template #ngxIntegrationSyncExcept>
			<div>
				<!-- Content to display if the user does not have 'active' integration -->
			</div>
		</ng-template>
		<ng-template #ngxIntegrationSync>
			<nb-tabset class="mt-4" (changeTab)="onChangeTab($event)">
				<nb-tab
					[tabTitle]="'INTEGRATIONS.GITHUB_PAGE.TAB.AUTO_SYNC' | translate"
					[active]="true"
					[tabId]="syncTabsEnum.AUTO_SYNC"
				>
					<ng-container *ngIf="(nbTab$ | async) === syncTabsEnum.AUTO_SYNC">
						<ng-template [ngIf]="integration">
							<nb-card>
								<nb-card-header>
									<div class="row">
										<div class="col-4">
											<ngx-github-repository-selector
												id="auto-repository"
												[sourceId]="(project$ | async)?.customFields?.repository?.repositoryId"
												[integration]="integration"
												(onChanged)="selectAutoRepository($event)"
												[selected]="true"
											></ngx-github-repository-selector>
										</div>
										<div class="sync-arrow-right">
											<i class="fas fa-arrow-right"></i>
										</div>
										<div class="col-4">
											<ga-project-selector
												[placeholder]="'FORM.PLACEHOLDERS.SELECT_PROJECT' | translate"
												[label]="'FORM.LABELS.PROJECT'"
												[defaultSelected]="false"
												[showAllOption]="false"
												[skipGlobalChange]="true"
												[shortened]="true"
												(onChanged)="selectedProject$.next($event)"
											></ga-project-selector>
										</div>
										<div class="sync-process-button">
											<button
												[disabled]="!project || !repository"
												nbButton
												status="success"
												class="mr-2"
												size="small"
												debounceClick
												(throttledClick)="autoSyncIssues()"
											>
												<nb-icon
													class="sync"
													icon="sync-outline"
													[ngClass]="{ spin: syncing }"
												></nb-icon>
												{{
													(syncing ? 'BUTTONS.AUTO_SYNCING' : 'BUTTONS.AUTO_SYNC') | translate
												}}
											</button>
										</div>
									</div>
								</nb-card-header>
								<nb-card-body>
									<div class="table-scroll-container">
										<div class="mb-2">
											<h6 class="subtitle">
												{{ 'INTEGRATIONS.GITHUB_PAGE.AUTO_SYNC_TABLE_LABEL' | translate }}
											</h6>
										</div>
										<angular2-smart-table
											[settings]="settingsSmartTableProjects"
											[source]="projects$ | async"
											style="cursor: pointer"
										></angular2-smart-table>
									</div>
								</nb-card-body>
							</nb-card>
						</ng-template>
					</ng-container>
				</nb-tab>
				<nb-tab
					[tabTitle]="'INTEGRATIONS.GITHUB_PAGE.TAB.MANUAL_SYNC' | translate"
					[tabId]="syncTabsEnum.MANUAL_SYNC"
				>
					<ng-container *ngIf="(nbTab$ | async) === syncTabsEnum.MANUAL_SYNC">
						<ng-template [ngIf]="integration">
							<nb-card>
								<nb-card-header>
									<div class="row">
										<div class="col-4">
											<ngx-github-repository-selector
												id="manual-repository"
												[sourceId]="(project$ | async)?.customFields?.repository?.repositoryId"
												[integration]="integration"
												(onChanged)="selectManualRepository($event)"
												[selected]="true"
											></ngx-github-repository-selector>
										</div>
										<div class="sync-arrow-right">
											<i class="fas fa-arrow-right"></i>
										</div>
										<div class="col-4">
											<ga-project-selector
												[placeholder]="'FORM.PLACEHOLDERS.SELECT_PROJECT' | translate"
												[label]="'FORM.LABELS.PROJECT'"
												[defaultSelected]="false"
												[showAllOption]="false"
												[skipGlobalChange]="true"
												[shortened]="true"
												(onChanged)="selectedProject$.next($event)"
											></ga-project-selector>
										</div>
										<div class="sync-process-button">
											<button
												[disabled]="selectedIssues.length == 0 || !project || !repository"
												nbButton
												status="success"
												class="mr-2"
												size="small"
												debounceClick
												(throttledClick)="manualSyncIssues()"
											>
												<div class="sync-container">
													<nb-icon
														class="sync"
														icon="sync-outline"
														[ngClass]="{ spin: syncing }"
													></nb-icon>
													{{ (syncing ? 'BUTTONS.SYNCING' : 'BUTTONS.SYNC') | translate }}
													{{ 'BUTTONS.SELECTED_TASKS' | translate }}
												</div>
											</button>
										</div>
									</div>
								</nb-card-header>
								<nb-card-body>
									<div class="table-scroll-container">
										<angular2-smart-table
											[settings]="settingsSmartTableIssues"
											[source]="issues"
											(userRowSelect)="selectIssues($event)"
											style="cursor: pointer"
											#issuesTable
										></angular2-smart-table>
									</div>
									<ng-container *ngIf="(page$ | async)">
										<div class="pagination-container">
											<ng-container *ngIf="pagination.totalItems > minItemPerPage">
												<ga-pagination
													(selectedOption)="onUpdateOption($event)"
													(selectedPage)="onPageChange($event)"
													[doEmit]="false"
													[totalItems]="pagination?.totalItems"
													[itemsPerPage]="pagination?.itemsPerPage"
													[activePage]="pagination?.activePage"
												></ga-pagination>
											</ng-container>
										</div>
									</ng-container>
								</nb-card-body>
							</nb-card>
						</ng-template>
					</ng-container>
				</nb-tab>
			</nb-tabset>
		</ng-template>
	</nb-card-body>
</nb-card>

<!-- Integration Setting Popover -->
<ng-template #settingsPopover>
	<ngx-github-settings [integration]="integration" (canceled)="openSettingModalPopover()"></ngx-github-settings>
</ng-template>
