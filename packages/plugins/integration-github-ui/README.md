# @gauzy/plugin-integration-github-ui

This library was generated with [Nx](https://nx.dev).

## Building

Run `yarn nx build plugin-integration-github-ui` to build the library.

## Running unit tests

Run `yarn nx test plugin-integration-github-ui` to execute the unit tests.

## Publishing

After building your library with `yarn nx build plugin-integration-github-ui`, go to the dist folder `dist/packages/plugins/integration-github-ui` and run `npm publish`.

## Installation

Install the Integration Github UI Plugin using your preferred package manager:

```bash
npm install @gauzy/plugin-integration-github-ui
# or
yarn add @gauzy/plugin-integration-github-ui
```
