# @gauzy/plugin-job-matching-ui

This library was generated with [Nx](https://nx.dev).

## Building

Run `yarn nx build plugin-job-matching-ui` to build the library.

## Running unit tests

Run `yarn nx test plugin-job-matching-ui` to execute the unit tests.

## Publishing

After building your library with `yarn nx build plugin-job-matching-ui`, go to the dist folder `dist/packages/plugins/job-matching-ui` and run `npm publish`.

## Installation

To install the Plugin Job Matching UI, simply run the following command in your terminal:

```bash
npm install @gauzy/plugin-job-matching-ui
# or
yarn add @gauzy/plugin-job-matching-ui
```
