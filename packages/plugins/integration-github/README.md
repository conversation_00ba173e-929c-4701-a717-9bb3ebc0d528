# @gauzy/plugin-integration-github

This library was generated with [Nx](https://nx.dev).

## Building

Run `yarn nx build plugin-integration-github` to build the library.

## Running unit tests

Run `yarn nx test plugin-integration-github` to execute the unit tests via [Jest](https://jestjs.io).

## Publishing

After building your library with `yarn nx build plugin-integration-github`, go to the dist folder `cd dist/packages/plugins/integration-github` and run `npm publish`.

## Installation

Install the Integration Github Plugin using your preferred package manager:

```bash
npm install @gauzy/plugin-integration-github
# or
yarn add @gauzy/plugin-integration-github
```
