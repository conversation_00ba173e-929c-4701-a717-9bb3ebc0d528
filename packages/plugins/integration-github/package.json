{"name": "@gauzy/plugin-integration-github", "version": "0.1.0", "description": "Ever Gauzy Platform plugin for integration with Github APIs", "author": {"name": "Ever Co. LTD", "email": "<EMAIL>", "url": "https://ever.co"}, "repository": {"type": "git", "url": "https://github.com/ever-co/ever-gauzy", "directory": "packages/plugins/integration-github"}, "bugs": {"url": "https://github.com/ever-co/ever-gauzy/issues"}, "homepage": "https://ever.co", "license": "AGPL-3.0", "private": true, "type": "commonjs", "main": "./src/index.js", "typings": "./src/index.d.ts", "scripts": {"lib:build": "yarn nx build plugin-integration-github", "lib:build:prod": "yarn nx build plugin-integration-github", "lib:watch": "yarn nx build plugin-integration-github --watch"}, "dependencies": {"@gauzy/config": "^0.1.0", "@gauzy/constants": "^0.1.0", "@gauzy/contracts": "^0.1.0", "@gauzy/core": "^0.1.0", "@gauzy/plugin": "^0.1.0", "@gauzy/utils": "^0.1.0", "@mikro-orm/nestjs": "^6.1.1", "@nestjs/axios": "github:ever-co/nestjs-axios#master", "@nestjs/cache-manager": "^3.0.1", "@nestjs/common": "^11.1.0", "@nestjs/core": "^11.1.0", "@nestjs/cqrs": "^11.0.3", "@nestjs/typeorm": "^11.0.0", "@nestjs/swagger": "^11.1.5", "@octokit/rest": "^20.0.2", "cache-manager": "^6.4.2", "chalk": "^4.1.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "express": "^5.1.0", "moment": "^2.30.1", "octokit": "^3.1.2", "pino-std-serializers": "^6.2.2", "probot": "^12.3.3", "rxjs": "^7.8.2", "smee-client": "^1.2.3", "tslib": "^2.6.2", "typeorm": "^0.3.24", "underscore": "^1.13.3", "uuid": "^11.1.0"}, "devDependencies": {"@types/jest": "29.5.14", "@types/node": "^20.14.9", "typescript": "^5.8.3"}, "keywords": ["github", "plugin", "integration", "<PERSON>", "NestJS", "typescript", "API", "automation", "webhooks"], "engines": {"node": ">=20.18.1", "yarn": ">=1.22.19"}, "sideEffects": false}