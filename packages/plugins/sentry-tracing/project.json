{"name": "plugin-sentry", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/plugins/sentry-tracing/src", "projectType": "library", "release": {"version": {"generatorOptions": {"packageRoot": "dist/{projectRoot}", "currentVersionResolver": "git-tag"}}}, "tags": [], "implicitDependencies": [], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/plugins/sentry-tracing", "tsConfig": "packages/plugins/sentry-tracing/tsconfig.lib.json", "packageJson": "packages/plugins/sentry-tracing/package.json", "main": "packages/plugins/sentry-tracing/src/index.ts", "assets": ["packages/plugins/sentry-tracing/*.md"]}}, "nx-release-publish": {"options": {"packageRoot": "dist/{projectRoot}"}}, "lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/plugins/sentry-tracing/jest.config.ts"}}}}