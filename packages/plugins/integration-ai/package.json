{"name": "@gauzy/plugin-integration-ai", "version": "0.1.0", "description": "Enhance Ever Gauzy Platform with advanced AI integration capabilities for streamlined business management and automation.", "author": {"name": "Ever Co. LTD", "email": "<EMAIL>", "url": "https://ever.co"}, "repository": {"type": "git", "url": "https://github.com/ever-co/ever-gauzy", "directory": "packages/plugins/integration-ai"}, "bugs": {"url": "https://github.com/ever-co/ever-gauzy/issues"}, "homepage": "https://ever.co", "license": "AGPL-3.0", "private": true, "type": "commonjs", "main": "./src/index.js", "typings": "./src/index.d.ts", "scripts": {"lib:build": "yarn nx build plugin-integration-ai", "lib:build:prod": "yarn nx build plugin-integration-ai", "lib:watch": "yarn nx build plugin-integration-ai --watch"}, "dependencies": {"@apollo/client": "^3.13.8", "@gauzy/contracts": "^0.1.0", "@gauzy/core": "^0.1.0", "@gauzy/utils": "^0.1.0", "@nestjs/axios": "github:ever-co/nestjs-axios#master", "@nestjs/cache-manager": "^3.0.1", "@nestjs/common": "^11.1.0", "@nestjs/config": "^4.0.2", "@nestjs/cqrs": "^11.0.3", "@nestjs/swagger": "^11.1.5", "axios": "^1.9.0", "cache-manager": "^6.4.2", "chalk": "^4.1.0", "express": "^5.1.0", "form-data": "^4.0.1", "rxjs": "^7.8.2"}, "devDependencies": {"@types/jest": "29.5.14", "@types/node": "^20.14.9", "typescript": "^5.8.3"}, "keywords": ["<PERSON>", "Gauzy AI", "plugin", "integration", "platform", "management", "business", "tool", "automation"], "engines": {"node": ">=20.18.1", "yarn": ">=1.22.19"}, "sideEffects": false}