# @gauzy/plugin-soundshot

This library was generated with [Nx](https://nx.dev). This plugin provides functionality to upload and manage sound shots.

## Overview

This plugin enables sound shot management capabilities, including:

- Sound shot upload and storage
- Sound shot metadata management

## Building

Run `yarn nx build plugin-soundshot` to build the library.

## Running unit tests

Run `yarn nx test plugin-soundshot` to execute the unit tests via [Jest](https://jestjs.io).

## Publishing

After building your library with `yarn nx build plugin-soundshot`, go to the dist folder `dist/packages/plugins/soundshot` and run `npm publish`.

## Installation

Install the Soundshot plugin using your preferred package manager:

```bash
npm install @gauzy/plugin-soundshot
# or
yarn add @gauzy/plugin-soundshot
```
