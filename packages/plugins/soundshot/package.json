{"name": "@gauzy/plugin-soundshot", "version": "0.1.0", "description": "This plugin provides functionality to upload and manage sound shots", "author": {"name": "Ever Co. LTD", "email": "<EMAIL>", "url": "https://ever.co"}, "repository": {"type": "git", "url": "https://github.com/ever-co/ever-gauzy", "directory": "packages/plugins/soundshot"}, "bugs": {"url": "https://github.com/ever-co/ever-gauzy/issues"}, "homepage": "https://ever.co", "license": "AGPL-3.0", "private": true, "type": "commonjs", "main": "./src/index.js", "typings": "./src/index.d.ts", "scripts": {"lib:build": "yarn nx build plugin-soundshot", "lib:build:prod": "yarn nx build plugin-soundshot", "lib:watch": "yarn nx build plugin-soundshot --watch"}, "dependencies": {"@gauzy/contracts": "^0.1.0", "@gauzy/core": "^0.1.0", "@gauzy/plugin": "^0.1.0", "tslib": "^2.6.2"}, "devDependencies": {"@types/express": "^5.0.1", "@types/jest": "29.5.14", "@types/node": "^20.14.9", "typescript": "^5.8.3"}, "keywords": ["soundshot", "soundshot-upload", "soundshot-management", "media", "media-upload", "soundshot-library", "plugin", "gauzy", "soundshot-plugin"], "engines": {"node": ">=20.18.1", "yarn": ">=1.22.19"}, "sideEffects": false}