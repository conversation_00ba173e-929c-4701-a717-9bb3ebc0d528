{"name": "@gauzy/plugin-onboarding-ui", "version": "0.1.0", "description": "A plugin for the onboarding UI in the Ever Gauzy platform, providing user-friendly components and modules to facilitate the onboarding process.", "author": {"name": "Ever Co. LTD", "email": "<EMAIL>", "url": "https://ever.co"}, "repository": {"type": "git", "url": "https://github.com/ever-co/ever-gauzy", "directory": "packages/plugins/onboarding-ui"}, "bugs": {"url": "https://github.com/ever-co/ever-gauzy/issues"}, "homepage": "https://ever.co", "license": "AGPL-3.0", "private": true, "scripts": {"lib:build": "yarn nx build plugin-onboarding-ui --configuration=development", "lib:build:prod": "yarn nx build plugin-onboarding-ui --configuration=production", "lib:watch": "yarn nx build plugin-onboarding-ui --watch --configuration=development"}, "peerDependencies": {"@angular/common": "^19.2.0", "@angular/core": "^19.2.0"}, "dependencies": {"@angular/router": "^19.2.10", "@gauzy/contracts": "^0.1.0", "@nebular/theme": "^15.0.0", "@ngneat/until-destroy": "^10.0.0", "@ngx-translate/core": "^16.0.4", "ngx-permissions": "^19.0.0", "rxjs": "^7.8.2", "tslib": "^2.6.2"}, "devDependencies": {"@types/jest": "29.5.14", "@types/node": "^20.14.9", "jest-preset-angular": "14.5.5"}, "keywords": ["onboarding", "plugin", "angular", "ui", "gauzy"], "engines": {"node": ">=20.18.1", "yarn": ">=1.22.19"}, "sideEffects": false}