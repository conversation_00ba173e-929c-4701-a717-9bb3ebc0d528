.logo {
	display: flex;
	align-items: center;
	flex-direction: column;
	justify-content: center;
	margin-bottom: 32px;

	h4 {
		font-size: 20px;
		margin-top: 20px;
	}

	h4, h6 {
		font-weight: 600;
	}

	h6 {
		font-size: 16px;
	}

	ngx-gauzy-logo::ng-deep {
		object {
			width: 216px;
		}
	}
}

.shortcut-card {
	margin-bottom: 20px;
	cursor: pointer;
	background-color: var(--gauzy-card-2);
	nb-card-body {
		border-radius: 0 0 var(--border-radius) var(--border-radius);
	}

	&:hover {
		transform: translateY(-1px);
		box-shadow: 0 2px 9px 0 rgba(0, 0, 0, 0.26);
	}
}

.centered {
	justify-content: center;
}

.switch-theme {
	position: absolute;
	right: 0;
	top: 0;
}

:host {
	position: relative;
}
