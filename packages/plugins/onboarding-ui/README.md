# @gauzy/plugin-onboarding-ui

This library was generated with [Nx](https://nx.dev). It contains the Onboarding UI Plugin for the Gauzy platform.

## Features

- **Onboarding UI Plugin:** Provides a user-friendly interface for onboarding new users into the Gauzy platform.

## Building

Run `yarn nx build plugin-onboarding-ui` to build the library.

## Running unit tests

Run `yarn nx test plugin-onboarding-ui` to execute the unit tests.

## Publishing

After building your library with `yarn nx build plugin-onboarding-ui`, go to the dist folder `cd dist/packages/plugins/onboarding-ui` and run `npm publish`.

## Installation

Install the Onboarding UI Plugin using your preferred package manager:

```bash
npm install @gauzy/plugin-onboarding-ui
# or
yarn add @gauzy/plugin-onboarding-ui
```
