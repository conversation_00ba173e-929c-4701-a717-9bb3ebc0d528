# @gauzy/plugin-integration-hubstaff

This library was generated with [Nx](https://nx.dev).

## Overview

The Integration Hubstaff plugin is a Gauzy plugin that provides a user-friendly interface for integrating Hubstaff with the Gauzy platform.

## Features

- **Integration Hubstaff**: Provides a user-friendly interface for integrating Hubstaff with the Gauzy platform.

## Building

Run `yarn nx build plugin-integration-hubstaff` to build the library.

## Running unit tests

Run `yarn nx test plugin-integration-hubstaff` to execute the unit tests via [Jest](https://jestjs.io).

## Publishing

After building your library with `yarn nx build plugin-integration-hubstaff`, go to the dist folder `dist/packages/plugins/integration-hubstaff` and run `npm publish`.

## Installation

Install the Integration Hubstaff plugin using your preferred package manager:

```bash
npm install @gauzy/plugin-integration-hubstaff
# or
yarn add @gauzy/plugin-integration-hubstaff
```
