{"name": "plugin-integration-hubstaff", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/plugins/integration-hubstaff/src", "projectType": "library", "release": {"version": {"generatorOptions": {"packageRoot": "dist/{projectRoot}", "currentVersionResolver": "git-tag"}}}, "tags": [], "implicitDependencies": [], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/plugins/integration-hubstaff", "tsConfig": "packages/plugins/integration-hubstaff/tsconfig.lib.json", "packageJson": "packages/plugins/integration-hubstaff/package.json", "main": "packages/plugins/integration-hubstaff/src/index.ts", "assets": ["packages/plugins/integration-hubstaff/*.md"]}}, "nx-release-publish": {"options": {"packageRoot": "dist/{projectRoot}"}}, "lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/plugins/integration-hubstaff/jest.config.ts"}}}}