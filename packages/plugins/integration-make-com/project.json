{"name": "plugin-integration-make-com", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/plugins/integration-make-com/src", "projectType": "library", "release": {"version": {"generatorOptions": {"packageRoot": "dist/{projectRoot}", "currentVersionResolver": "git-tag"}}}, "tags": [], "implicitDependencies": [], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/plugins/integration-make-com", "tsConfig": "packages/plugins/integration-make-com/tsconfig.lib.json", "packageJson": "packages/plugins/integration-make-com/package.json", "main": "packages/plugins/integration-make-com/src/index.ts", "assets": ["packages/plugins/integration-make-com/*.md"]}}, "nx-release-publish": {"options": {"packageRoot": "dist/{projectRoot}"}}, "lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/plugins/integration-make-com/jest.config.ts"}}}}