<div class="contracts-container">
	<div class="mb-3 mt-3">
		<button nbButton [disabled]="!selectedContracts.length" (click)="manageEntitiesSync()" status="primary">
			<nb-icon class="mr-1" icon="edit-outline"></nb-icon>
			{{ 'BUTTONS.MANAGE' | translate }}
		</button>
	</div>
	<angular2-smart-table
		style="cursor: pointer"
		[settings]="smartTableSettings"
		(userRowSelect)="selectContract($event)"
		[source]="contracts$ | async"
	></angular2-smart-table>
</div>
