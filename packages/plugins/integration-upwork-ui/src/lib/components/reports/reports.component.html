<div class="reports-container">
	<div class="mb-3 mt-3">
		<div class="input-group">
			<div class="input-group-append">
				<button nbButton status="primary" (click)="previousMonth()">
					<nb-icon icon="arrow-ios-back-outline"></nb-icon>
				</button>
			</div>
			<div class="ml-1">
				<input
					nbInput
					[placeholder]="'INTEGRATIONS.UPWORK_PAGE.DATE_RANGE_PLACEHOLDER' | translate"
					[nbDatepicker]="rangePicker"
					[value]="displayDate"
				/>
				<nb-rangepicker
					#rangePicker
					[range]="defaultDateRange$ | async"
					[max]="today"
					(rangeChange)="handleRangeChange($event)"
				></nb-rangepicker>
			</div>
			<div class="input-group-append ml-1">
				<button
					nbButton
					status="primary"
					(click)="nextMonth()"
					[disabled]="isNextButtonDisabled()"
				>
					<nb-icon icon="arrow-ios-forward-outline"></nb-icon>
				</button>
			</div>
		</div>
	</div>
	<angular2-smart-table
		style="cursor: pointer"
		[settings]="settingsSmartTable"
		[source]="reports$ | async"
	></angular2-smart-table>
</div>
