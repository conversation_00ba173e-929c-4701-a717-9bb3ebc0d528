# @gauzy/plugin-integration-upwork-ui

This library was generated with [Nx](https://nx.dev).

## Building

Run `yarn nx build plugin-integration-upwork-ui` to build the library.

## Running unit tests

Run `yarn nx test plugin-integration-upwork-ui` to execute the unit tests via [Jest](https://jestjs.io).

## Publishing

After building your library with `yarn nx build plugin-integration-upwork-ui`, go to the dist folder `dist/packages/plugins/integration-upwork-ui` and run `npm publish`.

## Installation

Install the Integration Upwork UI Plugin using your preferred package manager:

```bash
npm install @gauzy/plugin-integration-upwork-ui
# or
yarn add @gauzy/plugin-integration-upwork-ui
```
