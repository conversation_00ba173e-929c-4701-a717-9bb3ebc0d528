# @gauzy/plugin-changelog

This library was generated with [Nx](https://nx.dev). It contains the authentication for the Gauzy API platform.

## Building

Run `yarn nx build plugin-changelog` to build the library.

## Running unit tests

Run `yarn  nx test plugin-changelog` to execute the unit tests via [Jest](https://jestjs.io).

## Publishing

After building your library with `yarn nx build plugin-changelog`, go to the dist folder `dist/packages/plugins/changelog` and run `npm publish`.

## Installation

To install the changelog plugin Library, simply run the following command in your terminal:

```bash
npm install @gauzy/plugin-changelog
# or
yarn add @gauzy/plugin-changelog
```
