import { IChangelog } from '@gauzy/contracts';
import { <PERSON><PERSON><PERSON>ler, ICommandHandler } from '@nestjs/cqrs';
import { ChangelogService } from '../../changelog.service';
import { ChangelogCreateCommand } from '../changelog.create.command';

@CommandHandler(ChangelogCreateCommand)
export class Changelog<PERSON>reateHandler
	implements ICommandHandler<ChangelogCreateCommand> {
	constructor(private readonly changelogService: ChangelogService) {}

	public async execute(command: ChangelogCreateCommand): Promise<IChangelog> {
		const { input } = command;
		if (input.hasOwnProperty('id')) {
			delete input['id'];
		}
		return this.changelogService.create(input);
	}
}
