{"name": "@gauzy/plugin-posthog-ui", "version": "0.1.0", "description": "A UI plugin for integrating PostHog analytics within the Gauzy ecosystem.", "author": {"name": "Ever Co. LTD", "email": "<EMAIL>", "url": "https://ever.co"}, "license": "AGPL-3.0", "repository": {"type": "git", "url": "https://github.com/ever-co/ever-gauzy", "directory": "packages/plugins/posthog-ui"}, "bugs": {"url": "https://github.com/ever-co/ever-gauzy/issues"}, "homepage": "https://ever.co", "private": true, "scripts": {"lib:build": "yarn nx build plugin-posthog-ui --configuration=development", "lib:build:prod": "yarn nx build plugin-posthog-ui --configuration=production", "lib:watch": "yarn nx build plugin-posthog-ui --watch --configuration=development"}, "peerDependencies": {"@angular/common": "^19.2.0", "@angular/core": "^19.2.0"}, "dependencies": {"posthog-js": "^1.236.2", "rxjs": "^7.8.0", "tslib": "^2.6.2"}, "devDependencies": {"@types/jest": "29.5.14", "@types/node": "^20.14.9", "jest-preset-angular": "14.5.5"}, "keywords": ["gauzy", "angular", "posthog", "analytics", "tracking", "feature-flags", "plugin", "ui-plugin"], "engines": {"node": ">=20.18.1", "yarn": ">=1.22.19"}, "sideEffects": false}