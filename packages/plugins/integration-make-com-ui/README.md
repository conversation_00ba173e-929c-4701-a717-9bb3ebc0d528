# @gauzy/plugin-integration-make-com-ui

This library was generated with [Nx](https://nx.dev).

## Building

Run `yarn nx build plugin-integration-make-com-ui` to build the library.

## Running unit tests

Run `yarn nx test plugin-integration-make-com-ui` to execute the unit tests via [Jest](https://jestjs.io).

## Publishing

After building your library with `yarn nx build plugin-integration-make-com-ui`,
go to the dist folder `dist/packages/plugins/integration-make-com-ui`
and run `npm publish`.

## Installation

Install the Integration Make.com UI Plugin using your preferred package manager:

```bash
npm install @gauzy/plugin-integration-make-com-ui
# or
yarn add @gauzy/plugin-integration-make-com-ui
```
