<nb-card class="card-scroll">
	<nb-card-header class="d-flex">
		<ngx-back-navigation></ngx-back-navigation>
		<h5>{{ 'MENU.MAKE_COM' | translate }}</h5>
	</nb-card-header>
	<nb-card-body>
		<form class="col-xl-6 col-12" [formGroup]="form" (ngSubmit)="startAuthorization()">
			<div class="form-group">
				<label for="clientId" class="label">{{ 'INTEGRATIONS.MAKE_COM_PAGE.CLIENT_ID' | translate }}</label>
				<input
					fullWidth
					id="clientId"
					formControlName="clientId"
					type="text"
					nbInput
					[placeholder]="'FORM.PLACEHOLDERS.MAKE_COM_CLIENT_ID' | translate"
				/>
				<div
					*ngIf="form.get('clientId')?.touched && form.get('clientId')?.errors?.required"
					class="text-danger"
				>
					{{ 'VALIDATION.FIELD_REQUIRED' | translate }}
				</div>
			</div>
			<div class="form-group">
				<label for="clientSecret" class="label">{{
					'INTEGRATIONS.MAKE_COM_PAGE.CLIENT_SECRET' | translate
				}}</label>
				<input
					fullWidth
					id="clientSecret"
					formControlName="clientSecret"
					type="password"
					nbInput
					[placeholder]="'FORM.PLACEHOLDERS.MAKE_COM_CLIENT_SECRET' | translate"
				/>
				<div
					*ngIf="form.get('clientSecret')?.touched && form.get('clientSecret')?.errors?.required"
					class="text-danger"
				>
					{{ 'VALIDATION.FIELD_REQUIRED' | translate }}
				</div>
			</div>
			<div class="hint">
				<nb-icon icon="info-outline"></nb-icon>
				{{ 'INTEGRATIONS.MAKE_COM_PAGE.NEXT_STEP_INFO' | translate }}
			</div>
			<button nbButton status="primary" [disabled]="form.invalid" size="small" outline type="submit">
				{{ 'BUTTONS.NEXT' | translate }}
			</button>
		</form>
	</nb-card-body>
</nb-card>
