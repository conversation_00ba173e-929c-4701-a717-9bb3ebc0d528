{"extends": "./tsconfig.json", "compilerOptions": {"outDir": "../../../dist/out-tsc", "declaration": true, "types": ["node"], "target": "es2021", "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": false}, "include": ["src/**/*.ts"], "exclude": ["jest.config.ts", "src/**/*.spec.ts", "src/**/*.test.ts"]}