# @gauzy/plugin-videos

This library was generated with [Nx](https://nx.dev). This plugin provides functionality to upload and manage videos.

## Overview

This plugin enables video management capabilities including:

- Video upload and storage
- Video metadata management

## Building

Run `yarn nx build plugin-videos` to build the library.

## Running unit tests

Run `yarn nx test plugin-videos` to execute the unit tests via [Jest](https://jestjs.io).

## Publishing

After building your library with `yarn nx build plugin-videos`, go to the dist folder `dist/packages/plugins/videos` and run `npm publish`.

## Installation

Install the Videos plugin using your preferred package manager:

```bash
npm install @gauzy/plugin-videos
# or
yarn add @gauzy/plugin-videos
```
