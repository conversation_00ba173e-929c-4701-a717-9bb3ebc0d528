# @gauzy/plugin-integration-ai-ui

This library was generated with [Nx](https://nx.dev). It contains the AI Integration UI Plugin for the Gauzy platform.

## Features

- **Integration AI UI Plugin:** Provides a user-friendly interface for integrating AI capabilities into the Gauzy platform.

## Building

Run `yarn nx build plugin-integration-ai-ui` to build the library.

## Running unit tests

Run `yarn nx test plugin-integration-ai-ui` to execute the unit tests.

## Publishing

After building your library with `yarn nx build plugin-integration-ai-ui`, go to the dist folder `cd dist/packages/plugins/integration-ai-ui` and run `npm publish`.

## Installation

Install the Integration AI UI Plugin using your preferred package manager:

```bash
npm install @gauzy/plugin-integration-ai-ui
# or
yarn add @gauzy/plugin-integration-ai-ui
```
