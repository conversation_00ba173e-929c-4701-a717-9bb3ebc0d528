<nb-card class="card-scroll">
	<nb-card-header class="card-header-title">
		<div class="card-header-title">
			<h5>
				<ngx-back-navigation
					class="float-left"
					[haveLink]="true"
					(click)="navigateToIntegrations()"
				></ngx-back-navigation>
				{{ 'MENU.GAUZY_AI' | translate }}
			</h5>
		</div>
	</nb-card-header>
	<nb-card-body>
		<div class="col-xl-6 col-12">
			<form [formGroup]="form" #formDirective="ngForm" (ngSubmit)="onSubmit()">
				<div class="form-group">
					<label for="apiKey" class="label">
						{{ 'INTEGRATIONS.GAUZY_AI_PAGE.API_KEY' | translate }}
					</label>
					<input
						id="apiKey"
						formControlName="apiKey"
						nbInput
						[placeholder]="'FORM.PLACEHOLDERS.GAUZY_API_KEY' | translate"
						required
						fullWidth
					/>
				</div>
				<div class="form-group">
					<label for="apiSecret" class="label">
						{{ 'INTEGRATIONS.GAUZY_AI_PAGE.API_SECRET' | translate }}
					</label>
					<input
						id="apiSecret"
						formControlName="apiSecret"
						nbInput
						[placeholder]="'FORM.PLACEHOLDERS.GAUZY_API_SECRET' | translate"
						required
						fullWidth
					/>
				</div>
				<div class="form-group">
					<label for="openAiSecretKey" class="label">
						{{ 'INTEGRATIONS.GAUZY_AI_PAGE.OPEN_AI_API_SECRET_KEY' | translate }}
					</label>
					<input
						id="openAiSecretKey"
						formControlName="openAiSecretKey"
						nbInput
						[placeholder]="'FORM.PLACEHOLDERS.OPEN_AI_API_SECRET_KEY' | translate"
						fullWidth
					/>
				</div>
				<div class="form-group">
					<label for="openAiOrganizationId" class="label">
						{{ 'INTEGRATIONS.GAUZY_AI_PAGE.OPEN_AI_ORGANIZATION_ID' | translate }}
					</label>
					<input
						id="openAiOrganizationId"
						formControlName="openAiOrganizationId"
						nbInput
						[placeholder]="'FORM.PLACEHOLDERS.OPEN_AI_ORGANIZATION_ID' | translate"
						fullWidth
					/>
				</div>
				<button
					type="submit"
					nbButton
					status="primary"
					[disabled]="form.invalid"
					size="small"
					outline
				>
					{{ 'BUTTONS.SAVE' | translate }}
				</button>
			</form>
		</div>
	</nb-card-body>
</nb-card>
