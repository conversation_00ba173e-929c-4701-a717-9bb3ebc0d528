# @gauzy/plugin-job-search-ui

This library was generated with [Nx](https://nx.dev).

## Build

Run `yarn nx build plugin-job-search-ui` to build the library.

## Running unit tests

Run `yarn nx test plugin-job-search-ui` to execute the unit tests via [Jest](https://jestjs.io).

## Publishing

After building your library with `yarn nx build plugin-job-search-ui`, go to the dist folder `dist/packages/plugins/job-search-ui` and run `npm publish`.

## Installation

Install the Job Search UI Plugin using your preferred package manager:

```bash
npm install @gauzy/plugin-job-search-ui
# or
yarn add @gauzy/plugin-job-search-ui
```
