<nb-card>
	<nb-card-header class="header d-flex justify-content-between align-items-center p-3">
		<h4>
			<ngx-header-title>
				{{ 'JOBS.JOB_SEARCH' | translate }}
			</ngx-header-title>
		</h4>
		<div>
			<ng-template [ngxPermissionsOnly]="PermissionsEnum.ORG_JOB_EMPLOYEE_VIEW">
				<button
					status="primary"
					[routerLink]="'/pages/jobs/employee'"
					size="small"
					outline
					class="action"
					nbButton
				>
					{{ 'JOBS.EMPLOYEES' | translate }}
				</button>
			</ng-template>
			<ng-template [ngxPermissionsOnly]="PermissionsEnum.ORG_JOB_MATCHING_VIEW">
				<button
					status="primary"
					[routerLink]="'/pages/jobs/matching'"
					size="small"
					class="action"
					outline
					nbButton
				>
					{{ 'JOBS.MATCHINGS' | translate }}
				</button>
			</ng-template>
			<ng-template [ngxPermissionsOnly]="PermissionsEnum.ORG_PROPOSAL_TEMPLATES_VIEW">
				<button
					status="primary"
					[routerLink]="'/pages/jobs/proposal-template'"
					size="small"
					class="action"
					outline
					nbButton
				>
					{{ 'JOBS.PROPOSALS_TEMPLATE' | translate }}
				</button>
			</ng-template>
		</div>
	</nb-card-header>
	<nb-card-body class="p-0">
		<div class="gauzy-button-container">
			<ngx-gauzy-button-action
				[isDisable]="disableButton"
				[buttonTemplateVisible]="visibleButton"
				[buttonTemplate]="actionButtons"
				[hasLayoutSelector]="false"
			></ngx-gauzy-button-action>
			<div class="d-flex align-items-center">
				<nb-toggle
					class="mr-3 ml-3"
					(checkedChange)="setAutoRefresh($event)"
					[(ngModel)]="autoRefresh"
					status="basic"
					outline
					size="small"
				>
					{{ 'BUTTONS.AUTO_REFRESH' | translate }}
				</nb-toggle>
				<ng-container *ngIf="autoRefresh">
					<ng-container *ngTemplateOutlet="sync"></ng-container>
				</ng-container>
				<button
					*ngIf="!autoRefresh"
					type="button"
					(click)="refresh()"
					nbButton
					status="basic"
					outline
					size="small"
					class="refresh-button"
				>
					<ng-container *ngTemplateOutlet="sync"></ng-container>
					{{ 'BUTTONS.REFRESH' | translate }}
				</button>
			</div>
		</div>
		<nb-tabset (changeTab)="onTabChange($event)" class="mt-4">
			<nb-tab [tabId]="JobSearchTabsEnum.ACTIONS" [tabTitle]="'JOBS.BROWSE' | translate">
				<ng-container *ngIf="(nbTab$ | async) === JobSearchTabsEnum.ACTIONS">
					<ng-template [ngTemplateOutlet]="tableLayout"></ng-template>
				</ng-container>
			</nb-tab>
			<nb-tab [tabId]="JobSearchTabsEnum.SEARCH" [tabTitle]="'JOBS.SEARCH' | translate">
				<div class="job-filters">
					<div class="advanced-filter">
						<nb-card>
							<nb-card-body class="body-filter">
								<form
									[formGroup]="form"
									(ngSubmit)="searchJobs()"
									(keydown.enter)="handleSubmitOnEnter()"
								>
									<div class="row">
										<div class="col-xl-6 col-md-8 col-12 form-group m-0">
											<input
												nbInput
												type="text"
												class="w-100"
												[placeholder]="'JOBS.JOB_SEARCH' | translate"
												formControlName="title"
											/>
										</div>
									</div>
									<div class="row selects mt-3">
										<div class="col-xl-2 col-lg-3 col-md-6 form-group">
											<label class="d-block" for="jobSource">
												{{ 'JOBS.FILTER.SOURCE' | translate }}
											</label>
											<nb-select
												id="jobSource"
												[placeholder]="'JOBS.FILTER.SOURCE' | translate"
												fullWidth
												multiple
												formControlName="jobSource"
											>
												<nb-option
													*ngFor="let source of JobPostSourceEnum | keyvalue"
													[value]="source?.value | lowercase"
												>
													{{ 'JOBS.' + source.key | translate }}
												</nb-option>
											</nb-select>
										</div>
										<div class="col-xl-2 col-lg-3 col-md-6 form-group">
											<label class="d-block" for="jobType">
												{{ 'JOBS.FILTER.JOB_TYPE' | translate }}
											</label>
											<nb-select
												id="jobType"
												[placeholder]="'JOBS.FILTER.JOB_TYPE' | translate"
												fullWidth
												multiple
												formControlName="jobType"
											>
												<nb-option
													*ngFor="let type of JobPostTypeEnum | keyvalue"
													[value]="type.value | lowercase"
												>
													{{ 'JOBS.' + type.key | translate }}
												</nb-option>
											</nb-select>
										</div>
										<div class="col-xl-2 col-lg-3 col-md-6 form-group">
											<label class="d-block" for="jobStatus">
												{{ 'JOBS.FILTER.JOB_STATUS' | translate }}
											</label>
											<nb-select
												id="jobStatus"
												[placeholder]="'JOBS.FILTER.JOB_STATUS' | translate"
												fullWidth
												formControlName="jobStatus"
												[multiple]="true"
											>
												<nb-option
													*ngFor="let type of JobPostStatusEnum | keyvalue"
													[value]="type.value | lowercase"
												>
													{{ 'JOBS.' + type.key | translate }}
												</nb-option>
											</nb-select>
										</div>
										<div class="col-xl-2 col-lg-3 col-md-6 form-group">
											<label class="d-block" for="budget">
												{{ 'JOBS.FILTER.BUDGET' | translate }}
											</label>
											<nb-select
												id="budget"
												[placeholder]="'JOBS.FILTER.BUDGET' | translate"
												fullWidth
												multiple
												formControlName="budget"
											>
												<nb-option [value]="[null, 100]">
													{{ 'JOBS.FILTER.LESS_THAN' | translate }}
													$100
												</nb-option>
												<nb-option [value]="[100, 500]"> $100 - $500 </nb-option>
												<nb-option [value]="[500, 1000]"> $500 - $1K </nb-option>
												<nb-option [value]="[1000, 5000]"> $1K - $5K </nb-option>
												<nb-option [value]="[5000, null]"> $5K+ </nb-option>
											</nb-select>
										</div>
									</div>
									<div class="row">
										<button
											type="submit"
											size="small"
											status="success"
											class="mr-3 ml-3"
											nbButton
											[disabled]="form.invalid"
										>
											{{ 'BUTTONS.SEARCH' | translate }}
										</button>
										<button
											type="reset"
											nbButton
											size="small"
											status="basic"
											outline
											[disabled]="form.invalid"
											(click)="reset()"
										>
											{{ 'BUTTONS.RESET' | translate }}
										</button>
									</div>
								</form>
							</nb-card-body>
						</nb-card>
					</div>
				</div>
				<ng-container *ngIf="(nbTab$ | async) === JobSearchTabsEnum.SEARCH">
					<ng-template [ngTemplateOutlet]="tableLayout"></ng-template>
				</ng-container>
			</nb-tab>
		</nb-tabset>
	</nb-card-body>
</nb-card>

<!-- Template for the table layout -->
<ng-template #tableLayout>
	<ng-template [ngxPermissionsOnly]="['ORG_JOB_SEARCH']">
		<div class="table-scroll-container">
			<ng-template [ngIf]="settingsSmartTable">
				<angular2-smart-table
					style="cursor: pointer"
					[settings]="settingsSmartTable"
					(userRowSelect)="onSelectJob($event)"
					[source]="smartTableSource"
				></angular2-smart-table>
			</ng-template>
		</div>
		<div class="pagination-container">
			<ng-container *ngIf="smartTableSource">
				<ngx-pagination [source]="smartTableSource"></ngx-pagination>
			</ng-container>
		</div>
	</ng-template>
	<ng-template [ngxPermissionsExcept]="['ORG_JOB_SEARCH']">
		<div>
			<!-- Content to display if the user does not have 'canEditComponent' permission -->
		</div>
	</ng-template>
</ng-template>

<!-- Template for the action buttons -->
<ng-template #actionButtons let-buttonSize="buttonSize" let-selectedItem="selectedItem">
	<ng-template [ngxPermissionsOnly]="['ALL_ORG_VIEW']">
		<div class="btn-group actions">
			<ng-template [ngxPermissionsOnly]="['ORG_JOB_SEARCH']">
				<button status="basic" class="action secondary" size="small" (click)="viewJob()" nbButton>
					<nb-icon icon="eye-outline" pack="eva"></nb-icon>
					<span> {{ 'JOBS.VIEW' | translate }} </span>
				</button>
			</ng-template>
			<ng-template [ngxPermissionsOnly]="['ORG_JOB_APPLY']">
				<button
					size="small"
					style="cursor: pointer"
					status="basic"
					class="action success"
					(click)="applyToJobManually()"
					nbButton
				>
					<nb-icon icon="checkmark-outline" pack="eva"></nb-icon>
					<span>{{ 'JOBS.APPLY' | translate }}</span>
				</button>
				<button
					size="small"
					style="cursor: pointer"
					status="basic"
					class="action success"
					(click)="applyToJobAutomatically()"
					nbButton
				>
					<nb-icon icon="checkmark-outline" pack="eva"></nb-icon>
					<span>{{ 'JOBS.APPLY_AUTO' | translate }}</span>
				</button>
				<button
					size="small"
					style="cursor: pointer"
					status="basic"
					class="action primary"
					nbButton
					(click)="appliedJob()"
				>
					<nb-icon icon="checkmark-outline" pack="eva"></nb-icon>
					<span>{{ 'JOBS.APPLIED' | translate }}</span>
				</button>
			</ng-template>
			<ng-template [ngxPermissionsOnly]="['ORG_JOB_EDIT']">
				<button
					style="cursor: pointer"
					status="basic"
					class="action warning"
					size="small"
					(click)="hideJob()"
					[nbTooltip]="'JOBS.HIDE' | translate"
					nbButton
				>
					<nb-icon icon="eye-off-outline" pack="eva"></nb-icon>
				</button>
			</ng-template>
		</div>
	</ng-template>
</ng-template>

<!-- Template for the visible button -->
<ng-template #visibleButton>
	<ng-template [ngxPermissionsOnly]="['ORG_JOB_EDIT']">
		<button
			size="small"
			status="basic"
			class="action warning"
			type="button"
			(confirm)="hideAll()"
			ngxConfirmDialog
			[message]="'JOBS.HIDE_ALL_CONFIRM' | translate"
			[yesText]="'BUTTONS.YES_HIDE_ALL_JOBS' | translate"
			[noText]="'BUTTONS.CANCEL' | translate"
			nbButton
		>
			<nb-icon icon="eye-off-outline"></nb-icon>
			{{ 'BUTTONS.HIDE_ALL' | translate }}
		</button>
	</ng-template>
</ng-template>

<!-- Template for the sync button -->
<ng-template #sync>
	<div class="sync-container">
		<nb-icon class="sync" icon="sync-outline" [ngClass]="{ spin: isRefresh }" size="small"></nb-icon>
	</div>
</ng-template>
