@use 'gauzy/_gauzy-dialogs' as *;

:host {
	.main {
		display: flex;
		justify-content: center;
	}
	.main > nb-card {
		height: 100vh;
		width: 95%;
	}
	nb-card {
		background-color: var(--gauzy-card-1);
		nb-card-body {
			background-color: var(--gauzy-card-2);
			nb-card, nb-card-body {
				background-color: var(--gauzy-card-1);
				border-radius: var(--border-radius);
			}
		}
	}
	::ng-deep {
		.cancel {
			@include nb-rtl(justify-content, flex-start);
		}
		.title {
			@include nb-rtl(text-align, right);
		}
		job-title-description-details {
			div.job-detail {
				display: flex;
				flex-direction: column;
				.job-body {
					overflow: auto;
					max-height: calc(100vh - 30.75rem);
				}
			}
		}
	}
	.well {
		height: 50px;
		display: flex;
		align-items: center;
		justify-content: center;
		color: var(--gauzy-text-color-2);
	}
	.my-drop-zone {
		border: dashed 3px var(--gauzy-border-default-color);
		border-radius: var(--border-radius);
	}
	.nv-file-over {
		border: dashed 3px red;
		border-radius: var(--border-radius);
	}
	.sync-container {
		display: flex;
		align-items: center;
		justify-content: flex-end;
		gap: 4px;
		margin-bottom: 4px;
		.sync {
			color: var(--gauzy-text-color-2);
			cursor: pointer;
			&.spin {
				color: var(--text-primary-color);
				animation: rotate 1s linear 0s infinite;
			}
			@keyframes rotate {
				0% {
					transform: rotate(0deg);
				}
				100% {
					transform: rotate(360deg);
				}
			}
		}
		span {
			color: var(--text-primary-color);
		}
	}
	.button-generate-proposal {
		width: 100%;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		display: block;
		margin-bottom: 5.5px;
	}

	form {
		height: calc(100vh - 22.5rem);
		display: flex;
		flex-direction: column;
		.drag-drop {
			margin-top: auto;
		}
	}

	::ng-deep .cke_reset {
		height: calc(100vh - 40.5rem) !important;
	}
}
