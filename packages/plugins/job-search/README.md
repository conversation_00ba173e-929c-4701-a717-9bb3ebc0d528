# @gauzy/plugin-job-search

## Overview

The Job Search Plugin is a powerful tool designed to enhance your job search experience. Whether you're a job seeker or an employer, this plugin offers features to streamline the process and improve efficiency.

## Features

- **Advanced Search:** Utilize advanced search filters to find relevant job listings tailored to your preferences.
- **Job Alerts:** Set up job alerts to receive notifications when new job listings matching your criteria are posted.

## Building

Run `yarn nx build plugin-job-search` to build the library.

## Running unit tests

Run `yarn nx test plugin-job-search` to execute the unit tests via [Jest](https://jestjs.io).

## Publishing

After building your library with `yarn nx build plugin-job-search`, go to the dist folder `dist/packages/plugins/plugin-job-search` and run `npm publish`.

## Installation

To install the Job Search Plugin, simply run the following command in your terminal:

```bash
npm install @gauzy/plugin-job-search
# or
yarn add @gauzy/plugin-job-search
```
