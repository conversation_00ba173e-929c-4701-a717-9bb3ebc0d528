{"extends": "../../../tsconfig.json", "compilerOptions": {"module": "commonjs", "forceConsistentCasingInFileNames": true, "strict": false, "noImplicitOverride": false, "noPropertyAccessFromIndexSignature": false, "noImplicitReturns": false, "noFallthroughCasesInSwitch": false, "baseUrl": ".", "paths": {"@gauzy/plugin-integration-ai": ["./../../../dist/packages/plugins/integration-ai/*/index.d.ts"]}}, "files": [], "include": [], "references": [{"path": "./tsconfig.lib.json"}, {"path": "./tsconfig.spec.json"}]}