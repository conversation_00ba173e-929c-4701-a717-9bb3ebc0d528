{"name": "@gauzy/plugin-posthog", "version": "0.1.0", "description": "Ever Gauzy Platform PostHog Plugin", "author": {"name": "Ever Co. LTD", "email": "<EMAIL>", "url": "https://ever.co"}, "repository": {"type": "git", "url": "https://github.com/ever-co/ever-gauzy", "directory": "packages/plugins/posthog"}, "bugs": {"url": "https://github.com/ever-co/ever-gauzy/issues"}, "homepage": "https://ever.co", "license": "AGPL-3.0", "private": true, "type": "commonjs", "main": "./src/index.js", "typings": "./src/index.d.ts", "scripts": {"lib:build": "yarn nx build plugin-posthog", "lib:build:prod": "yarn nx build plugin-posthog", "lib:watch": "yarn nx build plugin-posthog --watch"}, "dependencies": {"@gauzy/config": "^0.1.0", "@gauzy/core": "^0.1.0", "@gauzy/plugin": "^0.1.0", "@nestjs/common": "^11.1.0", "chalk": "^4.1.0", "express": "^5.1.0", "posthog-node": "^4.13.0", "tslib": "^2.6.2"}, "devDependencies": {"@types/jest": "29.5.14", "@types/node": "^20.14.9", "typescript": "^5.8.3"}, "keywords": ["gauzy", "plugin", "posthog", "ever", "platform", "error-tracking"], "engines": {"node": ">=20.18.1", "yarn": ">=1.22.19"}, "sideEffects": false}