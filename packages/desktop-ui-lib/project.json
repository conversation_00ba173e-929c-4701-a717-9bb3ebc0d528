{"name": "desktop-ui-lib", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/desktop-ui-lib/src", "prefix": "lib", "projectType": "library", "tags": [], "implicitDependencies": [], "generators": {"@nx/angular:component": {"style": "scss", "prefix": "lib", "skipTests": true}}, "targets": {"build": {"executor": "@nx/angular:package", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "packages/desktop-ui-lib/ng-package.json", "tsConfig": "packages/desktop-ui-lib/tsconfig.json"}, "configurations": {"production": {"tsConfig": "packages/desktop-ui-lib/tsconfig.lib.prod.json"}, "development": {"tsConfig": "packages/desktop-ui-lib/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/desktop-ui-lib/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}