# @gauzy/desktop-ui-lib

This library was generated with [Nx](https://nx.dev).

## Building

Run `yarn nx build desktop-ui-lib` to build the library.

## Running unit tests

Run `yarn nx test desktop-ui-lib` to execute the unit tests.

## Publishing

After building your library with `yarn nx build desktop-ui-lib`, go to the dist folder `dist/packages/desktop-ui-lib` and run `npm publish`.

## Installation

Install the Desktop UI Lib Library using your preferred package manager:

```bash
npm install @gauzy/desktop-ui-lib
# or
yarn add @gauzy/desktop-ui-lib
```
