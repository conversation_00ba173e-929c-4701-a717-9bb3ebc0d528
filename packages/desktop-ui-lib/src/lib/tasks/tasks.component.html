<nb-card [formGroup]="form">
	<nb-card-header class="d-flex flex-column">
		<span class="cancel"><i (click)="close()" class="fas fa-times"></i></span>
		<h5>
			{{ 'TIMER_TRACKER.ADD_TASK' | translate }}
		</h5>
	</nb-card-header>
	<nb-card-body>
		<div class="body">
			<div class="selector-container">
				<div class="row">
					<div class="col-sm-6">
						<div class="form-group">
							<gauzy-client-selector formControlName="organizationContactId"></gauzy-client-selector>
						</div>
					</div>
					<div class="col-sm-6">
						<div class="form-group">
							<gauzy-project-selector formControlName="projectId"></gauzy-project-selector>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-sm-6">
						<div class="form-group">
							<gauzy-team-selector formControlName="organizationTeamId"></gauzy-team-selector>
						</div>
					</div>
					<div class="col-sm-6">
						<div class="form-group">
							<label class="label">{{ 'TASKS_PAGE.TASKS_STATUS' | translate }}</label>
							<ng-select
								[clearable]="true"
								[items]="taskStatuses"
								[placeholder]="'TASKS_PAGE.TASKS_STATUS' | translate"
								[searchable]="false"
								bindLabel="name"
								formControlName="taskStatus"
							>
								<ng-template let-index="index" let-item="item" ng-option-tmp>
									<gauzy-task-badge-view [taskBadge]="item"></gauzy-task-badge-view>
								</ng-template>
								<ng-template let-item="item" ng-label-tmp>
									<gauzy-task-badge-view [taskBadge]="item"></gauzy-task-badge-view>
								</ng-template>
							</ng-select>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-sm-12">
						<div class="form-group">
							<label class="label">{{ 'TASKS_PAGE.TASKS_TITLE' | translate }}</label>
							<input
								[placeholder]="'TASKS_PAGE.TASKS_TITLE' | translate"
								class="name-input"
								formControlName="title"
								nbInput
								type="text"
							/>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-sm-12">
						<label class="label">
							{{ 'SM_TABLE.TAGS' | translate }}
						</label>
						<div class="form-group">
							<ng-select
								[addTag]="(hasAddTagPermission$ | async) ? createTag : null"
								[clearable]="true"
								[items]="tags"
								[multiple]="true"
								[placeholder]="'SM_TABLE.TAGS' | translate"
								[loading]="isLoading"
								bindLabel="name"
								formControlName="tags"
							>
								<ng-template let-tag="item" let-tag$="item$" ng-option-tmp>
									<div class="d-flex align-items-center">
										<ng-container *ngIf="multiple">
											<input [ngModel]="tag$.selected" type="checkbox" />
										</ng-container>
										<nb-badge
											[style.background]="background(tag.color)"
											class="tag-color"
										></nb-badge>
										<span class="text">{{ tag.name }}</span>
									</div>
								</ng-template>

								<ng-template let-clear="clear" let-tag="item" ng-label-tmp>
									<nb-badge
										(click)="clear(tag)"
										[style.background]="background(tag.color)"
										[style.color]="backgroundContrast(tag.color)"
										[text]="tag.name"
										class="tag-color tag-label"
									></nb-badge>
								</ng-template>
							</ng-select>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-sm-6">
						<div class="form-group">
							<label>{{ 'TASKS_PAGE.TASK_PRIORITY' | translate }}</label>
							<ng-select
								[clearable]="true"
								[items]="taskPriorities"
								[placeholder]="'TASKS_PAGE.TASK_PRIORITY' | translate"
								bindLabel="name"
								formControlName="taskPriority"
								nbTooltipStatus="warning"
							>
								<ng-template let-index="index" let-item="item" ng-option-tmp>
									<gauzy-task-badge-view [taskBadge]="item"></gauzy-task-badge-view>
								</ng-template>
								<ng-template let-item="item" ng-label-tmp>
									<gauzy-task-badge-view [taskBadge]="item"></gauzy-task-badge-view>
								</ng-template>
							</ng-select>
						</div>
					</div>
					<div class="col-sm-6">
						<div class="form-group">
							<label class="label">{{ 'TASKS_PAGE.TASK_SIZE' | translate }}</label>
							<ng-select
								[clearable]="true"
								[items]="taskSizes"
								[placeholder]="'TASKS_PAGE.TASK_SIZE' | translate"
								[searchable]="false"
								bindLabel="name"
								formControlName="taskSize"
							>
								<ng-template let-index="index" let-item="item" ng-option-tmp>
									<gauzy-task-badge-view [taskBadge]="item"></gauzy-task-badge-view>
								</ng-template>
								<ng-template let-item="item" ng-label-tmp>
									<gauzy-task-badge-view [taskBadge]="item"></gauzy-task-badge-view>
								</ng-template>
							</ng-select>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-sm-6">
						<div class="form-group">
							<label class="label" for="dueDate">{{ 'TASKS_PAGE.DUE_DATE' | translate }}</label>
							<input
								[nbDatepicker]="taskDueDatePicker"
								[placeholder]="'TASKS_PAGE.DUE_DATE' | translate"
								formControlName="dueDate"
								fullWidth
								id="dueDate"
								nbInput
								type="text"
							/>
							<nb-datepicker #taskDueDatePicker></nb-datepicker>
						</div>
					</div>
					<div class="col-sm-6">
						<label class="label" for="estimate">{{ 'TASKS_PAGE.ESTIMATE' | translate }}</label>
						<div class="estimate-inputs">
							<input
								[min]="0"
								[placeholder]="'TASKS_PAGE.ESTIMATE_DAYS' | translate"
								formControlName="estimateDays"
								nbInput
								type="number"
							/>

							<input
								[placeholder]="'TASKS_PAGE.ESTIMATE_HOURS' | translate"
								formControlName="estimateHours"
								max="23"
								min="0"
								nbInput
								type="number"
							/>

							<input
								[min]="0"
								[placeholder]="'TASKS_PAGE.ESTIMATE_MINUTES' | translate"
								formControlName="estimateMinutes"
								max="59"
								min="0"
								nbInput
								type="number"
							/>
						</div>
					</div>
				</div>
			</div>
			<div class="w-50">
				<div class="row">
					<div class="col-12">
						<div class="form-group">
							<label class="label">{{ 'TASKS_PAGE.TASKS_DESCRIPTION' | translate }}</label>
							<ckeditor
								[config]="editorConfig"
								class="description"
								formControlName="description"
							></ckeditor>
						</div>
					</div>
				</div>
			</div>
		</div>
	</nb-card-body>
	<nb-card-footer>
		<button (click)="close()" nbButton outline status="basic">
			{{ 'BUTTONS.CANCEL' | translate }}
		</button>
		<button (click)="save()" [disabled]="form?.invalid" nbButton status="success">
			{{ 'BUTTONS.SAVE' | translate }}
			<nb-icon *gauzySpinnerButton="isSaving" [ngStyle]="{ display: 'none' }"></nb-icon>
		</button>
	</nb-card-footer>
</nb-card>
