@use 'gauzy/_gauzy-dialogs' as *;

:host {
  nb-card {
    width: 1030px;
    margin: auto;

    nb-card-body {
      background-color: var(--gauzy-card-2);
      @include nb-rtl(padding, 0.5rem 1rem 0.5rem 0.5rem);
      @include nb-ltr(padding, 0.5rem 0.5rem 0.5rem 1rem);

      .body {
        min-height: 59vh;
        overflow: auto;
        overflow-x: hidden;
        @include nb-rtl(padding, 0 0 0 0.5rem);
        @include nb-ltr(padding, 0 0.5rem 0 0);
        flex-direction: row;
        display: flex;
        gap: 0.3125rem;

        .selector-container {
          width: 60%;
          min-height: 59vh;
          overflow: overlay;
          overflow-x: hidden;
          @include nb-rtl(padding, 0 0 0 0.6875rem);
          @include nb-ltr(padding, 0 0.6875rem 0 0);

          .col-sm-6,
          .col-sm-12 {
            padding-left: 0.5rem !important;
            padding-right: 0.5rem !important;
          }

          .row {
            margin-left: -0.5rem !important;
            margin-right: -0.5rem !important;
          }
        }
      }
    }
  }
}

.description {
  margin-top: 10px;
  height: 100%;
}

.name-input {
  min-width: 100%;
}

h5 {
  font-size: 14px;
  font-weight: 600;
  line-height: 20px;
  letter-spacing: 0em;
  background: var(--color-primary-transparent-default);
  padding: 0px 4px;
  border-radius: calc(var(--border-radius) / 2);
  color: var(--text-primary-color);
  width: fit-content;
}

.estimate-inputs {
  display: flex;
  justify-content: space-between;

  input {
    width: 30%;
  }
}

.nb-radio {
  display: inline-block;
}

.cancel {
  border: none;
}

// Project selector

:host {
  .multiple-select {
    width: 100%;
  }

  ng-select {
    .selector-template {
      display: flex;
      align-items: center;
      height: 100%;
    }

    img {
      margin-right: 5px;
      border-radius: calc(var(--border-radius) * 2 / 3);
      box-shadow: var(--gauzy-shadow);
    }
  }

  ::ng-deep {
    .ng-select .ng-select-container .ng-value-container {
      align-items: center;
      padding-left: 5px;
    }
  }
}

img {
  object-fit: cover;
}

// tags selector

.tag-color {
  position: unset;
  display: inline-flex;
  align-items: center;
  width: 1rem;
  height: 1rem;
}

.tag-label {
  display: unset;
  margin-right: 0;
  border-radius: 2rem;
  font-size: 11px;
  font-weight: 600;
  line-height: 13px;
  letter-spacing: 0em;
  padding: 3px 8px;
}

input {
  box-shadow: unset;
  text-overflow: ellipsis;
}

[nbInput] {
  box-shadow: var(--gauzy-shadow) inset;
  padding: 10px;
}

:host ::ng-deep {
  .ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value {
    background-color: transparent;
    margin-right: 0;
  }

  .ng-select .ng-select-focused .ng-select-container {
    &:hover,
    &:focus,
    &:active,
    &:visited {
      box-shadow: var(--gauzy-shadow) inset !important;
    }
  }

  .ng-select .ng-select-container {
    box-shadow: var(--gauzy-shadow) inset !important;
  }

  .ng-select .ng-select-container .ng-value-container {
    align-items: center;
    padding-left: 5px;
    overflow-x: overlay;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex-wrap: initial;
    gap: 4px;
  }

  .cke_contents {
    height: 309px !important;
  }
}

.text {
  overflow-x: hidden;
  text-overflow: ellipsis;
  margin-left: 4px;
}

:host {
  nb-card-header,
  nb-card-footer {
    padding: 1rem;
  }

  nb-card-footer {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
  }

  ::ng-deep {
    .ng-select .ng-select-container .ng-value-container {
      padding-left: 10px !important;
    }
  }
}
