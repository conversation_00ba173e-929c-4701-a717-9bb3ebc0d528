<nb-layout>
	<nb-layout-column class="top-bottom-space">
		<div class="gallery-inner" [@fadeInOut]>
			<div class="gallery-content">
				<div class="left-button">
					<button
						class="nav-button"
						nbButton
						status="primary"
						[disabled]="active_index == 0"
						(click)="prev($event)"
					>
						<nb-icon
							pack="font-awesome"
							icon="angle-left"
						></nb-icon>
					</button>
				</div>
				<div [nbSpinner]="!(item?.fullUrl | async)" nbSpinnerStatus="primary" class="media-viewer" (click)="$event.stopPropagation()">
					<ng-template
							[ngTemplateOutlet]="defaultLoader"
							[ngTemplateOutletContext]="{
								$implicit: (item?.thumbUrl | async)
							}"
						></ng-template>
					<img
						#imageMedia
						loading="eager"
						[ngClass]="{'blur-transition': (item?.fullUrl | async)}"
						[src]="item?.fullUrl | async"
					/>
					<div *ngIf="item?.fullUrl | async" class="date">
						{{ item?.recordedAt | dateTime : 'llll' }}
					</div>
				</div>
				<div class="right-button">
					<button
						class="nav-button"
						nbButton
						[disabled]="active_index == items.length - 1"
						status="primary"
						(click)="next($event)"
					>
						<nb-icon
							pack="font-awesome"
							icon="angle-right"
						></nb-icon>
					</button>
				</div>
			</div>
			<div class="gallery-footer">
				<div class="thumb-items custom-scroll" #customScroll>
					<div
						class="thumb-item"
						*ngFor="let thumb of items; let index = index; ; trackBy: trackById"
						[ngClass]="{ 'thumb-item-active': item == thumb }"
						(click)="setFocus(thumb)"
					>
						<ng-template
							[ngTemplateOutlet]="defaultLoader"
							[ngTemplateOutletContext]="{
								$implicit: (thumb?.thumbUrl | async)
							}"
						></ng-template>
						<img loading="eager" [ngClass]="{'blur-transition': (thumb?.thumbUrl | async)}" [src]="thumb?.thumbUrl | async" />
						<div *ngIf="thumb?.thumbUrl | async" class="date-box">
							<div class="thumb-date">
								{{ thumb?.recordedAt | dateTime }}
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</nb-layout-column>
</nb-layout>

<ng-template #defaultLoader let-thumb>
	<div *ngIf="!thumb; else blurredImage" [nbSpinner]="!thumb" class="loading"></div>
	<ng-template #blurredImage>
		<img class="loading-thumb" [src]="thumb">
	</ng-template>
</ng-template>
