// @import 'themes';
.top-bottom-space {
	padding: 0px !important;
	margin-top: 28px;
}
:host {
	nb-icon {
		width: var(--icon-width) !important;
		height: var(--icon-height) !important;
	}
	.gallery-inner {
		display: flex;
		flex-direction: column;
		height: calc(100vh - 28px);
		width: 100%;
		gap: 8px;
		padding: 8px;
		background-color: var(--gauzy-sidebar-background-1);
	}
	.gallery-content {
		position: relative;
		height: calc(100% - 98px);
		.media-viewer {
			height: 100%;
			border-radius: var(--border-radius);
			img {
				width: 100%;
				height: 100%;
				border-radius: var(--border-radius);
			}
		}
		.left-button,
		.right-button {
			position: absolute;
			top: 50%;
			z-index: 999;
		}
		.left-button {
			left: 16px;
		}
		.right-button {
			right: 16px;
		}
	}
	.gallery-footer {
		img {
			width: 120px;
			height: 90px;
			cursor: pointer;
			border-radius: var(--border-radius);
		}
		.thumb-items {
			overflow-y: hidden;
			display: flex;
			justify-content: flex-start;
			margin-top: auto;
			gap: 8px;
			overflow-x: auto;
			border-radius: var(--border-radius);
			.thumb-item {
				position: relative;
				&.thumb-item-active {
					img {
						border: 4px solid var(--text-primary-color);
					}
				}
			}
		}
	}
}

::ng-deep body {
	-webkit-app-region: drag;
}

.date-box {
	width: 100%;
    position: absolute;
    display: flex;
    justify-content: center;
	.thumb-date {
		border-radius: calc(var(--border-radius) / 2);
		padding: 1px 2px;
		font-size: 8px;
		position: absolute;
		background-color: rgba(25, 25, 25, 0.8);
		font-weight: 600;
		color: white;
		bottom: 8px;
	}
}

.date {
	position: absolute;
	background-color: rgba(25, 25, 25, 0.8);
	font-size: 13px;
	font-weight: 600;
	color: white;
	border-radius: var(--border-radius);
	bottom: 8px;
	right: 8px;
	padding: 4px 6px;
}

img {
	object-fit: cover;
}

.loading {
	position: absolute;
	width: 100%;
	height: 100%;
	background-color: #00000099;
	border-radius: var(--border-radius);
	& + img {
		filter: blur(0.25rem);
	}
}

.loading-thumb {
	position: absolute;
	width: 100%;
	height: 100%;
	border-radius: var(--border-radius);
}

img {
	filter: blur(0.25rem);
	&.blur-transition {
		filter: blur(0);
		transition: filter 0.8s ease;
	}
}
