<lib-form-section *ngIf="form" [formGroup]="form" title="{{ 'PLUGIN.FORM.SOURCE' | translate }}">
	<lib-form-row>
		<nb-form-field *ngIf="form.get('operatingSystem')" class="form-group">
			<label class="label" for="source-os">{{ 'PLUGIN.FORM.OPERATING_SYSTEM' | translate }} *</label>
			<nb-select
				fullWidth
				id="source-os"
				formControlName="operatingSystem"
				[status]="getFieldError('operatingSystem') ? 'danger' : 'basic'"
			>
				@for(os of sourceOs; track os) {
				<nb-option [value]="os">
					{{ os | translate | titlecase }}
				</nb-option>
				}
			</nb-select>
		</nb-form-field>
		<nb-form-field *ngIf="form.get('architecture')" class="form-group">
			<label class="label" for="source-arch">{{ 'PLUGIN.FORM.ARCHITECTURE' | translate }} *</label>
			<nb-select
				fullWidth
				id="source-arch"
				formControlName="architecture"
				[status]="getFieldError('architecture') ? 'danger' : 'basic'"
			>
				@for(arch of sourceArchs; track arch) {
				<nb-option [value]="arch">
					{{ arch | translate }}
				</nb-option>
				}
			</nb-select>
		</nb-form-field>
	</lib-form-row>

	<!-- Source-specific fields -->
	<div class="source-container">
		<div class="source-content">
			<!-- choose source -->
			@switch (form.get('type')?.value){
			<!-- NPM -->
			@case (pluginSourceType.NPM) {
			<lib-npm-form [form]="form"></lib-npm-form>
			}

			<!-- CDN -->
			@case (pluginSourceType.CDN) {
			<lib-cdn-form [form]="form"></lib-cdn-form>
			}
			<!-- GAUZY -->
			@case (pluginSourceType.GAUZY) {
			<lib-gauzy-form [form]="form"></lib-gauzy-form>
			}

			<!-- DEFAULT -->
			@default {
			<ngx-no-data-message [message]="'PLUGIN.FORM.VALIDATION.INVALID_SOURCE' | translate"></ngx-no-data-message>
			} }
		</div>
	</div>
</lib-form-section>
