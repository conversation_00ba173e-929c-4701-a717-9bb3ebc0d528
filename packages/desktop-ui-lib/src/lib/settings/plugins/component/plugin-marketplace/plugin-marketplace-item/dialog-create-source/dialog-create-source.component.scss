@use 'gauzy/_gauzy-dialogs' as *;

// Custom variables
$card-border-radius: 0.75rem;
$input-padding: 0.75rem 1rem;
$button-spacing: 0.75rem;
$transition-speed: 0.2s;

:host {
  display: block;
}

.dialog-card {
  border-radius: $card-border-radius;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
  overflow: hidden;
  width: 600px;
  margin: 0 auto;
  height: 78vh;

  nb-card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    border-bottom: 1px solid nb-theme(divider-color);

    .title {
      margin: 0;
      font-weight: 600;
      font-size: 1rem;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }

    .close-button {
      padding: 0.25rem;
      height: 2rem;
      width: 2rem;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all $transition-speed ease;
      border-radius: 50%;
      cursor: pointer;
      opacity: 0.8;

      &:hover {
        opacity: 1;
        background-color: var(--background-basic-color-3);
      }
    }
  }

  nb-card-body {
    padding: 1rem;
    overflow-y: auto;

    form {
      height: 100%;
    }
  }

  nb-card-footer {
    gap: 1rem;
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .dialog-card {
    form {
      .form-row {
        flex-direction: column;
        gap: 0.5rem;

        nb-form-field,
        .form-group {
          width: 100%;
        }
      }

      .source-container [formGroupName='source'] nb-form-field {
        width: 100%;
      }
    }
  }
}

::ng-deep {
  .nb-theme-default nb-card.dialog-card {
    border: none;
  }

  .nb-theme-dark {
    .file-upload-area {
      &.has-file {
        background-color: rgba(nb-theme(color-success-500), 0.1);
      }

      &.error {
        background-color: rgba(nb-theme(color-danger-500), 0.1);
      }
    }
  }

  nb-hint {
    font-size: 0.8rem;
    color: var(--color-danger-default);
  }

  nb-spinner {
    background: transparent;
  }
}
