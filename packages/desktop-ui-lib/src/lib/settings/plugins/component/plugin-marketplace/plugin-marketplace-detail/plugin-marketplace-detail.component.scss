.plugin-container {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 16px;
  padding: 16px;
}

.plugin-details {
  flex-grow: 1;
}

.plugin-header {
  display: flex;
  align-items: center;
  font-weight: bold;
  gap: 1rem;
}

.plugin-name {
  font-size: 16px;
  color: var(--text-primary-color);
  cursor: pointer;
}

.plugin-version {
  font-size: 14px;
  color: var(--gauzy-text-color-1);
}

.plugin-description {
  font-size: 14px;
  color: var(--nb-text-hint-color);
  margin-top: 4px;
}

.plugin-actions {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem !important;

  .plugin-edit {
    color: var(--gauzy-text-color-1);
    cursor: pointer;
  }
}
