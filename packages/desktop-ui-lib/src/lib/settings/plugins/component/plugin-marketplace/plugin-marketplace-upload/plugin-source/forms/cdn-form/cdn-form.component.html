<lib-form-section *ngIf="form" [formGroup]="form" class="source-content">
	<!-- CDN fields here -->
	<lib-form-row *ngIf="form.get('url')">
		<nb-form-field class="form-group full-width">
			<label class="label" for="cdn-url">{{ 'PLUGIN.FORM.CDN.URL' | translate }} *</label>
			<input
				nbInput
				fullWidth
				id="cdn-url"
				formControlName="url"
				placeholder="https://cdn.example.com/plugin.js"
				type="url"
				[status]="getFieldError('url') ? 'danger' : 'basic'"
			/>
			<nb-hint *ngIf="getFieldError('url', 'required')" status="danger">
				{{ 'PLUGIN.FORM.VALIDATION.URL_REQUIRED' | translate }}
			</nb-hint>
			<nb-hint *ngIf="getFieldError('url', 'pattern')" status="danger">
				{{ 'PLUGIN.FORM.VALIDATION.INVALID_URL' | translate }}
			</nb-hint>
		</nb-form-field>
	</lib-form-row>

	<lib-form-row>
		<nb-form-field *ngIf="form.get('integrity')" class="form-group">
			<label class="label" for="cdn-integrity">{{ 'PLUGIN.FORM.CDN.INTEGRITY' | translate }}</label>
			<input nbInput fullWidth id="cdn-integrity" formControlName="integrity" placeholder="sha384-..." />
		</nb-form-field>

		<nb-form-field *ngIf="form.get('crossOrigin')" class="form-group">
			<label class="label" for="cdn-crossorigin">{{ 'PLUGIN.FORM.CDN.CROSS_ORIGIN' | translate }}</label>
			<input nbInput fullWidth id="cdn-crossorigin" formControlName="crossOrigin" placeholder="anonymous" />
		</nb-form-field>
	</lib-form-row>
</lib-form-section>
