@use 'gauzy/_gauzy-table' as *;

:host {
  display: block;
  width: 100%;

  .no-data {
    height: 100%;
    width: 100%;
  }

  nb-card {
    margin-bottom: 0;
  }

  nb-card-body {
    border-radius: var(--border-radius);
    padding: 0;
  }

  nb-card-footer {
    display: flex;
    gap: 1rem;
    padding: 0;
    padding-top: 1rem;
  }
}

// Section styling
.form-section {
  margin-bottom: 1rem;
  background-color: var(--gauzy-card-2);
  border-radius: var(--border-radius);
  padding: 1rem;

  &:last-child {
    margin-bottom: 0;
  }

  .section-title {
    font-weight: 600;
    margin-bottom: 1rem;
    color: nb-theme(text-basic-color);
    border-bottom: 1px solid nb-theme(border-basic-color-3);
    padding-bottom: 0.5rem;
  }
}

// Info rows and items
.info-row {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 1rem;

  &:last-child {
    margin-bottom: 0;
  }

  .b-status {
    right: 1rem;
    top: 1rem;
    border-radius: 4px;

    &.left {
      right: 4.5rem;
    }
  }

  .info-item {
    flex: 1 1 45%;
    margin-right: 5%;
    margin-bottom: 0.5rem;

    &:last-child {
      margin-right: 0;
    }

    &.full-width {
      flex: 0 0 100%;
      margin-right: 0;
    }

    .label {
      font-size: 0.875rem;
      color: var(--gauzy-text-color-2);
      font-weight: 600;
      margin-bottom: 0.25rem;
    }

    .value {
      font-size: 1rem;
      word-break: break-word;

      &.description {
        white-space: pre-line;
        margin-top: 0.25rem;
        margin-bottom: 0.25rem;
        color: var(--gauzy-text-color-1);
      }

      a {
        color: nb-theme(color-primary-default);
        text-decoration: none;

        &:hover {
          text-decoration: underline;
        }

        .external-link {
          font-size: 0.875rem;
          margin-left: 0.25rem;
        }
      }
    }
  }
}

// Tags styling
nb-tag-list {
  display: inline-flex;
  flex-wrap: wrap;

  nb-tag {
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;

    &:last-child {
      margin-right: 0;
    }
  }
}

// Truncate long text
.text-truncate {
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// Responsive adjustments
@media (max-width: 767px) {
  .info-row {
    flex-direction: column;

    .info-item {
      flex: 0 0 100%;
      margin-right: 0;
      margin-bottom: 1rem;
    }
  }
}

[nbButton].appearance-hero {
  text-transform: capitalize !important;
}

:host {
  display: block;

  .plugin-details-card {
    nb-card-header {
      padding: 1rem 1.5rem;
      border-bottom: 1px solid var(--gauzy-border-default-color);
    }

    nb-card-body {
      padding: 1.5rem;
    }

    nb-card-footer {
      padding: 1rem 1.5rem;
      border-top: 1px solid var(--gauzy-border-default-color);
    }
  }

  .plugin-content {
    .quick-actions {
      display: flex;
      align-items: center;
    }

    .description-container {
      padding: 1.25rem;
      background-color: var(--gauzy-card-1);
      border-radius: var(--border-radius);
      margin-bottom: 1.5rem;

      .plugin-description {
        margin-bottom: 0;
        line-height: 1.6;
      }
    }

    .info-card {
      background-color: var(--gauzy-card-1);
      border-radius: var(--border-radius);
      padding: 1.25rem;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

      .section-title {
        color: var(--gauzy-text-color-1);
        font-weight: 600;
        margin-bottom: 1rem;

        nb-badge {
          right: 24px;
          top: 24px;
        }
      }
    }

    .info-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 1.25rem;

      .info-item {
        .label {
          font-size: 0.875rem;
          color: var(--gauzy-text-color-2);
          margin-bottom: 0.5rem;
        }

        .value {
          font-size: 0.9375rem;
          word-break: break-word;

          &.code-like {
            font-family: monospace;
            background-color: #f7f9fc;
            padding: 0.5rem;
            border-radius: 4px;
            font-size: 0.875rem;
          }
        }
      }
    }

    .source-container {
      .info-item {
        margin-bottom: 1rem;

        &:last-child {
          margin-bottom: 0;
        }

        &.full-width {
          grid-column: 1 / -1;
        }
      }
    }

    .external-link {
      text-decoration: none;
      display: inline-flex;
      align-items: center;

      .link-icon {
        font-size: 0.875rem;
        margin-left: 0.25rem;
      }

      &:hover {
        text-decoration: underline;
      }
    }

    .version-select {
      min-width: 120px;
    }

    .plugin-tabs {
      margin-bottom: 1rem;

      ::ng-deep {
        .tabset {
          border-radius: var(--border-radius);
          overflow: hidden;
        }

        .tab-link {
          padding: 1rem;
        }
      }
    }
  }

  .no-data {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 3rem 0;
  }

  .badge {
    top: 24px;
    right: 24px;
  }

  .info-version,
  .source-container {
    .label {
      color: var(--gauzy-text-color-2);
      font-size: 14px;
    }
    .value {
      color: var(--gauzy-text-color-1);
    }
  }

  .action.danger {
    color: var(--color-danger-default);
  }

  .action-buttons {
    display: flex;
  }
}
