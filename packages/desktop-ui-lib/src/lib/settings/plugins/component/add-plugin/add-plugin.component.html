@let installing = installing$ |async;

<nb-card>
	<nb-card-header class="d-flex flex-column">
		<span class="cancel"><i (click)="close()" class="fas fa-times"></i></span>
		<h4>{{ 'TIMER_TRACKER.SETTINGS.PLUGIN' | translate }}</h4>
	</nb-card-header>
	<nb-card-body>
		<ng-container *ngIf="context === 'local'; else web">
			<div class="full-width mb-3 description">
				{{ 'TIMER_TRACKER.SETTINGS.ADD_PLUGIN_INSTALLATION_DESCRIPTION' | translate }}
			</div>
			<div class="load-button">
				<button
					nbButton
					[disabled]="installing"
					class="full-width button"
					status="primary"
					type="submit"
					(click)="localPluginInstall()"
				>
					<nb-icon *gauzySpinnerButton="installing" icon="monitor-outline"></nb-icon
					>{{ 'BUTTONS.LOAD_PLUGIN' | translate }}
				</button>
				<button
					nbButton
					[disabled]="installing"
					class="full-width button"
					status="info"
					type="submit"
					(click)="context = 'cdn'"
				>
					<nb-icon icon="globe-outline"></nb-icon>{{ 'BUTTONS.FROM_CDN' | translate }}
				</button>
				<button
					nbButton
					[disabled]="installing"
					class="full-width button"
					status="danger"
					type="submit"
					(click)="context = 'npm'"
				>
					<i class="fab fa-npm"></i>
				</button>
				<button
					nbButton
					[disabled]="installing"
					class="full-width button"
					status="warning"
					type="submit"
					(click)="redirectToStore()"
				>
					<nb-icon icon="grid-outline"></nb-icon>
					{{ 'BUTTONS.MARKETPLACE' | translate }}
				</button>
			</div>
		</ng-container>
	</nb-card-body>
</nb-card>

<ng-template #web>
	<ng-container *ngIf="context === 'cdn'; else npm">
		<form class="form" (ngSubmit)="installPlugin(input.value)">
			<label
				><button (click)="reset()" class="return" status="primary" size="small" outline nbButton>
					<nb-icon icon="arrow-back-outline"></nb-icon>
				</button>
				{{ 'BUTTONS.ADD_PLUGIN' | translate }}</label
			>
			<div class="description">
				{{ 'TIMER_TRACKER.SETTINGS.ADD_PLUGIN_CDN_DESCRIPTION' | translate }}
			</div>
			<input
				nbInput
				#input
				type="url"
				[status]="error ? 'danger' : 'basic'"
				placeholder="https://cdn.server.tld/public/plugin.zip"
			/>
			<div class="error" *ngIf="error">
				{{ error | translate }}
			</div>
			<button
				nbButton
				[disabled]="!input.value || installing"
				status="primary"
				type="submit"
				(click)="installPlugin(input.value)"
			>
				{{ 'BUTTONS.INSTALL' | translate
				}}<nb-icon *gauzySpinnerButton="installing" icon="cloud-download-outline"></nb-icon>
			</button>
		</form>
	</ng-container>
</ng-template>

<ng-template #npm>
	<ng-container *ngIf="context === 'npm'">
		<form class="form" (ngSubmit)="installPluginFromNPM()">
			<label>
				<button (click)="reset()" class="return" status="primary" size="small" outline nbButton>
					<nb-icon icon="arrow-back-outline"></nb-icon>
				</button>
				{{ 'BUTTONS.ADD_PLUGIN' | translate }}
			</label>

			<div class="description">
				{{ 'TIMER_TRACKER.SETTINGS.ADD_PLUGIN_NPM_DESCRIPTION' | translate }}
			</div>

			<input
				nbInput
				type="text"
				id="pkgName"
				name="pkgName"
				[(ngModel)]="npmModel.pkg.name"
				required
				#pkgName="ngModel"
				[status]="error ? 'danger' : 'basic'"
				[placeholder]="'TIMER_TRACKER.SETTINGS.PACKAGE_NAME' | translate"
			/>

			<input
				nbInput
				type="text"
				id="pkgVersion"
				name="pkgVersion"
				[(ngModel)]="npmModel.pkg.version"
				[status]="error ? 'danger' : 'basic'"
				[placeholder]="'TIMER_TRACKER.SETTINGS.PACKAGE_VERSION' | translate"
			/>

			<nb-toggle [checked]="showRegistry" (checkedChange)="toggleRegistry($event)" status="basic">
				{{ 'TIMER_TRACKER.SETTINGS.REGISTRY' | translate }}
			</nb-toggle>

			<ng-container *ngIf="showRegistry">
				<input
					nbInput
					gaTextMask
					[config]="{
						text: npmModel.registry.authToken,
						replacement: 0.75,
						maskFromLeft: false
					}"
					(unmaskedValueChange)="handleUnmaskedValueChange($event)"
					type="text"
					id="authToken"
					name="authToken"
					[status]="error ? 'danger' : 'basic'"
					[placeholder]="'TIMER_TRACKER.SETTINGS.AUTH_TOKEN' | translate"
				/>
				<input
					nbInput
					type="url"
					id="privateURL"
					name="privateURL"
					[(ngModel)]="npmModel.registry.privateURL"
					pattern="https?://.+"
					#privateURL="ngModel"
					[status]="error ? 'danger' : 'basic'"
					[placeholder]="'TIMER_TRACKER.SETTINGS.PRIVATE_REGISTRY_URL' | translate"
				/>
			</ng-container>

			<div class="error" *ngIf="error">
				{{ error | translate }}
			</div>

			<button nbButton [disabled]="!pkgName.value || installing" status="primary" type="submit">
				{{ 'BUTTONS.INSTALL' | translate }}
				<nb-icon *gauzySpinnerButton="installing" icon="cloud-download-outline"></nb-icon>
			</button>
		</form>
	</ng-container>
</ng-template>
