.error {
	color: red;
	font-weight: 500;
	font-size: 13px;
	padding: 8px;
	background-color: rgb(255 0 0 / 10%);
	border-radius: 4px;
	text-wrap: pretty;
	overflow: hidden;
	text-overflow: ellipsis;
}

nb-card {
	width: 350px;
	display: flex;
}

nb-card-body {
	background-color: var(--gauzy-card-2);
	border-radius: var(--border-radius);
	padding: 1rem;
}

h4 {
	font-size: 20px;
	font-weight: 600;
	line-height: 24px;
	letter-spacing: 0em;
	color: var(--gauzy-text-color-1);
}


label {
	font-weight: 600;
	color: var(--gauzy-text-color-2);
}

button {
	width: fit-content;
	align-self: flex-end;
}

nb-card-header {
	position: relative;
	padding: 1rem;
}

.cancel {
	position: absolute;
	right: 0.75rem;
	top: 0.25rem;
	cursor: pointer;
	font-size: 11px;
    color: var(--gauzy-text-color-1);
}

.form {
	display: flex;
	flex-direction: column;
	gap: 1rem;
}

.load-button {
	justify-content: center;
    align-items: stretch;
    align-content: space-evenly;
    display: grid;
    grid-template-columns: 1fr 1fr;
    column-gap: 1rem;
    row-gap: 1rem;

	.button {
		height: 72px;
		border-radius: var(--border-radius);
		i {
			font-size: 2rem;
		}
	}
}

.description {
	font-size: 13px;
	font-weight: 500;
	color: var(--gauzy-text-color-2);
}

.return {
	padding: 4px 2px !important;
}
