@use 'gauzy/_gauzy-table' as *;

.custom-table {
  background-color: var(--gauzy-card-2);
  margin: 0;
  display: flex;
  flex-direction: column;
  padding: 8px 4px 0px;
  border-radius: var(--border-radius);
  height: calc(100vh - 15.5rem);
  width: auto;

  .table-scroll-container {
    flex-grow: 10;
    max-height: unset;
    padding: 0 4px 0;
    margin-bottom: 8px;
    overflow-x: hidden;
  }
}

.button-container,
.button-hide {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 0.5rem;
}

.no-data {
  height: 552px;
  width: 100%;
}

.danger {
  color: var(--icon-danger-color) !important;
  background-color: #ff00002b !important;
}
