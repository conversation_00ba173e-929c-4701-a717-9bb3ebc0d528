@let installing = installationQuery.installing$ |async; @let uninstalling = installationQuery.uninstalling$|async; @let
activating = query.activating$ |async; @let deactivating = query.deactivating$ |async; @let disabled = installing ||
uninstalling || activating || deactivating; @let selected = query.plugin$ | async; @let loading = query.isLoading$ |
async; @let source = sourceData$ | async;

<div class="content-active">
	<div class="button-container mb-3">
		<div class="button-add">
			<button (click)="addPlugin()" [disabled]="disabled" status="success" size="small" nbButton>
				<nb-icon *gauzySpinnerButton="installing" icon="plus-outline"></nb-icon>
				{{ 'BUTTONS.ADD_PLUGIN' | translate }}
			</button>
		</div>
		<div *ngIf="selected" class="button-hide">
			<button
				*ngIf="selected.renderer"
				class="action secondary"
				status="basic"
				size="small"
				(click)="view()"
				[disabled]="disabled"
				nbButton
			>
				<nb-icon icon="eye-outline"></nb-icon>{{ 'BUTTONS.VIEW' | translate }}
			</button>
			<button
				size="small"
				[status]="selected.isActivate ? 'warning' : 'primary'"
				class="button-install"
				(click)="changeStatus()"
				[disabled]="disabled"
				nbButton
			>
				<nb-icon
					*gauzySpinnerButton="activating || deactivating"
					[icon]="selected.isActivate ? 'close-circle-outline' : 'checkmark-circle-outline'"
				></nb-icon
				>{{ (selected.isActivate ? 'BUTTONS.DEACTIVATE' : 'BUTTONS.ACTIVATE') | translate }}
			</button>
			<button
				size="small"
				class="action danger"
				status="basic"
				(click)="uninstall()"
				[disabled]="disabled"
				nbButton
			>
				<nb-icon *gauzySpinnerButton="uninstalling" status="danger" icon="slash-outline"></nb-icon>
				{{ 'BUTTONS.UNINSTALL' | translate }}
			</button>
		</div>
	</div>
	<div *ngIf="(plugins$ | async)?.length > 0; else noPlugin" class="col-12 custom-table">
		<div class="table-scroll-container">
			<angular2-smart-table
				#pluginTable
				(userRowSelect)="handleRowSelection($event)"
				[settings]="smartTableSettings"
				[source]="source"
				style="cursor: pointer"
			>
			</angular2-smart-table>
		</div>
		<div class="pagination-container">
			<ga-pagination [source]="source"></ga-pagination>
		</div>
	</div>
</div>
<ng-template #noPlugin>
	<div class="no-data">
		<ngx-no-data-message [message]="(loading ? 'SM_TABLE.NO_DATA.LOADING' : 'SM_TABLE.NO_DATA.PLUGIN') | translate">
		</ngx-no-data-message>
	</div>
</ng-template>
