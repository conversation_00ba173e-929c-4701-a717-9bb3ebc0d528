h4 {
  font-size: 20px;
  font-weight: 600;
  line-height: 24px;
  letter-spacing: 0em;
  color: var(--gauzy-text-color-1);
}

.titlebar-space {
  margin-top: 15px;
}

:host ::ng-deep {
  .content-active {
    background-color: var(--gauzy-card-2);
    border-radius: var(--border-radius);
    &.first {
      border-radius: 0 var(--border-radius) var(--border-radius) var(--border-radius);
    }
    height: calc(100vh - 10rem);
    padding: 1rem;
    width: 100%;

    nb-card {
      margin-bottom: 1rem;
    }
  }

  .content-active nb-card {
    margin-bottom: 0;
  }

  .nb-tab-card,
  .nb-tab-body {
    background-color: unset;
    padding: 0;
    margin: 0;
  }
}

::ng-deep nb-route-tabset .route-tab .tab-link {
  display: flex !important;
  border-radius: var(--border-radius) var(--border-radius) 0 0;

  nb-icon {
    margin-bottom: unset;
  }
  svg {
    fill: var(--text-primary-color);
  }
  span {
    display: inline-block;
    text-transform: initial;
    &:first-letter {
      text-transform: uppercase;
    }
  }
}
