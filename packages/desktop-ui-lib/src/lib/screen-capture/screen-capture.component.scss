.screen-capture-img {
  width: 100%;
  border-radius: var(--border-radius);
  height: 100%;
  object-fit: cover;
}

.top-bottom-space {
  padding: 1px !important;
}

.icon-logo {
  width: 16px;
  height: 16px;
  border-radius: calc(var(--border-radius) / 2);
  box-shadow: var(--gauzy-shadow);
}

.title-window {
  font-size: 14px;
  font-weight: 600;
}

::ng-deep .layout-container {
  background-color: var(--gauzy-card-1);
}

::ng-deep .nb-theme-default nb-card {
  border: none;
}

::ng-deep nb-card {
  border: none;
}

.no-padding {
  padding: 0px !important;
}

.hidden-long-text {
  display: -webkit-box;
  overflow: hidden;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

.notification {
  display: flex;
  flex-direction: column;
  overflow: unset;
  align-items: center;
  height: 100%;
}

.notification-header {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  align-self: flex-start;
  gap: 4px;
  padding: 6px 6px 0 6px;

  .notification-header-title {
    display: flex;
    align-items: center;
    gap: 4px;
  }
}

.notification-body {
  height: 100%;
  width: 100%;
  padding: 6px;
  flex-grow: 4;
  max-height: 135px;
}
