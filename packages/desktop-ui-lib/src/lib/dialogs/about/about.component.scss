nb-card-body,
nb-card-footer {
    padding: 1rem;
    background-color: var(--gauzy-card-2);
}

div.logo {
    box-shadow: var(--gauzy-shadow);
    width: fit-content;
    border-radius: calc(var(--border-radius) * 1.5);
    background: var(--gauzy-card-1);

    img {
        height: 64px;
    }
}

.about-container {
    max-width: 350px;
}

.name-text {
    font-weight: 600;
    font-size: 13px;
}

.version-text {
    text-align: center;
    font-size: 11px;
    font-weight: 500;
}

.copyright-text {
    font-size: 11px;
    font-weight: 500;

    .link {
        color: var(--text-primary-color);
        cursor: pointer;

        &:hover {
            text-decoration: underline;
        }
    }
}

::ng-deep nb-layout .layout .layout-container .content nb-layout-footer nav {
    padding: 0px;
}
