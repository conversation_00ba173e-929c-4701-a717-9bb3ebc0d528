$border-radius: calc(2 * var(--border-radius));

.button-at-bottom {
  bottom: 0;
}

.full-height {
  height: 80% !important;
  min-height: 600px !important;
}

.btn-save {
  position: absolute;
  bottom: 0;
  right: 0;
}

.btn-width {
  width: 80px;
  position: absolute;
  bottom: 10px;
  right: -500px;
}

$height: calc(100vh - 10.5rem);

.content-body {
  margin-top: 40px;
  max-height: $height;
  overflow: overlay;
  margin-bottom: 10px;

  &.final-step {
    height: $height;
  }
}

.margin-content {
  margin-top: 40px !important;
}

.margin-icon {
  margin-top: 35px;
  height: 56px;
}

.button-home {
  font-size: 16px;
  font-weight: 600;
  height: 56px;
  width: 130px;
}

.top-bottom-space {
  padding: 0px !important;
}

::ng-deep nb-layout .layout .layout-container .content.center {
  width: 960px;
  flex: 0 100 960px !important;
}

.col-center {
  margin: auto;
}

::ng-deep nb-stepper .step-content {
  padding: 0px !important;
  height: calc(100vh - 3.5rem);
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
}

.card-full-height {
  min-height: 680px;
}

::ng-deep body {
  overflow: hidden !important;
}

.card-item {
  border-radius: $border-radius;
  text-align: left;
  box-shadow: var(--gauzy-shadow);
}

.icon-item {
  font-size: 40px;
  color: var(--text-primary-color);
}

.card-item-content {
  padding: 40px 33px 40px 40px;
}

.card-item-icon {
  background: var(--gauzy-background-transparent-1) 0 0 no-repeat padding-box;
  text-align: center;
  padding-top: 40px;
  border-radius: 0 $border-radius $border-radius 0;
  position: relative;
  left: -15px;
}

.card-item ::ng-deep nb-card-body {
  padding: 0;
  overflow: unset;
}

.card-item-label {
  font-size: 18px;
  font-weight: 600;
}

.btn-fixed {
  position: fixed;
}

.pull-right {
  right: 60px;
}

.button-next-prev {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.icon-medium {
  font-size: 24px;
}

.card-small-item {
  border-radius: $border-radius;
  text-align: left;
  box-shadow: var(--gauzy-shadow);
  min-height: 6rem;

  nb-card-body {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.icon-connectivity {
  height: 55px;
  position: relative;
  top: 10px;

  img {
    filter: hue-rotate(20deg);
  }
}

.integration-label {
  font-size: 11px;
  color: var(--gauzy-text-color-2);
  font-weight: 600;
}

.margin-form {
  padding: 5px;
}

.btn-next {
  font-size: 16px;
  font-weight: 600;
  height: 56px;
  width: 100%;
}

.margin-setting-content {
  margin-top: 1rem !important;
}

.note {
  font-size: 10px;
  line-height: 0px;
}

::ng-deep nb-stepper .header {
  pointer-events: none !important;
}

.progress-setup {
  width: 50%;
  overflow: hidden;
  z-index: 1;
  position: absolute;
  bottom: 1rem;
  left: 25%;

  .progress-anim {
    transition: .75s;
    transform: translateY(100%);

    .progress-header {
      transition: .75s;
      transform: translateY(-100%);
    }

    .progress-message-container {
      background: black;
      border-radius: 1rem;
      box-shadow: 0 1px 1px 0 black;
      padding: 1rem;
    }


    .progress-message {
      overflow: auto;
      height: 35.125vh;

      .content-text {
        color: #08c234;
        font-size: 11px;
        font-family: monospace !important;
      }
    }
  }
}
.progress-setup:hover .progress-anim,
.progress-setup:hover .progress-header {
  transform: translateY(0);
}

.progress-icon {
  text-align: center;
}

.gear-small {
  font-size: 28;
}

.gear-medium {
  font-size: 35px;
  position: relative;
  top: -11px;
  left: -11px;
}

.progress-message {
  font-size: 10px;
}

.icon-spin {
  animation: rotate 2s linear 0s infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.pull-right-custom {
  right: -40px;
}

.btn-check {
  font-size: 16px;
  font-weight: 600;
  height: 56px;
  width: 100%;
}

.main-card-body {
  background-color: var(--gauzy-card-2);
  padding: 1rem;
}

h4 {
  font-weight: 600;
}

.lang-selector {
  position: absolute;
  left: 2rem;
  top: 4rem;
}

input {
  height: 42px !important;
}

::ng-deep .ssl nb-card {
  background-color: var(--gauzy-card-2);
}