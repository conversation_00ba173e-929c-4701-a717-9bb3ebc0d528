<section class="login-container">
	<div class="login-wrapper">
		<div class="svg-wrapper">
			<gauzy-logo></gauzy-logo>
			<gauzy-switch-theme></gauzy-switch-theme>
		</div>
		<div class="headings" [class]="isDemo ? 'headings-demo' : ''">
			<div class="headings-inner">
				<h2 id="title" class="title">{{ 'LOGIN_PAGE.TITLE' | translate }}</h2>
				<p class="sub-title">{{ 'LOGIN_PAGE.LOGIN_MAGIC.TITLE' | translate }}</p>
			</div>
			<ng-template [ngIf]="isCodeSent">
				<div class="sent-code-container">
					<p
						[ngClass]="{
							'normal-text': email?.value.length < 30,
							'minimum-text': email?.value.length >= 30
						}"
					>
						{{ 'LOGIN_PAGE.LOGIN_MAGIC.SUCCESS_SENT_CODE_TITLE' | translate }}
						<b class="title">{{ email?.value }}</b>
						<br />
						<span>{{ 'LOGIN_PAGE.LOGIN_MAGIC.SUCCESS_SENT_CODE_SUB_TITLE' | translate }}</span>
					</p>
				</div>
			</ng-template>
		</div>
		<div class="hr-div-strong"></div>
		<!--  -->
		<form #formDirective="ngForm" [formGroup]="form" (ngSubmit)="confirmSignInCode()">
			<!-- Email input -->
			<div class="form-control-group">
				<label class="label" for="input-email">{{ 'LOGIN_PAGE.LABELS.EMAIL' | translate }}</label>
				<nb-form-field>
					<input
						name="input-email"
						id="input-email"
						nbInput
						fullWidth
						formControlName="email"
						[placeholder]="'LOGIN_PAGE.PLACEHOLDERS.EMAIL' | translate"
						[status]="email.dirty ? (email.invalid ? 'danger' : 'success') : 'basic'"
						[attr.aria-invalid]="email.invalid && email.touched ? true : null"
						autofocus
						autocomplete="off"
						[ngClass]="isCodeSent ? 'not-allowed' : ''"
					/>
					<nb-icon
						class="edit-email"
						*ngIf="isCodeSent"
						nbSuffix
						nbButton
						size="small"
						ghost
						icon="edit-outline"
						nbTooltip="edit email"
						nbTooltipPosition="top"
					></nb-icon>
				</nb-form-field>
				<ng-container *ngIf="email.invalid && email.touched && !email.pristine">
					<p class="caption status-danger" *ngIf="email?.errors?.required">
						{{ 'LOGIN_PAGE.VALIDATION.EMAIL_REQUIRED' | translate }}
					</p>
					<p class="caption status-danger" *ngIf="email?.errors?.pattern">
						{{ 'LOGIN_PAGE.VALIDATION.EMAIL_REAL_REQUIRED' | translate }}
					</p>
				</ng-container>
			</div>
			<!-- Code input -->
			<ng-container *ngIf="isCodeSent">
				<div class="form-control-group">
					<label class="label" for="input-code">{{ 'LOGIN_PAGE.LABELS.CODE' | translate }}</label>
					<input
						name="input-code"
						id="input-code"
						nbInput
						fullWidth
						formControlName="code"
						noSpaceEdges
						[placeholder]="'LOGIN_PAGE.PLACEHOLDERS.CODE' | translate"
						[status]="code.dirty ? (code.invalid ? 'danger' : 'success') : 'basic'"
						[attr.aria-invalid]="code.invalid && code.touched ? true : null"
						maxlength="6"
						autofocus
						autocomplete="off"
					/>
					<ng-container *ngIf="code.invalid && code.touched">
						<p class="caption status-danger" *ngIf="code.errors?.required">
							{{ 'LOGIN_PAGE.VALIDATION.CODE_REQUIRED' | translate }}
						</p>
						<p class="caption status-danger" *ngIf="code.errors?.minlength">
							{{
								'LOGIN_PAGE.VALIDATION.CODE_REQUIRED_LENGTH'
									| translate : { requiredLength: code.errors?.minlength?.requiredLength }
							}}
						</p>
					</ng-container>
					<!-- Resend Code Button & Text -->
					<ng-template [ngIf]="isCodeSent">
						<p class="new-code-wrapper">
							<ng-template [ngIf]="isCodeResent" [ngIfElse]="resendButton">
								<span class="request-new-code">
									{{
										'LOGIN_PAGE.LOGIN_MAGIC.REQUEST_NEW_CODE_TITLE'
											| translate : { countdown: countdown }
									}}
								</span>
							</ng-template>

							<ng-template #resendButton>
								<a class="resend-code" debounceClick (throttledClick)="onResendCode()">
									{{ 'LOGIN_PAGE.LOGIN_MAGIC.RESEND_CODE_TITLE' | translate }}
								</a>
							</ng-template>
						</p>
					</ng-template>
				</div>
			</ng-container>
			<!-- Submit Button -->
			<div class="submit-btn-wrapper">
				<a class="forgot-email caption-2 forgot-email-big" href="mailto:<EMAIL>">
					{{ 'LOGIN_PAGE.FORGOT_EMAIL_TITLE' | translate }}
				</a>
				<div class="submit-inner-wrapper">
					<ng-template [ngIf]="isCodeSent" [ngIfElse]="sendCodeButtonTemplate">
						<button
							type="submit"
							nbButton
							size="tiny"
							class="submit-btn"
							[disabled]="form.invalid || isLoading"
						>
							<span class="btn-text">
								{{ 'BUTTONS.LOGIN' | translate }}
							</span>
							<ng-template [ngIf]="isLoading">
								<nb-icon [ngStyle]="{ display: 'none' }" *gauzySpinnerButton="isLoading"></nb-icon>
							</ng-template>
						</button>
					</ng-template>
					<ng-template #sendCodeButtonTemplate>
						<button
							type="button"
							nbButton
							size="tiny"
							class="submit-btn"
							[disabled]="email.invalid || isLoading"
							(click)="sendLoginCode()"
						>
							<span class="btn-text">
								{{ 'BUTTONS.SEND_CODE' | translate }}
							</span>
							<ng-template [ngIf]="isLoading">
								<nb-icon [ngStyle]="{ display: 'none' }" *gauzySpinnerButton="isLoading"></nb-icon>
							</ng-template>
						</button>
					</ng-template>
				</div>
			</div>
			<div class="magic-description">
				<p class="sub-title">
					{{ 'LOGIN_PAGE.LOGIN_MAGIC.DESCRIPTION_TITLE' | translate }}
					<span class="sub-title">
						<a routerLink="/auth/login">
							{{ 'LOGIN_PAGE.LOGIN_MAGIC.OR_SIGN_IN_WITH_PASSWORD' | translate }}
						</a>
					</span>
				</p>
			</div>
		</form>
		<div class="hr-div-soft"></div>
		<section>
			<ngx-social-links></ngx-social-links>
		</section>
		<div class="hr-div-soft"></div>
		<section class="another-action" aria-label="Register">
			{{ 'LOGIN_PAGE.DO_NOT_HAVE_ACCOUNT_TITLE' | translate }}
			<a class="text-link" routerLink="/auth/register">
				{{ 'BUTTONS.REGISTER' | translate }}
			</a>
		</section>
	</div>
</section>
