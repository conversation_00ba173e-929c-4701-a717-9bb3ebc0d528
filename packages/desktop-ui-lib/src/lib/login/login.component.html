<section class="login-container">
	<div class="login-wrapper">
		<div class="svg-wrapper">
			<gauzy-logo></gauzy-logo>
			<gauzy-switch-theme></gauzy-switch-theme>
		</div>
		<div class="headings">
			<div class="headings-inner">
				<h2 id="title" class="title">{{ 'LOGIN_PAGE.TITLE' | translate }}</h2>
				<p class="sub-title">{{ 'LOGIN_PAGE.SUB_TITLE' | translate }}</p>
			</div>
		</div>
		<nb-alert class="w-100" *ngIf="showMessages.error && errors?.length && !submitted" outline="danger" role="alert">
			<p class="alert-title"><b>Oh snap!</b></p>
			<ul class="alert-message-list">
				<li *ngFor="let error of errors" class="alert-message">
					{{ error }}
				</li>
			</ul>
		</nb-alert>
		<nb-alert class="w-100" *ngIf="showMessages.success && messages?.length && !submitted" outline="success" role="alert">
			<p class="alert-title"><b>Hooray!</b></p>
			<ul class="alert-message-list">
				<li *ngFor="let message of messages" class="alert-message">
					{{ message }}
				</li>
			</ul>
		</nb-alert>
		<div class="hr-div-strong"></div>
		<form (ngSubmit)="login()" #form="ngForm" aria-labelledby="title">
			<div class="form-control-group">
				<label class="label" for="input-email">
					{{ 'LOGIN_PAGE.LABELS.EMAIL' | translate }}
				</label>
				<input nbInput fullWidth [(ngModel)]="user.email" #email="ngModel" name="email" id="input-email"
					pattern=".+@.+\..+" [placeholder]="'LOGIN_PAGE.PLACEHOLDERS.EMAIL' | translate" fieldSize="large"
					autofocus [status]="
					  email.dirty
						  ? email.invalid
							  ? 'danger'
							  : 'success'
						  : 'basic'
				  " [required]="getConfigValue('forms.validation.email.required')" [attr.aria-invalid]="
					  email.invalid && email.touched ? true : null
				  " autocomplete="off" />
				<ng-container *ngIf="email.invalid && email.touched && !email.pristine">
					<p class="caption status-danger" *ngIf="email.errors?.required">
						{{ 'LOGIN_PAGE.VALIDATION.EMAIL_REQUIRED' | translate }}
					</p>
					<p class="caption status-danger" *ngIf="email.errors?.pattern">
						{{
						'LOGIN_PAGE.VALIDATION.EMAIL_REAL_REQUIRED' | translate
						}}
					</p>
				</ng-container>
			</div>
			<div class="form-control-group">
				<span class="label-with-link">
					<label class="label" for="input-password">
						{{ 'LOGIN_PAGE.LABELS.PASSWORD' | translate }}
					</label>
				</span>
				<nb-form-field>
					<input nbInput fullWidth [(ngModel)]="user.password" #password="ngModel" name="password"
						[type]="showPassword ? 'text' : 'password'" id="input-password"
						[placeholder]="'LOGIN_PAGE.PLACEHOLDERS.PASSWORD' | translate" fieldSize="large" [status]="
					  password.dirty
						  ? password.invalid
							  ? 'danger'
							  : 'success'
						  : 'basic'
				  " [required]="
					  getConfigValue('forms.validation.password.required')
				  " [minlength]="
					  getConfigValue('forms.validation.password.minLength')
				  " [maxlength]="
					  getConfigValue('forms.validation.password.maxLength')
				  " [attr.aria-invalid]="
					  password.invalid && password.touched ? true : null
				  " autocomplete="off" />
					<a nbSuffix nbButton size="small" ghost (click)="showPassword = !showPassword"
						class="show-password-icon">
						<nb-icon [icon]="showPassword ? 'eye-outline' : 'eye-off-outline'" pack="eva"
							[attr.aria-label]="showPassword ? 'hide password' : 'show password'"></nb-icon>
					</a>
				</nb-form-field>
				<ng-container *ngIf="password.invalid && password.touched && !password.pristine">
					<p class="caption status-danger" *ngIf="password.errors?.required">
						{{ 'LOGIN_PAGE.VALIDATION.PASSWORD_REQUIRED' | translate }}
					</p>
					<p class="caption status-danger" *ngIf="
						  password.errors?.minlength || password.errors?.maxlength
					  ">
						{{
						'LOGIN_PAGE.VALIDATION.PASSWORD_SHOULD_CONTAIN'
						| translate
						: {
						minLength: getConfigValue(
						'forms.validation.password.minLength'
						),
						maxLength: getConfigValue(
						'forms.validation.password.maxLength'
						)
						}
						}}
					</p>
				</ng-container>
			</div>
			<div class="form-control-group accept-group" *ngIf="!isAgent">
				<nb-checkbox name="rememberMe" [(ngModel)]="user.rememberMe" class="remember-me" *ngIf="rememberMe">{{
					'LOGIN_PAGE.REMEMBER_ME_TITLE' | translate }}</nb-checkbox>
			</div>
			<div class="submit-btn-wrapper">
				<a class="forgot-password caption-2 forgot-password-small" (click)="forgot()">
					{{ 'LOGIN_PAGE.FORGOT_PASSWORD_TITLE' | translate }}
				</a>
				<div class="submit-inner-wrapper">
					<button type="submit" nbButton size="small" class="submit-btn" [disabled]="submitted || !form.valid"
						[class.btn-pulse]="submitted">
						{{ 'BUTTONS.LOGIN' | translate }}
						<nb-icon [ngStyle]="{'display': 'none'}" *gauzySpinnerButton="submitted"></nb-icon>
					</button>

				</div>
			</div>
			<div class="login-magic-wrapper">
				<a routerLink="/auth/login-magic">
					{{ 'LOGIN_PAGE.LOGIN_MAGIC.TITLE' | translate }}
				</a>
			</div>
		</form>
		<div class="hr-div-soft"></div>
		<ngx-social-links *ngIf="!isAgent"></ngx-social-links>
		<div class="hr-div-soft"></div>
		<section *ngIf="!isAgent" class="another-action" aria-label="Sign In Workspace">
			{{ 'WORKSPACES.UNKNOWN_WORKSPACE' | translate }}
			<a class="text-link" routerLink="/auth/login-workspace">
				{{ 'WORKSPACES.FIND_WORKSPACE' | translate }}
			</a>
		</section>
		<section *ngIf="!isAgent" class="another-action" aria-label="Register">
			{{ 'LOGIN_PAGE.DO_NOT_HAVE_ACCOUNT_TITLE' | translate }}
			<a class="text-link" (click)="register()">
				{{ 'BUTTONS.REGISTER' | translate }}
			</a>
		</section>
	</div>
</section>
