@use 'themes' as *;

@mixin avatarMixin($radius: var(--border-radius), $imageSize: 48px) {
	.inner-wrapper {
		display: flex;
		align-items: center;
		overflow: hidden;
		border-radius: $radius;
		gap: 4px;
	}

	.avatar-wrapper,
	.image-container,
	img {
		border-radius: $radius !important;
	}

	.names-wrapper {
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
	}

	.link-text {
		cursor: pointer;
		text-decoration: none;
		font-style: normal;
		line-height: 15px;
		letter-spacing: 0;
		white-space: nowrap;
		width: 100%;
		text-overflow: ellipsis;

		&:hover {
			text-decoration: underline;
		}
	}

	.image-container {
		width: $imageSize;
		cursor: pointer;
		display: flex;
		position: relative;

		img {
			width: $imageSize;
			height: $imageSize;
			object-fit: cover;
		}

		.status-indicator {
			position: absolute;
			width: calc($imageSize / 2);
			height: calc($imageSize / 2);
			border-radius: calc($radius / 2);
			border: 2px solid #ebebeb;
			right: 0;
			top: 0;

			&.online {
				background-color: #4caf50;
				/* Green for online */
			}

			&.offline {
				background-color: #f44336;
				/* Red for offline */
			}
		}
	}
}

@mixin avatarSizeVariant($size) {
	.image-container {
		width: $size;

		img {
			width: $size;
			height: $size;
		}
	}
}

:host {
	display: block;
	@include avatarMixin();

	.link-text {
		font-size: 14px;
		font-weight: 600;
		line-height: 16px;
		color: nb-theme(gauzy-text-contact);
	}

	.caption {
		font-size: 11px;
		font-weight: 400;
		line-height: 11px;
		color: var(--gauzy-text-color-2);
	}

	@include avatarSizeVariant(48px);

	&.lg {
		@include avatarSizeVariant(64px);
	}

	&.sm {
		@include avatarSizeVariant(32px);
	}
}

:host-context(.report-table),
:host-context(.avatar-dashboard),
:host-context(.workspace) {
	width: 100%;

	.inner-wrapper {
		width: fit-content;
		padding: calc(var(--border-radius) / 4);
		background-color: var(--color-primary-transparent-100);
	}

	.link-text {
		color: var(--text-primary-color) !important;
	}
}

:host-context(.avatar-dashboard) {
	.link-text {
		font-size: 14px;
		font-weight: 600;
		color: var(--gauzy-text-color-1) !important;
	}

	&.activity {
		@include avatarMixin($radius: var(--button-rectangle-border-radius), $imageSize: 28px);
	}
}

:host-context(.workspace) {
	@include avatarMixin($radius: calc(var(--border-radius) / 2), $imageSize: 24px);

	.names-wrapper a.link-text {
		text-decoration: none;
	}

	.inner-wrapper {
		max-width: 135px;
	}
}
