.error-border {
	border: solid 1px red;
	border-radius: var(--border-radius);
}

.black-bold-text {
	color: var(--gauzy-text-color-1);
	font-weight: 600;
}

.gray-bold-text {
	color: var(--gauzy-text-color-2);
	font-weight: 600;
}

$input-background-color: var(--gauzy-card-1);
$height: 42px;

:host {

	::ng-deep input,
	::ng-deep nb-select.appearance-outline.status-basic .select-button,
	::ng-deep .ng-select .ng-select-container {
		background-color: $input-background-color !important;
		border: none;
		height: $height !important;
		min-height: unset;
	}

	::ng-deep .ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-placeholder,
	::ng-deep .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-input {
		top: unset !important;
	}

	::ng-deep label,
	::ng-deep .label {
		font-size: 11px;
		font-weight: 600;
		line-height: 13px;
		letter-spacing: -0.01em;
		color: var(--gauzy-text-color-2);
	}

	::ng-deep textarea {
		background-color: $input-background-color !important;
		border: none;
	}

	::ng-deep .ng-select .ng-select-container,
	::ng-deep nb-tag-list {
		input {
			background-color: unset !important;
		}
	}

	.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value {
		background-color: transparent !important;
		margin-right: 0;
	}

	.ng-select .ng-select-focused .ng-select-container {

		&:hover,
		&:focus,
		&:active,
		&:visited {
			box-shadow: var(--gauzy-shadow) inset !important;
		}
	}

	.ng-select .ng-select-container {
		box-shadow: var(--gauzy-shadow) inset !important;
	}
}

:host {
	.multiple-select {
		width: 100%;
	}

	ng-select {
		.selector-template {
			display: flex;
			align-items: center;
			height: 100%;
		}

		img {
			margin-right: 5px;
			border-radius: calc(var(--border-radius) * 1 / 2);
			box-shadow: var(--gauzy-shadow);
		}
	}

	::ng-deep {
		.ng-select .ng-select-container .ng-value-container {
			align-items: center;
			padding-left: 10px;

			span.text-container {
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
			}
		}
	}
}
