import { Injectable } from '@angular/core';
import { HttpRequest, HttpHandler, HttpEvent, HttpInterceptor } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Store } from '../services';

@Injectable()
export class TokenInterceptor implements HttpInterceptor {
	constructor(private store: Store) { }
	intercept(
		request: HttpRequest<any>,
		next: <PERSON>ttpHandler
	): Observable<HttpEvent<any>> {
		const token = this.store.token;
		if (token) {
			request = request.clone({
				setHeaders: {
					Authorization: `Bearer ${token}`
				}
			});
		}
		return next.handle(request);
	}
}
