@use 'themes' as *;

:host {
    width: 100%;

    .single-project-template {
        display: flex;
        align-items: center;
        padding-top: 5px;

        .logo-wrapper {
            min-width: 28px;
            min-height: 28px;
            width: 28px;
            height: 28px;
            @include nb-rtl(margin-left, 10px);
            @include nb-ltr(margin-right, 10px);

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                border-radius: var(--border-radius);
                box-shadow: var(--gauzy-shadow);
            }
        }

        .headers-wrapper {
            display: flex;
            flex-direction: column;
            overflow: hidden;

            .main-header {
                font-size: 14px;
                font-style: normal;
                font-weight: 600;
                line-height: 17px;
                letter-spacing: 0em;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .employees-count {
                font-size: 11px;
                font-style: normal;
                font-weight: 400;
                line-height: 13px;
                letter-spacing: 0em;
            }
        }
    }
}
