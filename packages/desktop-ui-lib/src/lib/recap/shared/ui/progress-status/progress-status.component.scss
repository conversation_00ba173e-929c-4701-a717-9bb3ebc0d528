@use 'var' as *;

.wrapper {
	display: flex;
	align-items: center;
	gap: 0.5rem
}

:host {
	display: block;

	.percentage-col {
		width: 90px;
		font-size: 14px;
		font-weight: 500;
		color: var(--gauzy-text-color-2);
	}

	::ng-deep {
		nb-progress-bar {
			.progress-container {
				height: 5px !important;
			}
		}
	}
}

:host-context(.report-progress) {
	width: 100%;

	.percentage-col {
		width: 60px;
	}

	.progress-col {
		width: 100%;
		display: flex;
		align-items: flex-end;
	}

	::ng-deep {
		.progress-container {
			height: 1rem !important;
			border-radius: calc(var(--border-radius) / 2);
		}

		nb-progress-bar {
			width: 100%;
		}
	}

	.wrapper {
		width: 100%;
		display: flex;

	}
}

@include respond(sm) {
	.wrapper {
		flex-wrap: wrap;
	}
}
