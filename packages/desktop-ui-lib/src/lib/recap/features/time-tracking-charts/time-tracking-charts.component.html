<nb-card>
	<nb-card-header>{{'TIMER_TRACKER.RECAP.HOURLY_TIME_TRACKING' | translate}} &#183; <span class="today">{{dailyDuration$ | async | durationFormat : 'h[h]
			m[m]':
			{trim: 'both'}
			}}</span> &#183;
		<span class="today">{{dailyActivities$ | async | percent:'1.0-2' }}</span></nb-card-header>
	<nb-card-body *ngIf="(chartData$ | async).length; else noDataChart">
		<ngx-charts-bar-vertical class="charts-bar-vertical" [animations]="animations" [results]="(chartData$ | async)" [gradient]="gradient"
			[xAxis]="showXAxis" [yAxis]="showYAxis" [legend]="showLegend" [showXAxisLabel]="showXAxisLabel"
			[showYAxisLabel]="showYAxisLabel" [xAxisLabel]="xAxisLabel" [yAxisLabel]="yAxisLabel"
			[barPadding]="barPadding" [roundEdges]="roundEdges" [rotateXAxisTicks]="rotateXAxisTicks"
			[scheme]="colorScheme" [maxXAxisTickLength]="maxXAxisTickLength" [wrapTicks]="wrapTicks">
			<ng-template #tooltipTemplate let-model="model">
				<div class="model-container">
					<h1>{{ model.name }}</h1>
					<nb-badge class="model-badge" [status]="getStatus(model.value)"
						[text]="model.value | durationFormat : 'h[h] m[m] s[s]': {trim: 'both' } "></nb-badge>
				</div>
			</ng-template>
		</ngx-charts-bar-vertical>
	</nb-card-body>
</nb-card>
<ng-template #noDataChart>
	<ngx-no-data-message [message]="'TIMER_TRACKER.RECAP.HOURLY_TIME_TRACKING_DATA' | translate"></ngx-no-data-message>
</ng-template>
