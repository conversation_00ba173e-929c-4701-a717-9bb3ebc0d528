nb-card {
	background-color: var(--gauzy-card-1);
	height: 100%;
	margin: 0 !important;

	nb-card-body {
		padding: 0 !important;
	}

	.charts-bar-vertical {
		fill: var(--gauzy-text-color-2);
   		font-weight: 500;
	}
}

.today {
	font-size: 12px;
	font-weight: 500;
	color: var(--gauzy-text-color-2);
}

.model-container {
	position: relative;
	padding: 2px;

	h1 {
		color: #fff;
	}

	.model-badge {
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		position: relative;
		width: fit-content;
		height: 1.5rem;
		font-size: 12px;
		font-weight: 600;
		letter-spacing: 0;
		text-align: left;
		padding: 2px 4px;
		line-height: 1;
		border-radius: calc(var(--border-radius) / 3);
	}
}
