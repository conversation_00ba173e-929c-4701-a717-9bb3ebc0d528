<button nbButton [nbPopover]="gauzyFilter" size="small" nbPopoverTrigger="click" status="basic">
	<nb-icon icon="more-horizontal-outline"></nb-icon>
</button>

<ng-template #gauzyFilter>
	<ngx-gauzy-filters class="vertical" [filters]="filters$ | async" [hasTimeZoneFilter]="false" [saveFilters]="false"
		(filtersChange)="filtersChange($event)"></ngx-gauzy-filters>
	<ngx-auto-refresh></ngx-auto-refresh>
</ng-template>
