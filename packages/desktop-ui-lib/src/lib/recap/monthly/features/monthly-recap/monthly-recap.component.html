<nb-card class="main-report-wrapper">
	<nb-card-header class="p-3 main-report-header">
		<ng-container *ngIf="isLoading$ | async">
			<nb-icon class="loader" size="medium" icon="loader-outline"></nb-icon>
		</ng-container>
		<div class="tools ml-auto">
			<ngx-monthly-calendar></ngx-monthly-calendar>
			<ngx-filter></ngx-filter>
		</div>
	</nb-card-header>
	<nb-card-body class="main-report-body recap-monthly">
		<ngx-monthly-statistic></ngx-monthly-statistic>
		<ngx-monthly-progress class="h-100"></ngx-monthly-progress>
	</nb-card-body>
</nb-card>
