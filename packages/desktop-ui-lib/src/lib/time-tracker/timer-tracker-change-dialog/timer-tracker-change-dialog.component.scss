@use 'gauzy/_gauzy-dialogs' as *;

nb-card {
	width: 320px;
	background-color: nb-theme(gauzy-card-1);

	&.large {
		width: 420px;
	}

	nb-card-header {
		padding: 1rem;

		nb-icon.close {
			cursor: pointer;
		}
	}

	nb-card-body {
		padding: 1rem;

		&.selector-container {
			display: flex;
			justify-content: center;
			align-items: center;
			width: 100%;
			gap: .5rem;
			flex-direction: column;
			position: relative;

			.selector {
				width: 100%;
			}
		}

	}

	nb-card-footer {
		padding: 1rem;
		display: flex;
		justify-content: flex-end;
		gap: 1rem;
	}
}

.full-width {
	width: 100%;
}
