<div class="task-render">
	<div (click)="showDetail()" class="detail">
		<div *ngIf="number" class="task-number">
			{{ number }}
		</div>
		<div class="task-title-container">
			<div class="task-title">{{ title }}</div>
		</div>
	</div>
	<div class="badges">
		<div *ngIf="priority">
			<gauzy-task-badge-view
				[taskBadge]="priority"
			></gauzy-task-badge-view>
		</div>
		<div *ngIf="size">
			<gauzy-task-badge-view [taskBadge]="size"></gauzy-task-badge-view>
		</div>
	</div>
</div>
