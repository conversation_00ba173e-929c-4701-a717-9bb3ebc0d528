{"name": "@gauzy/desktop-ui-lib", "version": "0.1.0", "description": "Ever Gauzy Platform desktop libs", "author": {"name": "Ever Co. LTD", "email": "<EMAIL>", "url": "https://ever.co"}, "repository": {"type": "git", "url": "https://github.com/ever-co/ever-gauzy", "directory": "packages/desktop-ui-lib"}, "bugs": {"url": "https://github.com/ever-co/ever-gauzy/issues"}, "homepage": "https://ever.co", "license": "AGPL-3.0", "private": true, "scripts": {"lib:build": "yarn nx build desktop-ui-lib --configuration=development", "lib:build:prod": "yarn nx build desktop-ui-lib --configuration=production", "lib:watch": "yarn nx build desktop-ui-lib --watch --configuration=development"}, "peerDependencies": {"@angular/common": "^19.2.0", "@angular/core": "^19.2.0"}, "dependencies": {"@angular-slider/ngx-slider": "^19.0.0", "@angular/animations": "^19.2.10", "@angular/forms": "^19.2.10", "@angular/platform-browser": "^19.2.10", "@angular/platform-browser-dynamic": "^19.2.10", "@angular/router": "^19.2.10", "@datorama/akita": "^8.0.1", "@datorama/akita-ngdevtools": "^7.0.0", "@electron/remote": "^2.0.8", "@fortawesome/angular-fontawesome": "^1.0.0", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@gauzy/constants": "^0.1.0", "@gauzy/contracts": "^0.1.0", "@gauzy/desktop-window": "^0.1.0", "@kurkle/color": "^0.3.4", "@nebular/auth": "^15.0.0", "@nebular/bootstrap": "^9.1.0-rc.6", "@nebular/eva-icons": "^15.0.0", "@nebular/security": "^15.0.0", "@nebular/theme": "^15.0.0", "@ng-select/ng-select": "^14.8.1", "@ngneat/until-destroy": "^10.0.0", "@ngx-translate/core": "^16.0.4", "@ngx-translate/http-loader": "^16.0.1", "@ngneat/effects-ng": "3.1.4", "@swimlane/ngx-charts": "^22.0.0-alpha.1", "angular2-smart-table": "^3.6.2", "ckeditor4-angular": "4.0.1", "electron-log": "^4.4.8", "electron-store": "^8.1.0", "hash-it": "^6.0.0", "moment": "^2.30.1", "moment-duration-format": "^2.3.2", "ngx-daterangepicker-material": "^6.0.4", "rxjs": "^7.8.2", "tslib": "^2.6.2", "underscore": "^1.13.3"}, "devDependencies": {"@types/jest": "29.5.14", "@types/moment-duration-format": "^2.2.3", "@types/node": "^20.14.9", "electron": "^30.0.1", "jest-preset-angular": "14.5.5"}, "keywords": ["angular", "desktop-ui", "ui-library", "electron", "ngx", "angular-library", "ever-gauzy", "gauzy", "<PERSON><PERSON><PERSON>", "nebular", "smart-table", "charts", "ngx-translate", "angular-ui", "electron-angular"], "engines": {"node": ">=20.18.1", "yarn": ">=1.22.19"}, "sideEffects": false}