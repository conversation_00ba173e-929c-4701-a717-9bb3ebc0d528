# @gauzy/utils

This library was generated with [Nx](https://nx.dev).

## Description

`@gauzy/utils` is a collection of common utility functions for streamlined development in JavaScript and TypeScript. It is designed for use across various frameworks, including Angular, NestJS, or any TypeScript-based environment.

## Installation

Install the package via npm or yarn:

```bash
npm install @gauzy/utils
# or
yarn add @gauzy/utils
```

## Build

Run `nx build utils` to build the library. The build artifacts will be stored in the `dist/` directory.

## Publishing

After building your library with `nx build utils`, go to the dist folder `cd dist/utils` and run `npm publish`.

## Running unit tests

Run `nx test utils` to execute the unit tests via [Jest](https://jestjs.io).
