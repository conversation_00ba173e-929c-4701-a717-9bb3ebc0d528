{"name": "@gauzy/utils", "version": "0.1.0", "description": "A set of common utility functions for streamlined development in JavaScript and TypeScript, compatible with Angular, NestJS, and other frameworks.", "author": {"name": "Ever Co. LTD", "email": "<EMAIL>", "url": "https://ever.co"}, "repository": {"type": "git", "url": "https://github.com/ever-co/ever-gauzy", "directory": "packages/utils"}, "bugs": {"url": "https://github.com/ever-co/ever-gauzy/issues"}, "homepage": "https://ever.co", "license": "AGPL-3.0", "private": true, "type": "commonjs", "main": "./src/index.js", "typings": "./src/index.d.ts", "scripts": {"lib:build": "yarn nx build utils", "lib:build:prod": "yarn nx build utils", "lib:watch": "yarn nx build utils --watch"}, "dependencies": {"@gauzy/constants": "^0.1.0", "generate-password": "^1.7.1", "slugify": "^1.6.6", "tslib": "^2.6.2"}, "devDependencies": {"@types/jest": "29.5.14", "@types/node": "^20.14.9", "typescript": "^5.8.3"}, "keywords": ["common", "utils", "utility", "typescript", "libraries", "helpers", "cross-framework", "toolkit"], "engines": {"node": ">=20.18.1", "yarn": ">=1.22.19"}, "sideEffects": false}