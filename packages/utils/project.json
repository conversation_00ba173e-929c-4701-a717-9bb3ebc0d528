{"name": "utils", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/utils/src", "projectType": "library", "release": {"version": {"generatorOptions": {"packageRoot": "dist/{projectRoot}", "currentVersionResolver": "git-tag"}}}, "tags": [], "implicitDependencies": [], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/utils", "main": "packages/utils/src/index.ts", "tsConfig": "packages/utils/tsconfig.lib.json", "assets": ["packages/utils/*.md"]}}, "nx-release-publish": {"options": {"packageRoot": "dist/{projectRoot}"}}, "lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/utils/jest.config.ts"}}}}