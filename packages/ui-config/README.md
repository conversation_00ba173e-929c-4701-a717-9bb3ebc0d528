# @gauzy/ui-config

This library was generated with [Nx](https://nx.dev).

## Features

- **Environment Settings:** Provides environment settings and configurations to be shared across various Angular applications within the Gauzy ecosystem.

## Getting Started

The `@gauzy/ui-config` library is generated with [Nx](https://nx.dev) and is intended to be used within the Gauzy ecosystem. Before installing, make sure you have a Gauzy workspace set up and that all necessary dependencies are in place.

## Table of Contents

- [Installation](#installation)
- [Usage](#usage)
- [Development](#development)
- [Contributing](#contributing)
- [License](#license)

## Running unit tests

Run `nx test ui-config` to execute the unit tests.

## Publishing

After building your library with `yarn nx build ui-config`, go to the dist folder `dist/packages/ui-config` and run `npm publish`.

## Installation

To install the `@gauzy/ui-config` library, run the following command in your Angular project:

```bash
npm install @gauzy/ui-auth
# or
yarn add @gauzy/ui-auth
```
