{"name": "@gauzy/desktop-window", "version": "0.1.0", "description": "Ever Gauzy Platform desktop window module", "author": {"name": "Ever Co. LTD", "email": "<EMAIL>", "url": "https://ever.co"}, "repository": {"type": "git", "url": "https://github.com/ever-co/ever-gauzy", "directory": "packages/desktop-window"}, "bugs": {"url": "https://github.com/ever-co/ever-gauzy/issues"}, "homepage": "https://ever.co", "license": "AGPL-3.0", "private": true, "type": "commonjs", "main": "./src/index.js", "typings": "./src/index.d.ts", "scripts": {"lib:build": "yarn nx build desktop-window", "lib:build:prod": "yarn nx build desktop-window", "lib:watch": "yarn nx build desktop-window --watch"}, "dependencies": {"@electron/remote": "^2.0.8", "@gauzy/desktop-core": "^0.1.0", "electron-log": "^4.4.8", "electron-store": "^8.1.0", "tslib": "^2.6.2"}, "devDependencies": {"@types/node": "^20.14.9", "electron": "^30.0.1"}, "keywords": ["desktop", "window", "electron", "desktop-application", "gauzy", "desktop-integration", "remote", "electron-log", "electron-store"], "engines": {"node": ">=20.18.1", "yarn": ">=1.22.19"}, "sideEffects": false}