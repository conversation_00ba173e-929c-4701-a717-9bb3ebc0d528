{"name": "desktop-window", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/desktop-window/src", "projectType": "library", "release": {"version": {"generatorOptions": {"packageRoot": "dist/{projectRoot}", "currentVersionResolver": "git-tag"}}}, "tags": [], "implicitDependencies": [], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/desktop-window", "main": "packages/desktop-window/src/index.ts", "tsConfig": "packages/desktop-window/tsconfig.lib.json", "assets": ["packages/desktop-window/*.md"]}}, "nx-release-publish": {"options": {"packageRoot": "dist/{projectRoot}"}}, "lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/desktop-window/jest.config.ts"}}}}