{"name": "@gauzy/plugin", "version": "0.1.0", "description": "Ever Gauzy Platform Plugin module", "author": {"name": "Ever Co. LTD", "email": "<EMAIL>", "url": "https://ever.co"}, "repository": {"type": "git", "url": "https://github.com/ever-co/ever-gauzy", "directory": "packages/plugin"}, "bugs": {"url": "https://github.com/ever-co/ever-gauzy/issues"}, "homepage": "https://ever.co", "license": "AGPL-3.0", "private": true, "type": "commonjs", "main": "./src/index.js", "typings": "./src/index.d.ts", "scripts": {"lib:build": "yarn nx build plugin", "lib:build:prod": "yarn nx build plugin", "lib:watch": "yarn nx build plugin --watch"}, "dependencies": {"@gauzy/config": "^0.1.0", "@gauzy/utils": "^0.1.0", "@nestjs/common": "^11.1.0", "@nestjs/core": "^11.1.0", "chalk": "^4.1.0", "tslib": "^2.6.2", "underscore": "^1.13.3"}, "devDependencies": {"@types/jest": "29.5.14", "@types/node": "^20.14.9", "typescript": "^5.8.3"}, "keywords": ["plugin", "ever-gauzy", "<PERSON><PERSON><PERSON>", "typescript", "common-module", "shared-module", "platform", "chalk", "underscore", "modular", "enterprise", "open-source", "agpl", "nx", "plugin-system"], "engines": {"node": ">=20.18.1", "yarn": ">=1.22.19"}, "sideEffects": false}