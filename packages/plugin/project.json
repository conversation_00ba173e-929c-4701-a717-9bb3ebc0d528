{"name": "plugin", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/plugin/src", "projectType": "library", "release": {"version": {"generatorOptions": {"packageRoot": "dist/{projectRoot}", "currentVersionResolver": "git-tag"}}}, "tags": [], "implicitDependencies": [], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/plugin", "tsConfig": "packages/plugin/tsconfig.lib.json", "packageJson": "packages/plugin/package.json", "main": "packages/plugin/src/index.ts", "assets": ["packages/plugin/*.md"]}}, "nx-release-publish": {"options": {"packageRoot": "dist/{projectRoot}"}}, "lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/plugin/jest.config.ts"}}}}