# @gauzy/plugin

This library was generated with [Nx](https://nx.dev). It contains the Plugin Module for the Gauzy platform.

## Building

Run `nx build plugin` to build the library.

## Running unit tests

Run `nx test plugin` to execute the unit tests via [Jest](https://jestjs.io).

## Publishing

After building your library with `yarn nx build plugin`, go to the dist folder `dist/packages/plugin` and run `npm publish`.

## Installation

Install the Plugin Module using your preferred package manager:

```bash
npm install @gauzy/plugin
# or
yarn add @gauzy/plugin
```
