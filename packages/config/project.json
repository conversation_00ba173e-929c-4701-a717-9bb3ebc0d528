{"name": "config", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/config/src", "projectType": "library", "release": {"version": {"generatorOptions": {"packageRoot": "dist/{projectRoot}", "currentVersionResolver": "git-tag"}}}, "tags": [], "implicitDependencies": [], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/config", "tsConfig": "packages/config/tsconfig.lib.json", "packageJson": "packages/config/package.json", "main": "packages/config/src/index.ts", "assets": ["packages/config/*.md"]}}, "nx-release-publish": {"options": {"packageRoot": "dist/{projectRoot}"}}, "lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/config/jest.config.ts"}}}}