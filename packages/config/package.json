{"name": "@gauzy/config", "version": "0.1.0", "description": "Ever Gauzy Platform Config module", "author": {"name": "Ever Co. LTD", "email": "<EMAIL>", "url": "https://ever.co"}, "repository": {"type": "git", "url": "https://github.com/ever-co/ever-gauzy", "directory": "packages/config"}, "bugs": {"url": "https://github.com/ever-co/ever-gauzy/issues"}, "homepage": "https://ever.co", "license": "AGPL-3.0", "private": true, "type": "commonjs", "main": "./src/index.js", "typings": "./src/index.d.ts", "scripts": {"lib:build": "yarn nx build config", "lib:build:prod": "cross-env NODE_ENV=production yarn ts-node scripts/replace-env-files.ts --environment=prod && yarn nx build config", "lib:watch": "yarn nx build config --watch"}, "dependencies": {"@gauzy/constants": "^0.1.0", "@gauzy/common": "^0.1.0", "@gauzy/contracts": "^0.1.0", "@gauzy/utils": "^0.1.0", "@mikro-orm/better-sqlite": "^6.4.13", "@mikro-orm/core": "^6.4.13", "@mikro-orm/mysql": "^6.4.13", "@mikro-orm/nestjs": "^6.1.1", "@mikro-orm/postgresql": "^6.4.13", "@nestjs/common": "^11.1.0", "@nestjs/config": "^4.0.2", "@nestjs/typeorm": "^11.0.0", "app-root-path": "^3.0.0", "chalk": "^4.1.0", "dotenv": "^16.0.3", "mikro-orm-soft-delete": "^1.0.0-alpha.1", "nest-knexjs": "^0.0.26", "tslib": "^2.6.2", "typeorm": "^0.3.24"}, "devDependencies": {"@types/jest": "29.5.14", "@types/node": "^20.14.9", "cross-env": "^7.0.3", "typescript": "^5.8.3"}, "keywords": ["api", "config", "nest", "<PERSON><PERSON><PERSON>", "gauzy", "platform", "module", "configuration"], "engines": {"node": ">=20.18.1", "yarn": ">=1.22.19"}, "sideEffects": false}