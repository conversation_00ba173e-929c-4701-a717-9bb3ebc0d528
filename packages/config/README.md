# @gauzy/config

This library was generated with [Nx](https://nx.dev). It contains the configuration for the Gauzy API platform.

## Building

Run `nx build config` to build the library.

## Running unit tests

Run `nx test config` to execute the unit tests via [Jest](https://jestjs.io).

## Publishing

After building your library with `yarn nx build config`, go to the dist folder `dist/packages/config` and run `npm publish`.

## Installation

To install the API config Library, simply run the following command in your terminal:

```bash
npm install @gauzy/config
# or
yarn add @gauzy/config
```
