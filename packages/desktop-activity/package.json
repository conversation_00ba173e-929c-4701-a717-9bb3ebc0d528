{"name": "@gauzy/desktop-activity", "version": "0.1.0", "description": "Ever Gauzy Platform desktop activity", "author": {"name": "Ever Co. LTD", "email": "<EMAIL>", "url": "https://ever.co"}, "repository": {"type": "git", "url": "https://github.com/ever-co/ever-gauzy", "directory": "packages/desktop-activity"}, "bugs": {"url": "https://github.com/ever-co/ever-gauzy/issues"}, "homepage": "https://ever.co", "license": "AGPL-3.0", "private": true, "type": "commonjs", "main": "./src/index.js", "typings": "./src/index.d.ts", "scripts": {"lib:build": "yarn nx build desktop-activity", "lib:build:prod": "yarn nx build desktop-activity", "lib:watch": "yarn nx build desktop-activity --watch"}, "dependencies": {"moment": "^2.30.1", "uiohook-napi": "^1.5.4", "tslib": "^2.6.2", "underscore": "^1.13.3", "get-windows": "^9.2.0"}, "devDependencies": {"@types/node": "^20.14.9", "@types/unzipper": "^0.10.9", "electron": "^30.0.1", "@types/underscore": "^1.11.4"}, "keywords": ["electron", "desktop", "desktop-activity", "ever-gauzy", "typescript", "sqlite", "knex", "embedded-queue", "moment", "offline-mode", "active-window", "file-management", "enterprise", "platform", "open-source", "agpl"], "engines": {"node": ">=20.18.1", "yarn": ">=1.22.19"}, "sideEffects": false}