# @gauzy/desktop-activity

This library was generated with [Nx](https://nx.dev).

## Building

Run `yarn nx build desktop-activity` to build the library.

## Running unit tests

Run `yarn nx test desktop-activity` to execute the unit tests via [Jest](https://jestjs.io).

## Publishing

After building your library with `yarn nx build desktop-activity`, go to the dist folder `dist/packages/desktop-activity` and run `npm publish`.

## Installation

Install the Desktop Activity Library using your preferred package manager:

```bash
npm install @gauzy/desktop-activity
# or
yarn add @gauzy/desktop-activity
