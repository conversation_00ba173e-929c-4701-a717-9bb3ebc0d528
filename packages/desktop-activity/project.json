{"name": "desktop-activity", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/desktop-activity/src", "projectType": "library", "release": {"version": {"generatorOptions": {"packageRoot": "dist/{projectRoot}", "currentVersionResolver": "git-tag"}}}, "tags": [], "implicitDependencies": [], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/desktop-activity", "main": "packages/desktop-activity/src/index.ts", "tsConfig": "packages/desktop-activity/tsconfig.lib.json", "assets": ["packages/desktop-activity/*.md"]}}, "nx-release-publish": {"options": {"packageRoot": "dist/{projectRoot}"}}, "lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/desktop-activity/jest.config.ts"}}}}