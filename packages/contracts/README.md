# @gauzy/contracts

This library was generated with [Nx](https://nx.dev). It contains the contracts for the Gauzy platform.

## Building

Run `nx build contracts` to build the library.

## Running unit tests

Run `nx test contracts` to execute the unit tests via [Jest](https://jestjs.io).

## Publishing

After building your library with `yarn nx build contracts`, go to the dist folder `dist/packages/contracts` and run `npm publish`.

## Installation

To install the Contracts Library, simply run the following command in your terminal:

```bash
npm install @gauzy/contracts
# or
yarn add @gauzy/contracts
```
