{"name": "contracts", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/contracts/src", "projectType": "library", "release": {"version": {"generatorOptions": {"packageRoot": "dist/{projectRoot}", "currentVersionResolver": "git-tag"}}}, "tags": [], "implicitDependencies": [], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/contracts", "tsConfig": "packages/contracts/tsconfig.lib.json", "packageJson": "packages/contracts/package.json", "main": "packages/contracts/src/index.ts", "assets": ["packages/contracts/*.md"]}}, "nx-release-publish": {"options": {"packageRoot": "dist/{projectRoot}"}}, "lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/contracts/jest.config.ts"}}}}