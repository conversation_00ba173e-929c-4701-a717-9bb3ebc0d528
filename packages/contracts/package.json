{"name": "@gauzy/contracts", "version": "0.1.0", "description": "Ever Gauzy Platform Contracts module", "author": {"name": "Ever Co. LTD", "email": "<EMAIL>", "url": "https://ever.co"}, "repository": {"type": "git", "url": "https://github.com/ever-co/ever-gauzy", "directory": "packages/contracts"}, "bugs": {"url": "https://github.com/ever-co/ever-gauzy/issues"}, "homepage": "https://ever.co", "license": "AGPL-3.0", "private": true, "type": "commonjs", "main": "./src/index.js", "typings": "./src/index.d.ts", "scripts": {"lib:build": "yarn nx build contracts", "lib:build:prod": "yarn nx build contracts", "lib:watch": "yarn nx build contracts --watch"}, "dependencies": {"@gauzy/constants": "^0.1.0", "tslib": "^2.6.2"}, "devDependencies": {"@types/jest": "29.5.14", "@types/node": "^20.14.9", "typescript": "^5.8.3"}, "keywords": ["<PERSON><PERSON><PERSON>", "typescript", "contracts", "shared-module", "common-module", "ever-gauzy", "platform", "open-source", "enterprise", "agpl", "nx"], "engines": {"node": ">=20.18.1", "yarn": ">=1.22.19"}, "sideEffects": false}