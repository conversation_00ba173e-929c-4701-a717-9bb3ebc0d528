import { IBaseEntityModel } from './base-entity.model';

export interface ICurrency extends IBaseEntityModel {
	isoCode: string;
	currency: string;
}

export const DEFAULT_CURRENCIES = {
	AFN: 'Afghan Afghani',
	AFA: 'Afghan Afghani (1927\u20132002)',
	ALL: 'Albanian Lek',
	ALK: 'Albanian Lek (1946\u20131965)',
	DZD: 'Algerian Dinar',
	ADP: 'Andorran Peseta',
	AOA: 'Angolan Kwanza',
	AOK: 'Angolan Kwanza (1977\u20131991)',
	AON: 'Angolan New Kwanza (1990\u20132000)',
	AOR: 'Angolan Readjusted Kwanza (1995\u20131999)',
	ARA: 'Argentine Austral',
	ARS: 'Argentine Peso',
	ARM: 'Argentine Peso (1881\u20131970)',
	ARP: 'Argentine Peso (1983\u20131985)',
	ARL: 'Argentine Peso Ley (1970\u20131983)',
	AMD: 'Armenian Dram',
	AWG: 'Aruban Florin',
	AUD: 'Australian Dollar',
	ATS: 'Austrian Schilling',
	AZN: 'Azerbaijani Manat',
	AZM: 'Azerbaijani Manat (1993\u20132006)',
	BSD: 'Bahamian Dollar',
	BHD: 'Bahraini Dinar',
	BDT: 'Bangladeshi Taka',
	BBD: 'Barbadian Dollar',
	BYN: 'Belarusian Ruble',
	BYB: 'Belarusian Ruble (1994\u20131999)',
	BYR: 'Belarusian Ruble (2000\u20132016)',
	BEF: 'Belgian Franc',
	BEC: 'Belgian Franc (convertible)',
	BEL: 'Belgian Franc (financial)',
	BZD: 'Belize Dollar',
	BMD: 'Bermudan Dollar',
	BTN: 'Bhutanese Ngultrum',
	BOB: 'Bolivian Boliviano',
	BOL: 'Bolivian Boliviano (1863\u20131963)',
	BOV: 'Bolivian Mvdol',
	BOP: 'Bolivian Peso',
	BAM: 'Bosnia-Herzegovina Convertible Mark',
	BAD: 'Bosnia-Herzegovina Dinar (1992\u20131994)',
	BAN: 'Bosnia-Herzegovina New Dinar (1994\u20131997)',
	BWP: 'Botswanan Pula',
	BRC: 'Brazilian Cruzado (1986\u20131989)',
	BRZ: 'Brazilian Cruzeiro (1942\u20131967)',
	BRE: 'Brazilian Cruzeiro (1990\u20131993)',
	BRR: 'Brazilian Cruzeiro (1993\u20131994)',
	BRN: 'Brazilian New Cruzado (1989\u20131990)',
	BRB: 'Brazilian New Cruzeiro (1967\u20131986)',
	BRL: 'Brazilian Real',
	GBP: 'British Pound',
	BND: 'Brunei Dollar',
	BGL: 'Bulgarian Hard Lev',
	BGN: 'Bulgarian Lev',
	BGO: 'Bulgarian Lev (1879\u20131952)',
	BGM: 'Bulgarian Socialist Lev',
	BUK: 'Burmese Kyat',
	BIF: 'Burundian Franc',
	XPF: 'CFP Franc',
	KHR: 'Cambodian Riel',
	CAD: 'Canadian Dollar',
	CVE: 'Cape Verdean Escudo',
	KYD: 'Cayman Islands Dollar',
	XAF: 'Central African CFA Franc',
	CLE: 'Chilean Escudo',
	CLP: 'Chilean Peso',
	CLF: 'Chilean Unit of Account (UF)',
	CNX: 'Chinese People\u2019s Bank Dollar',
	CNY: 'Chinese Yuan',
	COP: 'Colombian Peso',
	COU: 'Colombian Real Value Unit',
	KMF: 'Comorian Franc',
	CDF: 'Congolese Franc',
	CRC: 'Costa Rican Col\u00f3n',
	HRD: 'Croatian Dinar',
	HRK: 'Croatian Kuna',
	CUC: 'Cuban Convertible Peso',
	CUP: 'Cuban Peso',
	CYP: 'Cypriot Pound',
	CZK: 'Czech Koruna',
	CSK: 'Czechoslovak Hard Koruna',
	DKK: 'Danish Krone',
	DJF: 'Djiboutian Franc',
	DOP: 'Dominican Peso',
	NLG: 'Dutch Guilder',
	XCD: 'East Caribbean Dollar',
	DDM: 'East German Mark',
	ECS: 'Ecuadorian Sucre',
	ECV: 'Ecuadorian Unit of Constant Value',
	EGP: 'Egyptian Pound',
	GQE: 'Equatorial Guinean Ekwele',
	ERN: 'Eritrean Nakfa',
	EEK: 'Estonian Kroon',
	ETB: 'Ethiopian Birr',
	EUR: 'Euro',
	XEU: 'European Currency Unit',
	FKP: 'Falkland Islands Pound',
	FJD: 'Fijian Dollar',
	FIM: 'Finnish Markka',
	FRF: 'French Franc',
	XFO: 'French Gold Franc',
	XFU: 'French UIC-Franc',
	GMD: 'Gambian Dalasi',
	GEK: 'Georgian Kupon Larit',
	GEL: 'Georgian Lari',
	DEM: 'German Mark',
	GHS: 'Ghanaian Cedi',
	GHC: 'Ghanaian Cedi (1979\u20132007)',
	GIP: 'Gibraltar Pound',
	GRD: 'Greek Drachma',
	GTQ: 'Guatemalan Quetzal',
	GWP: 'Guinea-Bissau Peso',
	GNF: 'Guinean Franc',
	GNS: 'Guinean Syli',
	GYD: 'Guyanaese Dollar',
	HTG: 'Haitian Gourde',
	HNL: 'Honduran Lempira',
	HKD: 'Hong Kong Dollar',
	HUF: 'Hungarian Forint',
	ISK: 'Icelandic Kr\u00f3na',
	ISJ: 'Icelandic Kr\u00f3na (1918\u20131981)',
	INR: 'Indian Rupee',
	IDR: 'Indonesian Rupiah',
	IRR: 'Iranian Rial',
	IQD: 'Iraqi Dinar',
	IEP: 'Irish Pound',
	ILS: 'Israeli New Shekel',
	ILP: 'Israeli Pound',
	ILR: 'Israeli Shekel (1980\u20131985)',
	ITL: 'Italian Lira',
	JMD: 'Jamaican Dollar',
	JPY: 'Japanese Yen',
	JOD: 'Jordanian Dinar',
	KZT: 'Kazakhstani Tenge',
	KES: 'Kenyan Shilling',
	KWD: 'Kuwaiti Dinar',
	KGS: 'Kyrgystani Som',
	LAK: 'Laotian Kip',
	LVL: 'Latvian Lats',
	LVR: 'Latvian Ruble',
	LBP: 'Lebanese Pound',
	LSL: 'Lesotho Loti',
	LRD: 'Liberian Dollar',
	LYD: 'Libyan Dinar',
	LTL: 'Lithuanian Litas',
	LTT: 'Lithuanian Talonas',
	LUL: 'Luxembourg Financial Franc',
	LUC: 'Luxembourgian Convertible Franc',
	LUF: 'Luxembourgian Franc',
	MOP: 'Macanese Pataca',
	MKD: 'Macedonian Denar',
	MKN: 'Macedonian Denar (1992\u20131993)',
	MGA: 'Malagasy Ariary',
	MGF: 'Malagasy Franc',
	MWK: 'Malawian Kwacha',
	MYR: 'Malaysian Ringgit',
	MVR: 'Maldivian Rufiyaa',
	MVP: 'Maldivian Rupee (1947\u20131981)',
	MLF: 'Malian Franc',
	MTL: 'Maltese Lira',
	MTP: 'Maltese Pound',
	MRO: 'Mauritanian Ouguiya',
	MUR: 'Mauritian Rupee',
	MXV: 'Mexican Investment Unit',
	MXN: 'Mexican Peso',
	MXP: 'Mexican Silver Peso (1861\u20131992)',
	MDC: 'Moldovan Cupon',
	MDL: 'Moldovan Leu',
	MCF: 'Monegasque Franc',
	MNT: 'Mongolian Tugrik',
	MAD: 'Moroccan Dirham',
	MAF: 'Moroccan Franc',
	MZE: 'Mozambican Escudo',
	MZN: 'Mozambican Metical',
	MZM: 'Mozambican Metical (1980\u20132006)',
	MMK: 'Myanmar Kyat',
	NAD: 'Namibian Dollar',
	NPR: 'Nepalese Rupee',
	ANG: 'Netherlands Antillean Guilder',
	TWD: 'New Taiwan Dollar',
	NZD: 'New Zealand Dollar',
	NIO: 'Nicaraguan C\u00f3rdoba',
	NIC: 'Nicaraguan C\u00f3rdoba (1988\u20131991)',
	NGN: 'Nigerian Naira',
	KPW: 'North Korean Won',
	NOK: 'Norwegian Krone',
	OMR: 'Omani Rial',
	PKR: 'Pakistani Rupee',
	PAB: 'Panamanian Balboa',
	PGK: 'Papua New Guinean Kina',
	PYG: 'Paraguayan Guarani',
	PEI: 'Peruvian Inti',
	PEN: 'Peruvian Sol',
	PES: 'Peruvian Sol (1863\u20131965)',
	PHP: 'Philippine Peso',
	PLN: 'Polish Zloty',
	PLZ: 'Polish Zloty (1950\u20131995)',
	PTE: 'Portuguese Escudo',
	GWE: 'Portuguese Guinea Escudo',
	QAR: 'Qatari Rial',
	XRE: 'RINET Funds',
	RHD: 'Rhodesian Dollar',
	RON: 'Romanian Leu',
	ROL: 'Romanian Leu (1952\u20132006)',
	RUB: 'Russian Ruble',
	RUR: 'Russian Ruble (1991\u20131998)',
	RWF: 'Rwandan Franc',
	SVC: 'Salvadoran Col\u00f3n',
	WST: 'Samoan Tala',
	SAR: 'Saudi Riyal',
	RSD: 'Serbian Dinar',
	CSD: 'Serbian Dinar (2002\u20132006)',
	SCR: 'Seychellois Rupee',
	SLL: 'Sierra Leonean Leone',
	SGD: 'Singapore Dollar',
	SKK: 'Slovak Koruna',
	SIT: 'Slovenian Tolar',
	SBD: 'Solomon Islands Dollar',
	SOS: 'Somali Shilling',
	ZAR: 'South African Rand',
	ZAL: 'South African Rand (financial)',
	KRH: 'South Korean Hwan (1953\u20131962)',
	KRW: 'South Korean Won',
	KRO: 'South Korean Won (1945\u20131953)',
	SSP: 'South Sudanese Pound',
	SUR: 'Soviet Rouble',
	ESP: 'Spanish Peseta',
	ESA: 'Spanish Peseta (A account)',
	ESB: 'Spanish Peseta (convertible account)',
	LKR: 'Sri Lankan Rupee',
	SHP: 'St. Helena Pound',
	SDD: 'Sudanese Dinar (1992\u20132007)',
	SDG: 'Sudanese Pound',
	SDP: 'Sudanese Pound (1957\u20131998)',
	SRD: 'Surinamese Dollar',
	SRG: 'Surinamese Guilder',
	SZL: 'Swazi Lilangeni',
	SEK: 'Swedish Krona',
	CHF: 'Swiss Franc',
	SYP: 'Syrian Pound',
	STD: 'S\u00e3o Tom\u00e9 & Pr\u00edncipe Dobra',
	TJR: 'Tajikistani Ruble',
	TJS: 'Tajikistani Somoni',
	TZS: 'Tanzanian Shilling',
	THB: 'Thai Baht',
	TPE: 'Timorese Escudo',
	TOP: 'Tongan Pa\u02bbanga',
	TTD: 'Trinidad & Tobago Dollar',
	TND: 'Tunisian Dinar',
	TRY: 'Turkish Lira',
	TRL: 'Turkish Lira (1922\u20132005)',
	TMT: 'Turkmenistani Manat',
	TMM: 'Turkmenistani Manat (1993\u20132009)',
	USD: 'US Dollar',
	USN: 'US Dollar (Next day)',
	USS: 'US Dollar (Same day)',
	UGX: 'Ugandan Shilling',
	UGS: 'Ugandan Shilling (1966\u20131987)',
	UAH: 'Ukrainian Hryvnia',
	UAK: 'Ukrainian Karbovanets',
	AED: 'United Arab Emirates Dirham',
	UYU: 'Uruguayan Peso',
	UYP: 'Uruguayan Peso (1975\u20131993)',
	UYI: 'Uruguayan Peso (Indexed Units)',
	UZS: 'Uzbekistani Som',
	VUV: 'Vanuatu Vatu',
	VEF: 'Venezuelan Bol\u00edvar',
	VEB: 'Venezuelan Bol\u00edvar (1871\u20132008)',
	VND: 'Vietnamese Dong',
	VNN: 'Vietnamese Dong (1978\u20131985)',
	CHE: 'WIR Euro',
	CHW: 'WIR Franc',
	XOF: 'West African CFA Franc',
	YDD: 'Yemeni Dinar',
	YER: 'Yemeni Rial',
	YUN: 'Yugoslavian Convertible Dinar (1990\u20131992)',
	YUD: 'Yugoslavian Hard Dinar (1966\u20131990)',
	YUM: 'Yugoslavian New Dinar (1994\u20132002)',
	YUR: 'Yugoslavian Reformed Dinar (1992\u20131993)',
	ZRN: 'Zairean New Zaire (1993\u20131998)',
	ZRZ: 'Zairean Zaire (1971\u20131993)',
	ZMW: 'Zambian Kwacha',
	ZMK: 'Zambian Kwacha (1968\u20132012)',
	ZWD: 'Zimbabwean Dollar (1980\u20132008)',
	ZWR: 'Zimbabwean Dollar (2008)',
	ZWL: 'Zimbabwean Dollar (2009)'
};

export enum CurrenciesEnum {
	AFN = 'AFN',
	AFA = 'AFA',
	ALL = 'ALL',
	ALK = 'ALK',
	DZD = 'DZD',
	ADP = 'ADP',
	AOA = 'AOA',
	AOK = 'AOK',
	AON = 'AON',
	AOR = 'AOR',
	ARA = 'ARA',
	ARS = 'ARS',
	ARM = 'ARM',
	ARP = 'ARP',
	ARL = 'ARL',
	AMD = 'AMD',
	AWG = 'AWG',
	AUD = 'AUD',
	ATS = 'ATS',
	AZN = 'AZN',
	AZM = 'AZM',
	BSD = 'BSD',
	BHD = 'BHD',
	BDT = 'BDT',
	BBD = 'BBD',
	BYN = 'BYN',
	BYB = 'BYB',
	BYR = 'BYR',
	BEF = 'BEF',
	BEC = 'BEC',
	BEL = 'BEL',
	BZD = 'BZD',
	BMD = 'BMD',
	BTN = 'BTN',
	BOB = 'BOB',
	BOL = 'BOL',
	BOV = 'BOV',
	BOP = 'BOP',
	BAM = 'BAM',
	BAD = 'BAD',
	BAN = 'BAN',
	BWP = 'BWP',
	BRC = 'BRC',
	BRZ = 'BRZ',
	BRE = 'BRE',
	BRR = 'BRR',
	BRN = 'BRN',
	BRB = 'BRB',
	BRL = 'BRL',
	GBP = 'GBP',
	BND = 'BND',
	BGL = 'BGL',
	BGN = 'BGN',
	BGO = 'BGO',
	BGM = 'BGM',
	BUK = 'BUK',
	BIF = 'BIF',
	XPF = 'XPF',
	KHR = 'KHR',
	CAD = 'CAD',
	CVE = 'CVE',
	KYD = 'KYD',
	XAF = 'XAF',
	CLE = 'CLE',
	CLP = 'CLP',
	CLF = 'CLF',
	CNX = 'CNX',
	CNY = 'CNY',
	COP = 'COP',
	COU = 'COU',
	KMF = 'KMF',
	CDF = 'CDF',
	CRC = 'CRC',
	HRD = 'HRD',
	HRK = 'HRK',
	CUC = 'CUC',
	CUP = 'CUP',
	CYP = 'CYP',
	CZK = 'CZK',
	CSK = 'CSK',
	DKK = 'DKK',
	DJF = 'DJF',
	DOP = 'DOP',
	NLG = 'NLG',
	XCD = 'XCD',
	DDM = 'DDM',
	ECS = 'ECS',
	ECV = 'ECV',
	EGP = 'EGP',
	GQE = 'GQE',
	ERN = 'ERN',
	EEK = 'EEK',
	ETB = 'ETB',
	EUR = 'EUR',
	XEU = 'XEU',
	FKP = 'FKP',
	FJD = 'FJD',
	FIM = 'FIM',
	FRF = 'FRF',
	XFO = 'XFO',
	XFU = 'XFU',
	GMD = 'GMD',
	GEK = 'GEK',
	GEL = 'GEL',
	DEM = 'DEM',
	GHS = 'GHS',
	GHC = 'GHC',
	GIP = 'GIP',
	GRD = 'GRD',
	GTQ = 'GTQ',
	GWP = 'GWP',
	GNF = 'GNF',
	GNS = 'GNS',
	GYD = 'GYD',
	HTG = 'HTG',
	HNL = 'HNL',
	HKD = 'HKD',
	HUF = 'HUF',
	ISK = 'ISK',
	ISJ = 'ISJ',
	INR = 'INR',
	IDR = 'IDR',
	IRR = 'IRR',
	IQD = 'IQD',
	IEP = 'IEP',
	ILS = 'ILS',
	ILP = 'ILP',
	ILR = 'ILR',
	ITL = 'ITL',
	JMD = 'JMD',
	JPY = 'JPY',
	JOD = 'JOD',
	KZT = 'KZT',
	KES = 'KES',
	KWD = 'KWD',
	KGS = 'KGS',
	LAK = 'LAK',
	LVL = 'LVL',
	LVR = 'LVR',
	LBP = 'LBP',
	LSL = 'LSL',
	LRD = 'LRD',
	LYD = 'LYD',
	LTL = 'LTL',
	LTT = 'LTT',
	LUL = 'LUL',
	LUC = 'LUC',
	LUF = 'LUF',
	MOP = 'MOP',
	MKD = 'MKD',
	MKN = 'MKN',
	MGA = 'MGA',
	MGF = 'MGF',
	MWK = 'MWK',
	MYR = 'MYR',
	MVR = 'MVR',
	MVP = 'MVP',
	MLF = 'MLF',
	MTL = 'MTL',
	MTP = 'MTP',
	MRO = 'MRO',
	MUR = 'MUR',
	MXV = 'MXV',
	MXN = 'MXN',
	MXP = 'MXP',
	MDC = 'MDC',
	MDL = 'MDL',
	MCF = 'MCF',
	MNT = 'MNT',
	MAD = 'MAD',
	MAF = 'MAF',
	MZE = 'MZE',
	MZN = 'MZN',
	MZM = 'MZM',
	MMK = 'MMK',
	NAD = 'NAD',
	NPR = 'NPR',
	ANG = 'ANG',
	TWD = 'TWD',
	NZD = 'NZD',
	NIO = 'NIO',
	NIC = 'NIC',
	NGN = 'NGN',
	KPW = 'KPW',
	NOK = 'NOK',
	OMR = 'OMR',
	PKR = 'PKR',
	PAB = 'PAB',
	PGK = 'PGK',
	PYG = 'PYG',
	PEI = 'PEI',
	PEN = 'PEN',
	PES = 'PES',
	PHP = 'PHP',
	PLN = 'PLN',
	PLZ = 'PLZ',
	PTE = 'PTE',
	GWE = 'GWE',
	QAR = 'QAR',
	XRE = 'XRE',
	RHD = 'RHD',
	RON = 'RON',
	ROL = 'ROL',
	RUB = 'RUB',
	RUR = 'RUR',
	RWF = 'RWF',
	SVC = 'SVC',
	WST = 'WST',
	SAR = 'SAR',
	RSD = 'RSD',
	CSD = 'CSD',
	SCR = 'SCR',
	SLL = 'SLL',
	SGD = 'SGD',
	SKK = 'SKK',
	SIT = 'SIT',
	SBD = 'SBD',
	SOS = 'SOS',
	ZAR = 'ZAR',
	ZAL = 'ZAL',
	KRH = 'KRH',
	KRW = 'KRW',
	KRO = 'KRO',
	SSP = 'SSP',
	SUR = 'SUR',
	ESP = 'ESP',
	ESA = 'ESA',
	ESB = 'ESB',
	LKR = 'LKR',
	SHP = 'SHP',
	SDD = 'SDD',
	SDG = 'SDG',
	SDP = 'SDP',
	SRD = 'SRD',
	SRG = 'SRG',
	SZL = 'SZL',
	SEK = 'SEK',
	CHF = 'CHF',
	SYP = 'SYP',
	STD = 'STD',
	TJR = 'TJR',
	TJS = 'TJS',
	TZS = 'TZS',
	THB = 'THB',
	TPE = 'TPE',
	TOP = 'TOP',
	TTD = 'TTD',
	TND = 'TND',
	TRY = 'TRY',
	TRL = 'TRL',
	TMT = 'TMT',
	TMM = 'TMM',
	USD = 'USD',
	USN = 'USN',
	USS = 'USS',
	UGX = 'UGX',
	UGS = 'UGS',
	UAH = 'UAH',
	UAK = 'UAK',
	AED = 'AED',
	UYU = 'UYU',
	UYP = 'UYP',
	UYI = 'UYI',
	UZS = 'UZS',
	VUV = 'VUV',
	VEF = 'VEF',
	VEB = 'VEB',
	VND = 'VND',
	VNN = 'VNN',
	CHE = 'CHE',
	CHW = 'CHW',
	XOF = 'XOF',
	YDD = 'YDD',
	YER = 'YER',
	YUN = 'YUN',
	YUD = 'YUD',
	YUM = 'YUM',
	YUR = 'YUR',
	ZRN = 'ZRN',
	ZRZ = 'ZRZ',
	ZMW = 'ZMW',
	ZMK = 'ZMK',
	ZWD = 'ZWD',
	ZWR = 'ZWR',
	ZWL = 'ZWL'
}
