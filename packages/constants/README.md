# @gauzy/constants

This library was generated with [Nx](https://nx.dev). It contains constants used internally in different UI/API packages.

## Building

Run `yarn nx build constants` to build the library.

## Running unit tests

Run `yarn nx test constants` to execute the unit tests via [Jest](https://jestjs.io).

## Publishing

After building your library with `yarn nx build constants`, go to the dist folder `dist/packages/constants` and run `npm publish`.

## Installation

Install the Package using your preferred package manager:

```bash
npm install @gauzy/constants
# or
yarn add @gauzy/constants
```
