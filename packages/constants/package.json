{"name": "@gauzy/constants", "version": "0.1.0", "description": "", "author": {"name": "Ever Co. LTD", "email": "<EMAIL>", "url": "https://ever.co"}, "repository": {"type": "git", "url": "https://github.com/ever-co/ever-gauzy", "directory": "packages/constants"}, "bugs": {"url": "https://github.com/ever-co/ever-gauzy/issues"}, "homepage": "https://ever.co", "license": "AGPL-3.0", "private": true, "type": "commonjs", "main": "./src/index.js", "typings": "./src/index.d.ts", "scripts": {"lib:build": "yarn nx build constants", "lib:build:prod": "yarn nx build constants", "lib:watch": "yarn nx build constants --watch"}, "dependencies": {"tslib": "^2.6.2"}, "devDependencies": {"@types/node": "^20.14.9"}, "keywords": [], "engines": {"node": ">=20.18.1", "yarn": ">=1.22.19"}, "sideEffects": false}