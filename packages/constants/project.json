{"name": "constants", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/constants/src", "projectType": "library", "release": {"version": {"generatorOptions": {"packageRoot": "dist/{projectRoot}", "currentVersionResolver": "git-tag"}}}, "tags": [], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/constants", "main": "packages/constants/src/index.ts", "tsConfig": "packages/constants/tsconfig.lib.json", "assets": ["packages/constants/*.md"]}}, "nx-release-publish": {"options": {"packageRoot": "dist/{projectRoot}"}}, "lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/constants/jest.config.ts"}}}}