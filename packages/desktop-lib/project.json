{"name": "desktop-lib", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/desktop-lib/src", "projectType": "library", "release": {"version": {"generatorOptions": {"packageRoot": "dist/{projectRoot}", "currentVersionResolver": "git-tag"}}}, "tags": [], "implicitDependencies": [], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/desktop-lib", "main": "packages/desktop-lib/src/index.ts", "packageJson": "packages/desktop-lib/package.json", "tsConfig": "packages/desktop-lib/tsconfig.lib.json", "assets": ["packages/desktop-lib/*.md"]}}, "nx-release-publish": {"options": {"packageRoot": "dist/{projectRoot}"}}, "lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/desktop-lib/jest.config.ts"}}}}