# @gauzy/desktop-lib

This library was generated with [Nx](https://nx.dev).

## Building

Run `yarn nx build desktop-lib` to build the library.

## Running unit tests

Run `yarn nx test desktop-lib` to execute the unit tests via [Jest](https://jestjs.io).

## Publishing

After building your library with `yarn nx build desktop-lib`, go to the dist folder `dist/packages/desktop-lib` and run `npm publish`.

## Installation

Install the Desktop Libs Library using your preferred package manager:

```bash
npm install @gauzy/desktop-lib
# or
yarn add @gauzy/desktop-lib
```
