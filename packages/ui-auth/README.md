# @gauzy/ui-auth

The `@gauzy/ui-auth` library provides authentication-related components, services, and utilities specifically designed for the Gauzy platform, enabling streamlined and secure user authentication and management.

## Features

- **Login & Sign Up Components:** Pre-built components for user login and sign up flows.
- **Auth Guards & Interceptors:** Protect routes and intercept HTTP requests to ensure secure access.

## Getting Started

The `@gauzy/ui-auth` library is generated with [Nx](https://nx.dev) and is intended to be used within the Gauzy ecosystem. Before installing, make sure you have a Gauzy workspace set up and that all necessary dependencies are in place.

## Building

Run `yarn nx build ui-auth` to build the library.

## Running unit tests

Run `yarn nx test ui-auth` to execute the unit tests.

## Publishing

After building your library with `yarn nx build ui-auth`, go to the dist folder `dist/packages/ui-auth` and run `npm publish`.

## Installation

To install `@gauzy/ui-auth` in your Gauzy project, run one of the following commands in your terminal:

```bash
npm install @gauzy/ui-auth
# or
yarn add @gauzy/ui-auth
```
