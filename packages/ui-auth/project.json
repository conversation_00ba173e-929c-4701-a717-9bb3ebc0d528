{"name": "ui-auth", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/ui-auth/src", "prefix": "lib", "projectType": "library", "tags": [], "implicitDependencies": [], "targets": {"build": {"executor": "@nx/angular:package", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "packages/ui-auth/ng-package.json", "tsConfig": "packages/ui-auth/tsconfig.json"}, "configurations": {"production": {"tsConfig": "packages/ui-auth/tsconfig.lib.prod.json"}, "development": {"tsConfig": "packages/ui-auth/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "packages/ui-auth/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}