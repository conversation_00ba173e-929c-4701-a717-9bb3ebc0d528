<section class="section-wrapper">
	<div class="forgot-password-wrapper">
		<div class="svg-wrapper">
			<ngx-gauzy-logo [isAccordion]="false" class="ever-logo-svg"></ngx-gauzy-logo>
			<gauzy-switch-theme class="theme-switch" [hasText]="false"></gauzy-switch-theme>
		</div>
		<h1 id="title" class="title">
			{{ 'FORGOT_PASSWORD_PAGE.TITLE' | translate }}
		</h1>
		<p class="sub-title">
			{{ 'FORGOT_PASSWORD_PAGE.SUB_TITLE' | translate }}
		</p>
		<nb-alert *ngIf="showMessages.error && errors?.length && !submitted" outline="danger" role="alert">
			<p class="alert-title">
				<b>{{ 'FORGOT_PASSWORD_PAGE.ALERT_TITLE' | translate }}</b>
			</p>
			<ul class="alert-message-list">
				<li *ngFor="let error of errors" class="alert-message">
					{{ error }}
				</li>
			</ul>
		</nb-alert>
		<nb-alert *ngIf="showMessages.success && messages?.length && !submitted" outline="success" role="alert">
			<p class="alert-title">
				<b>{{ 'FORGOT_PASSWORD_PAGE.ALERT_SUCCESS_TITLE' | translate }}</b>
			</p>
			<ul class="alert-message-list">
				<li *ngFor="let message of messages" class="alert-message">
					{{ message }}
				</li>
			</ul>
		</nb-alert>
		<div class="hr-div-strong"></div>
		<form (ngSubmit)="requestPass()" #requestPassForm="ngForm" aria-labelledby="title" class="form">
			<div class="form-control-group">
				<label class="label" for="input-email">{{ 'FORGOT_PASSWORD_PAGE.LABELS.EMAIL' | translate }}</label>
				<input
					nbInput
					noSpaceEdges
					[(ngModel)]="user.email"
					#email="ngModel"
					id="input-email"
					name="email"
					pattern=".+@.+\..+"
					[placeholder]="'FORGOT_PASSWORD_PAGE.PLACEHOLDERS.EMAIL' | translate"
					autofocus
					fullWidth
					fieldSize="large"
					[status]="email.dirty ? (email.invalid ? 'danger' : 'success') : 'basic'"
					[required]="getConfigValue('forms.validation.email.required')"
					[attr.aria-invalid]="email.invalid && email.touched ? true : null"
				/>
				<ng-container *ngIf="email.invalid && email.touched">
					<p class="caption status-danger" *ngIf="email.errors?.required">
						{{ 'FORGOT_PASSWORD_PAGE.VALIDATION.EMAIL_REQUIRED' | translate }}
					</p>
					<p class="caption status-danger" *ngIf="email.errors?.pattern">
						{{ 'FORGOT_PASSWORD_PAGE.VALIDATION.EMAIL_REAL_REQUIRED' | translate }}
					</p>
				</ng-container>
			</div>
			<div class="submit-btn-wrapper">
				<button
					nbButton
					size="small"
					[disabled]="submitted || !requestPassForm.valid"
					[class.btn-pulse]="submitted"
					class="submit-btn"
				>
					{{ 'FORGOT_PASSWORD_PAGE.REQUEST_PASSWORD_TEXT' | translate }}
				</button>
			</div>
		</form>
		<div class="hr-div-soft"></div>
		<section class="sign-in-or-up" aria-label="Sign in or sign up">
			<p class="redirect-link-p">
				<span>
					{{ 'FORGOT_PASSWORD_PAGE.BACK_TO_LOGIN' | translate }}
				</span>
				<a class="text-link" routerLink="/auth/login">
					<span>{{ 'BUTTONS.LOGIN' | translate }}</span>
				</a>
			</p>
			<p class="redirect-link-p">
				<a routerLink="/auth/register" class="text-link">
					{{ 'BUTTONS.REGISTER' | translate }}
				</a>
			</p>
		</section>
	</div>
	<ngx-faq></ngx-faq>
</section>
