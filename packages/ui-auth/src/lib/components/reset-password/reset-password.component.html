<section class="section-wrapper">
	<div class="reset-password-wrapper">
		<div class="svg-wrapper">
			<ngx-gauzy-logo [isAccordion]="false" class="ever-logo-svg"></ngx-gauzy-logo>
			<gauzy-switch-theme class="theme-switch" [hasText]="false"></gauzy-switch-theme>
		</div>
		<h1 id="title" class="title">{{ 'RESET_PASSWORD_PAGE.TITLE' | translate }}</h1>
		<p class="sub-title">{{ 'RESET_PASSWORD_PAGE.SUB_TITLE' | translate }}</p>

		<nb-alert *ngIf="showMessages.error && errors?.length && !submitted" outline="danger" role="alert">
			<p class="alert-title"><b>Oh snap!</b></p>
			<ul class="alert-message-list">
				<li *ngFor="let error of errors" class="alert-message">{{ error }}</li>
			</ul>
		</nb-alert>

		<nb-alert *ngIf="showMessages.success && messages?.length && !submitted" outline="success" role="alert">
			<p class="alert-title"><b>Hooray!</b></p>
			<ul class="alert-message-list">
				<li *ngFor="let message of messages" class="alert-message">{{ message }}</li>
			</ul>
		</nb-alert>
		<div class="hr-div-strong"></div>

		<form (ngSubmit)="resetPass()" #resetPassForm="ngForm" aria-labelledby="title" class="form">
			<div class="form-control-group">
				<label class="label" for="input-password">
					{{ 'RESET_PASSWORD_PAGE.LABELS.NEW_PASSWORD' | translate }}
				</label>
				<nb-form-field>
					<input
						nbInput
						[(ngModel)]="user.password"
						#password="ngModel"
						[type]="showPassword ? 'text' : 'password'"
						id="input-password"
						name="password"
						class="first"
						[placeholder]="'RESET_PASSWORD_PAGE.PLACEHOLDERS.NEW_PASSWORD' | translate"
						autofocus
						fullWidth
						fieldSize="large"
						[status]="password.dirty ? (password.invalid ? 'danger' : 'success') : 'basic'"
						[required]="getConfigValue('forms.validation.password.required')"
						[minlength]="getConfigValue('forms.validation.password.minLength')"
						[maxlength]="getConfigValue('forms.validation.password.maxLength')"
						[attr.aria-invalid]="password.invalid && password.touched ? true : null"
					/>
					<a
						nbSuffix
						nbButton
						size="small"
						ghost
						(click)="showPassword = !showPassword"
						class="show-password-icon"
					>
						<nb-icon
							[icon]="showPassword ? 'eye-outline' : 'eye-off-outline'"
							pack="eva"
							[attr.aria-label]="showPassword ? 'hide password' : 'show password'"
						></nb-icon>
					</a>
				</nb-form-field>
				<ng-container *ngIf="password.invalid && password.touched">
					<p class="caption status-danger" *ngIf="password.errors?.required">
						{{ 'RESET_PASSWORD_PAGE.VALIDATION.NEW_PASSWORD_REQUIRED' | translate }}
					</p>
					<p class="caption status-danger" *ngIf="password.errors?.minlength || password.errors?.maxlength">
						{{
							'RESET_PASSWORD_PAGE.VALIDATION.PASSWORD_SHOULD_CONTAIN'
								| translate
									: {
											minLength: getConfigValue('forms.validation.password.minLength'),
											maxLength: getConfigValue('forms.validation.password.maxLength')
									  }
						}}
					</p>
				</ng-container>
			</div>

			<div class="form-group">
				<label class="label" for="input-re-password">
					{{ 'RESET_PASSWORD_PAGE.LABELS.CONFIRM_PASSWORD' | translate }}
				</label>
				<nb-form-field>
					<input
						nbInput
						[(ngModel)]="user.confirmPassword"
						#rePass="ngModel"
						id="input-re-password"
						name="rePass"
						[type]="showConfirmPassword ? 'text' : 'password'"
						class="last"
						[placeholder]="'RESET_PASSWORD_PAGE.PLACEHOLDERS.CONFIRM_PASSWORD' | translate"
						fullWidth
						fieldSize="large"
						[status]="
							rePass.touched
								? rePass.invalid || password.value != rePass.value
									? 'danger'
									: 'success'
								: 'basic'
						"
						[required]="getConfigValue('forms.validation.password.required')"
						[attr.aria-invalid]="rePass.invalid && rePass.touched ? true : null"
					/>
					<a
						nbSuffix
						nbButton
						size="small"
						ghost
						(click)="showConfirmPassword = !showConfirmPassword"
						class="show-password-icon"
					>
						<nb-icon
							[icon]="showConfirmPassword ? 'eye-outline' : 'eye-off-outline'"
							pack="eva"
							[attr.aria-label]="showConfirmPassword ? 'hide password' : 'show password'"
						></nb-icon>
					</a>
				</nb-form-field>
				<ng-container *ngIf="rePass.touched">
					<p class="caption status-danger" *ngIf="rePass.invalid && rePass.errors?.required">
						{{ 'RESET_PASSWORD_PAGE.VALIDATION.CONFIRM_PASSWORD_REQUIRED' | translate }}
					</p>
					<p class="caption status-danger" *ngIf="password.value != rePass.value && !rePass.errors?.required">
						{{ 'RESET_PASSWORD_PAGE.VALIDATION.PASSWORDS_NOT_MATCH' | translate }}
					</p>
				</ng-container>
			</div>

			<div class="submit-btn-wrapper">
				<button
					nbButton
					[disabled]="submitted || !resetPassForm.valid || password.value != rePass.value"
					[class.btn-pulse]="submitted"
					class="submit-btn"
				>
					{{ 'BUTTONS.CHANGE_PASSWORD' | translate }}
				</button>
			</div>
		</form>

		<div class="hr-div-soft"></div>
		<section class="sign-in-or-up" aria-label="Sign in or sign up">
			<p class="redirect-link-p">
				<span>{{ 'RESET_PASSWORD_PAGE.LABELS.BACK_TO' | translate }} </span>
				<a class="text-link" routerLink="../login">
					<span>{{ 'BUTTONS.LOGIN' | translate }}</span>
				</a>
			</p>
			<p class="redirect-link-p">
				<a routerLink="../register" class="text-link">
					{{ 'BUTTONS.REGISTER' | translate }}
				</a>
			</p>
		</section>
	</div>
</section>
