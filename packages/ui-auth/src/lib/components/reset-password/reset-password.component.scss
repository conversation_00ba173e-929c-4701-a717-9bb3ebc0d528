@use 'themes' as *;
@use '@shared/reusable' as *;

.reset-password-wrapper {
  background: nb-theme(gauzy-card-2);
  border-radius: nb-theme(border-radius);
  padding: 30px;
  width: 476px;
  & > * {
    padding-left: 15px;
    padding-right: 15px;
  }
  & .svg-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 57px;
  }
  & .title {
    font-family: Inter;
    font-size: 24px;
    font-style: normal;
    font-weight: 700;
    line-height: 30px;
    letter-spacing: 0em;
    text-align: left !important;
  }
  & .sub-title {
    width: 358px;
    height: 34px;
    font-family: 'Inter';
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 17px;
    display: flex;
    align-items: center;
    text-align: start;
    margin-bottom: 15px;
  }
  & .hr-div-strong {
    @include hr-div-strong;
  }
  & .sign-in-or-up {
    margin-top: 15px;
  }
}
.form {
  & .form-control-group {
    margin-bottom: 24px;
    & .label {
      font-family: 'Inter';
      font-style: normal;
      font-weight: 600;
      font-size: 11px;
      line-height: 13px;
      display: flex;
      align-items: center;
      letter-spacing: -0.01em;
    }
  }
  & .submit-btn-wrapper {
    display: flex;
    justify-content: flex-end;
    & .submit-btn {
      @include submit-btn;
      margin-bottom: 25px;
      margin-top: 15px;
    }
  }
}
.hr-div-soft {
  @include hr-div-soft;
}
.redirect-link-p {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  /* identical to box height */
  color: #7e7e8f;
  margin-bottom: 0;
  & .text-link {
    font-family: Inter;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 17px;
    letter-spacing: 0em;
    color: #6e49e8;
    text-decoration: none;
  }
}

.theme-switch {
  @include not-mobile-screen {
    display: none;
  }
}

@include mobile-screen {
  :host,
  .reset-password-wrapper {
    width: 100%;
  }
  .section-wrapper {
    display: flex;
    flex-direction: column;
    height: 100vh;
    width: 100%;
    justify-content: center;
    align-items: center;
  }
  .reset-password-wrapper {
    padding: 12px 18px;
    & > * {
      padding-left: 0px;
      padding-right: 0px;
    }
    & .title,
    .sub-title {
      width: 100%;
      display: block;
      text-align: center !important;
    }
  }
  .form {
    & .submit-btn-wrapper {
      justify-content: center;
    }
  }
}
@include input-fields-color;
