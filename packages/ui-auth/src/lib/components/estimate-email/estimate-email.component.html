<div [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
	<div *ngIf="!loading">
		<div *ngIf="!errorMessage">
			<h1 *ngIf="isAccepted">
				{{ 'INVOICES_PAGE.ESTIMATES.ESTIMATE_ACCEPTED' | translate }}
			</h1>
			<h1 *ngIf="!isAccepted">
				{{ 'INVOICES_PAGE.ESTIMATES.ESTIMATE_REJECTED' | translate }}
			</h1>
		</div>
		<div *ngIf="errorMessage">
			<h1>{{ errorMessage }}</h1>
		</div>
	</div>
</div>
