@use 'themes' as *;
@use '@shared/reusable' as *;

.feature-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-right: 10px;
  padding-left: 10px;
}
.headings-wrapper {
  width: 100%;
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
  & > nb-icon {
    margin-right: 5px;
    margin-top: 1.5px;
    height: 14px;
    width: 14px;
  }
  & .heading {
    margin: 0;
    height: 100%;
    display: flex;
    align-items: flex-start;
    font-family: Inter;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 17px;
    letter-spacing: 0em;
    text-align: left;
  }
}
.feature-img {
  width: 201px;
  height: 130px;
  border: 1px solid black;
  -webkit-box-shadow: 0px 8px 21px -11px #000000;
  box-shadow: 0px 8px 21px -11px #000000;
  border-radius: 5px;
  object-fit: contain;
}
.description {
  font-family: Inter;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 16px;
  letter-spacing: 0em;
  text-align: left;
  width: 100%;
}
.heading,
nb-icon,
.description {
  color: rgba(255, 255, 255, 0.75);
}
@include small-laptop-screen {
  .description {
    font-size: 14px;
  }
  .feature-img {
    width: 208px;
    height: 135px;
  }
}

@include tablet-screen {
  .feature-img {
    width: 360px;
    height: 232px;
  }
}
@include mobile-screen {
  .feature-img {
    width: 90%;
    height: 45vw;
  }
}
