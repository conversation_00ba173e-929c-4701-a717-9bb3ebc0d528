<div [nbSpinner]="loading" nbSpinnerStatus="primary">
	<div *ngIf="!loading">
		<div class="register-header" *ngIf="inviteLoadErrorMessage">
			<h1>{{ inviteLoadErrorMessage }}</h1>
			<span>
				{{ 'ACCEPT_INVITE.INVALID_INVITE' | translate }}
			</span>
		</div>
		<div *ngIf="!inviteLoadErrorMessage">
			<div class="register-header mb-2">
				<h1>
					{{
						'ACCEPT_INVITE.HEADING'
							| translate
								: {
										organizationName:
											invitation?.organization?.name
								  }
					}}
				</h1>
				<span>
					{{
						'ACCEPT_INVITE.SUB_HEADING'
							| translate: { email: invitation?.email }
					}}
				</span>
			</div>
			<ga-accept-client-invite-form
				[invitation]="invitation"
				(submitForm)="submitForm($event)"
			></ga-accept-client-invite-form>
		</div>
	</div>
</div>
