{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "cli": {"warnings": {"versionMismatch": false}, "packageManager": "yarn", "analytics": false, "schematicCollections": ["@nstudio/xplat"]}, "version": 1, "newProjectRoot": "", "projects": {}, "schematics": {"@nx/angular": {"application": {"linter": "eslint"}, "library": {"linter": "eslint"}, "storybook-configuration": {"linter": "eslint"}}, "@nx/angular:application": {"style": "scss", "linter": "eslint", "unitTestRunner": "jest", "e2eTestRunner": "cypress"}, "@nx/angular:component": {"prefix": "ngx", "style": "scss"}, "@nx/angular:directive": {"prefix": "ngx"}, "@nx/angular:library": {"style": "scss", "linter": "eslint", "unitTestRunner": "jest"}, "@angular-eslint/schematics:application": {"setParserOptionsProject": true}, "@angular-eslint/schematics:library": {"setParserOptionsProject": true}}}