FROM electronuserland/builder:wine as windows

WORKDIR /opt/src

COPY apps/desktop-timer/package.json ./apps/desktop-timer/
COPY apps/api/package.json ./apps/api/
COPY package.json yarn.lock ./

RUN yarn install --network-timeout 1000000 --frozen-lockfile --ignore-scripts
RUN yarn postinstall.manual

COPY . .

RUN yarn build:desktop-timer:windows

FROM debian:buster-slim

WORKDIR /opt/artifacts

COPY --from=windows /opt/src/dist/apps/desktop-packages ./windows/
