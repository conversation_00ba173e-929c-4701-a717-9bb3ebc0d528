server {
    listen              443 ssl;
    server_name         apidemodts.gauzy.co;
    ssl_certificate     /etc/letsencrypt/live/apidemodts.gauzy.co/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/apidemodts.gauzy.co/privkey.pem;
    ssl_trusted_certificate /etc/letsencrypt/live/apidemodts.gauzy.co/chain.pem;

    # Load the Diffie-<PERSON> parameter.
    ssl_dhparam /etc/letsencrypt/dhparams/dhparam.pem;

    location / {
        proxy_pass http://api:3000;
    }
    
}
