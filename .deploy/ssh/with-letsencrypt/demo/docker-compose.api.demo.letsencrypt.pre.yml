version: '3.8'

services:
  proxy:
    image: jonasal/nginx-certbot:latest
    restart: always
    environment:
      CERTBOT_EMAIL: '<EMAIL>'
    env_file:
      - ./nginx-certbot.env
    ports:
      - '80:80'
      - '443:443'
    networks:
      - demo_overlay
    volumes:
      - nginx_secrets:/etc/letsencrypt
      - ./user_conf.d:/etc/nginx/user_conf.d
volumes:
  nginx_secrets: {}
  certificates: {}

networks:
  demo_overlay:
    external: true
