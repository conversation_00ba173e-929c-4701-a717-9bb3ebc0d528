server {
    listen              443 ssl;
    server_name         apistagedts.gauzy.co;
    ssl_certificate     /etc/letsencrypt/live/apistagedts.gauzy.co/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/apistagedts.gauzy.co/privkey.pem;
    ssl_trusted_certificate /etc/letsencrypt/live/apistagedts.gauzy.co/chain.pem;

    # Load the Diffie-<PERSON>man parameter.
    ssl_dhparam /etc/letsencrypt/dhparams/dhparam.pem;

    location / {
        proxy_pass http://api:3000;
    }
    
}
