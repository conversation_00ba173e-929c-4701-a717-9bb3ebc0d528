version: '3.8'

services:
  api:
    image: ghcr.io/ever-co/gauzy-api-stage:latest
    deploy:
      mode: replicated
      replicas: 2
    environment:
      API_HOST: '0.0.0.0'
      DEMO: '${DEMO}'
      NODE_ENV: '${NODE_ENV}'
      ADMIN_PASSWORD_RESET: '${ADMIN_PASSWORD_RESET}'
      API_BASE_URL: '${API_BASE_URL}'
      CLIENT_BASE_URL: '${CLIENT_BASE_URL}'
      DB_TYPE: '${DB_TYPE}'
      DB_URI: '${DB_URI}'
      DB_HOST: '${DB_HOST}'
      DB_USER: '${DB_USER}'
      DB_PASS: '${DB_PASS}'
      DB_NAME: '${DB_NAME}'
      DB_PORT: '${DB_PORT}'
      DB_CA_CERT: '${DB_CA_CERT}'
      DB_SSL_MODE: '${DB_SSL_MODE}'
      DB_POOL_SIZE: '${DB_POOL_SIZE}'
      DB_POOL_SIZE_KNEX: '${DB_POOL_SIZE_KNEX}'
      REDIS_ENABLED: '${REDIS_ENABLED}'
      REDIS_URL: '${REDIS_URL}'
      CLOUD_PROVIDER: 'DO'
      SENTRY_DSN: '${SENTRY_DSN}'
      SENTRY_TRACES_SAMPLE_RATE: '${SENTRY_TRACES_SAMPLE_RATE}'
      SENTRY_PROFILE_SAMPLE_RATE: '${SENTRY_PROFILE_SAMPLE_RATE}'
      SENTRY_HTTP_TRACING_ENABLED: '${SENTRY_HTTP_TRACING_ENABLED}'
      SENTRY_POSTGRES_TRACKING_ENABLED: '${SENTRY_POSTGRES_TRACKING_ENABLED}'
      SENTRY_PROFILING_ENABLED: '${SENTRY_PROFILING_ENABLED}'
      POSTHOG_KEY: '${POSTHOG_KEY}'
      POSTHOG_HOST: '${POSTHOG_HOST}'
      POSTHOG_ENABLED: '${POSTHOG_ENABLED}'
      POSTHOG_FLUSH_INTERVAL: '${POSTHOG_FLUSH_INTERVAL}'
      AWS_ACCESS_KEY_ID: '${AWS_ACCESS_KEY_ID}'
      AWS_SECRET_ACCESS_KEY: '${AWS_SECRET_ACCESS_KEY}'
      AWS_REGION: '${AWS_REGION}'
      AWS_S3_BUCKET: '${AWS_S3_BUCKET}'
      WASABI_ACCESS_KEY_ID: '${WASABI_ACCESS_KEY_ID}'
      WASABI_SECRET_ACCESS_KEY: '${WASABI_SECRET_ACCESS_KEY}'
      WASABI_REGION: '${WASABI_REGION}'
      WASABI_SERVICE_URL: '${WASABI_SERVICE_URL}'
      WASABI_S3_BUCKET: '${WASABI_S3_BUCKET}'
      WASABI_S3_FORCE_PATH_STYLE: '${WASABI_S3_FORCE_PATH_STYLE:-}'
      DIGITALOCEAN_ACCESS_KEY_ID: '${DIGITALOCEAN_ACCESS_KEY_ID:-}'
      DIGITALOCEAN_SECRET_ACCESS_KEY: '${DIGITALOCEAN_SECRET_ACCESS_KEY:-}'
      DIGITALOCEAN_REGION: '${DIGITALOCEAN_REGION:-}'
      DIGITALOCEAN_SERVICE_URL: '${DIGITALOCEAN_SERVICE_URL:-}'
      DIGITALOCEAN_CDN_URL: '${DIGITALOCEAN_CDN_URL:-}'
      DIGITALOCEAN_S3_BUCKET: '${DIGITALOCEAN_S3_BUCKET:-}'
      DIGITALOCEAN_S3_FORCE_PATH_STYLE: '${DIGITALOCEAN_S3_FORCE_PATH_STYLE:-}'
      EXPRESS_SESSION_SECRET: '${EXPRESS_SESSION_SECRET}'
      JWT_SECRET: '${JWT_SECRET}'
      JWT_REFRESH_TOKEN_SECRET: '${JWT_REFRESH_TOKEN_SECRET}'
      JWT_REFRESH_TOKEN_EXPIRATION_TIME: '${JWT_REFRESH_TOKEN_EXPIRATION_TIME}'
      CLOUDINARY_API_KEY: '${CLOUDINARY_API_KEY}'
      CLOUDINARY_API_SECRET: '${CLOUDINARY_API_SECRET}'
      CLOUDINARY_CLOUD_NAME: '${CLOUDINARY_CLOUD_NAME}'
      MAIL_FROM_ADDRESS: '${MAIL_FROM_ADDRESS}'
      MAIL_HOST: '${MAIL_HOST}'
      MAIL_PORT: '${MAIL_PORT}'
      MAIL_USERNAME: '${MAIL_USERNAME}'
      MAIL_PASSWORD: '${MAIL_PASSWORD}'
      ALLOW_SUPER_ADMIN_ROLE: '${ALLOW_SUPER_ADMIN_ROLE}'
      GOOGLE_CLIENT_ID: '${GOOGLE_CLIENT_ID}'
      GOOGLE_CLIENT_SECRET: '${GOOGLE_CLIENT_SECRET}'
      GOOGLE_CALLBACK_URL: '${GOOGLE_CALLBACK_URL}'
      FACEBOOK_CLIENT_ID: '${FACEBOOK_CLIENT_ID}'
      FACEBOOK_CLIENT_SECRET: '${FACEBOOK_CLIENT_SECRET}'
      FACEBOOK_GRAPH_VERSION: '${FACEBOOK_GRAPH_VERSION}'
      FACEBOOK_CALLBACK_URL: '${FACEBOOK_CALLBACK_URL}'
      INTEGRATED_USER_DEFAULT_PASS: '${INTEGRATED_USER_DEFAULT_PASS}'
      UPWORK_REDIRECT_URL: '${UPWORK_REDIRECT_URL}'
      FILE_PROVIDER: '${FILE_PROVIDER}'
      GAUZY_AI_GRAPHQL_ENDPOINT: '${GAUZY_AI_GRAPHQL_ENDPOINT}'
      GAUZY_AI_REST_ENDPOINT: '${GAUZY_AI_REST_ENDPOINT}'
      UNLEASH_APP_NAME: '${UNLEASH_APP_NAME}'
      UNLEASH_API_URL: '${UNLEASH_API_URL}'
      UNLEASH_INSTANCE_ID: '${UNLEASH_INSTANCE_ID}'
      UNLEASH_REFRESH_INTERVAL: '${UNLEASH_REFRESH_INTERVAL}'
      UNLEASH_METRICS_INTERVAL: '${UNLEASH_METRICS_INTERVAL}'
      UNLEASH_API_KEY: '${UNLEASH_API_KEY}'
      JITSU_SERVER_URL: '${JITSU_SERVER_URL}'
      JITSU_SERVER_WRITE_KEY: '${JITSU_SERVER_WRITE_KEY}'
      OTEL_ENABLED: '${OTEL_ENABLED}'
      OTEL_PROVIDER: '${OTEL_PROVIDER}'
      OTEL_EXPORTER_OTLP_TRACES_ENDPOINT: '${OTEL_EXPORTER_OTLP_TRACES_ENDPOINT}'
      OTEL_EXPORTER_OTLP_HEADERS: '${OTEL_EXPORTER_OTLP_HEADERS}'
      GAUZY_GITHUB_CLIENT_ID: '${GAUZY_GITHUB_CLIENT_ID}'
      GAUZY_GITHUB_CLIENT_SECRET: '${GAUZY_GITHUB_CLIENT_SECRET}'
      GAUZY_GITHUB_APP_PRIVATE_KEY: '${GAUZY_GITHUB_APP_PRIVATE_KEY}'
      GAUZY_GITHUB_WEBHOOK_URL: '${GAUZY_GITHUB_WEBHOOK_URL}'
      GAUZY_GITHUB_WEBHOOK_SECRET: '${GAUZY_GITHUB_WEBHOOK_SECRET}'
      GAUZY_GITHUB_APP_NAME: '${GAUZY_GITHUB_APP_NAME}'
      GAUZY_GITHUB_REDIRECT_URL: '${GAUZY_GITHUB_REDIRECT_URL}'
      GAUZY_GITHUB_POST_INSTALL_URL: '${GAUZY_GITHUB_POST_INSTALL_URL}'
      GAUZY_GITHUB_APP_ID: '${GAUZY_GITHUB_APP_ID}'
      GAUZY_GITHUB_OAUTH_CLIENT_ID: '${GAUZY_GITHUB_OAUTH_CLIENT_ID}'
      GAUZY_GITHUB_OAUTH_CLIENT_SECRET: '${GAUZY_GITHUB_OAUTH_CLIENT_SECRET}'
      GAUZY_GITHUB_OAUTH_CALLBACK_URL: '${GAUZY_GITHUB_OAUTH_CALLBACK_URL}'
      JITSU_BROWSER_URL: '${JITSU_BROWSER_URL}'
      JITSU_BROWSER_WRITE_KEY: '${JITSU_BROWSER_WRITE_KEY}'
      MAGIC_CODE_EXPIRATION_TIME: '${MAGIC_CODE_EXPIRATION_TIME}'
      APP_NAME: '${APP_NAME}'
      APP_LOGO: '${APP_LOGO}'
      APP_SIGNATURE: '${APP_SIGNATURE}'
      APP_LINK: '${APP_LINK}'
      APP_EMAIL_CONFIRMATION_URL: '${APP_EMAIL_CONFIRMATION_URL}'
      APP_MAGIC_SIGN_URL: '${APP_MAGIC_SIGN_URL}'
      COMPANY_LINK: '${COMPANY_LINK}'
      COMPANY_NAME: '${COMPANY_NAME}'

    entrypoint: './entrypoint.prod.sh'
    command: ['node', 'main.js']
    restart: on-failure
    ports:
      - '3000'
    networks:
      - overlay
volumes:
  certificates: {}

networks:
  overlay:
    driver: bridge
