server {
    listen              443 ssl;
    server_name         apidts.gauzy.co;
    ssl_certificate     /etc/letsencrypt/live/apidts.gauzy.co/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/apidts.gauzy.co/privkey.pem;
    ssl_trusted_certificate /etc/letsencrypt/live/apidts.gauzy.co/chain.pem;

    # Load the <PERSON><PERSON><PERSON>-<PERSON> parameter.
    ssl_dhparam /etc/letsencrypt/dhparams/dhparam.pem;

    location / {
        proxy_pass http://api:3000;
    }
    
}
