user nginx;
events {
    worker_connections 1024;
}
http {
    server {
        listen 80;
        server_name apidemodt.gauzy.co;

        location / {
            return 301 https://$host$request_uri;
        }
    }
    server {
        listen 443 ssl;
        server_name apidemodt.gauzy.co;

        ssl_certificate /etc/nginx/ssl/fullchain.pem;
        ssl_certificate_key /etc/nginx/ssl/privkey.pem;

        location / {
           proxy_pass http://api:3000;
        }
    }  
}
