---
kind: Service
apiVersion: v1
metadata:
    name: gauzy-demo
spec:
    selector:
        app: gauzy-demo-webapp
    ports:
        - name: web
          protocol: TCP
          port: 4200
          targetPort: 4200
---
kind: Service
apiVersion: v1
metadata:
    name: gauzy-demo-api
spec:
    selector:
        app: gauzy-demo-api
    ports:
        - name: web
          protocol: TCP
          port: 3000
          targetPort: 3000
---
apiVersion: apps/v1
kind: Deployment
metadata:
    name: gauzy-demo-api
spec:
    replicas: 1
    selector:
        matchLabels:
            app: gauzy-demo-api
    template:
        metadata:
            labels:
                app: gauzy-demo-api
        spec:
            affinity:
                nodeAffinity:
                    requiredDuringSchedulingIgnoredDuringExecution:
                        nodeSelectorTerms:
                            - matchExpressions:
                                  - key: topology.kubernetes.io/region
                                    operator: In
                                    values:
                                        - ORD1
                                  - key: node.coreweave.cloud/cpu
                                    operator: In
                                    values:
                                        - intel-xeon-v3
            imagePullSecrets:
                - name: ever-registry-docker-registry-credentials
            containers:
                - name: gauzy-demo-api
                  image: ever-registry.tenant-acb888-ever.ord1.ingress.coreweave.cloud/ever-co/gauzy-api-demo:latest
                  env:
                      - name: API_HOST
                        value: 0.0.0.0
                      - name: DEMO
                        value: 'true'
                      - name: CLOUD_PROVIDER
                        value: '$CLOUD_PROVIDER'
                      - name: NODE_ENV
                        value: 'development'
                      - name: ADMIN_PASSWORD_RESET
                        value: 'true'
                      - name: LOG_LEVEL
                        value: 'info'
                      - name: SENTRY_DSN
                        value: 'https://<EMAIL>/4397292'
                      - name: SENTRY_HTTP_TRACING_ENABLED
                        value: '$SENTRY_HTTP_TRACING_ENABLED'
                      - name: SENTRY_PROFILING_ENABLED
                        value: '$SENTRY_PROFILING_ENABLED'
                      - name: SENTRY_POSTGRES_TRACKING_ENABLED
                        value: '$SENTRY_POSTGRES_TRACKING_ENABLED'
                      - name: API_BASE_URL
                        value: 'https://apidemocw.gauzy.co'
                      - name: CLIENT_BASE_URL
                        value: 'https://democw.gauzy.co'
                      - name: EXPRESS_SESSION_SECRET
                        value: 'gauzy'
                      - name: JWT_SECRET
                        value: 'secretKey'
                      - name: JWT_REFRESH_TOKEN_SECRET
                        value: 'refreshSecretKey'
                      - name: JWT_REFRESH_TOKEN_EXPIRATION_TIME
                        value: '86400'
                      - name: OTEL_ENABLED
                        value: '$OTEL_ENABLED'
                      - name: OTEL_PROVIDER
                        value: '$OTEL_PROVIDER'
                      - name: OTEL_EXPORTER_OTLP_TRACES_ENDPOINT
                        value: '$OTEL_EXPORTER_OTLP_TRACES_ENDPOINT'
                      - name: OTEL_EXPORTER_OTLP_HEADERS
                        value: '$OTEL_EXPORTER_OTLP_HEADERS'
                  ports:
                      - containerPort: 3000
                        protocol: TCP
                  resources:
                      requests:
                          cpu: '0.5'
                          memory: 2Gi
                      limits:
                          cpu: '2'
                          memory: 8Gi
---
apiVersion: apps/v1
kind: Deployment
metadata:
    name: gauzy-demo-webapp
spec:
    replicas: 1
    selector:
        matchLabels:
            app: gauzy-demo-webapp
    template:
        metadata:
            labels:
                app: gauzy-demo-webapp
        spec:
            affinity:
                nodeAffinity:
                    requiredDuringSchedulingIgnoredDuringExecution:
                        nodeSelectorTerms:
                            - matchExpressions:
                                  - key: topology.kubernetes.io/region
                                    operator: In
                                    values:
                                        - ORD1
                                  - key: node.coreweave.cloud/cpu
                                    operator: In
                                    values:
                                        - intel-xeon-v3
            imagePullSecrets:
                - name: ever-registry-docker-registry-credentials
            containers:
                - name: gauzy-demo-webapp
                  image: ever-registry.tenant-acb888-ever.ord1.ingress.coreweave.cloud/ever-co/gauzy-webapp-demo:latest
                  env:
                      - name: DEMO
                        value: 'true'
                      - name: API_BASE_URL
                        value: 'https://apidemocw.gauzy.co'
                      - name: CLIENT_BASE_URL
                        value: 'https://democw.gauzy.co'
                      - name: SENTRY_DSN
                        value: 'https://<EMAIL>/4397292'
                      - name: CHATWOOT_SDK_TOKEN
                        value: 'jFoSXEjGmqhUhqU3zfgkFfMt'
                      - name: CLOUDINARY_API_KEY
                        value: '256868982483961'
                      - name: CLOUDINARY_CLOUD_NAME
                        value: 'dv6ezkfxg'
                      - name: GOOGLE_MAPS_API_KEY
                        value: 'AIzaSyCJmnKzgTSq5Pc93HXar5bZrdmANTwtSIo'
                      - name: GOOGLE_PLACE_AUTOCOMPLETE
                        value: 'true'
                      - name: DEFAULT_LATITUDE
                        value: '42.6459136'
                      - name: DEFAULT_LONGITUDE
                        value: '23.3332736'
                      - name: DEFAULT_CURRENCY
                        value: 'USD'
                      - name: GAUZY_GITHUB_CLIENT_ID
                        value: '$GAUZY_GITHUB_CLIENT_ID'
                      - name: GAUZY_GITHUB_APP_NAME
                        value: '$GAUZY_GITHUB_APP_NAME'
                      - name: GAUZY_GITHUB_REDIRECT_URL
                        value: '$GAUZY_GITHUB_REDIRECT_URL'
                      - name: GAUZY_GITHUB_POST_INSTALL_URL
                        value: '$GAUZY_GITHUB_POST_INSTALL_URL'
                      - name: GAUZY_GITHUB_APP_ID
                        value: '$GAUZY_GITHUB_APP_ID'
                      - name: JITSU_BROWSER_URL
                        value: '$JITSU_BROWSER_URL'
                      - name: JITSU_BROWSER_WRITE_KEY
                        value: '$JITSU_BROWSER_WRITE_KEY'

                  ports:
                      - containerPort: 4200
                        protocol: TCP
                  resources:
                      requests:
                          cpu: '0.5'
                          memory: 2Gi
                      limits:
                          cpu: '2'
                          memory: 8Gi
---
apiVersion: traefik.containo.us/v1alpha1
kind: Middleware
metadata:
    name: https-redirect
spec:
    redirectScheme:
        permanent: true
        scheme: https
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
    name: gauzy-demo
    annotations:
        kubernetes.io/ingress.class: traefik-everco
        traefik.ingress.kubernetes.io/router.middlewares: tenant-acb888-ever-https-redirect@kubernetescrd
spec:
    rules:
        - host: democw.gauzy.co
          http:
              paths:
                  - backend:
                        service:
                            name: gauzy-demo
                            port:
                                number: 4200
                    path: /
                    pathType: Prefix
    tls:
        - hosts:
              - democw.gauzy.co
          secretName: democw.gauzy.co-tls
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
    name: gauzy-demo-api
    annotations:
        kubernetes.io/ingress.class: traefik-everco
        traefik.ingress.kubernetes.io/router.middlewares: tenant-acb888-ever-https-redirect@kubernetescrd
spec:
    rules:
        - host: apidemocw.gauzy.co
          http:
              paths:
                  - backend:
                        service:
                            name: gauzy-demo-api
                            port:
                                number: 3000
                    path: /
                    pathType: Prefix
    tls:
        - hosts:
              - apidemocw.gauzy.co
          secretName: apidemocw.gauzy.co-tls
