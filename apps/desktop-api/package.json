{"name": "@gauzy/desktop-api", "version": "0.1.0", "description": "Ever Gauzy Platform desktop APIs", "author": {"name": "Ever Co. LTD", "email": "<EMAIL>", "url": "https://ever.co"}, "homepage": "https://gauzy.co", "repository": {"type": "git", "url": "https://github.com/ever-co/ever-gauzy.git"}, "bugs": {"url": "https://github.com/ever-co/ever-gauzy/issues"}, "license": "AGPL-3.0", "private": true, "scripts": {"build": "yarn nx build desktop-api --configuration=development", "build:prod": "yarn nx build desktop-api --configuration=production"}, "keywords": ["desktop", "api", "gauzy", "<PERSON><PERSON><PERSON>", "microservices", "grpc", "wakatime", "typeorm", "mikro-orm", "integration", "platform", "productivity", "open-source"], "dependencies": {"@grpc/grpc-js": "^1.6.7", "@mikro-orm/core": "^6.4.13", "@mikro-orm/nestjs": "^6.1.1", "@nestjs/common": "^11.1.0", "@nestjs/core": "^11.1.0", "@nestjs/typeorm": "^11.0.0", "@gauzy/plugin-integration-wakatime": "^0.1.0"}, "devDependencies": {"@nestjs/testing": "^11.0.0", "@types/node": "^20.14.9"}, "engines": {"node": ">=20.18.1", "yarn": ">=1.22.19"}, "sideEffects": false}