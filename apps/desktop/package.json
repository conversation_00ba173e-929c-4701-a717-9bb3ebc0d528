{"name": "@gauzy/desktop", "version": "0.1.0", "description": "Ever Gauzy Desktop App", "author": {"name": "Ever Co. LTD", "email": "<EMAIL>", "url": "https://ever.co"}, "homepage": "https://gauzy.co", "repository": {"type": "git", "url": "https://github.com/ever-co/ever-gauzy.git"}, "bugs": {"url": "https://github.com/ever-co/ever-gauzy/issues"}, "license": "AGPL-3.0", "private": true, "scripts": {"build": "yarn nx build desktop --configuration=development", "build:prod": "yarn nx build desktop --configuration=production", "start": "yarn nx serve desktop"}, "devDependencies": {"@electron/rebuild": "^3.2.10", "@types/moment-duration-format": "^2.2.3", "electron": "^30.0.1", "electron-builder": "^24.13.3", "electron-installer-dmg": "^4.0.0", "electron-packager": "^17.1.1", "electron-reload": "~1.5.0", "node-polyfill-webpack-plugin": "^1.1.4", "terser-webpack-plugin": "^5.3.14"}, "dependencies": {"@angular/animations": "^19.2.10", "@angular/cdk": "^19.2.11", "@angular/common": "^19.2.10", "@angular/core": "^19.2.10", "@angular/forms": "^19.2.10", "@angular/material": "^19.2.11", "@angular/router": "^19.2.10", "@datorama/akita": "^8.0.1", "@datorama/akita-ngdevtools": "^7.0.0", "@electron/remote": "^2.0.8", "@grpc/grpc-js": "^1.6.7", "@nebular/auth": "^15.0.0", "@nebular/bootstrap": "^9.1.0-rc.6", "@nebular/eva-icons": "^15.0.0", "@nebular/security": "^15.0.0", "@nebular/theme": "^15.0.0", "@nestjs/typeorm": "^11.0.0", "@ng-select/ng-select": "^14.8.1", "@ngneat/until-destroy": "^10.0.0", "@ngx-translate/core": "^16.0.4", "@ngx-translate/http-loader": "^16.0.1", "@sentry/angular-ivy": "^7.101.1", "@sentry/electron": "^4.18.0", "@sentry/node": "^7.101.1", "@sentry/profiling-node": "^7.101.1", "@sentry/replay": "^7.101.1", "@sentry/tracing": "^7.101.1", "@sentry/types": "^7.101.1", "app-root-path": "^3.0.0", "auto-launch": "5.0.5", "consolidate": "^0.16.0", "electron-log": "^4.4.8", "electron-store": "^8.1.0", "electron-updater": "^6.1.7", "electron-util": "^0.18.1", "form-data": "^4.0.1", "knex": "^3.1.0", "libsql": "^0.3.16", "locutus": "^2.0.30", "moment": "^2.30.1", "moment-duration-format": "^2.3.2", "moment-range": "^4.0.2", "moment-timezone": "^0.5.48", "node-fetch": "^2.6.7", "node-static": "^0.7.11", "pg": "^8.13.1", "pg-query-stream": "^4.7.1", "rxjs": "^7.8.2", "screenshot-desktop": "^1.15.0", "sound-play": "1.1.0", "squirrelly": "^8.0.8", "twing": "^5.0.2", "typeorm": "^0.3.24", "undici": "^6.10.2", "custom-electron-titlebar": "^4.2.8"}, "keywords": [], "optionalDependencies": {"node-linux": "^0.1.12", "node-mac": "^1.0.1", "node-windows": "^1.0.0-beta.8"}, "engines": {"node": ">=20.18.1", "yarn": ">=1.22.19"}, "sideEffects": false}