{"name": "@gauzy/ui", "version": "0.1.0", "description": "Gauzy UI", "license": "AGPL-3.0", "homepage": "https://gauzy.co", "repository": {"type": "git", "url": "https://github.com/ever-co/ever-gauzy.git"}, "bugs": {"url": "https://github.com/ever-co/ever-gauzy/issues"}, "private": true, "author": {"name": "Ever Co. LTD", "email": "<EMAIL>", "url": "https://ever.co"}, "resolutions": {"uglify-js": "npm:terser", "webpack": "^5.99.7", "webpack-cli": "^6.0.1"}, "dependencies": {"@akveo/ng2-completer": "^9.0.1", "@ali-hm/angular-tree-component": "^19.2.5", "@angular-slider/ngx-slider": "^19.0.0", "@angular/animations": "^19.2.10", "@angular/cdk": "^19.2.11", "@angular/common": "^19.2.10", "@angular/core": "^19.2.10", "@angular/forms": "^19.2.10", "@angular/language-service": "^19.2.10", "@angular/material": "^19.2.11", "@angular/platform-browser": "^19.2.10", "@angular/platform-browser-dynamic": "^19.2.10", "@angular/router": "^19.2.10", "@angular/service-worker": "^19.2.10", "@bluehalo/ngx-leaflet": "^19.0.0", "@datorama/akita": "^8.0.1", "@datorama/akita-ngdevtools": "^7.0.0", "@electron/remote": "^2.0.8", "@fortawesome/angular-fontawesome": "^1.0.0", "@fortawesome/fontawesome-free": "^6.7.2", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fullcalendar/angular": "^6.1.17", "@fullcalendar/core": "^6.1.17", "@fullcalendar/daygrid": "^6.1.17", "@fullcalendar/interaction": "^6.1.17", "@fullcalendar/moment-timezone": "^6.1.17", "@fullcalendar/timegrid": "^6.1.17", "@gauzy/contracts": "^0.1.0", "@gauzy/plugin-job-employee-ui": "^0.1.0", "@gauzy/plugin-job-proposal-ui": "^0.1.0", "@gauzy/plugin-job-search-ui": "^0.1.0", "@gauzy/plugin-legal-ui": "^0.1.0", "@gauzy/plugin-maintenance-ui": "^0.1.0", "@gauzy/plugin-onboarding-ui": "^0.1.0", "@gauzy/plugin-public-layout-ui": "^0.1.0", "@gauzy/plugin-posthog-ui": "^0.1.0", "@jitsu/js": "^1.10.0", "@kurkle/color": "^0.3.4", "@nebular/auth": "^15.0.0", "@nebular/bootstrap": "^9.1.0-rc.6", "@nebular/eva-icons": "^15.0.0", "@nebular/security": "^15.0.0", "@nebular/theme": "^15.0.0", "@ng-maps/core": "^8.0.0", "@ng-maps/google": "^8.0.0", "@ng-select/ng-select": "^14.8.1", "@ngneat/until-destroy": "^10.0.0", "@ngx-translate/core": "^16.0.4", "@ngx-translate/http-loader": "^16.0.1", "@sentry/angular-ivy": "^7.101.1", "@sentry/browser": "^7.90.0", "@sentry/node": "^7.101.1", "@sentry/tracing": "^7.101.1", "@sentry/types": "^7.101.1", "@sentry/utils": "^7.90.0", "@swimlane/ngx-charts": "^22.0.0-alpha.1", "angular2-smart-table": "^3.6.2", "bootstrap": "^4.3.1", "brace": "^0.11.1", "camelcase": "^6.3.0", "chart.js": "^4.4.9", "chart.piecelabel.js": "^0.15.0", "chartjs-plugin-annotation": "^3.1.0", "ckeditor4": "4.22.1", "ckeditor4-angular": "4.0.1", "core-js": "^3.8.3", "d3": "^7.4.4", "d3-selection-multi": "^1.0.1", "date-fns": "^2.28.0", "date-holidays": "^1.6.1", "dayjs": "^1.11.4", "detect-passive-events": "^1.0.4", "echarts": "^5.6.0", "eva-icons": "^1.1.3", "fast-json-stringify": "^2.7.12", "file-saver": "^2.0.5", "filepond": "^4.25.1", "filepond-plugin-file-encode": "^2.1.9", "filepond-plugin-file-validate-size": "^2.2.2", "filepond-plugin-file-validate-type": "^1.2.5", "filepond-plugin-image-crop": "^2.0.4", "filepond-plugin-image-exif-orientation": "^1.0.9", "filepond-plugin-image-preview": "^4.6.4", "filepond-plugin-image-resize": "^2.0.7", "filepond-plugin-image-transform": "^3.7.4", "formidable": "^3.5.4", "fullcalendar": "^6.1.17", "hammerjs": "^2.0.8", "handlebars": "^4.7.6", "hotkeys-js": "^3.12.0", "html-to-text": "^9.0.5", "html2canvas": "^1.0.0-rc.7", "immer": "^9.0.6", "intl": "^1.2.5", "ionicons": "^4.6.3", "keycloak-js": "^11.0.2", "leaflet": "^1.8.0", "locutus": "^2.0.30", "moment": "^2.30.1", "moment-duration-format": "^2.3.2", "moment-range": "^4.0.2", "moment-timezone": "^0.5.48", "nebular-icons": "^1.1.0", "ng2-charts": "^8.0.0", "ng2-file-upload": "^8.0.0", "ngx-ace-editor-wrapper": "^9.1.12", "ngx-clipboard": "^16.0.0", "ngx-color-picker": "^19.0.0", "ngx-cookie-service": "^19.1.2", "ngx-countdown": "^19.0.0", "ngx-daterangepicker-material": "^6.0.4", "ngx-draggable-dom": "^19.0.7", "ngx-echarts": "^19.0.0", "ngx-feature-toggle": "^12.0.0", "ngx-filepond": "^7.0.3", "ngx-infinite-scroll": "^19.0.0", "ngx-moment": "^6.0.2", "ngx-page-scroll": "^14.0.1", "ngx-page-scroll-core": "^14.0.1", "ngx-perfect-scrollbar": "^10.1.1", "ngx-permissions": "^19.0.0", "normalize.css": "^8.0.1", "pug": "^3.0.2", "randomcolor": "^0.6.2", "roboto-fontface": "^0.10.0", "rxjs": "^7.8.2", "screenfull": "^6.0.0", "smooth-scrollbar": "^8.7.4", "socicon": "^3.0.5", "squirrelly": "^8.0.8", "swiper": "^8.3.1", "tslib": "^2.6.2", "twing": "^5.0.2", "typeface-exo": "^0.0.61", "underscore": "^1.13.3", "underscore.string": "^3.3.6", "uuid": "^11.1.0", "web-animations-js": "^2.3.2", "yargs": "^17.5.0", "zone.js": "0.15.0"}, "devDependencies": {"@types/async": "^3.2.5", "@types/ckeditor": "^4.9.10", "@types/d3-color": "^3.1.0", "@types/file-saver": "^2.0.7", "@types/google.analytics": "^0.0.41", "@types/hammerjs": "^2.0.36", "@types/html-to-text": "^9.0.1", "@types/i18n": "^0.12.0", "@types/jest": "29.5.14", "@types/leaflet": "^1.8.0", "@types/moment-duration-format": "^2.2.3", "@types/node": "^20.14.9", "@types/underscore": "^1.11.4", "@types/underscore.string": "^0.0.38", "@types/yargs": "^15.0.9", "jasmine-core": "^3.6.0", "jasmine-spec-reporter": "^6.0.0", "jest": "^29.7.0", "jest-preset-angular": "14.5.5", "karma": "^6.4.4", "karma-chrome-launcher": "^3.2.0", "karma-cli": "^2.0.0", "karma-coverage-istanbul-reporter": "^3.0.3", "karma-jasmine": "^5.1.0", "karma-jasmine-html-reporter": "^1.5.4", "terser": "^5.30.3", "terser-webpack-plugin": "^5.3.14", "webpack": "^5.99.7", "webpack-cli": "^6.0.1", "webpack-merge": "^6.0.1", "webpack-node-externals": "^3.0.0"}}