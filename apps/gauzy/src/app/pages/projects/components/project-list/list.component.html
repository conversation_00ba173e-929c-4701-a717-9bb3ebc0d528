<nb-card [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
	<nb-card-header class="main-header pb-0">
		<div class="main-header">
			<h4>
				<ngx-header-title>
					{{ 'ORGANIZATIONS_PAGE.PROJECTS' | translate }}
				</ngx-header-title>
			</h4>
		</div>
		<div class="d-flex justify-content-end">
			<ngx-gauzy-button-action
				[buttonTemplateVisible]="visibleButton"
				[isDisable]="disableButton"
				[buttonTemplate]="actionButtons"
				[componentName]="viewComponentName"
			></ngx-gauzy-button-action>
		</div>
	</nb-card-header>
	<nb-card-body>
		<ng-template [ngxPermissionsOnly]="[PermissionsEnum.ORG_PROJECT_VIEW]">
			<ng-container [ngSwitch]="dataLayoutStyle">
				<!-- Table View -->
				<ng-template [ngSwitchCase]="componentLayoutStyleEnum.TABLE">
					<div class="table-scroll-container">
						<angular2-smart-table
							style="cursor: pointer"
							(userRowSelect)="selectProject($event)"
							[settings]="settingsSmartTable"
							[source]="smartTableSource"
						></angular2-smart-table>
					</div>
					<div class="pagination-container">
						<ng-container *ngIf="smartTableSource">
							<ngx-pagination [source]="smartTableSource"></ngx-pagination>
						</ng-container>
					</div>
				</ng-template>

				<!-- Card Grid View -->
				<ng-template [ngSwitchCase]="componentLayoutStyleEnum.CARDS_GRID">
					<ga-card-grid
						[totalItems]="pagination?.totalItems"
						(onSelectedItem)="selectProject($event)"
						(scroll)="onScroll()"
						[settings]="settingsSmartTable"
						[source]="projects"
						#grid
					></ga-card-grid>
				</ng-template>

				<!-- Optional: Default case if no specific layout matches -->
				<ng-template *ngSwitchDefault>
					<p>{{ 'SETTINGS_MENU.NO_LAYOUT' | translate }}</p>
				</ng-template>
			</ng-container>
		</ng-template>
		<ng-template [ngxPermissionsExcept]="[PermissionsEnum.ORG_PROJECT_VIEW]">
			<div>
				<!-- Content to display if the user does not have 'canEditComponent' permission -->
			</div>
		</ng-template>
	</nb-card-body>
</nb-card>

<!-- Actions Button -->
<ng-template #actionButtons>
	<!-- Favorite Button: Add or remove project from favorites -->
	<ngx-favorite-toggle
		*ngIf="selectedProject"
		[entityType]="'OrganizationProject'"
		[entityId]="selectedProject.id"
		[entityName]="selectedProject.name"
		size="small"
		status="basic"
		spacing="list"
		[disabled]="disableButton"
		[showLabel]="false"
		(favoriteToggled)="onProjectFavoriteToggled($event)"
	></ngx-favorite-toggle>

	<ng-template
		[ngxPermissionsOnly]="[
			PermissionsEnum.ALL_ORG_EDIT,
			PermissionsEnum.ORG_PROJECT_EDIT,
			PermissionsEnum.ORG_PROJECT_DELETE
		]"
	>
		<div class="actions">
			<ng-template [ngxPermissionsOnly]="['ALL_ORG_EDIT', 'ORG_PROJECT_EDIT']">
				<button
					nbButton
					status="basic"
					class="action primary"
					size="small"
					(click)="navigateToProject(selectedProject)"
					[disabled]="disableButton"
				>
					<nb-icon class="mr-1" icon="edit-outline"></nb-icon>
					{{ 'BUTTONS.EDIT' | translate }}
				</button>
			</ng-template>
			<ng-template [ngxPermissionsOnly]="['ALL_ORG_EDIT', 'ORG_PROJECT_EDIT']">
				<button
					nbButton
					status="basic"
					class="action success"
					size="small"
					(click)="createProjectModuleDialog(selectedProject)"
					[disabled]="disableButton"
				>
					<nb-icon class="mr-1" icon="plus-circle-outline"></nb-icon>
					{{ 'BUTTONS.ADD_MODULE' | translate }}
				</button>
			</ng-template>
			<ng-template [ngxPermissionsOnly]="[PermissionsEnum.ALL_ORG_EDIT, PermissionsEnum.ORG_PROJECT_DELETE]">
				<button
					[nbTooltip]="'BUTTONS.DELETE' | translate"
					nbButton
					status="basic"
					class="action"
					size="small"
					(click)="removeProject(selectedProject)"
					[disabled]="disableButton"
				>
					<nb-icon status="danger" icon="trash-2-outline"> </nb-icon>
				</button>
			</ng-template>
		</div>
	</ng-template>
</ng-template>

<!-- Visible Button -->
<ng-template #visibleButton>
	<ng-template [ngxPermissionsOnly]="[PermissionsEnum.ALL_ORG_EDIT, PermissionsEnum.ORG_PROJECT_ADD]">
		<button type="button" nbButton status="success" size="small" (click)="navigateToProject()">
			<nb-icon icon="plus-outline"></nb-icon>
			{{ 'BUTTONS.ADD' | translate }}
		</button>
	</ng-template>
</ng-template>
