@use 'themes' as *;

@include nb-install-component {
  nb-card {
    background-color: nb-theme(gauzy-card-2);
  }

  nb-card-body {
    padding: 0;
    background-color: nb-theme(gauzy-card-2);

    .email-list {
      background-color: nb-theme(gauzy-card-3);
      margin: 1rem;
      padding: 14px 10px;
      border-radius: nb-theme(border-radius);

      nb-list-item {
        padding: 5px 10px 5px 0;

        .email-list-item {
          background-color: nb-theme(gauzy-card-1);
          width: fit-content;
          border-radius: nb-theme(border-radius);
          padding: 8px;

          &.selected {
            background-color: nb-theme(gauzy-background-transparent-1);
          }

          ::ng-deep ngx-avatar {
            .inner-wrapper {
              background-color: nb-theme(color-primary-transparent-100);
              border-radius: nb-theme(button-rectangle-border-radius);
              padding: 3px 9px 3px 3px;
              display: flex;
              flex-direction: row;
              align-items: center;
              width: fit-content;
              font-size: 12px;
              font-weight: 400;
              line-height: 15px;
              letter-spacing: 0em;
              gap: 8px;

              .image-container {
                height: 20px;
                width: 20px;
                display: flex;
                align-items: center;
                justify-content: center;

                img[type='user'] {
                  height: 18px;
                  width: 18px;
                }
              }

              .link-text {
                color: nb-theme(text-primary-color);
                font-weight: normal;
              }
            }
          }

          .email-details-item {
            font-size: 12px;
            font-weight: 400;
            line-height: 15px;
            letter-spacing: 0em;

            b {
              font-weight: 600;
            }
          }

          .date {
            font-size: 9px;
            font-weight: 400;
            line-height: 11px;
            letter-spacing: 0em;
            color: nb-theme(gauzy-text-color-2);
            margin: 0.2rem 0 0.4rem 0;
          }
        }
      }
    }
  }

  .image-container {
    @include nb-ltr(margin-right, 10px);
    @include nb-rtl(margin-left, 10px);
  }

  h2 {
    text-align: center;
  }

  .email-history {
    &-header {
      margin-bottom: 15px;
    }

    &-archive {
      margin-top: 15px;
    }

    &-avatar {
      width: 40px;
      height: 100%;
      border-radius: 50%;
    }

    &-to {
      margin-left: 50px;
      @include nb-ltr(margin-left, 50px);
      @include nb-rtl(margin-right, 50px);
    }
  }

  .content-container {
    display: flex;

    nb-list {
      white-space: nowrap;
      overflow: hidden;
      min-width: 250px;
      flex: none;
      overflow-y: auto;
      max-height: calc(100vh - 18.5rem);

      nb-list-item {
        cursor: pointer;

        &:first-child {
          border-top: none;
        }

        &:hover {
          color: nb-theme(text-primary-color);
        }

        .email-list-item {
          display: flex;
          justify-content: center;
          flex-direction: column;

          span {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            display: block;
          }
        }
      }
    }

    .email-container {
      width: 100%;
      display: flex;
      flex-direction: column;
      padding: 1rem;
      @include nb-ltr(margin, 1rem 1rem 1rem 0);
      @include nb-rtl(margin, 1rem 0 1rem 1rem);
      background-color: nb-theme(gauzy-card-1);
      border-radius: nb-theme(border-radius);

      .email-details {
        display: flex;
        justify-content: center;
        flex-direction: column;
        background-color: nb-theme(gauzy-sidebar-background-4);
        margin: -1rem;
        padding: 1rem 1.5rem;
        border-radius: nb-theme(border-radius) nb-theme(border-radius) 0 0;

        > span {
          display: flex;

          .col-fixed {
            width: 90px;
            display: block;
          }
        }
      }
    }
  }

  .filter-count {
    margin-top: -1.5rem;
  }
}

.custom-parent-email-content {
  display: flex;
  justify-content: flex-start;
  width: 100%;
  margin-top: 1rem;
  padding: 0.5rem;
  overflow: auto;
  max-height: calc(100vh - 26rem);
}

.email-content {
  width: fit-content;
}

:host .email-history-archive {
  position: absolute;
  top: 1rem;
  @include nb-ltr(right, 2rem);
  @include nb-rtl(left, 2rem);
  display: flex;
  gap: 8px;

  &[nbButton].appearance-outline.status-primary {
    background-color: transparent;
    border-width: 2px;
    border-color: nb-theme(gauzy-background-transparent);
  }
}

h4 {
  font-size: 24px;
  font-weight: 600;
  line-height: 30px;
  letter-spacing: 0em;
  text-align: left;

  .menu-setting {
    font-weight: 400;
  }
}
