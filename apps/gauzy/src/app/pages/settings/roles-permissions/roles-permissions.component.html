<nb-card>
	<nb-card-header>
		<h4>
			<span class="menu-setting">{{ 'MENU.SETTINGS' | translate }}/</span>
			{{ 'ORGANIZATIONS_PAGE.EDIT.ROLES_PERMISSIONS' | translate }}
		</h4>
	</nb-card-header>
	<nb-card-body>
		<div class="row">
			<div class="col-6">
				<div class="form-group">
					<nb-form-field>
						<input
							#input
							[formControl]="formControl"
							(input)="onInputChange()"
							nbInput
							fullWidth
							[placeholder]="'FORM.PLACEHOLDERS.ROLE' | translate"
							[nbAutocomplete]="autocomplete"
						/>
						<button nbSuffix nbButton ghost>
							<nb-icon
								[icon]="'close'"
								pack="eva"
								(click)="formControl.reset(rolesEnum.EMPLOYEE)"
							></nb-icon>
						</button>
					</nb-form-field>
					<nb-autocomplete #autocomplete (selectedChange)="onSelectionChange($event)">
						<nb-option
							*ngFor="let role of roles$ | async"
							[value]="role.name"
							[class]="role.name === input.value ? 'active' : ''"
						>
							{{ role.name }}
						</nb-option>
					</nb-autocomplete>
				</div>
			</div>
			<div class="col-6">
				<input
					type="text"
					[formControl]="searchControl"
					nbInput
					fullWidth
					placeholder="Search permissions..."
				/>
			</div>
			<div class="col-6" *ngxPermissionsOnly="['CHANGE_ROLES_PERMISSIONS']">
				<ng-container *ngIf="isWantToCreate">
					<div class="actions create">
						<button
							nbButton
							status="success"
							(click)="createRole()"
							size="small"
							[nbTooltip]="'BUTTONS.CREATE' | translate"
						>
							<nb-icon icon="plus-outline"></nb-icon>
						</button>
						{{ 'BUTTONS.CREATE_NEW_ROLE' | translate : { name: input.value } }}
					</div>
				</ng-container>
				<ng-container *ngIf="role && role.isSystem === false && !isWantToCreate">
					<div class="actions delete">
						<button
							nbButton
							status="basic"
							class="action"
							(click)="deleteRole()"
							size="small"
							[nbTooltip]="'BUTTONS.DELETE' | translate"
						>
							<nb-icon status="danger" icon="trash-2-outline"></nb-icon>
						</button>
						{{ 'BUTTONS.DELETE_EXISTING_ROLE' | translate : { name: role.name } }}
					</div>
				</ng-container>
			</div>
		</div>
		<div class="row content" [nbSpinner]="loading" nbSpinnerSize="large">
			<div class="col-12 col-xl-6">
				<nb-card>
					<nb-card-header>
						{{ 'ORGANIZATIONS_PAGE.PERMISSIONS.GROUPS.GENERAL' | translate }}
					</nb-card-header>
					<nb-card-body class="permission-items-col">
						<nb-toggle
							*ngFor="let permission of filteredGeneralPermissions"
							[(checked)]="enabledPermissions[permission]"
							(checkedChange)="permissionChanged(permission, $event, !isDisabledGeneralPermissions())"
							labelPosition="start"
							status="basic"
							[disabled]="isDisabledGeneralPermissions()"
						>
							<div class="custom-permission-view">
								<strong>{{ 'ORGANIZATIONS_PAGE.PERMISSIONS.' + permission | translate }}</strong>
								<small>{{ 'ORGANIZATIONS_PAGE.PERMISSIONS.' + permission | translate }}</small>
							</div>
						</nb-toggle>
					</nb-card-body>
				</nb-card>
			</div>
			<div class="col-12 col-xl-6">
				<nb-card>
					<nb-card-header
						>{{ 'ORGANIZATIONS_PAGE.PERMISSIONS.GROUPS.ADMINISTRATION' | translate }}
						<nb-icon
							[nbTooltip]="'ORGANIZATIONS_PAGE.PERMISSIONS.ONLY_ADMIN' | translate"
							icon="question-mark-circle-outline"
							size="tiny"
						>
						</nb-icon>
					</nb-card-header>
					<nb-card-body class="permission-items-col">
						<nb-toggle
							*ngFor="let permission of filteredAdminPermissions"
							[(checked)]="enabledPermissions[permission]"
							(checkedChange)="
								permissionChanged(permission, $event, !isDisabledAdministrationPermissions())
							"
							labelPosition="start"
							status="basic"
							[disabled]="isDisabledAdministrationPermissions()"
						>
							<div class="custom-permission-view">
								<strong>{{ 'ORGANIZATIONS_PAGE.PERMISSIONS.' + permission | translate }}</strong>
								<small>{{ 'ORGANIZATIONS_PAGE.PERMISSIONS.' + permission | translate }}</small>
							</div>
						</nb-toggle>
					</nb-card-body>
				</nb-card>
			</div>
		</div>
	</nb-card-body>
</nb-card>
