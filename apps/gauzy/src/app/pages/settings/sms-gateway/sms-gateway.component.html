<nb-card class="card-scroll">
	<nb-card-header>
		<h4>
			<span class="menu-setting">{{ 'MENU.SETTINGS' | translate }}/ </span
			>{{ 'SMS_GATEWAY_PAGE.HEADER' | translate }}
		</h4>
	</nb-card-header>
	<nb-card-body class="permission-items-col">
		<nb-toggle
			*ngFor="let provider of smsProviders"
			(checkedChange)="providerChanged(provider, $event)"
			labelPosition="start"
			status="basic"
		>
			{{ 'SMS_GATEWAY_PAGE.' + provider | translate }}
		</nb-toggle>
		<div class="content"></div>
	</nb-card-body>
</nb-card>
