<nb-card [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
	<nb-card-header class="d-flex flex-column pb-0">
		<div class="card-header-title">
			<h4>
				<ngx-header-title [allowEmployee]="false">
					{{ 'EQUIPMENT_PAGE.HEADER' | translate }}
				</ngx-header-title>
			</h4>
			<div>
				<button
					class="float-right action sharing"
					nbButton
					status="primary"
					[routerLink]="'/pages/organization/equipment-sharing'"
				>
					{{ 'BUTTONS.EQUIPMENT_SHARING' | translate }}
				</button>
			</div>
		</div>
		<div class="align-self-end">
			<ngx-gauzy-button-action
				[isDisable]="disableButton"
				[buttonTemplate]="actionButtons"
				[componentName]="viewComponentName"
				[buttonTemplateVisible]="visibleButton"
			></ngx-gauzy-button-action>
		</div>
	</nb-card-header>
	<nb-card-body>
		<ng-template [ngIf]="dataLayoutStyle === componentLayoutStyleEnum.TABLE" [ngIfElse]="gridLayout">
			<div class="table-scroll-container">
				<angular2-smart-table
					style="cursor: pointer"
					(userRowSelect)="selectEquipment($event)"
					[settings]="settingsSmartTable"
					[source]="smartTableSource"
				></angular2-smart-table>
			</div>
			<div class="pagination-container align-self-end">
				<ng-container *ngIf="smartTableSource">
					<ngx-pagination [source]="smartTableSource"></ngx-pagination>
				</ng-container>
			</div>
		</ng-template>
		<ng-template #gridLayout>
			<ga-card-grid
				[totalItems]="pagination?.totalItems"
				[settings]="settingsSmartTable"
				[source]="equipments"
				(onSelectedItem)="selectEquipment($event)"
				(scroll)="onScroll()"
			></ga-card-grid>
		</ng-template>
	</nb-card-body>
</nb-card>
<ng-template #actionButtons let-buttonSize="buttonSize" let-selectedItem="selectedItem">
	<div class="btn-group actions">
		<button
			(click)="save(this.selectedEquipment)"
			nbButton
			status="basic"
			class="action primary"
			[disabled]="!selectedItem && disableButton"
			size="small"
		>
			<nb-icon class="mr-1" icon="edit-outline"></nb-icon>
			{{ 'BUTTONS.EDIT' | translate }}
		</button>
		<button
			(click)="delete(selectedItem)"
			nbButton
			status="basic"
			[disabled]="!selectedItem && disableButton"
			class="action"
			size="small"
		>
			<nb-icon status="danger" icon="trash-2-outline"> </nb-icon>
		</button>
	</div>
</ng-template>
<ng-template #visibleButton>
	<button (click)="save()" nbButton status="success" class="action" size="small">
		<nb-icon class="mr-1" icon="plus-outline"></nb-icon>
		{{ 'BUTTONS.ADD' | translate }}
	</button>
</ng-template>
