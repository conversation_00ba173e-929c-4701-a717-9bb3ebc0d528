@use "gauzy/_gauzy-table" as *;
@use "gauzy/_gauzy-dialogs" as *;

:host {
	@include input-appearance(2rem, var(--gauzy-sidebar-background-3));

	.content {
		background-color: var(--gauzy-card-2);
		border-radius: var(--border-radius);
		padding: 1rem 0.5rem 1rem 1rem;
		display: flex;
		flex-direction: column;
		overflow: unset;

		.table-scroll-container {
			flex-grow: 10;
		}
	}

	nb-card {
		width: 845px;
	}
}

:host ::ng-deep input[type='checkbox'] {
	width: 15px !important;
}

:host ::ng-deep .angular2-smart-action-multiple-select {
	display: flex;
	justify-content: center;
	align-content: center;
}

:host ::ng-deep td.angular2-smart-action-multiple-select {
	display: flex;
	height: 79px;
	align-items: center;
}
