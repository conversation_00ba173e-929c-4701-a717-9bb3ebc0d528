<nb-card>
	<nb-card-header class="d-flex flex-column">
		<span class="cancel"><i class="fas fa-times" (click)="dialogRef.close()"></i></span>
		<h5 class="title">
			{{ 'INVENTORY_PAGE.MANAGE_VARIANTS_QUANTITY' | translate }}
		</h5>
	</nb-card-header>
	<nb-card-body class="content">
		<div class="table-scroll-container">
			<angular2-smart-table
				[settings]="settingsSmartTable"
				[source]="smartTableSource"
				style="cursor: pointer"
				#warehouseVariantsTable
			></angular2-smart-table>
		</div>
	</nb-card-body>
	<nb-card-footer>
		<button (click)="onClose()" status="success" class="action" nbButton>
			{{ 'BUTTONS.OK' | translate }}
		</button>
	</nb-card-footer>
</nb-card>
