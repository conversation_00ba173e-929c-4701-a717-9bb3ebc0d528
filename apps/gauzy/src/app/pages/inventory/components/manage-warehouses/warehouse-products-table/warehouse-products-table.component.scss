@use "gauzy/gauzy-overrides" as *;
@use "gauzy/gauzy-table" as *;

:host {
	@include input-appearance(2rem, var(--gauzy-sidebar-background-3));

	.container-content {
		display: flex;
		flex-direction: column;
		height: calc(100vh - 22.5rem);
		overflow: unset;
		background-color: var(--gauzy-card-2);
		border-radius: var(--border-radius);
		padding: 0 0.5rem 1rem 1rem;

		.table-scroll-container {
			flex-grow: 10;
			max-height: unset;

			::ng-deep .form-control {
				border-radius: var(--border-radius);
			}
		}
	}

	.actions {
		display: flex;
		align-items: center;
	}
}
