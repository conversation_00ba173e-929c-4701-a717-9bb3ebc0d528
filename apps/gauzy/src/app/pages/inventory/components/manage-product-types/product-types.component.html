<nb-card [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
	<nb-card-header class="d-flex flex-column pb-0">
		<div class="card-header-title">
			<h4>
				<ngx-back-navigation></ngx-back-navigation>
				<ngx-header-title>
					{{ 'INVENTORY_PAGE.PRODUCT_TYPES' | translate }}
				</ngx-header-title>
			</h4>
		</div>
		<div class="gauzy-button-container">
			<ngx-gauzy-button-action
				[buttonTemplate]="actionButtons"
				[componentName]="viewComponentName"
				[buttonTemplateVisible]="visibleButton"
				[isDisable]="disableButton"
			>
			</ngx-gauzy-button-action>
		</div>
	</nb-card-header>
	<nb-card-body>
		<ng-template ngxPermissionsOnly="ORG_PRODUCT_TYPES_VIEW">
			<ng-template
				[ngIf]="dataLayoutStyle === componentLayoutStyleEnum.TABLE"
				[ngIfElse]="gridLayout"
			>
				<div class="table-scroll-container">
					<angular2-smart-table
						[settings]="settingsSmartTable"
						(userRowSelect)="selectProductType($event)"
						[source]="smartTableSource"
						style="cursor: pointer"
						#productTypesTable
					></angular2-smart-table>
				</div>
				<div class="pagination-container">
					<ng-container *ngIf="smartTableSource">
						<ngx-pagination
							[source]="smartTableSource"
						></ngx-pagination>
					</ng-container>
				</div>
			</ng-template>
			<ng-template #gridLayout>
				<ga-card-grid
					[totalItems]="pagination?.totalItems"
					[settings]="settingsSmartTable"
					[source]="productTypes"
					(onSelectedItem)="selectProductType($event)"
					(scroll)="onScroll()"
				></ga-card-grid>
			</ng-template>
		</ng-template>
	</nb-card-body>
</nb-card>
<ng-template #visibleButton>
	<ng-template ngxPermissionsOnly="ORG_PRODUCT_TYPES_EDIT">
		<button
			*ngIf="!showAddCard"
			(click)="onAdd()"
			nbButton
			status="success"
			class="action"
			size="small"
		>
			<nb-icon icon="plus-outline"></nb-icon
			>{{ 'BUTTONS.ADD' | translate }}
		</button>
	</ng-template>
</ng-template>
<ng-template
	#actionButtons
	let-buttonSize="buttonSize"
	let-selectedItem="selectedItem"
>
	<ng-template ngxPermissionsOnly="ORG_PRODUCT_TYPES_EDIT">
		<div class="btn-group actions">
			<button
				(click)="onEdit(selectedItem)"
				nbButton
				status="basic"
				class="action primary"
				size="small"
				[disabled]="!selectedItem && disableButton"
			>
				<nb-icon icon="edit-outline"></nb-icon>
				{{ 'BUTTONS.EDIT' | translate }}
			</button>
			<button
				(click)="delete(selectedItem)"
				nbButton
				status="basic"
				class="action"
				[disabled]="!selectedItem && disableButton"
				size="small"
				[nbTooltip]="'BUTTONS.DELETE' | translate"
			>
				<nb-icon status="danger" icon="trash-2-outline"></nb-icon>
			</button>
		</div>
	</ng-template>
</ng-template>
