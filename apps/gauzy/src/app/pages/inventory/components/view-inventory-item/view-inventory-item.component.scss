.featured-image {
  width: 100%;
}

.key-description {
  font-weight: 600;
  width: 6rem;
  display: inline-block;
}

img.gallery-sm-preview-item {
  width: 100%;
  height: 70px;

  &:hover {
    box-shadow: var(--color-primary-active-border) 0px 1px 2px 0px,
      var(--color-primary-hover-border) 0px 1px 3px 1px;
  }
}

.option-item {
  padding: 5px 10px;
  font-size: 12px;
  background: #3366ff;
  border-radius: 7px;
  color: #fff;
  margin-right: 10px;
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.tag-item {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 600;
  line-height: 13px;
  letter-spacing: 0em;

  display: flex;
  align-items: center;
  text-align: center;
  color: white;
  font-weight: bold;
  width: fit-content;
}

.d-flex {
  display: flex;
}

.options {
  display: flex;
  flex-wrap: wrap;
}

h5 {
  font-size: 24px;
  font-weight: 600;
  line-height: 30px;
  letter-spacing: 0em;
}

img {
  object-fit: cover;
  border-radius: 4px;
  box-shadow: var(--gauzy-shadow);
}

img.featured-image {
  height: 300px;
}

.product-container-inner{
  height: 300px;
}

.inventory-item-tags {
  display: flex;
  gap: 0.5rem;
}

:host {

  .main,
  nb-card-body {
    background: var(--gauzy-card-2);
  }

  nb-card-body {
    border-radius: 0 0 var(--border-radius) var(--border-radius);
  }
}