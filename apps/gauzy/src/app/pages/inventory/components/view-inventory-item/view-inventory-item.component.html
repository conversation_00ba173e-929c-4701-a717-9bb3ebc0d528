<nb-card class="main card-scroll">
	<nb-card-header class="d-flex">
		<ngx-back-navigation></ngx-back-navigation>
		<h5>
			{{ 'INVENTORY_PAGE.VIEW_INVENTORY_ITEM' | translate }}
		</h5>
	</nb-card-header>

	<nb-card-body
		[nbSpinner]="loading"
		nbSpinnerStatus="primary"
		nbSpinnerSize="large"
	>
		<div class="row" *ngIf="inventoryItem">
			<div class="col-md-3">
				<div class="product-container">
					<div class="product-container-inner">
						<img
							*ngIf="
								inventoryItem.featuredImage &&
								inventoryItem.featuredImage.fullUrl
							"
							class="featured-image"
							[src]="inventoryItem.featuredImage.fullUrl"
							alt="Feature image"
						/>
						<ga-no-image
							*ngIf="
								!inventoryItem?.featuredImage &&
								!inventoryItem?.featuredImage?.fullUrl
							"
							class="no-image"
							[fontSize]="16"
						></ga-no-image>
					</div>
					<div
						*ngIf="inventoryItem.gallery"
						class="gallery-preview-items mt-3"
					>
						<div class="row">
							<div
								class="col-sm-3 col-md-4 gallery-sm-preview"
								*ngFor="
									let galleryItem of inventoryItem.gallery
								"
								(click)="onGalleryItemClick(galleryItem)"
							>
								<img
									class="gallery-sm-preview-item"
									[src]="galleryItem.fullUrl"
								/>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="col-md-9 right-content">
				<div class="container">
					<div class="row mb-1">
						<div class="col-md-6 col-sm-12">
							<p>
								<span class="key-description"
									>{{
										'INVENTORY_PAGE.NAME' | translate
									}}:</span
								>{{ getTranslatedProp(inventoryItem, 'name') }}
							</p>
							<p>
								<span class="key-description"
									>{{
										'INVENTORY_PAGE.DESCRIPTION'
											| translate
									}}:</span
								>{{
									getTranslatedProp(
										inventoryItem,
										'description'
									)
								}}
							</p>
							<p>
								<span class="key-description"
									>{{
										'INVENTORY_PAGE.TYPE' | translate
									}}:</span
								>{{
									getTranslatedProp(
										inventoryItem.productType,
										'name'
									)
								}}
							</p>
							<p>
								<span class="key-description"
									>{{
										'INVENTORY_PAGE.CATEGORY' | translate
									}}:</span
								>{{
									getTranslatedProp(
										inventoryItem.productCategory,
										'name'
									)
								}}
							</p>
							<p>
								<span class="key-description"
									>{{
										'INVENTORY_PAGE.ENABLED' | translate
									}}:</span
								>
								<nb-icon
									*ngIf="inventoryItem.enabled"
									icon="checkmark"
									status="success"
								></nb-icon>
								<nb-icon
									*ngIf="!inventoryItem.enabled"
									icon="close"
									status="danger"
								></nb-icon>
							</p>
							<div class="d-flex">
								<div class="key-description">
									{{ 'INVENTORY_PAGE.OPTIONS' | translate }}:
								</div>
								<div class="options" *ngIf="inventoryItem">
									<div
										*ngFor="let option of options"
										class="option-item"
									>
										{{ option.name }}
									</div>
								</div>
							</div>
						</div>
						<div
							class="col-md-6 col-sm-12"
							*ngIf="inventoryItem.tags"
						>
							<span class="key-description mb-2"
								>{{ 'INVENTORY_PAGE.TAGS' | translate }}:</span
							>
							<div class="inventory-item-tags">
								<div
									*ngFor="let tag of inventoryItem.tags"
									class="tag-item"
									[ngStyle]="{
										background: tag.color
									}"
								>
									{{ tag.name }}
								</div>
							</div>
						</div>
					</div>
					<div class="row mt-5">
						<div class="table-scroll-container">
							<angular2-smart-table
								[settings]="settingsSmartTable"
								[source]="smartTableSource"
								#variantTable
							>
							</angular2-smart-table>
						</div>
					</div>
				</div>
			</div>
		</div>
	</nb-card-body>
</nb-card>
