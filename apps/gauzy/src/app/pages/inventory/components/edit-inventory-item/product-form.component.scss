@use 'gauzy/_gauzy-cards' as *;
@use 'gauzy/_gauzy-overrides' as ga-overrides;

.d-bottom {
  display: flex;
  align-items: flex-end;
  align-content: flex-end;
}

.options-wrap {
  display: flex;
}

.option-delete {
  height: 20px;
  width: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 5px;

  nb-icon {
    width: 100%;
    color: #fff;
    padding-bottom: 2px;
  }
}

.option {
  padding: 5px 10px;
  font-size: 12px;
  background: #3366ff;
  border-radius: 7px;
  color: #fff;
  margin-right: 10px;
  display: flex;
  align-items: center;
  cursor: pointer;
}

.text-danger {
  max-width: 100%;
  font-size: 12px;
  margin-top: 5px;
}

ng-option {
  z-index: 200;
}

.variants-container {
  max-width: 100%;
  display: flex;
  flex-wrap: wrap;
}

.width-100 {
  width: 100%;
}

nb-card-body {
  overflow-y: visible;
  height: 100%;
}

nb-card {
  overflow-y: visible;
  margin-bottom: 0px;
}

nb-card-footer {
  display: flex;
  gap: 15px;
  justify-content: center;
  padding: 0 24px;
}

.card-container {
  display: flex;
  flex-direction: column;
}

.product-container {
  width: 100%;
}

.product-photo-container {
  position: relative;

  & div:nth-child(2) {
    position: absolute;
    bottom: 0;
  }
}

.product-photo {
  width: 100%;
  background-color: white;
  overflow: hidden;
  position: relative;

  div {
    pointer-events: none;
    background: black;
    position: absolute;
    height: 100%;
    width: 100%;
  }

  img {
    width: 100%;
    height: auto;
  }

  input {
    width: 100%;
    height: 100%;
    opacity: 0;
    position: absolute;
    z-index: 3;
    cursor: pointer;
  }

  svg {
    z-index: 2;
    transition: opacity 0.2s ease-in;
    opacity: 0.3;
    position: absolute;
    top: calc(50% - 68px / 2);
    left: calc(50% - 68px / 2);
  }
}

h5 {
  font-size: 24px;
  font-weight: 600;
  line-height: 30px;
  letter-spacing: 0em;
  text-align: left;
}

nb-tab {
  background-color: var(--gauzy-card-2);
  border: 0 0 $default-radius $default-radius;

  &.content-active {
    margin: 0 -1.5rem -1rem 0;
    height: calc(100vh - 17rem);
    border-radius: 0 0 $default-radius 0;
  }

  input,
  textarea,
  ::ng-deep .ng-select .ng-select-container,
  ::ng-deep nb-select.appearance-outline.status-basic .select-button,
  ga-location-form ::ng-deep input {
    background-color: var(--gauzy-card-1);
  }
}

label {
  font-size: 11px;
  font-weight: 600;
  line-height: 13px;
  letter-spacing: -0.01em;
  text-align: left;
}
