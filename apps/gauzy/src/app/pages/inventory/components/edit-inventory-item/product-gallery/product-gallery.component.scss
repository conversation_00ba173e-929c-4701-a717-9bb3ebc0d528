@use 'gauzy/_gauzy-table' as *;

.actions-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.featured-image {
  width: 100%;
  height: 180px;
  object-fit: cover;
  border-radius: var(--border-radius);
}

.image-container-notSelected {
  height: 260px;
}

.image-container-selected {
  height: 180px;
}

.gallery-sm-preview-item {
  width: 100%;
  object-fit: cover;
  box-shadow: var(--gauzy-shadow);
  height: 70px;
  border-radius: 4px;

  &.selected {
    box-shadow: nb-theme(color-primary-active-border) 0px 1px 2px 0px,
      nb-theme(color-primary-hover-border) 0px 1px 3px 1px;
  }

  &:hover {
    box-shadow: nb-theme(color-primary-active-border) 0px 1px 2px 0px,
      nb-theme(color-primary-hover-border) 0px 1px 3px 1px;
  }
}

.gallery-sm-preview {
  transition: 0.1s all ease-in-out;
  cursor: pointer;
  margin-bottom: 10px;
}

button {
  margin-right: 3px;
  margin-left: 3px;
}

.featured-icon {
  width: 20px;
  height: 20px;
  position: absolute;
  top: -3px;
  left: 0px;
  border-radius: 4px;
  background: white;
  color: nb-theme(text-success-color);
}

.actions-container {
  display: flex;
  align-items: center;
  gap: 10px;

  button {
    border-width: 2px;
    background-color: unset;

    &[nbButton].appearance-outline.status-basic {
      border-color: var(--color-basic-transparent-300);
    }

    &[nbButton].appearance-outline.status-primary {
      border-color: var(--gauzy-background-transparent-1);
    }

    &[nbButton].appearance-outline.status-danger {
      border-color: var(--color-danger-transparent-300);
    }

    &[nbButton].appearance-outline.status-success {
      border-color: var(--color-success-transparent-300);
      padding: 4px 5px;
    }
  }

  .gallery-plus {
    border-radius: var(--button-rectangle-border-radius);
    padding: 4px 6px;
    background-color: var(--gauzy-card-2);

    i {
      font-size: 18px;
    }
  }

  .gallery-actions {
    background-color: var(--gauzy-card-2);
    border-radius: var(--button-rectangle-border-radius);
    padding: 3px 5px;
  }
}
