<div class="row d-bottom mb-3">
	<div class="col-sm-9">
		<label class="label" for="addVariant">
			{{ 'INVENTORY_PAGE.ADD_VARIANT' | translate }}
		</label>
		<ng-select
			id="addVariant"
			[multiple]="true"
			[closeOnSelect]="false"
			[searchable]="false"
			[notFoundText]="'INVENTORY_PAGE.NO_OPTIONS_LABEL' | translate"
			[placeholder]="'INVENTORY_PAGE.ADD_VARIANT' | translate"
			[(ngModel)]="editVariantCreateInput.options"
			(change)="onSelectOption($event)"
			appendTo="body"
		>
			<ng-option *ngFor="let option of optionsSelect" [value]="option">{{
				option
			}}</ng-option>
		</ng-select>
	</div>
	<div class="col-sm-3 align-self-end">
		<ng-container *ngxPermissionsOnly="'ALL_ORG_EDIT'">
			<div class="add-variant">
				<button
					class="green"
					status="success"
					nbButton
					(click)="onSaveVariant()"
					size="small"
				>
					{{ 'INVENTORY_PAGE.ADD_VARIANT' | translate }}
				</button>
			</div>
		</ng-container>
	</div>
</div>
<div class="row mb-3">
	<div class="col-sm-12 variants-container">
		<button
			class="mr-2 btn-variant"
			nbButton
			*ngFor="let productVariant of variantCreateInputs"
			(click)="onVariantBtnClick(productVariant)"
		>
			<span
				class="btn-variant-icon"
				[ngClass]="{
					'variant-stored-btn': productVariant.isStored
				}"
			>
				<nb-icon icon="edit-outline"></nb-icon
			></span>
			{{ getVariantDisplayName(productVariant) }}
		</button>
	</div>
</div>
