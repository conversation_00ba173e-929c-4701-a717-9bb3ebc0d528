<nb-card
	[nbSpinner]="loading"
	nbSpinnerStatus="primary"
	nbSpinnerSize="large"
>
	<nb-card-header class="d-flex flex-column pb-0">
		<div class="card-header-title">
			<div class="card-header-title">
				<h4>
					<ngx-header-title [allowEmployee]="false">
						{{ 'INVENTORY_PAGE.HEADER' | translate }}
					</ngx-header-title>
				</h4>
			</div>
			<div class="card-header-title">
				<div class="mr-2">
					<ng-container
						*ngTemplateOutlet="subActionButtons"
					></ng-container>
				</div>
			</div>
		</div>
		<div class="gauzy-button-container">
			<ngx-gauzy-button-action
				[buttonTemplate]="actionButtons"
				[componentName]="viewComponentName"
				[buttonTemplateVisible]="visibleButton"
				[isDisable]="disableButton"
			></ngx-gauzy-button-action>
		</div>
	</nb-card-header>

	<nb-card-body>
		<ng-template
			[ngIf]="dataLayoutStyle === componentLayoutStyleEnum.TABLE"
			[ngIfElse]="gridLayout"
		>
			<div class="table-scroll-container">
				<angular2-smart-table
					[settings]="settingsSmartTable"
					(userRowSelect)="selectProduct($event)"
					[source]="smartTableSource"
					style="cursor: pointer"
				></angular2-smart-table>
			</div>
			<div class="pagination-container">
				<ng-container *ngIf="smartTableSource">
					<ngx-pagination
						[source]="smartTableSource"
					></ngx-pagination>
				</ng-container>
			</div>
		</ng-template>
		<ng-template #gridLayout>
			<ga-card-grid
				[totalItems]="pagination?.totalItems"
				[settings]="settingsSmartTable"
				[source]="products"
				(scroll)="onScroll()"
				(onSelectedItem)="selectProduct($event)"
			></ga-card-grid>
		</ng-template>
	</nb-card-body>
</nb-card>
<ng-template #visibleButton>
	<ng-template ngxPermissionsOnly="ORG_INVENTORY_PRODUCT_EDIT">
		<button
			(click)="onAddInventoryItem()"
			nbButton
			status="success"
			class="action"
			size="small"
		>
			<nb-icon icon="plus-outline"></nb-icon
			>{{ 'BUTTONS.ADD' | translate }}
		</button>
	</ng-template>
</ng-template>
<ng-template
	#actionButtons
	let-buttonSize="buttonSize"
	let-selectedItem="selectedItem"
>
	<div class="btn-group actions">
		<ng-template ngxPermissionsOnly="ORG_INVENTORY_VIEW">
			<button
				(click)="onViewInventoryItem(selectedItem)"
				status="basic"
				class="action secondary"
				size="small"
				nbButton
			>
				<nb-icon icon="eye-outline"></nb-icon>
				{{ 'BUTTONS.VIEW' | translate }}
			</button>
		</ng-template>
		<ng-template ngxPermissionsOnly="ORG_INVENTORY_PRODUCT_EDIT">
			<button
				(click)="onEditInventoryItem(selectedItem)"
				nbButton
				status="basic"
				class="action primary"
				size="small"
				[disabled]="!selectedItem && disableButton"
			>
				<nb-icon icon="edit-outline"></nb-icon>
				{{ 'BUTTONS.EDIT' | translate }}
			</button>
		</ng-template>
		<ng-template ngxPermissionsOnly="ORG_INVENTORY_PRODUCT_EDIT">
			<button
				nbButton
				[disabled]="!selectedItem && disableButton"
				size="small"
				(click)="delete(selectedItem)"
				status="basic"
				class="action"
				[nbTooltip]="'BUTTONS.DELETE' | translate"
			>
				<nb-icon status="danger" icon="trash-2-outline"></nb-icon>
			</button>
		</ng-template>
	</div>
</ng-template>
<ng-template #subActionButtons>
	<ng-template ngxPermissionsOnly="ORG_INVENTORY_VIEW">
		<button
			(click)="manageStores()"
			status="warning"
			outline
			class="action"
			size="small"
			nbButton
		>
			<nb-icon icon="home-outline"></nb-icon>
			{{ 'INVENTORY_PAGE.MERCHANTS' | translate }}
		</button>
		<button
			(click)="manageWarehouses()"
			status="warning"
			class="action"
			outline
			size="small"
			nbButton
		>
			<nb-icon icon="home-outline"></nb-icon>
			{{ 'INVENTORY_PAGE.WAREHOUSES' | translate }}
		</button>
	</ng-template>
	<ng-template ngxPermissionsOnly="ORG_PRODUCT_CATEGORIES_VIEW">
		<button
			(click)="manageProductCategories()"
			status="primary"
			class="action"
			outline
			size="small"
			nbButton
		>
			<nb-icon icon="edit-outline"></nb-icon>
			{{ 'INVENTORY_PAGE.PRODUCT_CATEGORIES' | translate }}
		</button>
	</ng-template>
	<ng-template ngxPermissionsOnly="ORG_PRODUCT_TYPES_VIEW">
		<button
			(click)="manageProductTypes()"
			status="primary"
			class="action"
			outline
			size="small"
			nbButton
		>
			<nb-icon icon="edit-outline"></nb-icon>
			{{ 'INVENTORY_PAGE.PRODUCT_TYPES' | translate }}
		</button>
	</ng-template>
</ng-template>
