<nb-card>
	<nb-card-header>
		<div class="main-header">
			<div class="top-nav">
				<h4>
					<ngx-back-navigation></ngx-back-navigation>
					{{
						(!merchant
							? 'INVENTORY_PAGE.CREATE_MERCHANT'
							: 'INVENTORY_PAGE.EDIT_MERCHANT'
						) | translate
					}}
				</h4>
			</div>
		</div>
	</nb-card-header>

	<nb-card-body>
		<form [formGroup]="form">
			<nb-stepper #stepper [disableStepNavigation]="!merchant">
				<nb-step [label]="'INVENTORY_PAGE.MAIN' | translate">
					<div
						class="d-flex flex-column justify-content-between h-100"
					>
						<div class="row">
							<div class="col-md-3 col-sm-12 image-container">
								<ng-container
									*ngTemplateOutlet="storeImage"
								></ng-container>
							</div>
							<div class="col-md-9 col-sm-12">
								<div class="row">
									<div class="col-md-6 col-sm-12">
										<div class="form-group">
											<label
												class="label"
												for="name"
												[innerHtml]="
													'INVENTORY_PAGE.NAME'
														| translate
												"
											>
											</label>
											<input
												fullWidth
												id="name"
												type="text"
												formControlName="name"
												nbInput
												[placeholder]="
													'INVENTORY_PAGE.NAME'
														| translate
												"
												[status]="
													FormHelpers.isInvalidControl(form, 'name')
														? 'danger'
														: 'basic'
												"
											/>
											<div
												class="text-danger"
												*ngIf="FormHelpers.isInvalidControl(form, 'name')"
											>
												{{
													'INVENTORY_PAGE.THIS_FIELD_IS_REQUIRED'
														| translate
												}}
											</div>
										</div>
									</div>
									<div class="col-md-6 col-sm-12">
										<div class="form-group">
											<label
												class="label"
												for="code"
												[innerHtml]="
													'INVENTORY_PAGE.CODE'
														| translate
												"
											>
											</label>
											<input
												fullWidth
												id="code"
												type="text"
												formControlName="code"
												nbInput
												[placeholder]="
													'INVENTORY_PAGE.CODE'
														| translate
												"
												[status]="
													FormHelpers.isInvalidControl(form, 'code')
														? 'danger'
														: 'basic'
												"
											/>
											<div
												class="text-danger"
												*ngIf="FormHelpers.isInvalidControl(form, 'code')"
											>
												{{
													'INVENTORY_PAGE.THIS_FIELD_IS_REQUIRED'
														| translate
												}}
											</div>
										</div>
									</div>
								</div>
								<div class="row">
									<div class="col-md-6 col-sm-12">
										<div class="form-group">
											<label
												class="label"
												for="email"
												[innerHtml]="
													'INVENTORY_PAGE.EMAIL'
														| translate
												"
											>
											</label>
											<input
												fullWidth
												id="email"
												type="text"
												formControlName="email"
												nbInput
												[placeholder]="
													'INVENTORY_PAGE.EMAIL'
														| translate
												"
												[status]="
													FormHelpers.isInvalidControl(form, 'email')
														? 'danger'
														: 'basic'
												"
											/>
											<div
												class="text-danger"
												*ngIf="FormHelpers.isInvalidControl(form, 'email')"
											>
												{{
													'INVENTORY_PAGE.EMAIL_WRONG_FORMAT'
														| translate
												}}
											</div>
										</div>
									</div>
									<div class="col-md-6 col-sm-12">
										<div class="form-group">
											<label
												class="label"
												for="phone"
												[innerHtml]="
													'INVENTORY_PAGE.PHONE'
														| translate
												"
											>
											</label>
											<input
												fullWidth
												id="phone"
												type="text"
												formControlName="phone"
												nbInput
												[placeholder]="
													'INVENTORY_PAGE.PHONE'
														| translate
												"
												[status]="
													FormHelpers.isInvalidControl(form, 'phone')
														? 'danger'
														: 'basic'
												"
											/>
											<div
												class="text-danger"
												*ngIf="
													FormHelpers.isInvalidControl(form, 'phone')
												"
											>
												{{
													'INVENTORY_PAGE.PHONE_WRONG_FORMAT'
														| translate
												}}
											</div>
										</div>
									</div>
								</div>
								<div class="row">
									<div class="col-md-6 col-sm-12">
										<ga-currency
											[formControl]="form.get('currency')"
										></ga-currency>
									</div>
									<div class="col-md-6 col-sm-12">
										<div class="form-group">
											<label
												class="label"
												for="fax"
												[innerHtml]="
													'INVENTORY_PAGE.FAX'
														| translate
												"
											>
											</label>
											<input
												formControlName="fax"
												type="text"
												id="fax"
												nbInput
												fullWidth
												[placeholder]="
													'INVENTORY_PAGE.FAX'
														| translate
												"
											/>
										</div>
									</div>
								</div>
								<div class="row">
									<div class="col-md-6 col-sm-12">
										<div class="form-group">
											<label
												class="label"
												for="fiscalInformation"
												[innerHtml]="
													'INVENTORY_PAGE.FISCAL_INFORMATION'
														| translate
												"
											>
											</label>
											<input
												formControlName="fiscalInformation"
												type="text"
												id="fiscalInformation"
												nbInput
												fullWidth
												[placeholder]="
													'INVENTORY_PAGE.FISCAL_INFORMATION'
														| translate
												"
											/>
										</div>
									</div>
									<div class="col-md-6 col-sm-12">
										<div class="form-group">
											<label
												class="label"
												for="website"
												[innerHtml]="
													'INVENTORY_PAGE.WEBSITE'
														| translate
												"
											>
											</label>
											<input
												formControlName="website"
												type="text"
												id="website"
												nbInput
												fullWidth
												[placeholder]="
													'INVENTORY_PAGE.WEBSITE'
														| translate
												"
											/>
										</div>
									</div>
								</div>
								<div class="row">
									<div class="col-md-12 col-sm-12">
										<div class="form-group">
											<label
												class="label"
												for="description"
												[innerHtml]="
													'INVENTORY_PAGE.DESCRIPTION'
														| translate
												"
											>
											</label>
											<textarea
												fullWidth
												id="description"
												type="text"
												formControlName="description"
												nbInput
												[placeholder]="
													'INVENTORY_PAGE.DESCRIPTION'
														| translate
												"
											></textarea>
										</div>
									</div>
								</div>
								<div class="row">
									<div class="col-md-6 col-sm-12">
										<div class="form-group">
											<ga-tags-color-input
												class="mb-4"
												[selectedTags]="
													form.get('tags').value
												"
												(selectedTagsEvent)="
													selectedTagsEvent($event)
												"
												[isOrgLevel]="true"
											></ga-tags-color-input>
										</div>
									</div>
									<div class="col-md-6 col-sm-12 mt-4 pt-1">
										<div class="form-group">
											<nb-checkbox
												status="basic"
												formControlName="active"
											>
												{{
													'INVENTORY_PAGE.ACTIVE'
														| translate
												}}
											</nb-checkbox>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="col-4">
								<button
									(click)="stepClick()"
									[disabled]="form.invalid"
									nbStepperNext
									nbButton
									class="green"
									status="basic"
									outline
								>
									{{ 'BUTTONS.NEXT' | translate }}
								</button>
							</div>
						</div>
					</div>
				</nb-step>
				<nb-step [label]="'INVENTORY_PAGE.LOCATION' | translate">
					<div
						class="d-flex flex-column justify-content-between h-100"
					>
						<div class="row">
							<div class="col-md-4 col-sm-5">
								<ga-location-form
									#locationFormDirective
									[form]="locationForm"
									[showAutocompleteSearch]="true"
									(mapCoordinatesEmitter)="
										onCoordinatesChanges($event)
									"
								></ga-location-form>
							</div>
							<div class="col-md-8 col-sm-7">
								<ga-leaflet-map
									#leafletTemplate
									(mapClicked)="onMapClicked($event)"
								></ga-leaflet-map>
							</div>
						</div>
						<div class="row">
							<div class="col-4">
								<button
									nbButton
									class="gray mr-2"
									status="basic"
									outline
									nbStepperPrevious
								>
									{{ 'BUTTONS.PREVIOUS' | translate }}
								</button>
								<button
									nbButton
									class="green mr-2"
									status="basic"
									outline
									nbStepperNext
								>
									{{ 'BUTTONS.NEXT' | translate }}
								</button>
							</div>
						</div>
					</div>
				</nb-step>
				<nb-step [label]="'INVENTORY_PAGE.WAREHOUSES' | translate">
					<div
						class="d-flex flex-column justify-content-between h-100"
					>
						<div class="row">
							<div class="col-md-4 col-sm-6">
								<div class="form-group">
									<label
										class="label"
										[innerHtml]="
											'INVENTORY_PAGE.WAREHOUSES'
												| translate
										"
									>
									</label>
									<nb-select
										multiple
										[selected]="selectedWarehouses"
										(selectedChange)="
											onWarehouseSelect($event)
										"
										fullWidth
										[placeholder]="
											'INVENTORY_PAGE.WAREHOUSES'
												| translate
										"
									>
										<nb-option
											*ngFor="let warehouse of warehouses"
											[value]="warehouse.id"
										>
											<img
												[src]="warehouse?.logo?.url"
												alt="warehouse image"
												height="40"
												width="40"
												style="margin-right: 10px"
											/>{{ warehouse.name }}
										</nb-option>
									</nb-select>
								</div>
							</div>
						</div>
						<div class="row mt-5">
							<div class="col-4">
								<button
									status="basic"
									class="mr-2 gray"
									nbButton
									outline
									nbStepperPrevious
								>
									{{ 'BUTTONS.PREVIOUS' | translate }}
								</button>
								<button
									status="success"
									class="mr-2"
									nbButton
									(click)="onSaveRequest()"
								>
									{{ 'BUTTONS.SAVE' | translate }}
								</button>
							</div>
						</div>
					</div>
				</nb-step>
			</nb-stepper>
		</form>
	</nb-card-body>
</nb-card>

<ng-template #storeImage>
	<div
		class="product-store-photo"
		(mouseenter)="hoverState = true"
		(mouseleave)="hoverState = false"
		(click)="onImageSelect()"
	>
		<svg
			xmlns="http://www.w3.org/2000/svg"
			xmlns:xlink="http://www.w3.org/1999/xlink"
			width="68"
			height="68"
			viewBox="0 0 68 68"
			[ngStyle]="{ opacity: hoverState ? '1' : '0.3' }"
			*ngIf="image && image.url"
		>
			<defs>
				<path
					id="a"
					d="M28.667 31.333a2 2 0 1 0-.002-4.001 2 2 0 0 0 .002 4.001m13.333 12H26.748l9.34-7.793c.328-.279.923-.277 1.244-.001l6.001 5.12V42c0 .736-.597 1.333-1.333 1.333M26 24.667h16c.736 0 1.333.597 1.333 1.333v11.152l-4.27-3.643c-1.32-1.122-3.386-1.122-4.694-.008l-9.702 8.096V26c0-.736.597-1.333 1.333-1.333M42 22H26c-2.205 0-4 1.795-4 4v16c0 2.205 1.795 4 4 4h16c2.205 0 4-1.795 4-4V26c0-2.205-1.795-4-4-4"
				/>
			</defs>
			<g fill="none" fill-rule="evenodd">
				<circle cx="34" cy="34" r="34" fill="#0091FF" opacity=".3" />
				<circle cx="34" cy="34" r="26" fill="#0091FF" opacity=".9" />
				<use fill="#FFF" fill-rule="nonzero" xlink:href="#a" />
			</g>
		</svg>
		<div
			class="image-overlay"
			[ngStyle]="{ opacity: hoverState ? '0.2' : '0' }"
		></div>
		<ga-no-image
			*ngIf="!image || !image.url"
			[fontSize]="14"
			[placeholder]="'NO_IMAGE.ADD_DROP'"
			class="store-image"
		></ga-no-image>
		<img
			class="store-image"
			*ngIf="image && image.url"
			[src]="image.url"
			alt="Product Item Photo"
		/>
	</div>
</ng-template>
