<nb-card
	[nbSpinner]="loading"
	nbSpinnerStatus="primary"
	nbSpinnerSize="large"
>
	<nb-card-header class="d-flex flex-column pb-0">
		<div class="card-header-title">
			<h4>
				<ngx-back-navigation></ngx-back-navigation>
				<ngx-header-title>
					{{ 'INVENTORY_PAGE.MERCHANTS' | translate }}
				</ngx-header-title>
			</h4>
		</div>
		<div class="gauzy-button-container">
			<ngx-gauzy-button-action
				[buttonTemplate]="actionButtons"
				[componentName]="viewComponentName"
				[buttonTemplateVisible]="visibleButton"
				[isDisable]="disableButton"
			></ngx-gauzy-button-action>
		</div>
	</nb-card-header>
	<nb-card-body>
		<ng-template ngxPermissionsOnly="ORG_INVENTORY_VIEW">
			<ng-template
				[ngIf]="dataLayoutStyle === componentLayoutStyleEnum.TABLE"
				[ngIfElse]="gridLayout"
			>
				<div class="table-scroll-container">
					<angular2-smart-table
						style="cursor: pointer"
						[settings]="settingsSmartTable"
						(userRowSelect)="selectStore($event)"
						[source]="smartTableSource"
					></angular2-smart-table>
				</div>
				<div class="pagination-container">
					<ng-container *ngIf="smartTableSource">
						<ngx-pagination
							[source]="smartTableSource"
						></ngx-pagination>
					</ng-container>
				</div>
			</ng-template>
			<ng-template #gridLayout>
				<ga-card-grid
					[totalItems]="pagination?.totalItems"
					[settings]="settingsSmartTable"
					[source]="merchants"
					(onSelectedItem)="selectStore($event)"
					(scroll)="onScroll()"
				></ga-card-grid>
			</ng-template>
		</ng-template>
	</nb-card-body>
</nb-card>
<ng-template #visibleButton>
	<ng-template ngxPermissionsOnly="ORG_INVENTORY_PRODUCT_EDIT">
		<button
			(click)="onAddStoreClick()"
			nbButton
			status="success"
			class="action"
			size="small"
		>
			<nb-icon icon="plus-outline"></nb-icon>
			{{ 'BUTTONS.ADD' | translate }}
		</button>
	</ng-template>
</ng-template>
<ng-template
	#actionButtons
	let-buttonSize="buttonSize"
	let-selectedItem="selectedItem"
>
	<ng-container *ngxPermissionsOnly="['ORG_INVENTORY_VIEW', 'ORG_INVENTORY_PRODUCT_EDIT']">
		<div class="btn-group actions">
			<ng-template ngxPermissionsOnly="ORG_INVENTORY_VIEW">
				<button
					nbButton
					status="basic"
					class="action secondary"
					size="small"
					[disabled]="!selectedItem && disableButton"
					underConstruction
				>
					<nb-icon icon="eye-outline"></nb-icon>
					{{ 'BUTTONS.VIEW' | translate }}
				</button>
			</ng-template>
			<ng-template ngxPermissionsOnly="ORG_INVENTORY_PRODUCT_EDIT">
				<button
					(click)="onEditStore(selectedItem)"
					nbButton
					status="basic"
					class="action primary"
					size="small"
					[disabled]="!selectedItem && disableButton"
				>
					<nb-icon icon="edit-outline"></nb-icon>
					{{ 'BUTTONS.EDIT' | translate }}
				</button>
				<button
					(click)="onDelete(selectedItem)"
					nbButton
					status="basic"
					class="action"
					[disabled]="!selectedItem && disableButton"
					size="small"
					[nbTooltip]="'BUTTONS.DELETE' | translate"
				>
					<nb-icon status="danger" icon="trash-2-outline"></nb-icon>
				</button>
			</ng-template>
		</div>
	</ng-container>
</ng-template>
