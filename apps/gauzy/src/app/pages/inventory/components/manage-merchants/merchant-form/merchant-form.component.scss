@use 'gauzy/_gauzy-dialogs' as *;

.store-image {
  width: 100%;
  height: 300px;
}

h4 {
  font-size: 24px;
  font-weight: 600;
  line-height: 30px;
  letter-spacing: 0em;
  text-align: left;
}

.text-danger {
  font-size: 14px;
  margin-top: 5px;
}

.top-nav {
  display: flex;
  align-items: flex-start;
}

.image-container {
  display: flex;
  justify-content: center;
  align-items: flex-start;

  img {
    object-fit: cover;
  }
}

.product-store-photo,
.warehouse-photo {
  width: 90%;
  overflow: hidden;
  position: relative;
  height: 300px;
  border-radius: 13px;


  div {
    pointer-events: none;
    background: black;
    position: absolute;
    height: 100%;
    width: 100%;
  }

  input {
    width: 100%;
    height: 100%;
    opacity: 0;
    position: absolute;
    z-index: 3;
    cursor: pointer;
  }

  svg {
    z-index: 2;
    transition: opacity 0.2s ease-in;
    opacity: 0.3;
    position: absolute;
    top: calc(50% - 68px / 2);
    left: calc(50% - 68px / 2);

    g circle {
      fill: var(--text-primary-color)
    }
  }
}

:host {
  nb-card {
    background-color: var(--gauzy-card-2);
    margin: 0;
  }

  nb-stepper ::ng-deep .step-content {
    padding: 1rem;
    height: calc(100vh - 17.25rem);
  }

  @include dialog(var(--gauzy-card-2), var(--gauzy-card-1));
}
