<nb-card [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
	<nb-card-header class="d-flex flex-column pb-0">
		<div class="card-header-title">
			<h4>
				<ngx-header-title>
					{{ 'APPROVAL_REQUEST_PAGE.HEADER' | translate }}
				</ngx-header-title>
			</h4>
			<div>
				<ng-template ngxPermissionsOnly="APPROVALS_POLICY_VIEW">
					<button
						class="float-right"
						nbButton
						outline
						status="primary"
						size="small"
						class="action"
						(click)="manageApprovalPolicy()"
					>
						{{
							'APPROVAL_REQUEST_PAGE.APPROVAL_POLICY' | translate
						}}
					</button>
				</ng-template>
			</div>
		</div>
		<div class="gauzy-button-container">
			<ngx-gauzy-button-action
				[buttonTemplateVisible]="visibleButton"
				[buttonTemplate]="actionButtons"
				[isDisable]="disableButton"
				[componentName]="viewComponentName"
			></ngx-gauzy-button-action>
		</div>
	</nb-card-header>
	<nb-card-body>
		<ng-template
			[ngIf]="dataLayoutStyle === componentLayoutStyleEnum.TABLE"
			[ngIfElse]="gridLayout"
		>
			<div class="table-scroll-container">
				<angular2-smart-table
					(userRowSelect)="selectRequestApproval($event)"
					[settings]="settingsSmartTable"
					[source]="smartTableSource"
					style="cursor: pointer"
				></angular2-smart-table>
			</div>
			<div class="pagination-container">
				<ng-container *ngIf="smartTableSource">
					<ngx-pagination
						[source]="smartTableSource"
					></ngx-pagination>
				</ng-container>
			</div>
		</ng-template>
		<ng-template #gridLayout>
			<ga-card-grid
				[totalItems]="pagination?.totalItems"
				(scroll)="onScroll()"
				(onSelectedItem)="selectRequestApproval($event)"
				[settings]="settingsSmartTable"
				[source]="requestApprovalData"
			></ga-card-grid>
		</ng-template>
	</nb-card-body>
</nb-card>
<ng-template #actionButtons let-selectedItem="selectedItem">
	<div class="btn-group actions">
		<ngx-request-approval-action
			*ngIf="selectedRequestApproval"
			[rowData]="selectedRequestApproval"
			(updateResult)="onUpdateResult($event)"
		>
		</ngx-request-approval-action>
		<button
			nbButton
			status="basic"
			class="action secondary"
			[disabled]="!selectedItem && disableButton"
			size="small"
			underConstruction
		>
			<nb-icon icon="eye-outline"></nb-icon>
			{{ 'BUTTONS.VIEW' | translate }}
		</button>
		<ng-template ngxPermissionsOnly="REQUEST_APPROVAL_EDIT">
			<button
				(click)="save(false, selectedItem)"
				nbButton
				status="basic"
				class="action primary"
				[disabled]="!selectedItem && disableButton"
				size="small"
			>
				<nb-icon icon="edit-outline"></nb-icon>
				{{ 'BUTTONS.EDIT' | translate }}
			</button>
		</ng-template>
		<button
			(click)="delete(selectedItem)"
			nbButton
			status="basic"
			class="action"
			[disabled]="!selectedItem && disableButton"
			size="small"
			[nbTooltip]="'BUTTONS.DELETE' | translate"
		>
			<nb-icon status="danger" icon="trash-2-outline"> </nb-icon>
		</button>
	</div>
</ng-template>
<ng-template #visibleButton>
	<ng-template ngxPermissionsOnly="REQUEST_APPROVAL_EDIT">
		<button (click)="save(true)" nbButton status="success" size="small">
			<nb-icon class="mr-1" icon="plus-outline"></nb-icon
			>{{ 'BUTTONS.ADD' | translate }}
		</button>
	</ng-template>
</ng-template>
