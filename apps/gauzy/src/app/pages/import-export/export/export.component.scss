@use 'gauzy/_gauzy-cards' as *;

:host {
  .header-title-with-back {
    display: flex;
    align-items: center;
    @include nb-rtl(gap, 24px);
    margin-bottom: 50px;
  }

  ::ng-deep ngx-back-navigation button {
    @include nb-rtl(margin-right, 0px !important);
  }

  nb-card,
  nb-card-body {
    background-color: var(--gauzy-card-2);

    &.card-body {
      height: calc(100vh - 21rem);
      overflow: auto;
      padding: 1rem;
    }
  }

  nb-card-body {
    border-radius: 0 0 var(--border-radius) var(--border-radius);
  }

  nb-card {
    margin-bottom: 0;

    nb-card {
      margin-bottom: 0.625rem;
    }
  }

  nb-checkbox {
    ::ng-deep {

      span.checked+span.text {
        color: var(--text-primary-color);
      }

      span.text {
        font-size: 11px;
        font-weight: 400;
        line-height: 13px;
        letter-spacing: 0em;
        text-align: left;
        color: var(--gauzy-text-color-2);
      }
    }
  }
}

:host {
  ::ng-deep {
      .download {
          padding: 0.5rem;
          background-color: var(--gauzy-card-2);
          border-radius: var(--button-rectangle-border-radius);
          @include nb-ltr(float, right);
          @include nb-rtl(float, left);
          .action {
            box-shadow: var(--gauzy-shadow)(0, 0, 0, 0.15);
          }
      }
  }
}

.info-text {
  display: flex;
  align-items: center;
  gap: 50px;
}

.title {
  font-size: 24px;
  font-weight: 600;
  line-height: 30px;
  letter-spacing: 0em;
  text-align: left;
  color: var(--gauzy-text-color-1);
  white-space: nowrap;
}

.message {
  font-size: 14px;
  font-weight: 400;
  line-height: 17px;
  letter-spacing: 0em;
  text-align: left;
  color: var(--gauzy-text-color-2);
}
