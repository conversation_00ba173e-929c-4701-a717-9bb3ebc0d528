<nb-card [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
	<nb-card-header class="card-custom-header">
		<div class="card-header-title">
			<h4>
				<ngx-header-title [allowEmployee]="false">
					{{ 'PIPELINES_PAGE.HEADER' | translate }}
				</ngx-header-title>
			</h4>
		</div>
	</nb-card-header>
	<nb-card-body class="p-0">
		<div class="gauzy-button-container">
			<ngx-gauzy-button-action
				[buttonTemplateVisible]="visibleButton"
				[isDisable]="disableButton"
				[buttonTemplate]="actionButtons"
				[componentName]="viewComponentName"
			></ngx-gauzy-button-action>
		</div>
		<nb-tabset class="mt-4" (changeTab)="onChangeTab($event)">
			<nb-tab [tabTitle]="'PIPELINES_PAGE.BROWSE' | translate" [tabId]="pipelineTabsEnum.ACTIONS">
				<ng-container *ngIf="(nbTab$ | async) === pipelineTabsEnum.ACTIONS">
					<ng-template
						[ngTemplateOutlet]="
							dataLayoutStyle === componentLayoutStyleEnum.TABLE ? tableLayout : gridLayout
						"
					></ng-template>
				</ng-container>
			</nb-tab>
			<nb-tab
				[tabTitle]="'PIPELINES_PAGE.SEARCH' | translate"
				[tabId]="pipelineTabsEnum.SEARCH"
				class="search-tab"
			>
				<nb-accordion class="mb-3">
					<nb-accordion-item [collapsed]="false">
						<nb-accordion-item-header>
							{{ 'PIPELINES_PAGE.SEARCH_PIPELINE' | translate }}
						</nb-accordion-item-header>
						<nb-accordion-item-body>
							<form [formGroup]="searchForm" (ngSubmit)="search()">
								<div class="row">
									<div class="col-xl-4 col-lg-12">
										<label for="name">
											{{ 'PIPELINES_PAGE.NAME' | translate }}
										</label>
										<div class="w-100">
											<input
												[placeholder]="'PIPELINES_PAGE.NAME' | translate"
												formControlName="name"
												type="text"
												fullWidth
												nbInput
												id="name"
											/>
										</div>
									</div>
									<div class="col-xl-2 col-lg-4 col-12">
										<label for="stages">
											{{ 'PIPELINES_PAGE.STAGE' | translate }}
										</label>
										<div class="w-100">
											<input
												[placeholder]="'PIPELINES_PAGE.SEARCH_STAGE' | translate"
												type="text"
												nbInput
												fullWidth
												id="stages"
												formControlName="stages"
											/>
										</div>
									</div>
									<div class="col-xl-2 col-lg-4 col-12">
										<label for="status">
											{{ 'PIPELINES_PAGE.STATUS' | translate }}
										</label>
										<div class="w-100">
											<nb-select
												fullWidth
												[placeholder]="'PIPELINES_PAGE.ALL_STATUS' | translate"
												id="status"
												formControlName="status"
											>
												<nb-option [value]="true">
													{{ 'PIPELINES_PAGE.ACTIVE' | translate }}
												</nb-option>
												<nb-option [value]="false">
													{{ 'PIPELINES_PAGE.INACTIVE' | translate }}
												</nb-option>
											</nb-select>
										</div>
									</div>
								</div>
								<div class="row">
									<div class="col-12 mt-3">
										<button
											type="submit"
											nbButton
											size="small"
											status="success"
											outline
											[disabled]="searchForm.invalid"
										>
											{{ 'PIPELINES_PAGE.SEARCH' | translate }}
										</button>
										<button
											type="reset"
											nbButton
											size="small"
											status="basic"
											outline
											[disabled]="searchForm.invalid"
											(click)="reset()"
										>
											{{ 'PIPELINES_PAGE.RESET' | translate }}
										</button>
									</div>
								</div>
							</form>
						</nb-accordion-item-body>
					</nb-accordion-item>
				</nb-accordion>
				<ng-container *ngIf="(nbTab$ | async) === pipelineTabsEnum.SEARCH">
					<ng-template
						[ngTemplateOutlet]="
							dataLayoutStyle === componentLayoutStyleEnum.TABLE ? tableLayout : gridLayout
						"
					></ng-template>
				</ng-container>
			</nb-tab>
		</nb-tabset>
	</nb-card-body>
</nb-card>

<ng-template #actionButtons let-buttonSize="buttonSize" let-selectedItem="selectedItem">
	<div class="btn-group actions">
		<ng-template ngxPermissionsOnly="VIEW_SALES_PIPELINES">
			<button
				[disabled]="!selectedItem && disableButton"
				(click)="viewDeals(selectedItem)"
				status="basic"
				class="action secondary"
				size="small"
				nbButton
			>
				<nb-icon icon="eye-outline" pack="eva"></nb-icon>
				<span> {{ 'PIPELINES_PAGE.VIEW_DEALS' | translate }} </span>
			</button>
		</ng-template>
		<ng-template ngxPermissionsOnly="EDIT_SALES_PIPELINES">
			<button
				[disabled]="!selectedItem && disableButton"
				size="small"
				(click)="editPipeline(selectedItem)"
				style="cursor: pointer"
				status="basic"
				class="action primary"
				nbButton
			>
				<nb-icon icon="edit-outline" pack="eva"></nb-icon>
				<span>{{ 'BUTTONS.EDIT' | translate }}</span>
			</button>
			<button
				[disabled]="!selectedItem && disableButton"
				(click)="deletePipeline(selectedItem)"
				style="cursor: pointer"
				status="basic"
				class="action"
				size="small"
				[nbTooltip]="'BUTTONS.DELETE' | translate"
				nbButton
			>
				<nb-icon status="danger" icon="trash-2-outline" pack="eva"></nb-icon>
			</button>
		</ng-template>
	</div>
</ng-template>

<ng-template #visibleButton>
	<ng-template ngxPermissionsOnly="EDIT_SALES_PIPELINES">
		<button nbButton status="success" size="small" (click)="createPipeline()">
			<nb-icon icon="plus-outline"> </nb-icon>
			{{ 'BUTTONS.ADD' | translate }}
		</button>
	</ng-template>
</ng-template>

<ng-template #tableLayout>
	<div class="table-scroll-container">
		<angular2-smart-table
			style="cursor: pointer"
			(userRowSelect)="selectPipeline($event)"
			[settings]="smartTableSettings"
			[source]="smartTableSource"
			#pipelineTable
		></angular2-smart-table>
	</div>
	<div class="pagination-container">
		<ng-container *ngIf="smartTableSource">
			<ngx-pagination [source]="smartTableSource"></ngx-pagination>
		</ng-container>
	</div>
</ng-template>
<ng-template #gridLayout>
	<div class="grid">
		<ga-card-grid
			[totalItems]="pagination?.totalItems"
			[settings]="smartTableSettings"
			[source]="pipelines"
			(onSelectedItem)="selectPipeline($event)"
			(scroll)="onScroll()"
		></ga-card-grid>
	</div>
</ng-template>
