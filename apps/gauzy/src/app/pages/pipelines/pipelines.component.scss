@use '@shared/_pg-card' as *;

:host {
  height: 100%;
  nb-tab.content-active {
    padding: 1rem;
    height: calc(100% - 3rem);
    border-radius: 0 0 var(--border-radius) var(--border-radius);
    overflow: unset;
    display: flex;
    flex-direction: column;
    @include nb-ltr(padding, 1rem 0.5rem 1rem 18px);
    @include nb-rtl(padding, 1rem 18px 1rem 0.5rem);
    nb-accordion {
      @include nb-ltr(margin-right, 0.625rem);
      @include nb-rtl(margin-left, 0.625rem);
    }
  }
  nb-tabset {
    height: calc(100% - 1.5rem);
  }
  nb-card,
  nb-tab.content-active {
    background-color: var(--gauzy-card-2);
  }
  nb-card-body {
    overflow: unset;
    background-color: unset;
  }
  nb-card {
    height: 100%;
  }
}
:host .gauzy-button-container {
  position: absolute;
  @include nb-ltr(right, 18px);
  @include nb-rtl(left, 18px);
  top: 0;
}
nb-accordion-item-header ::ng-deep nb-icon {
  border: 1px solid nb-theme(border-basic-color-4);
  border-radius: nb-theme(input-rectangle-border-radius);
  width: 1.75rem;
  height: 1.75rem;
}

.grid {
  overflow: auto;
  height: 100%;
}
