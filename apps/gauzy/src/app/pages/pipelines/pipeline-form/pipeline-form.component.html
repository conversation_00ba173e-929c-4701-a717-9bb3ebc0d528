<form [formGroup]="form" (ngSubmit)="persist()">
	<nb-card style="width: 750px">
		<nb-card-header>
			<div class="cancel">
				<span><i class="fas fa-times" (click)="closeDialog()"></i></span>
			</div>
			<h4 class="title">
				{{
					(pipeline?.id ? 'PIPELINES_PAGE.HEADER_FORM_EDIT' : 'PIPELINES_PAGE.HEADER_FORM_CREATE') | translate
				}}
			</h4>
		</nb-card-header>
		<nb-card-body>
			<div class="row">
				<div class="col">
					<nb-form-field>
						<label for="name" class="label">{{ 'FORM.PLACEHOLDERS.NAME' | translate }}</label>
						<input
							type="text"
							nbInput
							formControlName="name"
							fullWidth
							[placeholder]="'FORM.PLACEHOLDERS.NAME' | translate"
						/>
					</nb-form-field>
					<br />
					<nb-form-field>
						<label for="description" class="label">
							{{ 'FORM.PLACEHOLDERS.DESCRIPTION' | translate }}
						</label>
						<textarea
							nbInput
							formControlName="description"
							fullWidth
							fullHeight
							[placeholder]="'FORM.PLACEHOLDERS.DESCRIPTION' | translate"
						></textarea>
					</nb-form-field>
					<br />
					<nb-checkbox (checkedChange)="setIsActive()" [checked]="isActive" formControlName="isActive">
						{{ 'PIPELINES_PAGE.ACTIVE' | translate }}
					</nb-checkbox>
				</div>
				<div class="col">
					<ga-stage-form
						formArrayName="stages"
						[pipelineId]="pipeline?.id"
						[values]="pipeline?.stages"
					></ga-stage-form>
				</div>
			</div>
		</nb-card-body>
		<nb-card-footer class="text-left">
			<button nbButton type="button" (click)="closeDialog()" status="basic">
				<span [innerHTML]="'BUTTONS.CANCEL' | translate"></span>
			</button>
			<ng-template [ngIf]="pipeline?.id" [ngIfElse]="createButtonTemplate">
				<button
					type="submit"
					nbButton
					status="info"
					[disabled]="form.invalid || form.disabled"
					class="mr-3 ml-3"
				>
					<span [innerHTML]="'BUTTONS.UPDATE' | translate"></span>
				</button>
			</ng-template>
			<ng-template #createButtonTemplate>
				<button
					type="submit"
					nbButton
					status="primary"
					[disabled]="form.invalid || form.disabled"
					class="mr-3 ml-3"
				>
					<span [innerHTML]="'BUTTONS.CREATE' | translate"></span>
				</button>
			</ng-template>
		</nb-card-footer>
	</nb-card>
</form>
