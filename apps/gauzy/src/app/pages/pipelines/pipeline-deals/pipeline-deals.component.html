<nb-card [nbSpinner]="loading" class="card-scroll" nbSpinnerSize="large" nbSpinnerStatus="primary">
	<nb-card-header class="card-header-title" *ngIf="pipeline$ | async as pipeline">
		<div class="card-header-title">
			<ngx-back-navigation></ngx-back-navigation>
			<h4>
				<span> {{ 'PIPELINE_DEALS_PAGE.HEADER' | translate }} </span> | <span> {{ pipeline?.name }} </span>
			</h4>
		</div>
		<div class="gauzy-button-container">
			<ngx-gauzy-button-action
				[buttonTemplateVisible]="visibleButton"
				[buttonTemplate]="actionButtons"
				[componentName]="viewComponentName"
				[isDisable]="disableButton"
			></ngx-gauzy-button-action>
		</div>
	</nb-card-header>
	<nb-card-body>
		<div class="row btn-container mb-3">
			<div class="col">
				<nb-select
					(selectedChange)="filterDealsByStage($event)"
					[formControl]="stageFormControl"
					[placeholder]="'PIPELINE_DEALS_PAGE.FILTER_BY_STAGE' | translate"
				>
					<nb-option [value]="''" style="white-space: nowrap">
						{{ 'PIPELINE_DEALS_PAGE.ALL_STAGES' | translate }}
					</nb-option>
					<nb-option *ngFor="let stage of pipeline?.stages" [value]="stage.id" style="white-space: nowrap">
						{{ stage.name }}
					</nb-option>
				</nb-select>
			</div>
		</div>
		<ng-container *ngIf="deals$ | async as deals">
			<ng-template
				[ngTemplateOutlet]="dataLayoutStyle === componentLayoutStyleEnum.TABLE ? tableLayout : gridLayout"
			></ng-template>
		</ng-container>
	</nb-card-body>
</nb-card>

<ng-template #tableLayout>
	<div class="table-scroll-container">
		<angular2-smart-table
			#pipelineDealsTable
			(userRowSelect)="selectPipelineDeals($event)"
			[settings]="smartTableSettings"
			[source]="smartTableSource"
			style="cursor: pointer"
		></angular2-smart-table>
	</div>
	<div class="pagination-container">
		<ng-container *ngIf="smartTableSource">
			<ngx-pagination [source]="smartTableSource"></ngx-pagination>
		</ng-container>
	</div>
</ng-template>

<ng-template #gridLayout>
	<div class="grid">
		<ga-card-grid
			(onSelectedItem)="selectPipelineDeals($event)"
			(scroll)="onScroll()"
			[settings]="smartTableSettings"
			[source]="deals"
			[totalItems]="pagination?.totalItems"
		></ga-card-grid>
	</div>
</ng-template>

<ng-template #actionButtons>
	<div class="btn-group actions">
		<ng-template ngxPermissionsOnly="EDIT_SALES_PIPELINES">
			<button
				[routerLink]="['./', selectedDeal?.id, 'edit']"
				[disabled]="disableButton"
				class="action primary"
				nbButton
				size="small"
				status="basic"
				style="cursor: pointer"
			>
				<nb-icon icon="edit-outline" pack="eva"></nb-icon>
				<span>{{ 'BUTTONS.EDIT' | translate }}</span>
			</button>
			<button
				(click)="deleteDeal()"
				[disabled]="disableButton"
				[nbTooltip]="'BUTTONS.DELETE' | translate"
				class="action"
				nbButton
				size="small"
				status="basic"
				style="cursor: pointer"
			>
				<nb-icon icon="trash-2-outline" pack="eva" status="danger"></nb-icon>
			</button>
		</ng-template>
	</div>
</ng-template>

<ng-template #visibleButton>
	<ng-template ngxPermissionsOnly="EDIT_SALES_PIPELINES">
		<button [routerLink]="['./create']" nbButton size="small" status="success">
			<nb-icon icon="plus-outline"></nb-icon>
			{{ 'BUTTONS.ADD' | translate }}
		</button>
	</ng-template>
</ng-template>
