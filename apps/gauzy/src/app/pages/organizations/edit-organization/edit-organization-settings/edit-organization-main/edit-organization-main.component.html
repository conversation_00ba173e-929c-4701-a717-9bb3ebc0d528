<div class="content main">
	<div class="organization-container">
		<div class="organization-photo ml-4">
			<ngx-image-uploader
				[folder]="'organization_avatars'"
				(changeHoverState)="hoverState = $event"
				(uploadedImageAsset)="updateImageAsset($event)"
				(uploadImageAssetError)="handleImageUploadError($event)"
			></ngx-image-uploader>
			<div class="image-overlay" [ngStyle]="{ opacity: hoverState ? '0.2' : '0' }"></div>
			<img
				[src]="form.get('imageUrl').value"
				alt="Organization Photo"
				(mouseenter)="hoverState = true"
				(mouseleave)="hoverState = false"
			/>
		</div>
		<h6 class="employees-count-text mt-2">
			{{ employeesCount }}
			{{ 'ORGANIZATIONS_PAGE.EMPLOYEES' | translate }}
		</h6>
	</div>
	<form class="main-form" [formGroup]="form">
		<div class="fields">
			<div class="row">
				<div class="col">
					<div class="form-group">
						<label class="label" for="nameInput">
							{{ 'FORM.LABELS.NAME' | translate }}
						</label>
						<input
							fullWidth
							id="nameInput"
							type="text"
							nbInput
							formControlName="name"
							[placeholder]=" 'FORM.PLACEHOLDERS.NAME' | translate"
						/>
					</div>
				</div>
				<div class="col">
					<span>
					<ga-currency
						formControlName="currency"
						[formControl]="form.get('currency')"
						(optionChange)="currencyChanged($event)"
					></ga-currency>
					</span>
				</div>
			</div>
			<div class="row">
				<div class="col-6">
					<div class="form-group">
						<label class="label" for="officialNameInput">
							{{ 'FORM.LABELS.OFFICIAL_NAME' | translate }}
						</label>
						<input
							fullWidth
							id="officialNameInput"
							type="text"
							nbInput
							formControlName="officialName"
							[placeholder]="'FORM.PLACEHOLDERS.OFFICIAL_NAME' | translate"
						/>
					</div>
				</div>
				<div class="col-6">
					<div class="form-group tax-form-group">
						<label class="label" for="taxIdInput">
							{{ 'FORM.LABELS.TAX_ID' | translate }}
						</label>
						<input
							fullWidth
							id="taxIdInput"
							type="text"
							nbInput
							formControlName="taxId"
							[placeholder]="'FORM.PLACEHOLDERS.TAX_ID' | translate"
						/>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-6">
					<div class="form-group">
						<label class="label" for="profileLinkInput">
							{{ 'FORM.LABELS.PROFILE_LINK' | translate }}
						</label>
						<input
							fullWidth
							id="profileLinkInput"
							type="text"
							nbInput
							formControlName="profile_link"
							[placeholder]="'FORM.PLACEHOLDERS.PROFILE_LINK' | translate"
						/>
					</div>
				</div>
				<div class="col-6">
					<div class="form-group">
						<ga-tags-color-input
							[selectedTags]="form.get('tags').value"
							(selectedTagsEvent)="selectedTagsEvent($event)"
							[isOrgLevel]="true"
						></ga-tags-color-input>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-6">
					<div class="form-group registration-form-group">
						<label class="label" for="registrationDate">
							{{ 'FORM.LABELS.REGISTRATION_DATE' | translate }}
						</label>
						<input
							nbInput
							fullWidth
							id="registrationDate"
							[placeholder]="'FORM.PLACEHOLDERS.REGISTRATION_DATE' | translate"
							[nbDatepicker]="registrationDate"
							formControlName="registrationDate"
						/>
						<nb-datepicker #registrationDate></nb-datepicker>
					</div>
				</div>
				<div class="col-6">
					<div class="form-group">
						<label class="label" for="website">
							{{ 'FORM.LABELS.WEBSITE' | translate }}
						</label>
						<input
							nbInput
							fullWidth
							id="website"
							[placeholder]="'FORM.PLACEHOLDERS.WEBSITE' | translate"
							formControlName="website"
						/>
					</div>
				</div>
			</div>
		</div>
		<div class="actions">
			<button
				[disabled]="this.form.invalid"
				(click)="updateOrganizationSettings()"
				nbButton
				status="success"
			>
				{{ 'BUTTONS.SAVE' | translate }}
			</button>
		</div>
	</form>
</div>
