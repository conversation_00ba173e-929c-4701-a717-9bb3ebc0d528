import { Component, OnInit, Input, AfterViewInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { IOrganization } from '@gauzy/contracts';
import { NbRouteTab } from '@nebular/theme';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { TranslateService } from '@ngx-translate/core';
import { tap } from 'rxjs/operators';
import { TranslationBaseComponent } from '@gauzy/ui-core/i18n';

@UntilDestroy({ checkProperties: true })
@Component({
    selector: 'ngx-edit-organization-settings',
    templateUrl: './edit-organization-settings.component.html',
    styleUrls: ['./edit-organization-settings.component.scss'],
    standalone: false
})
export class EditOrganizationSettingsComponent extends TranslationBaseComponent implements AfterViewInit, OnInit {
	@Input() organization: IOrganization;
	tabs: NbRouteTab[] = [];

	constructor(private readonly route: ActivatedRoute, public readonly translateService: TranslateService) {
		super(translateService);
	}

	ngOnInit() {
		this.route.params
			.pipe(
				tap(() => this.loadTabs()),
				untilDestroyed(this)
			)
			.subscribe();
	}

	ngAfterViewInit() {
		this._applyTranslationOnTabs();
	}

	getRoute(tab: string) {
		return `/pages/organizations/edit/${this.route.snapshot.paramMap.get('id')}/${tab}`;
	}

	loadTabs() {
		this.tabs = [
			{
				title: this.getTranslation('ORGANIZATIONS_PAGE.MAIN'),
				icon: 'person-outline',
				responsive: true,
				route: this.getRoute('main'),
				queryParamsHandling: 'merge'
			},
			{
				title: this.getTranslation('ORGANIZATIONS_PAGE.LOCATION'),
				icon: 'pin-outline',
				responsive: true,
				route: this.getRoute('location'),
				queryParamsHandling: 'merge'
			},
			{
				title: this.getTranslation('ORGANIZATIONS_PAGE.SETTINGS'),
				icon: 'settings-outline',
				responsive: true,
				route: this.getRoute('settings'),
				queryParamsHandling: 'merge'
			}
		];
	}

	private _applyTranslationOnTabs() {
		this.translateService.onLangChange
			.pipe(
				tap(() => this.loadTabs()),
				untilDestroyed(this)
			)
			.subscribe();
	}
}
