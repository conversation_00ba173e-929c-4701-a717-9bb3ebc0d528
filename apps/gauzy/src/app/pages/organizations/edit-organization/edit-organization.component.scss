@use 'var' as *;
@use 'themes' as *;
@use 'gauzy/_gauzy-overrides' as ga-overrides;

:host .card-scroll {
    background-color: nb-theme(gauzy-card-2);

    & .header {
        padding-left: 20px;
        padding-right: 20px;
        padding-top: 25px;
        border-bottom: none;
    }

    & .header-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .org-image {
        width: 48px;
        height: 48px;
        @include nb-ltr(margin-right, 17px !important);
        @include nb-ltr(margin-left, 0px !important);
        @include nb-rtl(margin-right, 0px !important);
        @include nb-rtl(margin-left, 17px !important);
    }
    .org-name {
        font-size: 14px !important;
        font-style: normal;
        font-weight: 600 !important;
        line-height: 17px;
        letter-spacing: 0em;
        margin-bottom: 3px;
    }
    .org-position {
        font-size: 12px !important;
        font-style: normal;
        font-weight: 400;
        line-height: 15px;
        letter-spacing: 0em;
    }

    & .edit-public-page {
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        padding: 2px 12px;
        box-sizing: border-box;
        border-radius: nb-theme(button-rectangle-border-radius);
        border: 2px solid nb-theme(color-primary-transparent-default);
        font-size: 12px;
        font-style: normal;
        font-weight: 600;
        line-height: 11px;
        letter-spacing: 0em;
        text-align: left;
        color: nb-theme(text-primary-color);
        cursor: pointer;
    }

    & nb-card-body {
        padding: 0;
    }
}

// old styles
.edit-icon {
    margin-left: 30px;
    position: relative;
    width: 36px;

    svg {
        position: absolute;
    }

    nb-icon {
        position: absolute;
    }
}

.org-details {
    .edit-public-page {
        cursor: pointer;
        color: #027ad6;
        padding-top: 3px;
    }
}

.setting-name {
    font-size: 24px;
    font-weight: bold;
}

.body-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 35px;
}

.mutation-card.setting-block {
    background: #eaf3fc;
}

.transparent {
    opacity: 0.7;
}

.settings-body {
    padding: 35px;
}

.sub-header {
    margin-bottom: 20px;
    font-weight: bold;
}

.header-content {
    display: flex;
    .header-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 560px;
        padding-left: 30px;
    }
}

@include respond(sm) {
    .header .header-container {
        flex-direction: column;
        align-items: flex-start;
    }
    .org-image {
        margin-bottom: 10px;
    }
}

:host{
    nb-card{
        height: unset;
    }
    @include ga-overrides.input-appearance(42px, var(--gauzy-card-1));
}
