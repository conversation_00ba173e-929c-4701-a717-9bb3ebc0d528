<div (mouseleave)="showList = false">
    <div class="form-group">
      <label class="label">{{ listType | titlecase }}</label>
      <input
        nbInput
        type="text"
        [placeholder]="'Add ' + listType | titlecase"
        (change)="inputHandler($event)"
        (mouseenter)="showList = true"
        fullWidth
      />
      <nb-list
        *ngIf="list.length > 0 && showList"
        [ngClass]="{
          'department-list-active': showList
        }"
      >
        <nb-list-item
          *ngFor="let item of list; let i = index"
        >
          <div>
            <span>{{ item }}</span>
            <nb-icon
              class="ml-auto mt-1 close"
              (click)="deleteFromList(i)"
              icon="close-outline"
            ></nb-icon>
          </div>
        </nb-list-item>
      </nb-list>
    </div>
  </div>