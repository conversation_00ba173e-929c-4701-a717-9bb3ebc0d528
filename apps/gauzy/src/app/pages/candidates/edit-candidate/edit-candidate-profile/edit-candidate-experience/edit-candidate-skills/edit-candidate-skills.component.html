<nb-card>
	<nb-card-header
		style="display: flex; justify-content: flex-end; align-items: center"
	>
		<div class="btn-group actions mr-1">
			<button
				nbButton
				status="success"
				*ngIf="!showAddCard"
				(click)="add()"
				class="action"
				size="small"
			>
				<nb-icon icon="plus-outline"></nb-icon>
				{{ 'BUTTONS.ADD' | translate }}
			</button>
		</div>
		<ga-layout-selector
      class="ml-3"
			*ngIf="!showAddCard"
			componentName="{{ viewComponentName }}"
		></ga-layout-selector>
		<div *ngIf="showAddCard" class="container">
			<form class="row" [formGroup]="form" *ngIf="form">
				<div class="col-11 pl-0" formArrayName="skills">
					<div *ngFor="let skill of skills?.controls; let i = index">
						<div [formGroupName]="i">
							<input
								formControlName="name"
								class="col-12"
								nbInput
								type="text"
								placeholder="{{
									'FORM.PLACEHOLDERS.ADD_SKILL' | translate
								}}"
								fullWidth
							/>
						</div>
					</div>
				</div>
				<div class="col-1 pr-0">
					<button
						(click)="submitForm()"
						class="w-100"
						nbButton
						status="success"
					>
						{{ 'BUTTONS.SAVE' | translate }}
					</button>
					<button
						(click)="showAddCard = !showAddCard"
						nbButton
						outline
						status="basic"
						class="w-100"
					>
						{{ 'BUTTONS.CANCEL' | translate }}
					</button>
				</div>
			</form>
		</div>
	</nb-card-header>
	<nb-card-body *ngIf="skillList?.length && dataLayoutStyle === 'TABLE'">
		<nb-card class="skill" *ngFor="let skill of skillList; let i = index">
			<nb-card-body>
				<span *ngIf="!showEditDiv[i]"> {{ skill.name }}</span>
				<div class="row m-0" *ngIf="showEditDiv[i]">
					<input
						#editInput
						class="col-10"
						nbInput
						type="text"
						[value]="skill.name"
						fullWidth
					/>
					<div class="col-1">
						<button
							(click)="cancel(i)"
							outline
							nbButton
							status="basic"
						>
							{{ 'BUTTONS.CANCEL' | translate }}
						</button>
					</div>
					<div class="col-1">
						<button
							(click)="editSkill(editInput.value, i)"
							nbButton
							status="success"
						>
							{{ 'BUTTONS.UPDATE' | translate }}
						</button>
					</div>
				</div>
				<nb-actions
					*ngIf="!showEditDiv[i]"
					class="float-right"
					e="medium"
				>
					<nb-action
						(click)="showEditCard(i, skill.id)"
						class="d-inline pr-2"
						icon="edit"
					></nb-action>
					<nb-action
						(click)="removeSkill(skill)"
						class="d-inline pr-0 pl-2"
						icon="close"
					></nb-action>
				</nb-actions>
			</nb-card-body>
		</nb-card>
	</nb-card-body>
	<nb-card-body *ngIf="skillList?.length">
		<ga-card-grid 
			[totalItems]="pagination?.totalItems"
			*ngIf="dataLayoutStyle === 'CARDS_GRID'"
			[settings]="settingsSmartTable"
			[source]="skillList"
			[buttonTemplate]="actionButtons"
		></ga-card-grid>
	</nb-card-body>
	<ng-template
		#actionButtons
		let-buttonSize="buttonSize"
		let-selectedItem="selectedItem"
	>
		<button
			(click)="gridEdit(selectedItem)"
			nbButton
			status="info"
			class="mr-2"
			[disabled]="!selectedItem && disableButton"
			[size]="buttonSize || 'medium'"
		>
			<nb-icon class="mr-1" icon="edit-outline"></nb-icon>
			{{ 'BUTTONS.EDIT' | translate }}
		</button>
		<button
			(click)="removeSkill(selectedItem.id)"
			nbButton
			status="danger"
			class="mr-2"
			[disabled]="!selectedItem && disableButton"
			[size]="buttonSize || 'medium'"
		>
			<nb-icon class="mr-1" icon="archive-outline"> </nb-icon>
			{{ 'BUTTONS.DELETE' | translate }}
		</button>
	</ng-template>
</nb-card>
