<nb-card class="card-scroll">
	<nb-card-header class="card-header-title">
		<div class="card-header-title">
			<ngx-back-navigation></ngx-back-navigation>
			<h4>
				{{ 'CANDIDATES_PAGE.EDIT_CANDIDATE.HEADER' | translate }}
				<ng-template [ngIf]="selectedCandidate?.user?.name">
					<span>{{ selectedCandidate?.user?.name }}</span>
				</ng-template>
				<span *ngIf="selectedCandidate?.user?.username">
					<ng-container *ngIf="selectedCandidate?.user.name; then withBraces; else withQuotes"></ng-container>
					<ng-template #withBraces> ({{ selectedCandidate?.user.username }}) </ng-template>
					<ng-template #withQuotes> '{{ selectedCandidate?.user.username }}' </ng-template>
				</span>
			</h4>
		</div>
		<div class="notification" (click)="interviewInfo()">
			<nb-icon class="bell" icon="bell-outline"></nb-icon>
			<div *ngIf="futureInterviews?.length > 0" class="exist"></div>
		</div>
	</nb-card-header>
	<nb-card-body class="p-3">
		<nb-route-tabset [tabs]="tabs"></nb-route-tabset>
	</nb-card-body>
</nb-card>
