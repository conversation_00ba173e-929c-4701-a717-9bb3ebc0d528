@use 'themes' as *;

:host {
  border-radius: nb-theme(border-radius);
  nb-tab {
    background-color: var(--gauzy-card-2);
  }
  background-color: var(--gauzy-card-2);
  ::ng-deep nb-tabset .tab .tab-link {
    span.tab-text {
      text-transform: lowercase;
      display: block;
      &:first-letter {
        text-transform: uppercase;
      }
    }
  }
}
nb-tab.content-active {
  max-height: calc(100vh - 30rem);
  overflow-y: auto;
  border-radius: nb-theme(border-radius);
}
nb-tab.content-active.custom-tab {
  border-radius: 0 nb-theme(border-radius) nb-theme(border-radius)
    nb-theme(border-radius);
}
