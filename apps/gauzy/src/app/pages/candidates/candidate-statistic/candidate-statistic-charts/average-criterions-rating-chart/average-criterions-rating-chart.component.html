<ng-container *ngIf="candidates.length > 0">
	<div class="select">
		<ga-candidate-select
			[placeholder]="'FORM.PLACEHOLDERS.ADD_REMOVE_CANDIDATE' | translate"
			(selectedChange)="onMembersSelected($event)"
		></ga-candidate-select>
	</div>
</ng-container>

<ng-container *ngIf="rating?.length > 0">
	<canvas style="height: 400px; width: 100%" baseChart [type]="'bar'" [data]="data" [options]="options"></canvas>
</ng-container>

<ng-container *ngIf="rating?.length === 0">
	<div class="no-data">
		<nb-icon icon="info-outline" class="no-data-icon"></nb-icon>
		<span class="no-data-text">{{ 'CANDIDATES_PAGE.STATISTIC.NO_DATA' | translate }}</span>

		<span class="no-data-text" *ngIf="candidates.length > 0">
			{{ 'CANDIDATES_PAGE.STATISTIC.SELECT_CANDIDATE' | translate }}
		</span>
	</div>
</ng-container>
