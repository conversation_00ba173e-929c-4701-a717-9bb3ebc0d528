<ng-container *ngIf="candidates.length > 0">
	<nb-select
		[placeholder]="'CANDIDATES_PAGE.STATISTIC.SELECT_INTERVIEW' | translate"
		class="select"
		(selectedChange)="onInterviewSelected($event)"
	>
		<nb-option-group *ngFor="let candidate of candidates" [title]="candidate.user?.name">
			<nb-option *ngFor="let interview of candidate.interview" [value]="interview">
				{{ interview.title }}
			</nb-option>
		</nb-option-group>
	</nb-select>
</ng-container>

<ng-container *ngIf="rating?.length > 0">
	<canvas style="height: 400px; width: 100%" baseChart [type]="'bar'" [data]="data" [options]="options"></canvas>
</ng-container>

<ng-container *ngIf="rating?.length === 0">
	<div class="no-data">
		<nb-icon icon="info-outline" class="no-data-icon"></nb-icon>
		<span class="no-data-text">{{ 'CANDIDATES_PAGE.STATISTIC.NO_DATA' | translate }}</span>

		<span class="no-data-text" *ngIf="candidates.length > 0">
			{{ 'CANDIDATES_PAGE.STATISTIC.SELECT_INTERVIEW' | translate }}
		</span>
	</div>
</ng-container>
