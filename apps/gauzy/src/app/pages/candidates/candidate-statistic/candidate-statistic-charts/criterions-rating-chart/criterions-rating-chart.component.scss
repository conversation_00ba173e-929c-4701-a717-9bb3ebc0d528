@use 'themes' as *;

.title {
  margin-top: 2rem;
}

.select {
  width: 100%;
}

.selectors {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  margin: 0 0 1.25rem 0;
}

:host .select-interviewer {
  width: 20rem;
  @include nb-ltr(padding-left, 1rem);
  @include nb-rtl(padding-right, 1rem);
}

.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 1px #e5e5e5 solid;
  height: 150px;

  &-text {
    color: #909cb4;
  }

  &-icon {
    font-size: 1.5rem !important;
    color: #909cb4;
  }
}
