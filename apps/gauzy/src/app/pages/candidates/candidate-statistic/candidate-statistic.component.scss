@use 'themes' as *;

:host {
  nb-accordion-item-header ::ng-deep nb-icon {
    border: 1px solid nb-theme(border-basic-color-4);
    border-radius: nb-theme(border-radius);
    width: 1.75rem;
    height: 1.75rem;
  }
  nb-accordion {
    box-shadow: unset;
  }
  nb-accordion-item-header {
    padding: 1.5rem;
  }
  nb-accordion-item-body ::ng-deep .item-body {
    padding: 1.5rem;
  }
  nb-card,
  nb-card-body {
    background: var(--gauzy-card-2);
  }
}

h6 {
  font-size: 16px;
  font-weight: 400;
  line-height: 16px;
  letter-spacing: 0em;
  text-align: center;
}
