<nb-card class="card-scroll">
	<nb-card-header style="display: flex">
		<ngx-back-navigation></ngx-back-navigation>
		<h4>
			<ngx-header-title [allowEmployee]="false">
				{{ 'CANDIDATES_PAGE.STATISTIC.STATISTICS' | translate }}
			</ngx-header-title>
		</h4>
	</nb-card-header>
	<nb-card-body>
		<nb-accordion>
			<nb-accordion-item>
				<nb-accordion-item-header>
					<h6>{{ 'CANDIDATES_PAGE.STATISTIC.RATING' | translate }}</h6>
				</nb-accordion-item-header>
				<nb-accordion-item-body>
					<ng-container *ngIf="candidates$ | async as candidates">
						<ng-template [ngIf]="candidates.length > 0">
							<ga-overall-rating-chart [candidates]="candidates"></ga-overall-rating-chart>
						</ng-template>
					</ng-container>
				</nb-accordion-item-body>
			</nb-accordion-item>
			<nb-accordion-item>
				<nb-accordion-item-header>
					<h6>{{ 'CANDIDATES_PAGE.STATISTIC.INTERVIEWER_ASSESSMENT' | translate }}</h6>
				</nb-accordion-item-header>
				<nb-accordion-item-body>
					<ng-container *ngIf="candidates$ | async as candidates">
						<ng-template [ngIf]="candidates.length > 0">
							<ga-interview-rating-chart
								[candidates]="candidates"
								[employees]="employees$ | async"
							></ga-interview-rating-chart>
						</ng-template>
					</ng-container>
				</nb-accordion-item-body>
			</nb-accordion-item>
			<nb-accordion-item>
				<nb-accordion-item-header>
					<h6>{{ 'CANDIDATES_PAGE.STATISTIC.CRITERIONS_RATING' | translate }}</h6>
				</nb-accordion-item-header>
				<nb-accordion-item-body>
					<ng-container *ngIf="candidates$ | async as candidates">
						<ng-template [ngIf]="candidates.length > 0">
							<ga-criterions-rating-chart
								[candidates]="candidates"
								[interviews]="interviews$ | async"
								[employees]="employees$ | async"
							></ga-criterions-rating-chart>
						</ng-template>
					</ng-container>
				</nb-accordion-item-body>
			</nb-accordion-item>
			<nb-accordion-item>
				<nb-accordion-item-header>
					<h6>{{ 'CANDIDATES_PAGE.STATISTIC.CANDIDATE_CRITERIONS_RATING' | translate }}</h6>
				</nb-accordion-item-header>
				<nb-accordion-item-body>
					<ng-container *ngIf="candidates$ | async as candidates">
						<ng-template [ngIf]="candidates.length > 0">
							<ga-average-criterions-rating-chart
								[candidates]="candidates"
								[interviews]="interviews$ | async"
							></ga-average-criterions-rating-chart>
						</ng-template>
					</ng-container>
				</nb-accordion-item-body>
			</nb-accordion-item>
		</nb-accordion>
	</nb-card-body>
</nb-card>
