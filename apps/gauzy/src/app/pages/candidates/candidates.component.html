<nb-card [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
	<nb-card-header class="d-flex flex-column pb-0">
		<div class="card-header-title">
			<h4>
				<ngx-header-title [allowEmployee]="false">
					{{ 'CANDIDATES_PAGE.HEADER' | translate }}
				</ngx-header-title>
			</h4>
			<div class="float-right">
				<ng-template ngxPermissionsOnly="ORG_CANDIDATES_INTERVIEW_EDIT">
					<button
						nbButton
						status="primary"
						(click)="goTo('interviews')"
						size="small"
						class="action header"
						outline
					>
						<i class="fas fa-user-friends"></i>
						{{ 'BUTTONS.MANAGE_INTERVIEWS' | translate }}
					</button>
				</ng-template>
				<ng-template ngxPermissionsOnly="ORG_INVITE_EDIT">
					<button
						nbButton
						status="primary"
						(click)="goTo('invites')"
						size="small"
						class="action header"
						outline
					>
						<i class="far fa-envelope"></i>
						{{ 'BUTTONS.MANAGE_INVITES' | translate }}
					</button>
				</ng-template>
				<ng-template ngxPermissionsOnly="ORG_CANDIDATES_VIEW">
					<button
						nbButton
						status="primary"
						(click)="goTo('statistic')"
						size="small"
						class="action header"
						outline
					>
						<i class="far fa-chart-bar"></i>
						{{ 'BUTTONS.CANDIDATE_STATISTIC' | translate }}
					</button>
				</ng-template>
			</div>
		</div>
		<div class="align-self-end d-flex align-items-center">
			<ngx-gauzy-button-action
				[buttonTemplate]="actionButtons"
				[buttonTemplateVisible]="visibleButton"
				[isDisable]="disableButton"
				[hasLayoutSelector]="false"
			></ngx-gauzy-button-action>
			<nb-toggle (checkedChange)="changeIncludeArchived($event)" status="primary" class="custom-toggle ml-3 mr-3">
				{{ 'FORM.CHECKBOXES.INCLUDE_ARCHIVED' | translate }}
			</nb-toggle>
			<ga-layout-selector [componentName]="viewComponentName"></ga-layout-selector>
		</div>
	</nb-card-header>
	<nb-card-body>
		<ng-template [ngIf]="dataLayoutStyle === componentLayoutStyleEnum.TABLE" [ngIfElse]="gridLayout">
			<div class="table-scroll-container custom-table">
				<div>
					<angular2-smart-table
						style="cursor: pointer"
						[settings]="settingsSmartTable"
						[source]="sourceSmartTable"
						(userRowSelect)="selectCandidate($event)"
					></angular2-smart-table>
				</div>
			</div>
			<ng-container *ngIf="smartTableSource">
				<ngx-pagination [source]="smartTableSource"></ngx-pagination>
			</ng-container>
		</ng-template>
		<ng-template #gridLayout>
			<ga-card-grid
				[totalItems]="pagination?.totalItems"
				[settings]="settingsSmartTable"
				[source]="candidates"
				(onSelectedItem)="selectCandidate($event)"
				(scroll)="onScroll()"
			></ga-card-grid>
		</ng-template>
	</nb-card-body>
</nb-card>
<ng-template #actionButtons let-selectedItem="selectedItem">
	<div class="btn-group actions">
		<ngx-favorite-toggle
			*ngIf="selectedItem"
			[entityType]="'Candidate'"
			[entityId]="selectedItem.id"
			[entityName]="getCandidateDisplayName(selectedItem)"
			size="small"
			status="basic"
			spacing="list"
			[disabled]="disableButton"
			[showLabel]="false"
			(favoriteToggled)="onCandidateFavoriteToggled($event)"
		></ngx-favorite-toggle>
		<ng-template ngxPermissionsOnly="ORG_CANDIDATES_EDIT">
			<button
				nbButton
				[disabled]="!selectedItem && disableButton"
				(click)="edit(selectedItem)"
				size="small"
				class="action primary"
			>
				<nb-icon class="mr-1" icon="edit-outline"></nb-icon>{{ 'BUTTONS.EDIT' | translate }}
				{{ selectedItem?.status }}
			</button>
			<button
				nbButton
				[disabled]="(!selectedItem && disableButton) || selectedItem?.isArchived"
				(click)="archive(selectedItem)"
				size="small"
				class="action secondary"
			>
				<nb-icon class="mr-1" icon="archive-outline"></nb-icon>{{ 'BUTTONS.ARCHIVE' | translate }}
			</button>
			<ng-container *ngIf="selectedCandidate?.status === candidateStatusEnum.APPLIED">
				<button
					nbButton
					[disabled]="!selectedItem && disableButton"
					(click)="hire(selectedItem)"
					status="basic"
					size="small"
					class="action success"
				>
					<nb-icon class="mr-1" icon="person-done-outline"></nb-icon>{{ 'BUTTONS.HIRE' | translate }}
				</button>
				<button
					nbButton
					[disabled]="!selectedItem && disableButton"
					(click)="reject(selectedItem)"
					status="basic"
					size="small"
					class="action warning"
				>
					<nb-icon class="mr-1" icon="person-remove-outline"></nb-icon>{{ 'BUTTONS.REJECT' | translate }}
				</button>
			</ng-container>
		</ng-template>
	</div>
</ng-template>
<ng-template #visibleButton>
	<ng-template ngxPermissionsOnly="ORG_INVITE_EDIT">
		<button
			nbButton
			*ngIf="organizationInvitesAllowed"
			status="primary"
			(click)="invite()"
			size="small"
			class="action"
		>
			<nb-icon class="mr-1" icon="email-outline"></nb-icon>{{ 'BUTTONS.INVITE' | translate }}
		</button>
	</ng-template>
	<ng-template ngxPermissionsOnly="ORG_CANDIDATES_EDIT">
		<button nbButton status="success" (click)="add()" size="small" class="action">
			<nb-icon class="mr-1" icon="plus-outline"></nb-icon>{{ 'BUTTONS.ADD' | translate }}
		</button>
	</ng-template>
</ng-template>
