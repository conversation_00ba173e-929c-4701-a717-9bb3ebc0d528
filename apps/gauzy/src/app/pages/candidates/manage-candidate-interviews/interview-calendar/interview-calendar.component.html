<nb-card>
	<nb-card-header class="main-header">
		<h5>
			{{ 'CANDIDATES_PAGE.EDIT_CANDIDATE.INTERVIEW.SCHEDULED_INTERVIEWS' | translate }}
		</h5>
		<div class="buttons">
			<ga-employee-multi-select
				class="multi-select"
				[allEmployees]="employees$ | async"
				[label]="false"
				(selectedChange)="onEmployeeSelected($event)"
			></ga-employee-multi-select>
			<ga-candidate-multi-select
				class="multi-select"
				(selectedChange)="onCandidateSelected($event)"
			></ga-candidate-multi-select>
		</div>
	</nb-card-header>
	<nb-card-body>
		<ng-container *ngIf="calendarOptions">
			<full-calendar class="custom-calendar" #calendar [options]="calendarOptions"></full-calendar>
		</ng-container>
	</nb-card-body>
</nb-card>
