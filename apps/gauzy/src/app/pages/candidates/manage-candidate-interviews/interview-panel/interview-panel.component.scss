@use 'gauzy/_gauzy-cards' as *;
@use 'gauzy/_gauzy-table' as *;
@use 'gauzy/_gauzy-overrides' as ga-overrides;

:host .card {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: stretch;
  width: 100%;
  border: none;
  padding: 0;
  overflow: unset;
  background-color: var(--gauzy-card-2);

  &-filter {
    padding-bottom: 1.5rem;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
  }
}

.card-header {
  background: unset;
}
.header {
  display: flex;
  align-items: center;
  justify-content: flex-end;

  .btn {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 1rem;
  }
}

.select {
  width: 18rem;
}
:host .card-container {
  display: flex;
  flex-wrap: wrap;

  .card-body {
    width: 100%;
    max-width: 300px;
    .card-header {
      padding: 0.5rem;
      display: flex;
      align-items: center;
      justify-content: space-between;

      &-badge {
        display: flex;
        align-items: center;
        justify-content: flex-end;
      }
      .header-container {
        text-align: center;
      }
    }

    .interview-card {
      display: flex;
      flex-direction: column;
      padding: 0.5rem;

      .name-container {
        display: flex;
        align-items: center;
        margin-bottom: 10px;

        &:hover {
          cursor: pointer;
          color: #3366ff;
        }

        .image-container {
          width: 70px;
          height: 50px;
          display: flex;
          justify-content: center;
          @include nb-ltr(margin-right, 10px);
          @include nb-rtl(margin-left, 10px);

          img {
            height: 100%;
            max-width: 70px;
          }
        }
      }
      .team-members {
        margin-bottom: 0.5rem;
      }
    }

    .button-container {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: auto;
      flex-wrap: wrap;
    }
  }
}
.btn {
  margin: 0.2rem;
}

.badge-warning {
  background-color: #fa0;
}

.badge-primary {
  background-color: #0095ff;
}

.badge {
  text-align: center;
  padding: 5px;
  margin-left: 0.2rem;
}
.client-info {
  padding: 0px 12px;
  display: flex;
  flex-direction: column;
  .info-line {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    font-size: 0.7em;
    color: darkgray;
    .info-value {
      display: flex;
      justify-content: flex-end;
      text-align: end;
      .info-list-item:not(:last-child)::after {
        content: ',';
        @include nb-ltr(margin-right, 5px);
        @include nb-rtl(margin-left, 5px);
      }
    }
    .criterions {
      display: flex;
      flex-direction: column;
      justify-content: flex-end;
      text-align: end;
    }
  }
}

:host {
  @include card-overrides(unset, calc($default-card-height), $default-radius);
  .custom-table {
    max-height: unset;
  }
  .custom-grid {
    height: 100%;
    overflow-y: auto;
  }
  ::ng-deep .header {
    @include ga-overrides.nb-select-overrides(
      2rem,
      ga-overrides.$default-button-radius,
      ga-overrides.$default-box-shadow
    );
  }
}

:host .gauzy-action {
  position: absolute;
  @include nb-ltr(right, 1rem);
  @include nb-rtl(left, 1rem);
  top: -4.5rem;
}

.no-data-found {
  height: 100%;
}

:host .interview-panel {
  @include nb-ltr(padding-right, 0.5rem !important);
  @include nb-rtl(padding-left, 0.5rem !important);
}
