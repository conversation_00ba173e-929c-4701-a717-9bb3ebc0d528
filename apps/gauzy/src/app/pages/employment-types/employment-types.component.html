<nb-card class="m-0">
	<nb-card-header class="card-custom-header">
		<div class="card-header-title">
			<h4>
				<ngx-header-title [allowEmployee]="false">
					{{ 'ORGANIZATIONS_PAGE.EMPLOYMENT_TYPES' | translate }}
				</ngx-header-title>
			</h4>
		</div>
	</nb-card-header>
	<nb-card-body class="p-0">
		<div class="gauzy-button-container">
			<ngx-gauzy-button-action
				[buttonTemplate]="actionButtons"
				[componentName]="viewComponentName"
				[buttonTemplateVisible]="visibleButton"
				[isDisable]="disabled"
			>
			</ngx-gauzy-button-action>
		</div>
		<nb-tabset class="mt-4">
			<nb-tab [tabTitle]="'ORGANIZATIONS_PAGE.BROWSE' | translate" [tabId]="employmentTypeTabsEnum.BROWSE">
				<ng-template
					[ngIf]="dataLayoutStyle === componentLayoutStyleEnum.TABLE && organizationEmploymentTypes?.length"
				>
					<div class="table-scroll-container">
						<nb-card class="p-0" *ngFor="let employmentType of organizationEmploymentTypes">
							<nb-card-body
								class="custom-table"
								[ngClass]="{
									selected: selected?.state && employmentType === selected?.employmentType
								}"
								(click)="selectOrganizationEmploymentType(employmentType)"
							>
								<ga-notes-with-tags [rowData]="employmentType"> </ga-notes-with-tags>
							</nb-card-body>
						</nb-card>
					</div>
				</ng-template>
				<ng-template [ngIf]="organizationEmploymentTypes?.length == 0">
					<div class="no-data">
						<ngx-no-data-message
							[message]="'ORGANIZATIONS_PAGE.EMPLOYMENT_TYPE_NO_DATA_MESSAGE' | translate"
						></ngx-no-data-message>
					</div>
				</ng-template>
				<ng-template
					[ngIf]="
						dataLayoutStyle === componentLayoutStyleEnum.CARDS_GRID && organizationEmploymentTypes?.length
					"
				>
					<div class="grid">
						<ga-card-grid
							[totalItems]="pagination?.totalItems"
							[settings]="settingsSmartTable"
							[source]="organizationEmploymentTypes"
							(onSelectedItem)="selectOrganizationEmploymentType($event)"
							(scroll)="onScroll()"
						></ga-card-grid>
					</div>
				</ng-template>
			</nb-tab>
			<nb-tab [tabTitle]="'ORGANIZATIONS_PAGE.SEARCH' | translate" [tabId]="employmentTypeTabsEnum.SEARCH">
				<ga-wip></ga-wip>
			</nb-tab>
		</nb-tabset>
	</nb-card-body>
</nb-card>
<ng-template #visibleButton>
	<button (click)="openDialog(editableTemplate, false)" nbButton status="success" size="small">
		<nb-icon icon="plus-outline"></nb-icon>
		{{ 'BUTTONS.ADD' | translate }}
	</button>
</ng-template>
<ng-template #actionButtons>
	<div class="actions">
		<button
			(click)="openDialog(editableTemplate, true)"
			nbButton
			status="basic"
			class="action primary"
			[disabled]="disabled"
			size="small"
			nbButton
		>
			<nb-icon icon="edit-outline"></nb-icon>
			{{ 'BUTTONS.EDIT' | translate }}
		</button>
		<button
			(click)="deleteEmploymentType(selected.employmentType.id, selected.employmentType.name)"
			nbButton
			status="basic"
			class="action"
			[disabled]="disabled"
			size="small"
			[nbTooltip]="'BUTTONS.DELETE' | translate"
		>
			<nb-icon status="danger" icon="trash-2-outline"> </nb-icon>
		</button>
	</div>
</ng-template>
<ng-template #editableTemplate let-ref="dialogRef">
	<div class="editable">
		<div class="container">
			<div class="row">
				<div class="col-sm-12 d-flex justify-content-end">
					<i class="fas fa-times" (click)="ref.close(); disabled = true"></i>
				</div>
			</div>
			<div class="row mb-3">
				<h5 class="title mr-3 ml-3">
					{{
						selectedOrgEmpType && selectedOrgEmpType?.id
							? ('POP_UPS.EDIT' | translate)
							: ('POP_UPS.ADD' | translate)
					}}
				</h5>
			</div>
			<form [formGroup]="form" *ngIf="form">
				<div class="row mb-3">
					<div class="col-sm-12 d-flex flex-column">
						<label class="label">{{ 'ORGANIZATIONS_PAGE.EMPLOYMENT_TYPE_NAME' | translate }}</label>
						<input
							required
							formControlName="name"
							nbInput
							type="text"
							[placeholder]="'ORGANIZATIONS_PAGE.EMPLOYMENT_TYPE_NAME' | translate"
							fullWidth
						/>
					</div>
				</div>
				<div class="row mb-3">
					<div class="col-sm-12 d-flex flex-column">
						<ga-tags-color-input
							[selectedTags]="tags"
							(selectedTagsEvent)="selectedTagsEvent($event)"
							[isOrgLevel]="true"
						>
						</ga-tags-color-input>
					</div>
				</div>
				<div class="row mb-3">
					<div class="col-sm-12"></div>
					<button
						class="delete mr-3 ml-3"
						(click)="ref.close(); disabled = true"
						nbButton
						status="basic"
						outline
					>
						{{ 'BUTTONS.CANCEL' | translate }}
					</button>
					<button
						(click)="submitForm(); disabled = true; ref.close()"
						nbButton
						status="success"
						[disabled]="form.invalid"
					>
						{{ 'BUTTONS.SAVE' | translate }}
					</button>
				</div>
			</form>
		</div>
	</div>
</ng-template>
