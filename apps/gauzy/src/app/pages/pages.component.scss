@use 'themes' as *;
@use 'gauzy/_gauzy-button-action' as *;

@include nb-install-component() {
	::ng-deep router-outlet + * {
		display: block;
		animation: fade 1s;

		@keyframes fade {
			from {
				opacity: 0;
			}

			to {
				opacity: 1;
			}
		}
	}

	::ng-deep {
		angular2-smart-table {
			nav {
				width: 100%;
			}
						.angular2-smart-row {
				&.selected {
					background: rgba(99, 19, 19, 0.05);
				}
			}
			angular2-smart-table-title {
				a {
					color: nb-theme(smart-table-fg);
				}
			}
			.form-control {
				background-color: nb-theme(input-basic-background-color);
				border-color: nb-theme(input-basic-border-color);
				border-style: nb-theme(input-border-style);
				border-width: nb-theme(input-border-width);
				color: nb-theme(input-basic-text-color);
				&::placeholder {
					color: nb-theme(input-basic-placeholder-text-color);
					text-overflow: ellipsis;
				}
			}

			& :nth-child(2) {
				overflow-x: unset !important;
			}
		}
	}
}
