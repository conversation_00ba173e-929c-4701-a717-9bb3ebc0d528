@use 'gauzy/_gauzy-dialogs' as *;

.main {
  display: flex;
  flex-direction: column;
  padding-bottom: 10px;
  width: 645px;
  background-color: var(--gauzy-card-1);
}

.ck-editor{
  width: 100%;
}

.select {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  width: 50%;
}

.select-label {
  color: #8f9bb3;
  font-family: Open Sans, sans-serif;
  font-size: 0.75rem;
  font-weight: 700;
  line-height: 1rem;
  margin: 0;
  margin-right: 1rem;
}

.header {
  display: flex;
  flex-direction: column;
}

.main-buttons {
  margin-right: 10px;
  margin-bottom: 10px;
}

.save-button {
  margin-top: 10px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
}

.first-row {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-bottom: 10px;
}

.second-row {
  width: 49%;
}

.selector {
  display: flex;
  flex-direction: row;
  margin-left: -15px;
  margin-bottom: 10px;
}

.multi-select {
  margin-bottom: 1rem;
}

::ng-deep {
  .nb-theme-default [nbInput].size-medium:not(.input-full-width) {
    max-width: 23rem !important;
    width: 23rem !important;
  }
}

:host {

  input,
  ::ng-deep nb-select.appearance-outline.status-basic .select-button,
  ::ng-deep .ng-select .ng-select-container {
    background-color: var(--gauzy-sidebar-background-4) !important;
    border: unset;
  }

  ::ng-deep .ng-select .ng-select-container {
    input {
      background-color: unset !important;
    }
  }

  ::ng-deep .toggle {
    border: 1px solid #7E7E8F !important;
    background-color: #7E7E8F !important;

    &.checked {
      background-color: var(--text-primary-color) !important;
      border: 1px solid var(--text-primary-color) !important;

      &+span {
        color: var(--text-primary-color);
      }
    }
  }
}
