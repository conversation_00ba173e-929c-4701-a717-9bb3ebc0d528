<div class="global">
	<ga-sidebar (clickedNode)="clickedNode($event)" (deletedNode)="deletedNode()"></ga-sidebar>
	<div class="main">
		<nb-card>
			<nb-card-header class="head">
				<div class="header">
					<div class="header-article">
						<h6 *ngIf="!categoryName">
							{{ 'HELP_PAGE.CHOOSE_ANY_CATEGORY' | translate }}
						</h6>
						<h6 *ngIf="categoryName">{{ categoryName }}</h6>
						<h6 class="articles">
							{{ this.articleList.length }}
							{{ 'HELP_PAGE.ARTICLES' | translate }}
						</h6>
					</div>
					<button
						[disabled]="!categoryName"
						status="primary"
						class="action"
						(click)="addNode()"
						size="small"
						outline
						nbButton
					>
						{{ 'HELP_PAGE.ADD_ARTICLE' | translate }}
						<nb-icon icon="plus-outline"></nb-icon>
					</button>
				</div>
				<div class="gauzy-button-container mt-4" *ngIf="categoryName">
					<ngx-gauzy-button-action
						[hasLayoutSelector]="false"
						[buttonTemplate]="actionButtons"
						[buttonTemplateVisible]="visible"
						[isDisable]="isDisable"
					></ngx-gauzy-button-action>
				</div>
				<ng-template [ngIf]="showFilters">
					<div *ngIf="categoryName" class="row row-search ml-1 mb-3 align-items-end">
						<div class="col-lg-5">
							<div class="form-group">
								<label for="employee" class="label">{{ 'HELP_PAGE.SEARCH_BY_NAME' | translate }}</label>
								<input
									fullWidth
									[formControl]="search"
									placeholder="Search"
									value="inputValue"
									type="text"
									nbInput
								/>
							</div>
						</div>
						<div class="col-lg-5">
							<div class="form-group">
								<label for="employee" class="label">{{
									'HELP_PAGE.FILTER_BY_AUTHOR' | translate
								}}</label>
								<ga-employee-multi-select
									[multiple]="false"
									[label]="false"
									[allEmployees]="employees"
									(selectedChange)="onEmployeeSelected($event)"
									[reset]="isResetSelect"
								></ga-employee-multi-select>
							</div>
						</div>
						<div class="col-lg-2">
							<button
								status="danger"
								class="action clear-button"
								(click)="clearFilters()"
								outline
								nbButton
							>
								{{ 'HELP_PAGE.CLEAR' | translate }}
							</button>
						</div>
					</div>
				</ng-template>
			</nb-card-header>
			<nb-card-body [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="giant">
				<div
					*ngIf="this.articleList.length; else nodata"
					class="table-scroll"
					[ngClass]="{ filter: showFilters }"
				>
					<div
						class="custom-table"
						[ngClass]="{
							selected: isSelected(i, article)
						}"
						*ngFor="let article of this.filteredArticles; let i = index"
						(click)="selectItem(i, article)"
					>
						<div class="row-name">
							<p class="name">
								{{ article.name }}
							</p>
							<p class="draft" *ngIf="article.draft === true">
								{{ 'HELP_PAGE.DRAFT' | translate }}
							</p>
							<p class="privacy" *ngIf="article.privacy === true">
								{{ 'HELP_PAGE.EMPLOYEES' | translate }}
							</p>
						</div>
						<p>{{ article.description }}</p>
						<div class="data" *ngIf="showData[i]" [innerHtml]="dataArray[i]"></div>
						<div class="user-info">
							<p>
								{{ 'HELP_PAGE.WRITTEN_BY' | translate }}
							</p>
							<div class="image-wrap">
								<span
									*ngFor="let employee of article.employees"
									nbTooltip=" {{ employee?.user?.firstName }}
									{{ employee?.user?.lastName }}"
									nbTooltipPlacement="top"
								>
									<ngx-avatar
										[src]="employee?.user?.imageUrl"
										[size]="sm"
										[id]="employee?.id"
										[employee]="employee"
									></ngx-avatar>
								</span>
							</div>
							<div class="create-at">
								<nb-icon class="icon" icon="clock-outline"></nb-icon>
								<p>
									{{ 'HELP_PAGE.CREATED_AT' | translate }}:
									{{ article.createdAt | date : 'short' }}
								</p>
							</div>
						</div>
					</div>
				</div>
			</nb-card-body>
		</nb-card>
	</div>
</div>

<ng-template #visible>
	<button status="success" class="action" (click)="addNode()" size="small" nbButton>
		<nb-icon icon="plus-outline"></nb-icon>
		{{ 'BUTTONS.ADD' | translate }}
	</button>
	<button status="basic" class="action info-text-1" (click)="showFilters = !showFilters" size="small" nbButton>
		<nb-icon icon="funnel-outline"></nb-icon>
		{{ 'BUTTONS.FILTER' | translate }}
	</button>
</ng-template>
<ng-template #actionButtons>
	<ng-container>
		<div class="actions">
			<button
				size="small"
				class="action secondary"
				(click)="openArticle(selectedItem?.index)"
				status="basic"
				nbButton
			>
				<nb-icon icon="eye-outline"></nb-icon>
				{{ 'BUTTONS.VIEW' | translate }}
			</button>
			<button size="small" class="action primary" (click)="editNode(selectedItem?.index)" status="basic" nbButton>
				<nb-icon icon="edit-outline"></nb-icon>
				{{ 'BUTTONS.EDIT' | translate }}
			</button>
			<button
				class="action primary"
				status="basic"
				size="small"
				[nbTooltip]="'BUTTONS.DELETE' | translate"
				(click)="deleteNode(selectedItem?.index)"
				nbButton
			>
				<nb-icon status="danger" icon="trash-2-outline"></nb-icon>
			</button>
		</div>
	</ng-container>
</ng-template>
<ng-template #nodata>
	<div class="no-data">
		<ngx-no-data-message [message]="'HELP_PAGE.CHOOSE_ANY_CATEGORY' | translate"></ngx-no-data-message>
	</div>
</ng-template>
