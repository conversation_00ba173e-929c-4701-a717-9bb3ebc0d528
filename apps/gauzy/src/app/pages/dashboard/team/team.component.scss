@use 'gauzy/_gauzy-cards' as *;

:host {
  @include nb-card_overrides(auto, calc($default-card-height + 3rem), $default-radius);

  .card {
    background-color: var(--gauzy-card-2);

    .card-header {
      background-color: unset;
    }
  }

  gauzy-team-card {
    cursor: pointer;
  }

  .team-selected-card {
    ::ng-deep {
      nb-card {
        background-color: var(--color-primary-transparent-100);
      }
    }
  }

  .team-list {
    display: flex;
    flex-direction: column;
	  gap: .5rem;
	  width: 100%;
	  overflow: auto;
	  padding-right: 0.5rem;
	  margin-right: -0.5rem;
  }

	.team-chart {
		height: 100%;
	}

	.team-header-list {
		margin-right: 0.5rem;

		nb-card-body {
			font-size: 12px;
			font-weight: 600;
			line-height: 15px;
			letter-spacing: 0em;
			color: var(--gauzy-text-color-2) !important;
		}
	}

	.team-selected-container {
		display: flex;
		flex-direction: column;
		height: calc(100% - 7rem);
		gap: 1rem;

		.user-title {
			display: flex;
			gap: 0.5rem;
		}
	}

  nb-card {
    background-color: var(--gauzy-card-2);
    margin: 0;

    nb-card-body {
		background-color: var(--gauzy-card-2);
		padding: 1rem;

		&.card-body-content {
			padding-right: 0.5rem;
		}

		.team-selected {
			height: 100%;
			display: flex;
			flex-direction: column;
			gap: 0.5rem;
		}

		.team-card {
			display: flex;
			flex-wrap: nowrap;
			overflow-x: auto;
			gap: 1rem;
			width: 100%;
			padding-bottom: 0.5rem;
		}
	}
  }
}

.header-title {
	display: flex;
	gap: 0.5rem;
	align-items: center;
}
