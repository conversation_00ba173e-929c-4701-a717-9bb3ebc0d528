.masonry {
	-webkit-column-count: 2;
	-moz-column-count: 2;
	column-count: 2;
	-webkit-column-gap: 1rem;
	-moz-column-gap: 1rem;
	column-gap: 1rem;


	overflow-y: auto;
	width: 100%;
	padding-right: 0.5rem;

	nb-card {
		width: 100%;
		display: inline-flex;
		margin-bottom: 1rem;
	}
}

@media only screen and (max-width: 1200px) {
	.masonry {
		-moz-column-count: 1;
		-webkit-column-count: 1;
		column-count: 1;
	}
}

@media only screen and (min-width: 1201px) {
	.grid-container {
		-moz-column-count: 2;
		-webkit-column-count: 2;
		column-count: 2;
	}
}

.legend-container {
	display: flex;
	gap: 1rem;
	flex-wrap: wrap;
}

.legend {
	
		nb-badge{
			position: relative;
			font-size: 14px;
			font-weight: 600;
			padding: 2px 4px;
			border-radius: calc(var(--border-radius) / 2);
		}
	  
}

.widgets-container {
	display: flex;
	align-items: center;
	gap: 1rem;
	flex-wrap: wrap;

	.title {
		font-size: 16px;
		font-weight: 400;
		line-height: 16px;
		letter-spacing: -0.009em;
		color: var(--gauzy-text-color-2);
	}

	nb-card {
		padding: 8px 12px 8px 15px;
		background-color: var(--gauzy-card-1);
		min-width: 242px;
	}

	nb-card-header {
		padding: 0;
		margin-bottom: 15px;
	}

	.counter-container {
		width: 71%;
	}

	nb-card-body {
		display: flex;
		flex-direction: column;
		padding: 0;
	}


}

.main-card-container {
	display: flex;
	flex-direction: column;
	gap: 1rem;
	width: 100%;
	height: calc(100vh - 19.7rem);
}

nb-card-body, nb-card-header {
	padding: 1rem;
}

.custom-legend {
  display: flex;
  gap: 4px;
}

.main-card{
  min-width: 500px;
}
