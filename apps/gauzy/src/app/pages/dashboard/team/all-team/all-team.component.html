<div class="main-card-container">
	<div class="widgets-container">
		<nb-card>
			<nb-card-header class="team-header">
				<h6 class="title">{{ 'ORGANIZATIONS_PAGE.TEAMS' | translate }}</h6>
			</nb-card-header>
			<nb-card-body class="team-container">
				<div class="d-flex align-items-center">
					<div class="h1">
						{{ organization?.statistics?.countWorking }}
					</div>
					<div>/{{ organization?.statistics?.countTeams }}</div>
				</div>

				<div class="counter-container">
					<gauzy-counter-point
						[total]="organization?.statistics?.countTeams"
						[value]="organization?.statistics?.countWorking"
					></gauzy-counter-point>
				</div>
			</nb-card-body>
		</nb-card>
		<nb-card>
			<nb-card-header class="team-header">
				<h6 class="title">{{ 'TIMESHEET.MEMBERS_WORKED' | translate }}</h6>
			</nb-card-header>
			<nb-card-body class="team-container">
				<div class="d-flex align-items-center">
					<div class="h1">
						{{ organization?.statistics?.counts?.employeesCount || 0 }}
					</div>
					<div>/{{ organization?.statistics?.counts?.employeesCountTotal || 0 }}</div>
				</div>
				<div class="counter-container">
					<gauzy-counter-point
						[total]="organization?.statistics?.counts?.employeesCountTotal"
						[value]="organization?.statistics?.counts?.employeesCount"
					></gauzy-counter-point>
				</div>
			</nb-card-body>
		</nb-card>
		<nb-card>
			<nb-card-header class="team-header">
				<h6 class="title">{{ 'TIMESHEET.PROJECTS_WORKED' | translate }}</h6>
			</nb-card-header>
			<nb-card-body class="team-container">
				<div class="d-flex align-items-center">
					<div class="h1">
						{{ organization?.statistics?.counts?.projectsCount || 0 }}
					</div>
					<div>/{{ projectCount }}</div>
				</div>
				<div class="counter-container">
					<gauzy-counter-point
						[total]="projectCount"
						[value]="organization?.statistics?.counts?.projectsCount"
					></gauzy-counter-point>
				</div>
			</nb-card-body>
		</nb-card>
		<nb-card>
			<nb-card-header>
				<h6 class="title">{{ 'TIMESHEET.WORKED_FOR_DAY' | translate }}</h6>
			</nb-card-header>
			<nb-card-body class="team-container">
				<div class="d-flex align-items-center">
					<div class="h1">
						{{ organization?.statistics?.counts?.weekActivities || 0 }}
					</div>
					<div>%</div>
				</div>
				<div class="counter-container">
					<gauzy-counter-point
						[progress]="true"
						[total]="100"
						[value]="organization?.statistics?.counts?.weekActivities || 0"
					></gauzy-counter-point>
				</div>
			</nb-card-body>
		</nb-card>
	</div>
	<div class="masonry">
		<nb-card *ngFor="let team of teams">
			<nb-card-header class="pb-0 d-flex justify-content-between">
				<div>
					<div class="d-flex align-items-center">
						<div class="h1">{{ team?.statistics?.countWorking }}</div>
						<div>/{{ team?.statistics?.countWorking + team?.statistics.countNotWorking }}</div>
					</div>
					<span>{{ team?.name }}</span>
				</div>
				<div class="legend-container">
					<div class="legend">
						<nb-badge
							status="success"
							[text]="
								('DASHBOARD_PAGE.CHARTS.WORKING_NOW' | translate) + ' ' + team?.statistics?.countOnline
							"
						></nb-badge>
					</div>
					<div class="legend">
						<nb-badge
							status="warning"
							[text]="
								('DASHBOARD_PAGE.CHARTS.WORKING_TODAY' | translate) +
								' ' +
								(team?.statistics?.countWorking - team?.statistics?.countOnline).toString()
							"
						></nb-badge>
					</div>
					<div class="legend">
						<nb-badge
							status="danger"
							[text]="
								('DASHBOARD_PAGE.CHARTS.NOT_WORKING' | translate) +
								' ' +
								team?.statistics?.countNotWorking
							"
						></nb-badge>
					</div>
				</div>
				<div>
					<button (click)="onSelectTeam(team)" nbButton outline size="small" status="primary">
						<nb-icon icon="arrow-forward-outline"></nb-icon>
					</button>
				</div>
			</nb-card-header>
			<nb-card-body>
				<div class="d-flex flex-column">
					<ng-container *ngFor="let member of team?.membersWorkingToday.concat(team?.membersNotWorkingToday)">
						<gauzy-team-member [member]="member" class="mt-2" isClassic="true"></gauzy-team-member>
					</ng-container>
				</div>
			</nb-card-body>
		</nb-card>
	</div>
</div>
