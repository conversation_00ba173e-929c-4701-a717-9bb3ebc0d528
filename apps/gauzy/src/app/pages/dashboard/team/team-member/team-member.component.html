<nb-card>
	<nb-card-body>
		<div class="row w-100">
			<div [class]="isClassic ? 'col-5' : 'col-3'">
				<div [class]="isClassic ? 'user-container' : 'row'">
					<div class="col-3">
						<div class="dot-container">
							<div
								[ngClass]="{
									online: member?.isRunningTimer,
									offline: !member?.isWorkingToday
								}"
								class="dot"
							></div>
						</div>
					</div>
					<div class="col-9">
						<ngx-avatar
							[class]="isClassic ? 'avatar-dashboard' : 'report-table'"
							[id]="member?.employee?.id"
							[name]="member?.employee?.user?.name"
							[src]="member?.employee?.user?.imageUrl"
							[employee]="member?.employee"
						></ngx-avatar>
					</div>
				</div>
			</div>
			<ng-template [ngIfElse]="classic" [ngIf]="!isClassic">
				<div class="col-8">
					<div *ngIf="!member?.tasks?.length" class="col-6">&#8212;</div>
					<div *ngFor="let task of member?.tasks" class="row">
						<div class="col-6">
							{{ task.title ? task.title : ('REPORT_PAGE.NO_TASK' | translate) }}
						</div>
						<div class="col-6">
							<div class="row align-items-center">
								<div class="col-3">
									{{ task.duration | durationFormat }}
								</div>
								<div class="col">
									<nb-progress-bar
										[displayValue]="false"
										[status]="progressStatus(calculatePercentage(task.duration, task?.estimate))"
										[value]="calculatePercentage(task.duration, task?.estimate)"
										class="mb-1"
										size="tiny"
									></nb-progress-bar>
								</div>
								<div class="col-3">
									<ng-template [ngIf]="task?.estimate">
										{{ task?.estimate | durationFormat }}
									</ng-template>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div *ngIf="member?.isWorkingToday" class="col-1">
					<nb-badge
						[status]="progressStatus(member?.activity || 0)"
						[text]="(member?.activity || 0) + '%'"
					></nb-badge>
				</div>
			</ng-template>
		</div>
	</nb-card-body>
</nb-card>

<ng-template #classic>
	<div class="col-7">
		<div class="row align-items-center">
			<div class="col-7">
				<nb-progress-bar
					[displayValue]="false"
					[status]="progressStatus(calculatePercentage(member?.todayWorkDuration, member?.workPeriod))"
					[value]="calculatePercentage(member?.todayWorkDuration, member?.workPeriod)"
					class="mb-1"
					size="tiny"
				>
				</nb-progress-bar>
			</div>
			<div [class]="member?.isWorkingToday ? 'col-3 text-right' : 'col-4'">
				{{
					member?.todayWorkDuration > 0
						? humanize(member?.todayWorkDuration)
						: ('ORGANIZATIONS_PAGE.NOT_WORKED' | translate)
				}}
			</div>
			<div *ngIf="member?.isWorkingToday" class="col-1">
				<nb-badge
					[status]="progressStatus(member?.activity || 0)"
					[text]="(member?.activity || 0) + '%'"
				></nb-badge>
			</div>
		</div>
	</div>
</ng-template>
