nb-card {
  background-color: var(--gauzy-card-1);

  nb-card-body {
    display: flex;
    align-items: center;
    gap: 0.5rem;
	  padding: 1rem;
  }

  .user-container {
    display: flex;
    gap: .5rem;
    align-items: center;
	  .col-3, .col-9{
		  width: fit-content;
	  }
  }

  .dot-container {
    width: 17px;
    height: 17px;
  }

  .dot {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background-color: orange;

    &.online {
      background-color: green;
    }

    &.offline {
      background-color: red;
    }
  }
}

::ng-deep nb-progress-bar.size-tiny .progress-container {
	height: 10px;
}

.text-right {
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}

:host {
  nb-badge{
      position: relative;
      font-size: 14px;
      font-weight: 600;
      padding: 2px 4px;
      border-radius: calc(var(--border-radius) / 2);
  }
}
