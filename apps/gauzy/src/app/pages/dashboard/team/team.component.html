<nb-card>
	<nb-card-header class="mb-3">
		<h4 class="header-title">
			<div>
				<button
					(click)="reset()"
					*ngIf="selectedTeam && selectedTeam?.isSelected"
					nbButton
					outline
					size="small"
					status="primary"
				>
					<nb-icon icon="arrow-back-outline"></nb-icon>
				</button>
			</div>
			<ngx-header-title>
				{{ 'ORGANIZATIONS_PAGE.TEAMS' | translate }}
			</ngx-header-title>
		</h4>
	</nb-card-header>
	<nb-card-body [nbSpinner]="isLoading" class="card-body-content" nbSpinnerSize="large" nbSpinnerStatus="primary">
		<ng-template [ngIfElse]="noTeam" [ngIf]="todayTeamsWorkers.length > 0">
			<div class="team-selected">
				<ng-template [ngIfElse]="noTeamSelected" [ngIf]="selectedTeam && selectedTeam?.isSelected">
					<div class="team-card">
						<ng-container *ngFor="let team of todayTeamsWorkers; trackBy: fnTracker">
							<gauzy-team-card
								(click)="select(team)"
								[ngClass]="{
									'team-selected-card': selectedTeam?.isSelected && selectedTeam?.data.id === team?.id
								}"
								[team]="team"
							>
							</gauzy-team-card>
						</ng-container>
					</div>
					<div class="team-selected-container">
						<div class="team-header-list">
							<nb-card>
								<nb-card-body class="h-auto">
									<div class="row w-100">
										<div class="col-3">
											<div class="row">
												<div class="col-3">
													{{ 'TIMESHEET.STATUS' | translate }}
												</div>
												<div class="col-9">
													{{ 'ORGANIZATIONS_PAGE.NAME' | translate }}
												</div>
											</div>
										</div>
										<div class="col-8">
											<div class="row">
												<div class="col-6">
													{{ 'MENU.IMPORT_EXPORT.TASK' | translate }}
												</div>
												<div class="col-6">
													<div class="row">
														<div class="col-3">
															{{ 'TIMESHEET.DURATION' | translate }}
														</div>
														<div class="col text-center">
															{{ 'MENU.IMPORT_EXPORT.PROGRESS' | translate }}
														</div>
														<div class="col-3">
															{{ 'TASKS_PAGE.ESTIMATE' | translate }}
														</div>
													</div>
												</div>
											</div>
										</div>
										<div class="col-1">
											{{ 'REPORT_PAGE.ACTIVITY' | translate }}
										</div>
									</div>
								</nb-card-body>
							</nb-card>
						</div>
						<div class="team-list">
							<ng-container *ngFor="let member of members; trackBy: fnTracker">
								<gauzy-team-member [member]="member"></gauzy-team-member>
							</ng-container>
						</div>
					</div>
				</ng-template>
			</div>
		</ng-template>
	</nb-card-body>
</nb-card>

<!--  -->
<ng-template #noTeamSelected>
	<gauzy-all-team
		(selectedTeam)="select($event)"
		[organization]="todayOrganization"
		[teams]="todayTeamsWorkers"
	></gauzy-all-team>
</ng-template>

<!--  -->
<ng-template #noTeam>
	<ngx-no-data-message [message]="'SM_TABLE.NO_DATA.TEAM_DASHBOARD' | translate"></ngx-no-data-message>
</ng-template>
