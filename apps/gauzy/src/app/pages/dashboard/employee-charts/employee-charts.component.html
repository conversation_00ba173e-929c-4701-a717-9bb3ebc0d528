<div class="charts">
	<label class="chart-select-label">
		{{ 'DASHBOARD_PAGE.CHARTS.CHART_TYPE' | translate }}
	</label>
	<nb-select
		placeholder="Select chart"
		class="select-charts"
		filled
		status="success"
		[(selected)]="selectedChart"
		size="tiny"
	>
		<nb-option [value]="EmployeeChartEnum.BAR">
			<img width="auto" height="24" style="margin-right: 10px" src="assets/images/others/bar-chart.svg" />
			{{ 'DASHBOARD_PAGE.CHARTS.BAR' | translate }}
		</nb-option>
		<nb-option [value]="EmployeeChartEnum.DOUGHNUT">
			<img width="auto" height="24" style="margin-right: 10px" src="assets/images/others/doughnut.svg" />
			{{ 'DASHBOARD_PAGE.CHARTS.DOUGHNUT' | translate }}
		</nb-option>
		<nb-option [value]="EmployeeChartEnum.STACKED_BAR">
			<img width="auto" height="24" style="margin-right: 10px" src="assets/images/others/stacked-bar-chart.svg" />
			{{ 'DASHBOARD_PAGE.CHARTS.STACKED_BAR' | translate }}
		</nb-option>
	</nb-select>
</div>

<ng-container [ngSwitch]="selectedChart">
	<div class="chart-wrap">
		<ng-template [ngSwitchCase]="EmployeeChartEnum.DOUGHNUT">
			<ga-employee-doughnut-chart [employeeStatistics]="employeeStatistics"></ga-employee-doughnut-chart>
		</ng-template>
		<ng-template [ngSwitchCase]="EmployeeChartEnum.STACKED_BAR">
			<ga-employee-stacked-bar-chart [employeeStatistics]="employeeStatistics"></ga-employee-stacked-bar-chart>
		</ng-template>
		<ng-template [ngSwitchDefault]>
			<ga-employee-horizontal-bar-chart
				[employeeStatistics]="employeeStatistics"
			></ga-employee-horizontal-bar-chart>
		</ng-template>
	</div>
</ng-container>
