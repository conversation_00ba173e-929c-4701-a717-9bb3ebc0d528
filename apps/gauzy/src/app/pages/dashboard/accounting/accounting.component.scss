@use 'gauzy/_gauzy-cards' as *;

.organization-employee-header {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(20rem, 1fr));
  gap: 1rem;
}

:host .table-scrollable {
  height: calc($default-card-height - 14rem);
  overflow: auto;
  @include nb-ltr(padding-right, 0.5rem);
  @include nb-rtl(padding-left, 0.5rem);
}

.cash-flow-report {
  margin: 1rem 0;
}

:host .setting-block {
  &:hover {
    box-shadow: unset;
    background: var(--color-primary-transparent-100) !important;
  }

  &.sub-header {
    @include nb-ltr(margin-right, 0.5rem);
    @include nb-rtl(margin-left, 0.5rem);
    font-size: 12px;
    font-weight: 600;
    line-height: 15px;
    letter-spacing: 0em;
    color: var(--gauzy-text-color-2) !important;
    background: nb-theme(gauzy-card-3) !important;
  }

  box-shadow: var(--gauzy-shadow);
  border-radius: $default-radius;
  margin-bottom: 4px;
  padding: 15px 20px;
  background: nb-theme(gauzy-card-1) !important;

  .block-content {
    .block-info {
      width: 100%;
    }

    .col {
      display: flex;
      align-items: center;
    }

    .image-container {
      width: 70px;
      height: 63px;
      display: inline-flex;
      justify-content: center;
      margin-right: 15px;
      margin-left: 15px;
    }

    img {
      height: 100%;
      width: 70px;
      border-radius: var(--button-rectangle-border-radius);
      object-fit: cover;
    }

    .row {
      cursor: pointer;
    }
  }
}

:host {
  nb-card {
    background: nb-theme(background-basic-color-1);
    box-shadow: 0px 6px 20px 0px rgb(0 0 0 / 5%);
    margin-bottom: 0;
  }

  .card-body {
    @include nb-ltr(padding-right, 0.5rem);
    @include nb-rtl(padding-left, 0.5rem);
  }

  nb-card-header {
    font-size: 14px;
    font-weight: 600;
    line-height: 16px;
    letter-spacing: -0.009em;
    color: var(--gauzy-text-color-2);
    padding-bottom: 0;
  }

  @include card_overrides(
    auto,
    unset,
    0 $default-radius $default-radius $default-radius
  );

  .card,
  .card-header:first-child {
    border-radius: 0 $default-radius 0 0;
  }

  background-color: rgba(50, 50, 50, 0.02);

  .card {
    background-color: var(--gauzy-card-2);

    .card-header {
      background-color: unset;
    }
  }
}
.body-header{
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
}
