<nb-card class="card">
	<nb-card-header class="card-header">
		<div class="row">
			<div class="col-auto">
				<ng-container *ngIf="selectedDateRange?.startDate && selectedDateRange?.endDate">
					<h4>
						<ngx-header-title>
							{{ headerTitle }}
							{{ 'TIMESHEET.TIME_TRACKING' | translate }}
						</ngx-header-title>
					</h4>
					<ngx-date-range-title
						[start]="selectedDateRange?.startDate"
						[end]="selectedDateRange?.endDate"
						[format]="'dddd, LL'"
					></ngx-date-range-title>
				</ng-container>
			</div>
			<div class="mb-4 ml-auto col-auto d-flex align-items-center">
				<ga-timezone-filter
					(timeFormatChange)="timeFormatChanged($event)"
					(timeZoneChange)="timeZoneChanged($event)"
				></ga-timezone-filter>
				<div class="mr-2"></div>
				<button
					class="manage-widget"
					status="basic"
					size="small"
					nbPopoverPlacement="bottom"
					[nbPopover]="widgetManager"
					nbPopoverTrigger="click"
					nbButton
				>
					{{ 'BUTTONS.MANAGE_WIDGET' | translate }}
					<nb-icon icon="more-vertical-outline"></nb-icon>
				</button>
			</div>
		</div>
		<div class="row">
			<div class="mb-2 ml-auto col-auto d-flex align-items-center">
				<nb-toggle
					class="custom-toggle mr-3 ml-3"
					size="small"
					[(ngModel)]="autoRefresh"
					(checkedChange)="setAutoRefresh($event)"
				>
					{{ 'BUTTONS.AUTO_REFRESH' | translate }}
				</nb-toggle>
				<button
					[disabled]="autoRefresh"
					size="small"
					type="button"
					nbButton
					outline
					status="basic"
					(click)="logs$.next(true)"
				>
					<nb-icon icon="sync-outline"></nb-icon>
					{{ 'BUTTONS.REFRESH' | translate }}
				</button>
			</div>
		</div>
	</nb-card-header>
	<nb-card-body class="card-body">
		<div class="container">
			<ga-widget-layout [widgets]="widgetsRef"></ga-widget-layout>
			<ga-window-layout [windows]="windowsRef"></ga-window-layout>
		</div>
	</nb-card-body>
</nb-card>
<!-- widgets -->
<ng-template #widget>
	<ng-container *ngIf="!hideEmployeeBlock">
		<ng-container *ngxPermissionsOnly="PermissionsEnum.CHANGE_SELECTED_EMPLOYEE">
			<nb-card [nbSpinner]="countsLoading" nbSpinnerSize="giant" nbSpinnerStatus="primary">
				<nb-card-body>
					<div class="header-widget">
						<div class="title">
							{{ 'TIMESHEET.MEMBERS_WORKED' | translate }}
						</div>
					</div>
					<div class="h1">
						{{ counts?.employeesCount || 0 }}
					</div>
					<div class="counter-container">
						<gauzy-counter-point
							[total]="employeesCount"
							[value]="counts?.employeesCount || 0"
							[color]="'#0088FE'"
						></gauzy-counter-point>
					</div>
				</nb-card-body>
			</nb-card>
		</ng-container>
	</ng-container>
</ng-template>
<ng-template #widget>
	<ng-container *ngIf="!hideProjectBlock">
		<nb-card [nbSpinner]="countsLoading" nbSpinnerSize="giant" nbSpinnerStatus="primary">
			<nb-card-body>
				<div class="header-widget">
					<div class="title">
						{{ 'TIMESHEET.PROJECTS_WORKED' | translate }}
					</div>
				</div>
				<div class="h1">
					{{ counts?.projectsCount || 0 }}
				</div>
				<div class="counter-container">
					<gauzy-counter-point
						[total]="projectCount"
						[value]="counts?.projectsCount || 0"
						[color]="'#00D68F'"
					></gauzy-counter-point>
				</div>
			</nb-card-body>
		</nb-card>
	</ng-container>
</ng-template>
<ng-template #widget>
	<nb-card [nbSpinner]="countsLoading" nbSpinnerSize="giant" nbSpinnerStatus="primary">
		<nb-card-body>
			<div class="header-widget">
				<div class="title">
					{{ 'TIMESHEET.TODAY_ACTIVITY' | translate }}
				</div>
			</div>
			<div class="h1">{{ counts?.todayActivities || 0 }}%</div>
			<div class="counter-container">
				<gauzy-counter-point [progress]="true" [value]="counts?.todayActivities || 0"></gauzy-counter-point>
			</div>
		</nb-card-body>
	</nb-card>
</ng-template>
<ng-template #widget>
	<nb-card [nbSpinner]="countsLoading" nbSpinnerSize="giant" nbSpinnerStatus="primary">
		<nb-card-body>
			<div class="header-widget">
				<div class="title">
					{{ 'TIMESHEET.WORKED_TODAY' | translate }}
				</div>
			</div>
			<div class="h1">
				{{ counts?.todayDuration || 0 | durationFormat }}
			</div>
			<div class="counter-container">
				<gauzy-counter-point
					[total]="period"
					[value]="counts?.todayDuration || 0"
					[color]="'#00D68F'"
				></gauzy-counter-point>
			</div>
		</nb-card-body>
	</nb-card>
</ng-template>
<ng-template #widget>
	<nb-card [nbSpinner]="countsLoading" nbSpinnerSize="giant" nbSpinnerStatus="primary">
		<nb-card-body>
			<ng-container [ngSwitch]="selectedPeriod">
				<div class="header-widget">
					<div class="title">
						<ng-template [ngSwitchCase]="RangePeriod.PERIOD">
							{{ 'TIMESHEET.WORKED_OVER_PERIOD' | translate }}
						</ng-template>
						<ng-template [ngSwitchCase]="RangePeriod.DAY">
							{{ 'TIMESHEET.WORKED_FOR_DAY' | translate }}
						</ng-template>
						<ng-template [ngSwitchDefault]>
							{{
								(isCurrentWeek() ? 'TIMESHEET.WORKED_THIS_WEEK' : 'TIMESHEET.WORKED_FOR_WEEK')
									| translate
							}}
						</ng-template>
					</div>
				</div>
			</ng-container>
			<div class="h1">
				{{ counts?.weekDuration || 0 | durationFormat }}
			</div>
			<div class="counter-container">
				<gauzy-counter-point
					[total]="period"
					[value]="counts?.weekDuration || 0"
					[color]="'#00D68E'"
				></gauzy-counter-point>
			</div>
		</nb-card-body>
	</nb-card>
</ng-template>
<ng-template #widget>
	<nb-card [nbSpinner]="countsLoading" nbSpinnerSize="giant" nbSpinnerStatus="primary">
		<nb-card-body>
			<ng-container [ngSwitch]="selectedPeriod">
				<div class="header-widget">
					<div class="title">
						<ng-template [ngSwitchCase]="RangePeriod.PERIOD">
							{{ 'TIMESHEET.ACTIVITY_OVER_PERIOD' | translate }}
						</ng-template>
						<ng-template [ngSwitchCase]="RangePeriod.DAY">
							{{ 'TIMESHEET.ACTIVITY_FOR_DAY' | translate }}
						</ng-template>
						<ng-template [ngSwitchDefault]>
							{{ 'TIMESHEET.ACTIVITY_FOR_WEEK' | translate }}
						</ng-template>
					</div>
				</div>
			</ng-container>
			<div class="h1">{{ counts?.weekActivities || 0 }}%</div>
			<div class="counter-container">
				<gauzy-counter-point [progress]="true" [value]="counts?.weekActivities || 0"></gauzy-counter-point>
			</div>
		</nb-card-body>
	</nb-card>
</ng-template>
<!-- Windows -->
<ng-template #window>
	<nb-card [nbSpinner]="timeSlotLoading" nbSpinnerSize="giant" nbSpinnerStatus="primary">
		<nb-card-header>{{ 'TIMESHEET.RECENT_ACTIVITIES' | translate }}</nb-card-header>
		<nb-card-body class="custom-card-body-inner">
			<ng-template [ngIf]="timeSlotEmployees?.length > 0" [ngIfElse]="noScreenshot">
				<div class="row" *ngFor="let employee of timeSlotEmployees">
					<ng-container *ngIf="employee?.timeSlots?.length > 0">
						<div class="col">
							<div class="hour-label mb-3">
								<ng-template [ngxPermissionsOnly]="PermissionsEnum.CHANGE_SELECTED_EMPLOYEE">
									<ngx-avatar
										class="avatar-dashboard activity"
										[size]="'sm'"
										[name]="employee?.user?.name"
										[src]="employee?.user?.imageUrl"
										[appendCaption]="'TIMESHEET.LAST_WORKED' | translate"
										[caption]="(employee?.timeSlots)[0]?.startedAt | utcToLocal | dateFormat"
										[id]="employee?.id"
										[employee]="employee"
									></ngx-avatar>
								</ng-template>
								<div></div>
								<div class="button-container">
									<div class="swiper-button-container">
										<div class="swiper-button" (click)="slidePrev(swiper)">
											<i class="fas fa-angle-left"></i>
										</div>
										<div class="swiper-button" (click)="slideNext(swiper)">
											<i class="fas fa-angle-right"></i>
										</div>
									</div>
									<div
										*ngxPermissionsOnly="PermissionsEnum.CHANGE_SELECTED_EMPLOYEE"
										class="view-all"
									>
										<ng-container>
											<button
												nbButton
												status="primary"
												size="small"
												outline
												(click)="redirectToScreenshots(employee)"
											>
												{{ 'BUTTONS.VIEW_ALL' | translate }}
											</button>
										</ng-container>
									</div>
								</div>
							</div>
							<div>
								<swiper
									[slidesPerView]="3"
									[spaceBetween]="16"
									[slidesPerGroup]="3"
									[loop]="false"
									[loopFillGroupWithBlank]="true"
									[pagination]="{
										clickable: true
									}"
									[navigation]="true"
									[virtual]="true"
									#swiper
									class="m-swiper"
								>
									<ng-container *ngFor="let timeSlot of employee.timeSlots">
										<ng-template swiperSlide>
											<ngx-screenshots-item
												[timeFormat]="filters?.timeFormat"
												[timezone]="filters?.timeZone"
												[timeSlot]="timeSlot"
												(delete)="onDelete()"
												[multiple]="false"
												[employeeId]="timeSlot?.employee?.id"
											></ngx-screenshots-item>
										</ng-template>
									</ng-container>
								</swiper>
							</div>
						</div>
					</ng-container>
				</div>
			</ng-template>
			<ng-template #noScreenshot>
				<ng-container *ngIf="!timeSlotLoading" [ngSwitch]="selectedPeriod">
					<div class="text-center">
						<ng-template [ngSwitchCase]="RangePeriod.DAY">
							{{ 'TIMESHEET.NO_SCREENSHOT_DAY' | translate }}
						</ng-template>
						<ng-template [ngSwitchCase]="RangePeriod.WEEK">
							{{ 'TIMESHEET.NO_SCREENSHOT_WEEK' | translate }}
						</ng-template>
						<ng-template [ngSwitchCase]="RangePeriod.PERIOD">
							{{ 'TIMESHEET.NO_SCREENSHOT_PERIOD' | translate }}
						</ng-template>
					</div>
				</ng-container>
			</ng-template>
		</nb-card-body>
	</nb-card>
</ng-template>
<ng-template #window>
	<nb-card [nbSpinner]="manualTimeLoading" nbSpinnerSize="giant" nbSpinnerStatus="primary">
		<nb-card-header class="nb-card-header">
			{{ 'TIMESHEET.MANUAL_TIME' | translate }}
		</nb-card-header>
		<ng-template [ngIf]="manualTimes?.length > 0" [ngIfElse]="noManualTimes">
			<nb-card-body class="custom-card-body-inner-list">
				<div class="custom-card-button">
					<button nbButton status="primary" outline size="small" (click)="redirectToManualTimeReport()">
						{{ 'BUTTONS.VIEW_REPORT' | translate }}
					</button>
				</div>
				<nb-list>
					<nb-list-item>
						<div class="w-100">
							<div class="row py-2 font-weight-bold">
								<div class="col-sm-3">
									{{ 'TIMESHEET.MEMBER' | translate }}
								</div>
								<div class="col">
									{{ 'TIMESHEET.PROJECT' | translate }}
								</div>
								<div class="col">
									{{ 'TIMESHEET.DURATION' | translate }}
								</div>
								<div class="col">
									{{ 'TIMESHEET.DATE' | translate }}
								</div>
							</div>
						</div>
					</nb-list-item>
					<nb-list-item *ngFor="let manualTime of manualTimes">
						<div class="w-100">
							<div class="row">
								<div class="col-sm-3">
									<ngx-avatar
										[name]="manualTime?.user?.name"
										[src]="manualTime?.user?.imageUrl"
										[id]="manualTime?.employeeId"
										[employee]="manualTime?.employee"
										size="sm"
										class="avatar-dashboard"
									></ngx-avatar>
								</div>
								<div class="col project-name">
									{{ manualTime.project.name }}
								</div>
								<div class="col">
									{{ manualTime.duration | durationFormat }}
								</div>
								<div class="col">
									{{ manualTime.startedAt | dateFormat }}
								</div>
							</div>
						</div>
					</nb-list-item>
				</nb-list>
			</nb-card-body>
		</ng-template>
		<ng-template #noManualTimes>
			<ng-container *ngIf="!manualTimeLoading" [ngSwitch]="selectedPeriod">
				<div class="text-center p-3">
					<ng-template [ngSwitchCase]="RangePeriod.DAY">
						{{ 'TIMESHEET.NO_MANUAL_TIME_DAY' | translate }}
					</ng-template>
					<ng-template [ngSwitchCase]="RangePeriod.WEEK">
						{{ 'TIMESHEET.NO_MANUAL_TIME_WEEK' | translate }}
					</ng-template>
					<ng-template [ngSwitchCase]="RangePeriod.PERIOD">
						{{ 'TIMESHEET.NO_MANUAL_TIME_PERIOD' | translate }}
					</ng-template>
				</div>
			</ng-container>
		</ng-template>
	</nb-card>
</ng-template>
<ng-template #window>
	<nb-card [nbSpinner]="tasksLoading" nbSpinnerSize="giant" nbSpinnerStatus="primary">
		<nb-card-header class="nb-card-header">
			{{ 'TIMESHEET.TASKS' | translate }}
		</nb-card-header>
		<ng-template [ngIf]="tasks?.length > 0" [ngIfElse]="noTasks">
			<nb-card-body class="custom-card-body-inner-list">
				<div class="custom-card-button">
					<button size="small" nbButton status="primary" outline (click)="redirectToTask()">
						{{ 'BUTTONS.VIEW_ALL' | translate }}
					</button>
				</div>
				<nb-list>
					<nb-list-item *ngFor="let task of tasks">
						<div class="w-100">
							<div class="row align-items-center">
								<div class="col-5 project-name text-left">
									{{ task.title }}
								</div>
								<div class="col-4 text-center">
									<div class="d-flex align-items-center">
										{{ task.durationPercentage }}%
										<nb-progress-bar
											class="mb-1 ml-3 mr-3 w-100 custom-progress"
											[value]="task.durationPercentage"
											[status]="progressStatus(task.durationPercentage)"
											[displayValue]="true"
											size="tiny"
										>
										</nb-progress-bar>
									</div>
								</div>
								<div class="col text-right">
									{{ task.duration | durationFormat }}
								</div>
							</div>
						</div>
					</nb-list-item>
				</nb-list>
			</nb-card-body>
		</ng-template>
		<ng-template #noTasks>
			<ng-container *ngIf="!tasksLoading" [ngSwitch]="selectedPeriod">
				<div class="text-center p-3">
					<ng-template [ngSwitchCase]="RangePeriod.DAY">
						{{ 'TIMESHEET.NO_TASK_ACTIVITY_DAY' | translate }}
					</ng-template>
					<ng-template [ngSwitchCase]="RangePeriod.WEEK">
						{{ 'TIMESHEET.NO_TASK_ACTIVITY_WEEK' | translate }}
					</ng-template>
					<ng-template [ngSwitchCase]="RangePeriod.PERIOD">
						{{ 'TIMESHEET.NO_TASK_ACTIVITY_PERIOD' | translate }}
					</ng-template>
				</div>
			</ng-container>
		</ng-template>
	</nb-card>
</ng-template>
<ng-template #window>
	<nb-card [nbSpinner]="projectsLoading" nbSpinnerSize="giant" nbSpinnerStatus="primary">
		<nb-card-header>{{ 'TIMESHEET.PROJECTS' | translate }}</nb-card-header>
		<ng-template [ngIf]="projects?.length > 0" [ngIfElse]="noProjects">
			<nb-card-body class="custom-card-body-inner-list">
				<nb-list>
					<nb-list-item *ngFor="let project of projects">
						<div class="w-100">
							<div class="row align-items-center">
								<div class="col-5 project-name text-left">
									{{ project.name }}
								</div>
								<div class="col-4 text-center">
									<div class="d-flex align-items-center">
										{{ project.durationPercentage }}%
										<nb-progress-bar
											class="mb-1 ml-3 mr-3 w-100 custom-progress"
											[value]="project.durationPercentage"
											[status]="progressStatus(project.durationPercentage)"
											[displayValue]="true"
											size="tiny"
										>
										</nb-progress-bar>
									</div>
								</div>
								<div class="col text-right">
									{{ project.duration | durationFormat }}
								</div>
							</div>
						</div>
					</nb-list-item>
				</nb-list>
			</nb-card-body>
		</ng-template>
		<ng-template #noProjects>
			<ng-container *ngIf="!projectsLoading" [ngSwitch]="selectedPeriod">
				<div class="text-center p-3">
					<ng-template [ngSwitchCase]="RangePeriod.DAY">
						{{ 'TIMESHEET.NO_PROJECT_ACTIVITY_DAY' | translate }}
					</ng-template>
					<ng-template [ngSwitchCase]="RangePeriod.WEEK">
						{{ 'TIMESHEET.NO_PROJECT_ACTIVITY_WEEK' | translate }}
					</ng-template>
					<ng-template [ngSwitchCase]="RangePeriod.PERIOD">
						{{ 'TIMESHEET.NO_PROJECT_ACTIVITY_PERIOD' | translate }}
					</ng-template>
				</div>
			</ng-container>
		</ng-template>
	</nb-card>
</ng-template>
<ng-template #window>
	<nb-card [nbSpinner]="activitiesLoading" nbSpinnerSize="giant" nbSpinnerStatus="primary">
		<nb-card-header class="nb-card-header">{{ 'TIMESHEET.APPS_URLS' | translate }} </nb-card-header>
		<ng-template [ngIf]="activities?.length > 0" [ngIfElse]="noActivities">
			<nb-card-body class="custom-card-body-inner-list">
				<div class="custom-card-button">
					<button nbButton status="primary" size="small" outline (click)="redirectToAppUrlReport()">
						{{ 'BUTTONS.VIEW_REPORT' | translate }}
					</button>
				</div>
				<nb-list>
					<nb-list-item *ngFor="let activity of activities">
						<div class="w-100">
							<ngx-activity-item
								class="tracking-progress"
								[item]="activity"
								[isDashboard]="true"
							></ngx-activity-item>
						</div>
					</nb-list-item>
				</nb-list>
			</nb-card-body>
		</ng-template>
		<ng-template #noActivities>
			<ng-container *ngIf="!activitiesLoading" [ngSwitch]="selectedPeriod">
				<div class="text-center p-3">
					<ng-template [ngSwitchCase]="RangePeriod.DAY">
						{{ 'TIMESHEET.NO_APP_URL_ACTIVITY_DAY' | translate }}
					</ng-template>
					<ng-template [ngSwitchCase]="RangePeriod.WEEK">
						{{ 'TIMESHEET.NO_APP_URL_ACTIVITY_WEEK' | translate }}
					</ng-template>
					<ng-template [ngSwitchCase]="RangePeriod.PERIOD">
						{{ 'TIMESHEET.NO_APP_URL_ACTIVITY_PERIOD' | translate }}
					</ng-template>
				</div>
			</ng-container>
		</ng-template>
	</nb-card>
</ng-template>
<ng-template #window>
	<nb-card
		class="member-list"
		[nbSpinner]="memberLoading"
		nbSpinnerSize="giant"
		nbSpinnerStatus="primary"
		*ngxPermissionsOnly="PermissionsEnum.CHANGE_SELECTED_EMPLOYEE"
	>
		<nb-card-header>{{ 'TIMESHEET.MEMBERS' | translate }}</nb-card-header>
		<nb-card-body class="custom-card-body-inner-list">
			<ng-template [ngIf]="members?.length > 0" [ngIfElse]="noMember">
				<div class="list">
					<nb-list>
						<nb-list-item>
							<div class="w-100">
								<div class="row font-weight-bold">
									<div class="col-3">
										{{ 'TIMESHEET.MEMBER_INFO' | translate }}
									</div>
									<div class="col-3 text-center">
										{{ 'TIMESHEET.TODAY' | translate }}
									</div>
									<div class="col text-left">
										{{
											(selectedPeriod === RangePeriod.PERIOD
												? 'TIMESHEET.OVER_PERIOD'
												: 'TIMESHEET.THIS_WEEK'
											) | translate
										}}
									</div>
								</div>
							</div>
						</nb-list-item>
						<nb-list-item *ngFor="let member of members">
							<div class="w-100">
								<div class="row">
									<div class="col-3">
										<ngx-avatar
											[name]="member?.user?.name"
											[src]="member?.user?.imageUrl"
											[id]="member?.id"
											[employee]="member"
											size="sm"
											class="avatar-dashboard"
										>
										</ngx-avatar>
									</div>
									<div class="col-3 text-center">
										<div class="activity">
											<div class="duration">
												{{ member?.todayTime?.duration || 0 | durationFormat }}
											</div>
											<div class="activity-percentage">
												<nb-badge
													[status]="progressStatus(member?.todayTime?.overall)"
													[text]="(member?.todayTime?.overall || 0) + '%'"
												></nb-badge>
											</div>
										</div>
									</div>
									<div class="col text-center">
										<div [ngClass]="!isMoreThanWeek() ? 'd-flex' : ''">
											<div class="activity text-center">
												<div class="duration">
													{{ member?.weekTime?.duration || 0 | durationFormat }}
												</div>
												<div class="activity-percentage">
													<nb-badge
														[status]="progressStatus(member?.weekTime?.overall)"
														[text]="(member?.weekTime?.overall || 0) + '%'"
													></nb-badge>
												</div>
											</div>
											<ng-container *ngIf="!isMoreThanWeek()">
												<div class="member-weekly-activity-graph">
													<div
														*ngFor="let weekHour of member.weekHours"
														class="bar-graph-entry"
														[style.height]="weekHour.duration + '%'"
													></div>
												</div>
											</ng-container>
										</div>
									</div>
								</div>
							</div>
						</nb-list-item>
					</nb-list>
				</div>
			</ng-template>
			<ng-template #noMember>
				<ng-container *ngIf="!memberLoading" [ngSwitch]="selectedPeriod">
					<div class="text-center">
						<ng-template [ngSwitchCase]="RangePeriod.DAY">
							{{ 'TIMESHEET.NO_MEMBER_ACTIVITY_DAY' | translate }}
						</ng-template>
						<ng-template [ngSwitchCase]="RangePeriod.WEEK">
							{{ 'TIMESHEET.NO_MEMBER_ACTIVITY_WEEK' | translate }}
						</ng-template>
						<ng-template [ngSwitchCase]="RangePeriod.PERIOD">
							{{ 'TIMESHEET.NO_MEMBER_ACTIVITY_PERIOD' | translate }}
						</ng-template>
					</div>
				</ng-container>
			</ng-template>
		</nb-card-body>
	</nb-card>
</ng-template>
<!-- Manage Widgets -->
<ng-template #widgetManager>
	<div class="widget-popover">
		<div class="category">
			<div class="view">
				{{ 'TIMESHEET.VIEW_WIDGETS' | translate }}
				<button class="manage-widget undo" (click)="undo()" nbButton><i class="fas fa-undo"></i>Undo</button>
			</div>
			<ng-container *ngIf="widgets.length > 0">
				<ng-container *ngFor="let widget of widgets">
					<div (click)="updateWidgetVisibility(widget)" class="title">
						<i
							[ngStyle]="{
								visibility: widget.hide ? 'hidden' : 'visible'
							}"
							class="fas fa-check"
						></i>
						<div>
							{{ titleMapper(widget.position) | translate }}
						</div>
					</div>
				</ng-container>
			</ng-container>
		</div>
		<div class="line"></div>
		<div class="category">
			<div class="view">
				{{ 'TIMESHEET.VIEW_WINDOWS' | translate
				}}<button class="manage-widget undo" (click)="undo(true)" nbButton>
					<i class="fas fa-undo"></i>Undo
				</button>
			</div>
			<ng-container *ngIf="windows.length > 0">
				<ng-container *ngFor="let window of windows">
					<div (click)="updateWindowVisibility(window)" class="title">
						<i
							[ngStyle]="{
								visibility: window.hide ? 'hidden' : 'visible'
							}"
							class="fas fa-check"
						></i>
						<div>
							{{ titleMapper(window.position, false) | translate }}
						</div>
					</div>
				</ng-container>
			</ng-container>
		</div>
	</div>
</ng-template>
