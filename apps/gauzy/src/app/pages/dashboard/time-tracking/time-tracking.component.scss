@use 'gauzy/_gauzy-cards' as *;

:host {
  background-color: rgba(50, 50, 50, 0.02);

  ::ng-deep {
    nb-progress-bar {
      .progress-value {
        span {
          display: none;
        }
      }
    }
    .swiper {
      width: 100%;
      height: 100%;
      padding-bottom: 1rem;
    }

    .swiper-slide {
      text-align: center;
      /* Center slide text vertically */
      display: -webkit-box;
      display: -ms-flexbox;
      display: -webkit-flex;
      display: flex;
      -webkit-box-pack: center;
      -ms-flex-pack: center;
      -webkit-justify-content: center;
      justify-content: center;
      -webkit-box-align: center;
      -ms-flex-align: center;
      -webkit-align-items: center;
      align-items: center;
    }

    .swiper-slide {
      display: block;
      width: 100%;
      height: 100%;
    }

    .swiper-button-next,
    .swiper-button-prev {
      display: none;
    }

    .swiper-button {
      padding: 2px 7.5px;
      border: 2px solid var(--gauzy-background-transparent);
      color: var(--text-primary-color);
      border-radius: var(--button-rectangle-border-radius);
      i {
        font-weight: 900;
      }
      height: fit-content;
    }

    .swiper-button-container {
      display: flex;
      gap: 4px;
    }

    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 8px;
      font-weight: 900;
    }

    .swiper-pagination-bullet-active {
      background-color: var(--text-primary-color);
    }

    .swiper-horizontal > .swiper-pagination-bullets,
    .swiper-pagination-bullets.swiper-pagination-horizontal,
    .swiper-pagination-custom,
    .swiper-pagination-fraction {
      bottom: -2px;
    }
  }

  nb-card.member-list {
    nb-card-body {
      padding: 8px;
      .list {
        max-height: 65vh;
        overflow-y: auto;
      }
    }
  }

  .nb-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .card-header {
    background-color: unset;
  }

  .card {
    background-color: var(--gauzy-card-2);
  }

  .button-container {
    display: flex;
    gap: 1rem;
  }

  [nbButton].appearance-outline.status-primary {
    background-color: unset;
    font-size: 12px;
    font-weight: 600;
    line-height: 15px;
    letter-spacing: 0em;
    border-color: var(--gauzy-background-transparent);
    color: var(--button-outline-primary-text-color);
    border-width: 2px;
    padding: 4px 10px;
  }
}

.hour-label {
  display: flex;
  justify-content: space-between;
}

.link-text {
  cursor: pointer;
  text-decoration: none;
  color: #1e6bb8;
  font-size: small;
}

.member-weekly-activity-graph {
  display: flex;
  align-items: flex-end;
  margin-left: 15px;
  min-height: 30px;

  .bar-graph-entry {
    width: 7px;
    margin-right: 3px;
    background-color: #0095ff;
    min-height: 1px;
  }
}

nb-badge {
  position: unset;
  display: table;
  margin: 10px auto 0;
}

.filter {
  width: 100%;
}

:host {
  nb-card {
    background-color: var(--background-basic-color-1);
    box-shadow: 0px 6px 20px 0px rgb(0 0 0 / 5%);
    border-radius: var(--border-radius);
  }

  nb-card-body,
  nb-list {
    border-radius: 0 0 var(--border-radius) var(--border-radius);
  }

  @include card_overrides(
    unset,
    calc($default-card-height - 1.5rem),
    0 $default-radius $default-radius $default-radius
  );

  ngx-activity-item.tracking-progress ::ng-deep {
    .progress-container {
      height: 5px !important;
    }
  }

  nb-progress-bar.custom-progress ::ng-deep {
    .progress-container {
      height: 5px !important;
    }
  }

  ::ng-deep {
    .toggle-label {
      margin-bottom: 0;
    }
  }

  .custom-toggle {
    ::ng-deep {
      span.text {
        color: nb-theme(text-primary-color);
      }
    }
  }

  .card-body {
    background-color: var(--gauzy-card-2);
  }

  .project-name {
    color: var(--gauzy-text-color-1);
    font-size: 14px;
    font-weight: 600;
  }
}

.custom-card-body-inner-list {
  padding: 0 9px;
  overflow: unset;
  .custom-card-button {
    display: flex;
    justify-content: flex-end;
    margin: 1rem 15px 0 15px;
  }
}

.font-weight-bold {
  font-weight: 600;
  color: var(--gauzy-text-color-2);
}
nb-card-header {
  font-size: 14px;
  font-weight: 600;
  line-height: 16px;
  letter-spacing: -0.009em;
  color: var(--gauzy-text-color-2);
}
.custom-container {
  display: grid;
  -webkit-box-align: center;
  align-items: center;
  -webkit-box-pack: start;
  justify-content: start;
  column-gap: 1rem;
  gap: 1rem;
  grid-template-columns: repeat(auto-fill, minmax(12rem, 1fr));
}

.custom-card-body-inner,
.custom-card-body-inner-list {
  background-color: nb-theme(gauzy-card-2);
}

nb-card-body > .header-widget {
  font-size: 16px;
  font-weight: 400;
  line-height: 16px;
  letter-spacing: -0.009em;
  color: var(--gauzy-text-color-2);
  margin-bottom: 15px;
  nb-icon {
    font-size: 11px;
  }
}

.counter-container {
  width: 71%;
}

.manage-widget {
  font-size: 12px;
  font-weight: 400;
  &[nbButton] {
    color: var(--gauzy-text-color-1);
    background-color: var(--gauzy-card-1);
  }
  nb-icon {
    height: 11px;
    width: 11px;
  }
  &.undo {
    font-size: 10px;
    font-weight: 600;
    &[nbButton].appearance-filled.status-basic {
      color: var(--text-primary-color);
    }
    display: flex;
    align-items: center;
    gap: 0.25rem;
  }
}

.widget-popover {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 14px;
  gap: 21px;
  min-width: 219px;
  .category {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 14px;
    width: 100%;
    .view {
      font-size: 10px;
      font-weight: 600;
      line-height: 12px;
      letter-spacing: 0em;
      color: rgba(126, 126, 143, 0.5);
      align-items: center;
      display: flex;
      justify-content: space-between;
      gap: 1rem;
      width: 100%;
    }
    .title {
      font-size: 12px;
      font-weight: 400;
      line-height: 15px;
      letter-spacing: 0em;
      color: var(--gauzy-text-color-2);
      display: flex;
      align-items: center;
      gap: 10px;
      cursor: pointer;
      width: 100%;
    }
  }
  .line {
    border-bottom: 0.5px solid rgba(126, 126, 143, 0.25);
    width: 100%;
  }
}

:host .container {
  max-width: unset;
  border-radius: 0 0 $default-radius $default-radius;
  overflow: auto;
  height: 100%;
  @include nb-ltr(padding, 0 0.5rem 0 0);
  @include nb-rtl(padding, 0 0 0 0.5rem);
}

:host .card-body {
  @include nb-ltr(padding, 1rem 0.5rem 1rem 18px);
  @include nb-rtl(padding, 1rem 18px 1rem 0.5rem);
  border-radius: 0 0 $default-radius $default-radius;
  height: calc($default-card-height + 1.5rem) !important;
}
