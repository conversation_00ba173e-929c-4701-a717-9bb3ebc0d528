@use 'gauzy/_gauzy-cards' as *;

.header {
	background-color: unset;
	.header-container {
		display: flex;
		justify-content: space-between;

		.employee-info,
		.org-info {
			display: flex;

			.employee-image,
			.org-image {
				max-width: 70px;
				max-height: 70px;
				border-radius: 13px;
				margin-right: 24px;
				margin-left: 24px;
			}
		}

		.employee-details,
		.org-details {
			display: flex;
			flex-direction: column;
			justify-content: center;

			.employee-name,
			.org-name {
				font-weight: bold;
				font-size: 18px;
			}

			.employee-position,
			.org-position {
				font-size: 14px;
			}

			.edit-icon {
				cursor: pointer;
			}
		}

		.employee-salary {
			font-size: 24px;
			font-weight: bold;
			color: #0091ff;
		}
	}
}

.open {
	cursor: pointer;
}

.body {
	display: flex;
	justify-content: space-between;

	.half-content {
		width: 49%;
		display: flex;
		flex-direction: column;
		align-items: stretch;
		ga-info-block {
			border-radius: var(--border-radius);
		}
	}
	.bonus {
		display: flex;
		flex-direction: column;
		align-items: flex-end;
		width: 100%;

		.bonus-value {
			margin-right: 39px;
			display: flex;
			flex-direction: column;
			align-items: flex-end;

			:first-child {
				text-align: right;
				font-size: 12px;
				margin-bottom: 15px;
				margin-right: 3px;
			}

			:last-child {
				font-size: 36px;
				font-weight: bold;
				color: #0091ff;
			}

			.negative-bonus-color {
				color: red;
			}
		}

		.bonus-disclaimer {
			margin-top: 15px;
			margin-right: 39px;
			font-size: 0.7rem;
			width: 215px;
		}
	}
}
$height: calc(
  100vh - nb-theme(header-height) - nb-theme(footer-height) - 9.5rem
);
:host {
	@include nb-card_overrides(
    auto,
    $height,
    0 $default-radius $default-radius $default-radius
  );
  nb-card,
  nb-card-body{
	background-color: var(--gauzy-card-2);
  }
}
