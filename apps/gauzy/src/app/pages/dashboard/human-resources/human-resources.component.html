<nb-card
	[nbSpinner]="loading"
	nbSpinnerStatus="primary"
	*ngIf="selectedEmployee && selectedEmployee.id"
>
	<nb-card-header class="header">
		<nb-alert
			outline="danger"
			*ngIf="incomePermissionsError || expensePermissionError"
		>
			{{ 'ORGANIZATIONS_PAGE.PERMISSIONS.INSUFFICIENT' | translate }}
			<span *ngIf="incomePermissionsError">{{
				'ORGANIZATIONS_PAGE.PERMISSIONS.ORG_INCOMES_VIEW' | translate
			}}</span>
			<span *ngIf="expensePermissionError">{{
				'ORGANIZATIONS_PAGE.PERMISSIONS.ORG_EXPENSES_VIEW' | translate
			}}</span>
		</nb-alert>
		<div class="header-container">
			<div class="employee-info">
				<img
					class="employee-image"
					[src]="selectedEmployee.imageUrl"
					alt="Employee Avatar"
				/>

				<div class="employee-details">
					<span class="employee-name">
						{{ selectedEmployee.fullName | truncate : 22 }}
						<nb-icon
							class="mr-1 edit-icon"
							icon="edit-outline"
							(click)="edit()"
						></nb-icon
					></span>
					<div
						class="employee-position"
						[innerText]="employeePosition"
					></div>
				</div>
			</div>

			<div>
				<div
					*ngIf="salary !== 0"
					class="employee-details employee-salary"
				>
					{{ 'DASHBOARD_PAGE.TITLE.SALARY' | translate }}:
					{{
						salary
							| currency: defaultCurrency
							| position: selectedOrganization?.currencyPosition
					}}
				</div>

				<div
					*ngIf="selectedOrganization?.bonusType && averageBonus"
					class="employee-details"
				>
					{{ 'DASHBOARD_PAGE.DEVELOPER.AVERAGE_BONUS' | translate }}:
					{{
						averageBonus
							| currency: defaultCurrency
							| position: selectedOrganization?.currencyPosition
					}}
				</div>
			</div>
		</div>
	</nb-card-header>

	<nb-card-body class="body">
		<div class="half-content">
			<div>
				<!-- Total Income block with non zero income -->
				<ga-info-block
					*ngIf="directIncomeBonus !== 0"
					accordion="true"
					title="{{
						'DASHBOARD_PAGE.DEVELOPER.TOTAL_INCOME' | translate
					}}"
					meta="{{
						'DASHBOARD_PAGE.TITLE.TOTAL_INCOME_CALC'
							| translate
								: {
										totalNonBonusIncome: defaultCurrency
											? (nonBonusIncome || 0
											  | currency: defaultCurrency
											  | position
													: selectedOrganization?.currencyPosition)
											: nonBonusIncome || 0,
										totalBonusIncome: defaultCurrency
											? (directIncomeBonus || 0
											  | currency: defaultCurrency
											  | position
													: selectedOrganization?.currencyPosition)
											: directIncomeBonus || 0
								  }
					}}"
					value="{{
						defaultCurrency
							? (income || 0
							  | currency: defaultCurrency
							  | position
									: selectedOrganization?.currencyPosition)
							: income || 0
					}}"
					color="#089c17"
					(openInfo)="openHistoryDialog('INCOME')"
				>
					<!-- Expanded Accordion Income info block -->
					<ga-info-block
						*ngIf="directIncomeBonus !== 0"
						listItem="true"
						title="{{ 'INCOME_PAGE.INCOME' | translate }}"
						value="{{
							defaultCurrency
								? (nonBonusIncome
								  | currency: defaultCurrency
								  | position
										: selectedOrganization?.currencyPosition)
								: nonBonusIncome
						}}"
						color="#089c17"
						(openInfo)="openHistoryDialog('NON_BONUS_INCOME')"
					>
					</ga-info-block>
					<!-- Expanded Accordion Direct Income info block -->
					<ga-info-block
						*ngIf="directIncomeBonus !== 0"
						listItem="true"
						title="{{
							'DASHBOARD_PAGE.TITLE.TOTAL_DIRECT_INCOME'
								| translate
						}}"
						meta="{{
							'DASHBOARD_PAGE.TITLE.TOTAL_DIRECT_INCOME_INFO'
								| translate
						}}"
						value="{{
							defaultCurrency
								? (directIncomeBonus
								  | currency: defaultCurrency
								  | position
										: selectedOrganization?.currencyPosition)
								: directIncomeBonus
						}}"
						color="#089c17"
						(openInfo)="openHistoryDialog('BONUS_INCOME')"
					>
					</ga-info-block>
				</ga-info-block>
				<!-- Total Income block with no income -->
				<ga-info-block
					*ngIf="directIncomeBonus === 0"
					title="{{
						'DASHBOARD_PAGE.DEVELOPER.TOTAL_INCOME' | translate
					}}"
					value="{{
						defaultCurrency
							? (income || 0
							  | currency: defaultCurrency
							  | position
									: selectedOrganization?.currencyPosition)
							: income || 0
					}}"
					color="#089c17"
					(openInfo)="openHistoryDialog('INCOME')"
				>
				</ga-info-block>

				<!-- Expenses without salary block  -->
				<ga-info-block
					title="{{
						'DASHBOARD_PAGE.TITLE.TOTAL_EXPENSES_WITHOUT_SALARY'
							| translate
					}}"
					meta="{{
						'DASHBOARD_PAGE.TITLE.TOTAL_EXPENSES_WITHOUT_SALARY_CALC'
							| translate
					}}"
					value="{{
						defaultCurrency
							? (expenseWithoutSalary || 0
							  | currency: defaultCurrency
							  | position
									: selectedOrganization?.currencyPosition)
							: expenseWithoutSalary || 0
					}}"
					color="#dbc300"
					(openInfo)="openHistoryDialog('EXPENSES_WITHOUT_SALARY')"
				>
				</ga-info-block>
				<!-- Total Expenses block -->
				<ga-info-block
					title="{{
						'DASHBOARD_PAGE.DEVELOPER.TOTAL_EXPENSES' | translate
					}}"
					meta="{{
						'DASHBOARD_PAGE.TITLE.TOTAL_EXPENSE_CALC' | translate
					}}"
					value="{{
						defaultCurrency
							? (expense || 0
							  | currency: defaultCurrency
							  | position
									: selectedOrganization?.currencyPosition)
							: expense || 0
					}}"
					color="#dbc300"
					(openInfo)="openHistoryDialog('EXPENSES')"
				>
				</ga-info-block>
				<!-- Profit block -->
				<ga-info-block
					title="{{ 'DASHBOARD_PAGE.DEVELOPER.PROFIT' | translate }}"
					meta="{{
						'DASHBOARD_PAGE.DEVELOPER.PROFIT_CALC'
							| translate
								: {
										totalAllIncome: defaultCurrency
											? (income || 0
											  | currency: defaultCurrency
											  | position
													: selectedOrganization?.currencyPosition)
											: income || 0,
										totalExpense: defaultCurrency
											? (expense || 0
											  | currency: defaultCurrency
											  | position
													: selectedOrganization?.currencyPosition)
											: expense || 0
								  }
					}}"
					value="{{
						defaultCurrency
							? (profit || 0
							  | currency: defaultCurrency
							  | position
									: selectedOrganization?.currencyPosition)
							: profit || 0
					}}"
					color="{{ profit >= 0 ? '#089c17' : '#ff7b00' }}"
					blockType="highlight"
					(openInfo)="openProfitDialog()"
				>
				</ga-info-block>
				<!-- Direct Income Bonus block -->
				<ga-info-block
					*ngIf="
						selectedOrganization?.bonusType &&
						directIncomeBonus !== 0
					"
					title="{{
						'DASHBOARD_PAGE.TITLE.TOTAL_DIRECT_BONUS' | translate
					}}"
					meta="{{
						'DASHBOARD_PAGE.TITLE.TOTAL_DIRECT_BONUS_INFO'
							| translate
					}}"
					value="{{
						defaultCurrency
							? (directIncomeBonus || 0
							  | currency: defaultCurrency
							  | position
									: selectedOrganization?.currencyPosition)
							: directIncomeBonus || 0
					}}"
					color="#66de0b"
					(openInfo)="openHistoryDialog('BONUS_INCOME')"
				>
				</ga-info-block>
				<!-- Bonus block: Profit Based Bonus  -->
				<ga-info-block
					*ngIf="
						bonusType === 'PROFIT_BASED_BONUS' &&
						calculatedBonus !== 0
					"
					title="{{
						'DASHBOARD_PAGE.TITLE.TOTAL_PROFIT_BONUS' | translate
					}}"
					meta="{{
						'DASHBOARD_PAGE.TITLE.TOTAL_PROFIT_BONUS_INFO'
							| translate
								: {
										bonusPercentage: bonusPercentage || 0,
										difference: defaultCurrency
											? (profit || 0
											  | currency: defaultCurrency
											  | position
													: selectedOrganization?.currencyPosition)
											: profit || 0
								  }
					}}"
					value="{{
						defaultCurrency
							? (calculatedBonus || 0
							  | currency: defaultCurrency
							  | position
									: selectedOrganization?.currencyPosition)
							: calculatedBonus || 0
					}}"
					color="{{ calculatedBonus >= 0 ? '#66de0b' : '#ff7b00' }}"
				>
				</ga-info-block>
				<!-- Bonus block: Revenue Based Bonus  -->
				<ga-info-block
					*ngIf="
						bonusType === 'REVENUE_BASED_BONUS' &&
						calculatedBonus !== 0
					"
					title="{{
						'DASHBOARD_PAGE.TITLE.TOTAL_INCOME_BONUS' | translate
					}}"
					meta="{{
						'DASHBOARD_PAGE.TITLE.TOTAL_INCOME_BONUS_INFO'
							| translate
								: {
										bonusPercentage: bonusPercentage || 0,
										totalIncome: defaultCurrency
											? (income || 0
											  | currency: defaultCurrency
											  | position
													: selectedOrganization?.currencyPosition)
											: income || 0
								  }
					}}"
					value="{{
						defaultCurrency
							? (calculatedBonus || 0
							  | currency: defaultCurrency
							  | position
									: selectedOrganization?.currencyPosition)
							: calculatedBonus || 0
					}}"
					color="{{ calculatedBonus >= 0 ? '#66de0b' : '#ff7b00' }}"
				>
				</ga-info-block>
				<!-- End of info blocks -->

				<div *ngIf="selectedOrganization?.bonusType" class="bonus">
					<div class="bonus-value">
						<!-- CASE: Total Bonus calculation breakup with non zero income bonus -->
						<div *ngIf="directIncomeBonus !== 0">
							{{
								'DASHBOARD_PAGE.TITLE.TOTAL_BONUS_CALC'
									| translate
										: {
												totalBonusIncome:
													defaultCurrency
														? (directIncomeBonus
														  | currency
																: defaultCurrency
														  | position
																: selectedOrganization?.currencyPosition)
														: directIncomeBonus,
												calculatedBonus: defaultCurrency
													? (calculatedBonus || 0
													  | currency
															: defaultCurrency
													  | position
															: selectedOrganization?.currencyPosition)
													: calculatedBonus || 0
										  }
							}}
						</div>

						<!-- CASE: Total Bonus calculation breakup with zero income bonus -->
						<div
							*ngIf="
								directIncomeBonus === 0 &&
								bonusType === 'REVENUE_BASED_BONUS'
							"
						>
							<!-- Revenue based bonus  -->
							{{
								'DASHBOARD_PAGE.TITLE.TOTAL_INCOME_BONUS_INFO'
									| translate
										: {
												bonusPercentage:
													bonusPercentage || 0,
												totalIncome: defaultCurrency
													? (income || 0
													  | currency
															: defaultCurrency
													  | position
															: selectedOrganization?.currencyPosition)
													: income || 0
										  }
							}}
						</div>
						<div
							*ngIf="
								directIncomeBonus === 0 &&
								bonusType === 'PROFIT_BASED_BONUS'
							"
						>
							<!-- Profit based bonus  -->
							{{
								'DASHBOARD_PAGE.TITLE.TOTAL_PROFIT_BONUS_INFO'
									| translate
										: {
												bonusPercentage:
													bonusPercentage || 0,
												difference: defaultCurrency
													? (profit || 0
													  | currency
															: defaultCurrency
													  | position
															: selectedOrganization?.currencyPosition)
													: profit || 0
										  }
							}}
						</div>
						<div
							*ngIf="defaultCurrency"
							[ngClass]="{
								'negative-bonus-color': bonus < 0
							}"
						>
							{{ 'DASHBOARD_PAGE.DEVELOPER.BONUS' | translate }}
							:
							{{
								bonus || 0
									| currency: defaultCurrency
									| position
										: selectedOrganization?.currencyPosition
							}}
						</div>
						<!--
							TODO: handle non-defaultCurrency case differently.
							currently same thing is rendered in both cases
						 -->
						<div
							*ngIf="!defaultCurrency"
							[ngClass]="{
								'negative-bonus-color': bonus < 0
							}"
						>
							{{ 'DASHBOARD_PAGE.DEVELOPER.BONUS' | translate }}
							:
							{{
								bonus || 0
									| currency: defaultCurrency
									| position
										: selectedOrganization?.currencyPosition
							}}
						</div>
					</div>

					<!-- Note for negative total bonus -->
					<div class="bonus-disclaimer" *ngIf="bonus < 0">
						{{ 'DASHBOARD_PAGE.DEVELOPER.NOTE' | translate }}
					</div>
				</div>
			</div>
		</div>

		<div
			*ngIf="selectedEmployee && selectedEmployee.id"
			class="half-content"
		>
			<ga-employee-charts
				[employeeStatistics]="employeeStatistics"
			></ga-employee-charts>
		</div>

		<ng-template #noRole>
			<span>{{
				'DASHBOARD_PAGE.INSERT_TEXT_FOR_NOT_AUTHENTICATED_USERS'
					| translate
			}}</span>
		</ng-template>
	</nb-card-body>
</nb-card>
