<nb-card>
	<nb-card-header>
		<h4>
			<ngx-header-title>
				{{ 'DASHBOARD_PAGE.PROJECT_MANAGEMENT' | translate }}
			</ngx-header-title>
		</h4>
		<ng-container *ngIf="selectedDateRange?.startDate && selectedDateRange?.endDate">
			<ngx-date-range-title
				[start]="selectedDateRange?.startDate"
				[end]="selectedDateRange?.endDate"
				[format]="'dddd, LL'"
			></ngx-date-range-title>
		</ng-container>
	</nb-card-header>
	<nb-card-body>
		<gauzy-project-management-details></gauzy-project-management-details>
	</nb-card-body>
</nb-card>
