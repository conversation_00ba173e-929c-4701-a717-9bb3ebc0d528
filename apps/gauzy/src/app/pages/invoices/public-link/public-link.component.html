<nb-card>
	<nb-card-header>
		<span class="cancel">
			<i class="fas fa-times" (click)="close()"></i>
		</span>
		<h5 class="title">
			{{ 'INVOICES_PAGE.PUBLIC_LINK.HEADER' | translate }}
		</h5>
	</nb-card-header>
	<nb-card-body>
		<div class="mb-3">
			{{
				'INVOICES_PAGE.PUBLIC_LINK.ACCESS'
					| translate
						: {
								text: invoice.isEstimate ? 'estimate' : 'invoice'
						  }
			}}
		</div>
		<div>
			<label for="publicLink" class="label">
				{{ 'FORM.LABELS.PUBLIC_LINK' | translate }}
			</label>
			<input id="publicLink" [ngModel]="publicLink" readonly nbInput fullWidth #inputTarget />
		</div>
	</nb-card-body>
	<nb-card-footer class="text-left">
		<button outline nbButton status="basic" class="mr-3" (click)="close()" type="button">
			{{ 'BUTTONS.CANCEL' | translate }}
		</button>
		<button
			[ngxClipboard]="inputTarget"
			class="mr-3"
			status="success"
			nbButton
			type="button"
			[disabled]="isCopied"
			(click)="copyLink()"
			[nbTooltip]="'INVOICES_PAGE.PUBLIC_LINK.COPY_TO_CLIPBOARD_TOOLTIP' | translate"
		>
			{{ isCopied ? ('BUTTONS.COPIED' | translate) : ('BUTTONS.COPY_LINK' | translate) }}
		</button>
	</nb-card-footer>
</nb-card>
