<nb-card [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
	<nb-card-header class="card-header-title">
		<div class="col-auto">
			<h4>
				<ngx-header-title [allowEmployee]="false">
					{{ (isEstimate ? 'INVOICES_PAGE.ESTIMATES.HEADER' : 'INVOICES_PAGE.HEADER') | translate }}
				</ngx-header-title>
			</h4>
			<ngx-date-range-title [format]="'dddd, LL'"></ngx-date-range-title>
		</div>
		<div>
			<div>
				<button
					nbButton
					nbPopoverPlacement="bottom"
					[nbPopover]="settingsPopover"
					nbPopoverTrigger="noop"
					(click)="toggleTableSettingsPopover()"
					size="small"
				>
					<nb-icon icon="settings-2-outline"></nb-icon>
				</button>
			</div>
		</div>
	</nb-card-header>
	<nb-card-body class="card-body">
		<ng-template [ngxPermissionsOnly]="['INVOICES_EDIT', 'INVOICES_VIEW']">
			<div class="actions-container">
				<ng-template ngxPermissionsOnly="INVOICES_EDIT">
					<div class="transition-container">
						<div class="transition" [ngClass]="{ show: !disableButton, hide: disableButton }">
							<span class="transition" [ngClass]="{ show: !disableButton, 'show-button': disableButton }">
								<button nbButton status="success" (click)="add()" size="small">
									<nb-icon icon="plus-outline"> </nb-icon>
									{{ 'BUTTONS.ADD' | translate }}
								</button>
							</span>
							<ng-container *ngTemplateOutlet="actionButtons"></ng-container>
						</div>
					</div>
				</ng-template>
				<ga-layout-selector [componentName]="viewComponentName"></ga-layout-selector>
			</div>
		</ng-template>
		<nb-tabset (changeTab)="onChangeTab($event)">
			<nb-tab
				class="remove-scroll"
				[tabTitle]="'INVOICES_PAGE.BROWSE' | translate | titlecase"
				[tabId]="invoiceTabsEnum.ACTIONS"
			>
				<ng-container *ngIf="(nbTab$ | async) === invoiceTabsEnum.ACTIONS">
					<div class="custom-content-body">
						<ng-template
							[ngTemplateOutlet]="
								dataLayoutStyle === componentLayoutStyleEnum.TABLE ? tableLayout : gridLayout
							"
						></ng-template>
					</div>
				</ng-container>
			</nb-tab>
			<nb-tab
				class="remove-scroll search-tab"
				[tabTitle]="'INVOICES_PAGE.SEARCH' | translate | titlecase"
				[tabId]="invoiceTabsEnum.SEARCH"
			>
				<ng-container *ngIf="(nbTab$ | async) === invoiceTabsEnum.SEARCH">
					<div class="custom-content-body">
						<nb-accordion class="mb-3">
							<nb-accordion-item collapsed="false">
								<nb-accordion-item-header>
									{{
										(isEstimate ? 'INVOICES_PAGE.ESTIMATES.HEADER' : 'INVOICES_PAGE.HEADER')
											| translate
									}}
									{{ 'INVOICES_PAGE.SEARCH' | translate | lowercase }}
								</nb-accordion-item-header>
								<nb-accordion-item-body>
									<form [formGroup]="searchForm" (ngSubmit)="search()">
										<div class="row w-100">
											<div class="col-sm-4">
												<div class="form-group">
													<label for="inputInvoiceNumber" class="label">
														<span>
															{{
																(isEstimate
																	? 'INVOICES_PAGE.ESTIMATE_NUMBER'
																	: 'INVOICES_PAGE.INVOICE_NUMBER'
																) | translate
															}}
														</span>
													</label>
													<input
														type="number"
														nbInput
														[placeholder]="
															(!isEstimate
																? 'INVOICES_PAGE.INVOICE_NUMBER'
																: 'INVOICES_PAGE.ESTIMATE_NUMBER'
															) | translate
														"
														formControlName="invoiceNumber"
														id="inputInvoiceNumber"
														fullWidth
													/>
												</div>
											</div>
											<div class="col-sm-3">
												<div class="form-group">
													<label for="inputInvoiceDate" class="label">
														<span>
															{{
																(!isEstimate
																	? 'INVOICES_PAGE.INVOICE_DATE'
																	: 'INVOICES_PAGE.ESTIMATE_DATE'
																) | translate
															}}
														</span>
													</label>
													<div class="input-icon">
														<input
															formControlName="invoiceDate"
															type="text"
															nbInput
															[placeholder]="
																(!isEstimate
																	? 'INVOICES_PAGE.INVOICE_DATE'
																	: 'INVOICES_PAGE.ESTIMATE_DATE'
																) | translate
															"
															[nbDatepicker]="invoiceDatePicker"
															id="inputInvoiceDate"
															fullWidth
															required
															class="input-date"
														/>
														<nb-icon class="icon ml-3" icon="calendar-outline"></nb-icon>
														<nb-datepicker #invoiceDatePicker></nb-datepicker>
													</div>
												</div>
											</div>
											<div class="col-sm-3">
												<div class="form-group">
													<label for="inputDueDate" class="label">{{
														'INVOICES_PAGE.DUE_DATE' | translate
													}}</label>
													<div class="input-icon">
														<input
															nbInput
															[placeholder]="'INVOICES_PAGE.DUE_DATE' | translate"
															[nbDatepicker]="dueDatePicker"
															formControlName="dueDate"
															id="inputDueDate"
															fullWidth
															class="input-date"
														/>
														<nb-icon class="icon ml-3" icon="calendar-outline"></nb-icon>
														<nb-datepicker #dueDatePicker></nb-datepicker>
													</div>
												</div>
											</div>
										</div>
										<div class="row">
											<div class="col-sm-3">
												<div class="form-group">
													<label for="inputOrganizationContact" class="label">
														<span>{{ 'INVOICES_PAGE.CONTACT' | translate }}</span>
													</label>
													<ga-contact-select
														[clearable]="true"
														formControlName="organizationContact"
														[placeholder]="'POP_UPS.ALL_CONTACTS' | translate"
													></ga-contact-select>
												</div>
											</div>
											<div class="col-sm-2">
												<div class="form-group">
													<label for="inputTotalValue" class="label">
														{{ 'INVOICES_PAGE.TOTAL_VALUE' | translate }}
													</label>
													<input
														type="number"
														nbInput
														[placeholder]="'INVOICES_PAGE.TOTAL_VALUE' | translate"
														formControlName="totalValue"
														id="inputTotalValue"
														fullWidth
													/>
												</div>
											</div>
											<div class="col-sm-2">
												<div class="form-group">
													<ga-currency
														formControlName="currency"
														[formControl]="searchForm.get('currency')"
														(optionChange)="currencyChanged($event)"
													>
													</ga-currency>
												</div>
											</div>
											<div class="col-sm-2">
												<ga-tags-color-input
													[selectedTags]="searchForm.get('tags').value"
													(selectedTagsEvent)="selectedTagsEvent($event)"
													[isOrgLevel]="true"
												>
												</ga-tags-color-input>
											</div>
											<div class="col-sm-2">
												<div class="form-group">
													<label for="inputStatus" class="label">
														{{ 'INVOICES_PAGE.STATUS' | translate }}
													</label>
													<nb-select
														id="inputStatus"
														*ngIf="!isEstimate"
														formControlName="status"
														fullWidth
														[placeholder]="'FORM.PLACEHOLDERS.STATUS' | translate"
													>
														<nb-option
															*ngFor="let status of invoiceStatusTypes"
															[value]="status"
														>
															{{ 'INVOICES_PAGE.STATUSES.' + status | translate }}
														</nb-option>
													</nb-select>
													<nb-select
														id="inputStatus"
														*ngIf="isEstimate"
														formControlName="status"
														fullWidth
														[placeholder]="'FORM.PLACEHOLDERS.STATUS' | translate"
													>
														<nb-option
															*ngFor="let status of estimateStatusTypes"
															[value]="status"
														>
															{{ 'INVOICES_PAGE.STATUSES.' + status | translate }}
														</nb-option>
													</nb-select>
												</div>
											</div>
										</div>
										<div class="row">
											<div>
												<button
													type="submit"
													status="success"
													class="ml-3"
													size="small"
													nbButton
												>
													{{ 'BUTTONS.SEARCH' | translate }}
												</button>
											</div>
											<div>
												<button
													type="reset"
													status="basic"
													outline
													size="small"
													nbButton
													(click)="reset()"
												>
													{{ 'BUTTONS.RESET' | translate }}
												</button>
											</div>
										</div>
									</form>
								</nb-accordion-item-body>
							</nb-accordion-item>
						</nb-accordion>
						<ng-template
							[ngTemplateOutlet]="
								dataLayoutStyle === componentLayoutStyleEnum.TABLE ? tableLayout : gridLayout
							"
						>
						</ng-template>
					</div>
				</ng-container>
			</nb-tab>
			<nb-tab
				[tabTitle]="'INVOICES_PAGE.HISTORY' | translate | titlecase"
				[tabId]="invoiceTabsEnum.HISTORY"
				class="remove-scroll history-tab"
			>
				<ng-container *ngIf="(nbTab$ | async) === invoiceTabsEnum.HISTORY">
					<div class="custom-content-body">
						<nb-accordion class="mb-3">
							<nb-accordion-item collapsed="true" [collapsed]="selectedInvoice ? 'false' : 'true'">
								<nb-accordion-item-header class="nb-accordion-item-header">
									{{
										(!isEstimate ? 'INVOICES_PAGE.HEADER' : 'INVOICES_PAGE.ESTIMATES.HEADER')
											| translate
									}}
									{{ 'INVOICES_PAGE.COMMENTS' | translate | lowercase }}
									<div class="accordion-header-hint history-list-select">
										{{
											(!isEstimate
												? 'INVOICES_PAGE.SELECT_INVOICE_TO_VIEW_HISTORY'
												: 'INVOICES_PAGE.ESTIMATES.SELECT_ESTIMATE_TO_VIEW_HISTORY'
											) | translate
										}}
									</div>
								</nb-accordion-item-header>
								<nb-accordion-item-body>
									<div class="row comments-container">
										<div class="col-6">
											<ng-container *ngIf="selectedInvoice">
												{{ 'INVOICES_PAGE.COMMENT' | translate }}
												<form
													[formGroup]="historyForm"
													#historyFormDirective="ngForm"
													(ngSubmit)="addComment(historyFormDirective)"
												>
													<div class="col-8 mt-3 px-0">
														<input
															nbInput
															type="text"
															placeholder="Title"
															fullWidth
															formControlName="title"
														/>
													</div>
													<div class="col px-0 mt-3">
														<textarea
															nbInput
															placeholder="Comment"
															fullWidth
															formControlName="comment"
														></textarea>
													</div>
													<div class="mt-3">
														<button
															type="submit"
															class="mx-0"
															nbButton
															status="info"
															[disabled]="historyForm.invalid"
														>
															{{ 'BUTTONS.ADD_COMMENT' | translate }}
														</button>
													</div>
												</form>
											</ng-container>
										</div>
										<div *ngIf="!disableButton" class="col-6">
											{{ 'INVOICES_PAGE.COMMENTS' | translate }}
											<div class="comments w-100">
												<nb-list class="history-list">
													<nb-list-item
														*ngFor="let history of histories"
														class="history-list-item"
														style="align-items: baseline"
													>
														<div class="d-flex">
															<div>
																<ngx-avatar
																	[src]="history.user.imageUrl"
																	[name]="history.user.name"
																	[value]="history.user"
																></ngx-avatar>
																<div class="history-date history-item">
																	{{ history.createdAt | dateTimeFormat }}
																</div>
																<div class="history-item history-title">
																	<span>{{ history.title }}</span>
																</div>
																<div class="history-item history-comment">
																	<span>{{ history.action }}</span>
																</div>
															</div>
														</div>
													</nb-list-item>
												</nb-list>
											</div>
										</div>
									</div>
								</nb-accordion-item-body>
							</nb-accordion-item>
						</nb-accordion>
						<ng-template
							[ngTemplateOutlet]="
								dataLayoutStyle === componentLayoutStyleEnum.TABLE ? tableLayout : gridLayout
							"
						>
						</ng-template>
					</div>
				</ng-container>
			</nb-tab>
		</nb-tabset>
		<ng-template #tableLayout>
			<div class="table-scroll-container">
				<angular2-smart-table
					[settings]="settingsSmartTable"
					[source]="smartTableSource"
					(userRowSelect)="selectInvoice($event)"
					style="cursor: pointer"
					#invoicesTable
				></angular2-smart-table>
			</div>
			<div *ngIf="dataLayoutStyle === componentLayoutStyleEnum.TABLE" class="pagination-container">
				<ng-container *ngIf="smartTableSource">
					<ngx-pagination [source]="smartTableSource"></ngx-pagination>
				</ng-container>
			</div>
		</ng-template>
		<ng-template #gridLayout>
			<div class="grid">
				<ga-card-grid
					[totalItems]="pagination?.totalItems"
					[settings]="settingsSmartTable"
					[source]="invoices"
					(onSelectedItem)="selectInvoice($event)"
					(scroll)="onScroll()"
				></ga-card-grid>
			</div>
		</ng-template>
	</nb-card-body>
</nb-card>

<ng-template #actionButtons let-buttonSize="buttonSize" let-selectedItem="selectedItem">
	<div class="btn-group actions">
		<ng-template ngxPermissionsOnly="INVOICES_EDIT">
			<button
				*ngIf="isEstimate"
				nbButton
				status="info"
				(click)="convert(selectedItem)"
				class="action info mr-2"
				[disabled]="!selectedItem && disableButton"
				size="small"
			>
				<nb-icon icon="swap"> </nb-icon>
				{{ 'BUTTONS.TO_INVOICE' | translate }}
			</button>
			<nb-select
				filled
				status="basic"
				class="action select-nb mr-2 ml-1"
				*ngIf="!isEstimate"
				[placeholder]="'INVOICES_PAGE.SET_STATUS' | translate | titlecase"
				[(selected)]="status"
				[disabled]="disableButton"
				(selectedChange)="selectStatus($event)"
				[size]="'small'"
			>
				<nb-option *ngFor="let status of invoiceStatusTypes" [value]="status">
					{{ 'INVOICES_PAGE.STATUSES.' + status | translate }}
				</nb-option>
			</nb-select>
			<nb-select
				filled
				status="basic"
				class="action select-nb mr-2"
				*ngIf="isEstimate"
				[placeholder]="'INVOICES_PAGE.SET_STATUS' | translate | titlecase"
				[(selected)]="status"
				[disabled]="disableButton"
				(selectedChange)="selectStatus($event)"
				[size]="'small'"
			>
				<nb-option *ngFor="let status of estimateStatusTypes" [value]="status">
					{{ 'INVOICES_PAGE.STATUSES.' + status | translate }}
				</nb-option>
			</nb-select>
			<ng-template ngxPermissionsOnly="INVOICES_VIEW">
				<button
					nbButton
					status="basic"
					(click)="view()"
					class="action secondary"
					[disabled]="disableButton"
					size="small"
				>
					<nb-icon icon="eye-outline" pack="eva"></nb-icon>
					{{ 'BUTTONS.VIEW' | translate }}
				</button>
			</ng-template>
			<button
				nbButton
				status="basic"
				(click)="edit(selectedItem)"
				class="action primary"
				[disabled]="!selectedItem && disableButton"
				size="small"
			>
				<nb-icon icon="edit-outline"></nb-icon>
				{{ 'BUTTONS.EDIT' | translate }}
			</button>
			<button
				nbButton
				status="basic"
				(click)="download(selectedItem)"
				class="action primary"
				[disabled]="!selectedItem && disableButton"
				size="small"
			>
				<nb-icon icon="download-outline"></nb-icon>
				{{ 'BUTTONS.DOWNLOAD' | translate }}
			</button>
		</ng-template>
		<button
			*ngIf="!isEstimate"
			nbButton
			status="basic"
			(click)="payments()"
			class="action secondary mr-2"
			[disabled]="!selectedItem && disableButton"
			size="small"
		>
			<nb-icon icon="clipboard-outline"></nb-icon>
			{{ 'BUTTONS.PAYMENTS' | translate }}
		</button>
		<button
			nbButton
			status="basic"
			class="action"
			(click)="delete()"
			[disabled]="disableButton"
			size="small"
			[nbTooltip]="'BUTTONS.DELETE' | translate"
		>
			<nb-icon status="danger" icon="trash-2-outline"> </nb-icon>
		</button>
		<button
			nbButton
			nbPopoverPlacement="bottom"
			class="action"
			[nbPopover]="actionsPopover"
			nbPopoverTrigger="noop"
			(click)="toggleActionsPopover()"
			size="small"
		>
			<nb-icon icon="more-vertical-outline"></nb-icon>
		</button>
	</div>
</ng-template>

<ng-template #settingsPopover>
	<div class="popover-container">
		<span class="d-flex justify-content-end w-100"
			><i class="fas fa-times" (click)="toggleTableSettingsPopover()"></i
		></span>
		<span class="title">Settings</span>
		<div class="show-columns" *ngIf="columns">
			<label class="show-columns-label label">
				{{ 'INVOICES_PAGE.SHOW_COLUMNS' | translate }}
			</label>
			<nb-select
				class="show-columns-select"
				[multiple]="true"
				[(selected)]="columns"
				(selectedChange)="selectColumn($event)"
				placeholder="{{ 'INVOICES_PAGE.SHOW_COLUMNS' | translate }}"
			>
				<nb-option class="column" *ngFor="let column of getColumns()" [value]="column">
					{{ 'INVOICES_PAGE.' + column | translate }}
				</nb-option>
			</nb-select>
		</div>
		<div class="per-page-container mr-3">
			<div class="per-page">
				<label class="label per-page-label" *ngIf="!isEstimate">
					{{ 'INVOICES_PAGE.INVOICES_PER_PAGE' | translate }}
				</label>
				<label class="label per-page-label" *ngIf="isEstimate">
					{{ 'INVOICES_PAGE.ESTIMATES_PER_PAGE' | translate }}
				</label>
				<input class="per-page-input" [(ngModel)]="perPage" type="number" nbInput />
			</div>
		</div>
		<div class="w-100 d-flex justify-content-between align-items-center mr-3">
			<nb-checkbox (checkedChange)="toggleIncludeArchived($event)" status="warning">
				{{ 'FORM.CHECKBOXES.INCLUDE_ARCHIVED' | translate }}
			</nb-checkbox>
			<button class="per-page-button" status="success" nbButton (click)="showPerPage()">
				{{ 'BUTTONS.SAVE' | translate }}
			</button>
		</div>
	</div>
</ng-template>

<ng-template #actionsPopover>
	<div gauzyOutside (clickOutside)="onClickOutside($event)" class="popover-container-action d-flex flex-column">
		<ng-container>
			<button
				nbButton
				status="basic"
				class="action primary"
				size="small"
				(click)="addInternalNote()"
				[disabled]="disableButton"
			>
				<nb-icon icon="book-open-outline"> </nb-icon>
				<span class="icon-text">{{ 'BUTTONS.NOTE' | translate }}</span>
			</button>
			<button
				nbButton
				status="basic"
				class="action primary"
				size="small"
				(click)="duplicated()"
				[disabled]="disableButton"
			>
				<nb-icon icon="copy-outline"></nb-icon>
				<span class="icon-text">{{ 'BUTTONS.DUPLICATE' | translate }}</span>
			</button>
			<button
				nbButton
				status="basic"
				class="action primary"
				size="small"
				(click)="send()"
				[disabled]="!canBeSend"
			>
				<nb-icon icon="upload-outline"> </nb-icon>
				<span class="icon-text">{{ 'BUTTONS.SEND' | translate }}</span>
			</button>
			<button
				nbButton
				status="basic"
				class="action primary"
				size="small"
				(click)="generatePublicLink()"
				[disabled]="disableButton"
			>
				<nb-icon icon="link-2-outline"> </nb-icon>
				<span class="icon-text">{{ 'BUTTONS.PUBLIC_LINK' | translate }}</span>
			</button>
			<button
				nbButton
				status="basic"
				class="action primary"
				size="small"
				(click)="email()"
				[disabled]="disableButton"
			>
				<nb-icon icon="email-outline"> </nb-icon>
				<span class="icon-text">{{ 'BUTTONS.EMAIL' | translate }}</span>
			</button>
			<button
				nbButton
				status="basic"
				class="action primary"
				size="small"
				(click)="exportToCsv()"
				[disabled]="disableButton"
			>
				<nb-icon icon="file-text-outline"> </nb-icon>
				<span class="icon-text">{{ 'BUTTONS.EXPORT_TO_CSV' | translate }}</span>
			</button>
			<button
				nbButton
				status="basic"
				class="action primary"
				size="small"
				(click)="archive()"
				[disabled]="disableButton || selectedInvoice.isArchived"
			>
				<nb-icon icon="archive-outline"> </nb-icon>
				<span class="icon-text">{{ 'BUTTONS.ARCHIVE' | translate }}</span>
			</button>
			<button
				nbButton
				status="basic"
				class="action danger"
				size="small"
				(click)="delete()"
				[disabled]="disableButton"
			>
				<nb-icon status="danger" icon="trash-2-outline"> </nb-icon>
				<span class="icon-text">{{ 'BUTTONS.DELETE' | translate }}</span>
			</button>
		</ng-container>
	</div>
</ng-template>
