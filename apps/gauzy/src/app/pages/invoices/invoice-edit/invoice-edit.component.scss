@use 'gauzy/_gauzy-table' as *;
@use 'gauzy/_gauzy-dialogs' as *;

:host ::ng-deep angular2-smart-table .angular2-smart-actions-title a {
  background-color: #00d68f !important;
  transform: scale(0.9);
}

:host ::ng-deep angular2-smart-table .angular2-smart-actions a {
  transform: scale(0.6);
  border-radius: var(--border-radius);
}

:host ::ng-deep angular2-smart-table .angular2-smart-actions a:nth-child(1) {
  background-color: #00d68f !important;
  color: white;
}

:host ::ng-deep angular2-smart-table .angular2-smart-actions a:nth-child(2) {
  background-color: white !important;
  color: #ff3d71;
  box-shadow: var(--gauzy-shadow);
}

.total {
  float: right;

  &-item {
    border: solid 2px var(--button-filled-info-disabled-border-color);
    border-radius: 5px;
    margin: 20px 20px 20px 10px;
    padding: 5px;
    font-size: 14px;
  }
}

:host ::ng-deep angular2-smart-table .angular2-smart-actions {
  width: 5%;
}

:host {
  @include dialog(var(--gauzy-card-2), var(--gauzy-card-1));
}

.form {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 36px;

  .block-wrap {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;

    .block {
      width: 48%;
    }
  }
}
.footer-block-wrap {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;

  .footer-block {
    width: 100%;
  }
}

.terms-textarea {
  width: 98%;
}

.group {
  border-radius: 0.6rem;
  margin-bottom: 20px;
  padding: 9px 18px 2px 12px;

  .label-group {
    font-size: 14px;
    font-weight: 600;
    line-height: 17px;
    letter-spacing: -0.01em;
    text-align: left;
    margin-bottom: 18px;
  }
}

.buttons {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
}

.discountCheckbox {
  margin: 20px -1rem;
}

nb-card {
  background-color: var(--gauzy-card-2);
}
div.button-action {
  background-color: var(--gauzy-card-2);
  padding: 6px 8px;
  border-radius: nb-theme(button-rectangle-border-radius);
  margin-bottom: 12px;
}
.content {
  background-color: var(--gauzy-card-2);
  .row.group {
    background: var(--gauzy-card-3);
  }
  .custom-table {
    display: flex;
    flex-direction: column;
    background-color: var(--gauzy-card-2);
    padding: 12px;
	.table {
		margin: 0;
	}
    div.btn-action {
      align-self: flex-end;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
    }
  }
}

:host button.gray {
  background-color: rgba(126, 126, 143, 1);
  color: nb-theme(text-control-color);
  border-color: var(--button-filled-basic-border-color);
  [nbButton].appearance-filled.status-basic {
    background-color: nb-theme(button-filled-basic-background-color);
    border-color: nb-theme(button-filled-basic-border-color);
    color: nb-theme(button-filled-basic-text-color);
    :hover {
      color: nb-theme(text-basic-color);
      background-color: rgba($color: rgba(126, 126, 143), $alpha: 0.1);
    }
  }
}
