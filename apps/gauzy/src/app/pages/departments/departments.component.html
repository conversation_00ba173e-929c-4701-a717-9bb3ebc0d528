<nb-card [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
	<nb-card-header class="card-custom-header">
		<div class="card-header-title">
			<h4>
				<ngx-header-title>
					{{ 'ORGANIZATIONS_PAGE.DEPARTMENTS' | translate }}
				</ngx-header-title>
			</h4>
		</div>
		<div class="gauzy-button-container">
			<ngx-gauzy-button-action
				[isDisable]="disableButton"
				[buttonTemplate]="actionButtons"
				[componentName]="viewComponentName"
				[buttonTemplateVisible]="visibleButton"
			></ngx-gauzy-button-action>
		</div>
	</nb-card-header>
	<nb-card-body>
		<ng-container
			*ngIf="
				dataLayoutStyle === componentLayoutStyleEnum.TABLE && !loading
			"
		>
			<div class="table-scroll-container">
				<angular2-smart-table
					style="cursor: pointer"
					[settings]="smartTableSettings"
					[source]="smartTableSource"
					(userRowSelect)="selectDepartment($event)"
				></angular2-smart-table>
			</div>
			<div class="pagination-container">
				<ng-container *ngIf="smartTableSource">
					<ngx-pagination
						[source]="smartTableSource"
					></ngx-pagination>
				</ng-container>
			</div>
		</ng-container>
		<ng-container
			*ngIf="
				dataLayoutStyle === componentLayoutStyleEnum.CARDS_GRID &&
				!loading
			"
		>
			<ga-card-grid
				[totalItems]="pagination?.totalItems"
				[settings]="smartTableSettings"
				[source]="departments"
				(onSelectedItem)="selectDepartment($event)"
				(scroll)="onScroll()"
			></ga-card-grid>
		</ng-container>
	</nb-card-body>
</nb-card>
<ng-template #visibleButton>
	<ng-template [ngxPermissionsOnly]="['ALL_ORG_EDIT']">
		<button
			(click)="openDialog(addEditTemplate, false)"
			nbButton
			status="success"
			class="action"
			size="small"
		>
			<nb-icon icon="plus-outline"></nb-icon
			>{{ 'BUTTONS.ADD' | translate }}
		</button>
	</ng-template>
</ng-template>
<ng-template
	#actionButtons
	let-buttonSize="buttonSize"
	let-selectedItem="selectedItem"
>
	<div class="btn-group actions">
		<ng-template [ngxPermissionsOnly]="['ALL_ORG_EDIT']">
			<button
				nbButton
				status="basic"
				class="action primary"
				[disabled]="!selectedItem && disableButton"
				(click)="openDialog(addEditTemplate, true)"
				size="small"
			>
				<nb-icon icon="edit-outline"></nb-icon>
				{{ 'BUTTONS.EDIT' | translate }}
			</button>
		</ng-template>
		<ng-template [ngxPermissionsOnly]="['ALL_ORG_EDIT']">
			<button
				nbButton
				status="basic"
				class="action"
				[disabled]="!selectedItem && disableButton"
				(click)="removeDepartment(selectedItem?.id, selectedItem?.name)"
				size="small"
				[nbTooltip]="'BUTTONS.DELETE' | translate"
			>
				<nb-icon status="danger" icon="trash-2-outline"></nb-icon>
			</button>
		</ng-template>
	</div>
</ng-template>
<ng-template #addEditTemplate let-ref="dialogRef">
	<ga-departments-mutation
		[department]="selectedDepartment"
		(canceled)="_clearItem()"
		(addOrEditDepartment)="addOrEditDepartment($event)"
	></ga-departments-mutation>
</ng-template>
