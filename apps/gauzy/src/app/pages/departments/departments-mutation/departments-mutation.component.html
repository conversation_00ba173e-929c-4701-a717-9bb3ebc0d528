<div class="editable">
	<div class="container">
		<div class="row">
			<div class="col-sm-12 d-flex justify-content-end">
				<i
					class="fas fa-times"
					(click)="cancel()"
				></i>
			</div>
		</div>
		<div class="row mb-3">
			<h5 class="title mr-3 ml-3">{{ this.department ? ('POP_UPS.EDIT' | translate) : ('POP_UPS.ADD' | translate) }}</h5>
		</div>
		<form [formGroup]="form" (ngSubmit)="onSubmit()">
			<div class="row mb-3">
				<div class="col-sm-12 d-flex flex-column">
					<div class="form-group">
						<label class="label">{{
							'SM_TABLE.DEPARTMENTS' | translate
						}}</label>
						<input
							type="text"
							nbInput
							fullWidth
							[placeholder]="'ORGANIZATIONS_PAGE.EDIT.DEPARTMENT_NAME' | translate"
							formControlName="name"
						/>
					</div>
				</div>
			</div>
			<div class="row mb-3">
				<div class="col-sm-12 d-flex flex-column">
					<div class="form-group">
						<ga-employee-multi-select
							[selectedEmployeeIds]="form.get('members').value"
							(selectedChange)="onMembersSelected($event)"
							(onLoadEmployees)="onLoadEmployees($event)"
						></ga-employee-multi-select>
					</div>
				</div>
			</div>
			<div class="row mb-3">
				<div class="col-sm-12 d-flex flex-column">
					<div class="form-group">
						<ga-tags-color-input
							[selectedTags]="form.get('tags').value"
							(selectedTagsEvent)="selectedTagsEvent($event)"
							[isOrgLevel]="true"
						></ga-tags-color-input>
					</div>
				</div>
			</div>
			<div class="row mb-3">
				<div class="col-sm-12"></div>
				<button
					(click)="cancel()"
					nbButton
					status="basic"
					outline
					class="delete mr-3 ml-3"
				>
					{{ 'BUTTONS.CANCEL' | translate }}
				</button>
				<button
					nbButton
					type="submit"
					status="success"
					[disabled]="form.invalid"
				>
					{{ 'BUTTONS.SAVE' | translate }}
				</button>
			</div>
		</form>
</div>
</div>
