<nb-card [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
	<nb-card-header class="flex flex-column">
		<h4>
			<ngx-header-title [allowEmployee]="false">
				{{ 'DASHBOARD_PAGE.RECURRING_EXPENSES' | translate }}
			</ngx-header-title>
		</h4>
		<div class="gauzy-button-container">
			<ngx-gauzy-button-action
				[hasLayoutSelector]="false"
				[buttonTemplateVisible]="visible"
				[buttonTemplate]="actionButtons"
				[isDisable]="!selectedRecurringExpense?.isSelected"
			></ngx-gauzy-button-action>
		</div>
	</nb-card-header>
	<nb-card-body class="settings-body">
		<div class="container">
			<div
				class="sub-header header-content mt-4"
				*ngIf="recurringExpenses?.length"
			>
				<div class="block header-info">
					<div class="block-item-big">
						{{ 'POP_UPS.EMPLOYEE' | translate }}
					</div>
					<div class="block-item-big">
						{{ 'POP_UPS.CATEGORY_NAME' | translate }}
					</div>
					<div class="block-item">
						{{ 'POP_UPS.STARTS_ON' | translate }}
					</div>
					<div class="block-item">
						{{ 'POP_UPS.EXPENSE_VALUE' | translate }}
					</div>
				</div>
			</div>
			<ng-container *ngIf="recurringExpenses.length">
				<div class="table-scroll">
					<ng-container
						*ngFor="let expense of recurringExpenses; let i = index"
					>
						<ga-recurring-expense-block
							(click)="selectRecurringExpense(expense, i)"
							(editRecurringExpense)="
								editEmployeeRecurringExpense(i)
							"
							(deleteRecurringExpense)="
								deleteEmployeeRecurringExpense(i)
							"
							(fetchRecurringExpenseHistory)="fetchHistory(i)"
							[fetchedHistories]="fetchedHistories[i]"
							[recurringExpense]="expense"
							[selectedOrganization]="organization"
							[selected]="
								selectedRecurringExpense?.data?.id ===
								expense.id
							"
							[showHistory]="showHistory"
						></ga-recurring-expense-block>
					</ng-container>
				</div>
			</ng-container>
			<ng-template [ngIf]="!loading && recurringExpenses?.length == 0">
				<div class="no-data">
					<ngx-no-data-message
						[message]="'ORGANIZATIONS_PAGE.EXPENSE_NO_DATA_MESSAGE' | translate"
					></ngx-no-data-message>
				</div>
			</ng-template>
		</div>
	</nb-card-body>
</nb-card>
<ng-template #visible>
	<ng-template ngxPermissionsOnly="EMPLOYEE_EXPENSES_EDIT">
		<button
			(click)="addEmployeeRecurringExpense()"
			nbButton
			status="success"
			class="action"
			size="small"
		>
			<nb-icon class="mr-1" icon="plus-outline"></nb-icon>
			{{ 'BUTTONS.ADD' | translate }}
		</button>
	</ng-template>
</ng-template>
<ng-template #actionButtons>
	<div class="actions">
		<ng-template ngxPermissionsOnly="EMPLOYEE_EXPENSES_VIEW">
			<button
				nbButton
				class="action secondary"
				size="small"
				status="basic"
				underConstruction
			>
				<nb-icon icon="eye-outline"></nb-icon>
				{{ 'BUTTONS.VIEW' | translate }}
			</button>
		</ng-template>
		<ng-template ngxPermissionsOnly="EMPLOYEE_EXPENSES_EDIT">
			<button
				nbButton
				(click)="editEmployeeRecurringExpense()"
				class="action primary"
				size="small"
				status="basic"
			>
				<nb-icon icon="edit-outline"></nb-icon>
				{{ 'BUTTONS.EDIT' | translate }}
			</button>
		</ng-template>
		<ng-template ngxPermissionsOnly="EMPLOYEE_EXPENSES_VIEW">
			<button
				nbButton
				class="history action secondary"
				(click)="fetchHistory()"
				size="small"
				status="basic"
			>
				<i class="fas fa-history"></i>
				{{ 'BUTTONS.HISTORY' | translate }}
			</button>
		</ng-template>
		<ng-template ngxPermissionsOnly="EMPLOYEE_EXPENSES_EDIT">
			<button
				nbButton
				(click)="deleteEmployeeRecurringExpense()"
				class="action"
				size="small"
				status="basic"
				[nbTooltip]="'BUTTONS.DELETE' | translate"
			>
				<nb-icon status="danger" icon="trash-2-outline"></nb-icon>
			</button>
		</ng-template>
	</div>
</ng-template>
