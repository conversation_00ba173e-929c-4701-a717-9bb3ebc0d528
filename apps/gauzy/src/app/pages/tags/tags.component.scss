@use 'gauzy/_gauzy-cards' as *;
@use 'gauzy/_gauzy-table' as *;

:host .search {
  margin: 20px 0 0 0;
  align-items: center;
  display: flex;
  background-color: var(--gauzy-sidebar-background-3);
  border-radius: 8px;
  height: 32px;
  padding: 0 8px;

  ::ng-deep {
    i.control-icon::before {
      font-size: 13px;
      margin: 0;
    }

    input {
      width: 100%;
      height: 32px;
      background-color: transparent;
      box-shadow: unset;
      margin: 0 2px;
    }
  }
}

.tags-table {
  padding-top: 10px;
}

.filter-item:hover {
  text-decoration: underline;
  cursor: pointer;
}

:host nb-card.filter {
  background-color: var(--gauzy-card-2);
  overflow: unset;
  @include nb-ltr(padding-right, 4px);
  @include nb-rtl(padding-left, 4px);
  .list {
    max-height: calc(100vh - 15.8rem);
    overflow: auto;
  }

  nb-card-header {
    font-size: 14px;
    font-weight: 600;
    line-height: 17px;
    letter-spacing: 0em;
    @include nb-ltr(padding-left, 12px);
    @include nb-rtl(padding-right, 12px);
  }
}

:host nb-list {
  flex-direction: row;
  flex-wrap: wrap;
  @include nb-ltr(padding-left, 10px);
  @include nb-rtl(padding-right, 10px);
  padding-bottom: 21px;
  gap: 10px;
}

.filter-item {
  padding: 10px 15px;
  box-shadow: var(--gauzy-shadow);
  background: var(--gauzy-card-3);
  border-radius: 20px;
  height: 36px;
}

.gauzy-action {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

:host {
  nb-card.tags-component,
  nb-card.tags-component nb-card-body {
    background-color: var(--gauzy-card-2);
    margin: 0;
    display: flex;
    flex-direction: column;

    .table-scroll-container {
      flex-grow: 10;
      max-height: unset;
    }
  }

  nb-card.tags-component nb-card-body {
    border-radius: 0 0 $default-radius $default-radius;
    @include nb-ltr(padding, 1rem 0.5rem 1rem 18px);
    @include nb-rtl(padding, 1rem 18px 1rem 0.5rem);
  }

  nb-card.tags-component,
  nb-card.tags-component nb-card-header {
    border-radius: var(--border-radius);
  }
  .col-10 {
    @include nb-card-overrides(unset, calc(100vh - 17.25rem), $default-radius);
  }
  .col-2,
  .col-10 {
    padding: 0 10px;
  }
  .row {
    margin: 0 -10px;
  }
}
