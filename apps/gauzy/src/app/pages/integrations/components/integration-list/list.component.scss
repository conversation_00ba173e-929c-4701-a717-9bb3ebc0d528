@use 'gauzy/_gauzy-table' as *;
@use 'gauzy/_gauzy-cards' as *;

:host {
	nb-card,
	nb-card-body {
		background-color: var(--gauzy-card-2);
		border-radius: var(--border-radius);
	}
	nb-card-body {
		display: flex;
		flex-direction: column;
		gap: 1rem;
		padding: 1rem;
		.layout-selector {
			align-self: flex-end;
		}
		.table-scroll-container {
			display: flex;
			flex-direction: column;
			gap: 4px;
		}
		nb-card {
			background-color: var(--gauzy-card-1);
			.card-body-table {
				display: flex;
				flex-direction: row;
				align-items: center;
				cursor: pointer;
				gap: 0;
				.title {
					font-weight: 600;
					color: var(--gauzy-text-color-1);
				}
				.description {
					font-size: 13px;
					color: var(--gauzy-text-color-2);
				}
				&:hover {
					background: rgba(50, 50, 50, 0.03);
				}
				img {
					width: 100%;
					height: 48px;
					object-fit: fill;
					max-width: fit-content;
				}
			}
		}
	}
	.columns-header {
		background-color: nb-theme(gauzy-card-2);
		border-radius: 8px;
		margin-bottom: 10px;
		@include nb-ltr(padding, 12px 30px 12px 24px);
		@include nb-rtl(padding, 12px 24px 12px 30px);
		font-size: 12px;
		font-style: normal;
		font-weight: 600;
		line-height: 15px;
		letter-spacing: 0em;
	}
	.last-sync {
		font-size: 12px;
		color: var(--gauzy-text-color-1);
	}
}
