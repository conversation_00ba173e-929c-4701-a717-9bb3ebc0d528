<nb-card [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
	<nb-card-header class="card-customer-header pb-0">
		<div class="card-header-title">
			<h4>
				<ngx-header-title [allowEmployee]="false">
					<ng-container *ngIf="isTasksPage()">
						{{ 'TASKS_PAGE.HEADER' | translate }}
					</ng-container>
					<ng-container *ngIf="isMyTasksPage()">
						{{ 'TASKS_PAGE.MY_TASK_HEADER' | translate }}
					</ng-container>
					<ng-container *ngIf="isTeamTaskPage()">
						{{ 'TASKS_PAGE.TEAM_TASKS_HEADER' | translate }}
					</ng-container>
				</ngx-header-title>
			</h4>
			<ng-template [ngxPermissionsOnly]="['ALL_ORG_EDIT', 'ORG_PROJECT_EDIT']">
				<ng-template [ngIf]="!isDefaultProject">
					<button size="small" nbButton (click)="openTasksSettings(selectedProject)">
						<nb-icon icon="settings-2-outline"></nb-icon>
					</button>
				</ng-template>
			</ng-template>
		</div>
		<div class="tasks-component__settings">
			<div class="gauzy-button-container">
				<ngx-gauzy-button-action
					[buttonTemplateVisible]="visibleButton"
					[isDisable]="disableButton"
					[buttonTemplate]="actionButtons"
					[componentName]="viewComponentName"
				></ngx-gauzy-button-action>
			</div>
		</div>
	</nb-card-header>
	<nb-card-body [ngClass]="{ project: selectedProject }">
		<ng-container [ngSwitch]="viewMode">
			<ng-template [ngxPermissionsOnly]="['ALL_ORG_VIEW', 'ORG_TASK_VIEW']">
				<ng-template [ngSwitchCase]="taskListTypeEnum.GRID">
					<ng-template [ngIf]="dataLayoutStyle === componentLayoutStyleEnum.TABLE" [ngIfElse]="gridLayout">
						<div class="table-scroll-container">
							<angular2-smart-table
								style="cursor: pointer"
								[settings]="settingsSmartTable"
								[source]="smartTableSource"
								(userRowSelect)="selectTask($event)"
							></angular2-smart-table>
						</div>
						<ng-container
							*ngTemplateOutlet="
								paginationTemplate;
								context: {
									pagination: pagination
								}
							"
						></ng-container>
					</ng-template>
					<ng-template #gridLayout>
						<ga-card-grid
							[totalItems]="pagination?.totalItems"
							[settings]="settingsSmartTable"
							[source]="availableTasks$ | async"
							(onSelectedItem)="selectTask($event)"
							(scroll)="onScroll()"
						></ga-card-grid>
					</ng-template>
				</ng-template>
				<ng-template [ngSwitchCase]="taskListTypeEnum.SPRINT">
					<div class="sprint-view">
						<ga-tasks-sprint-view
							(createTaskEvent)="createTaskDialog()"
							(editTaskEvent)="editTaskDialog($event)"
							(deleteTaskEvent)="deleteTask($event)"
							(selectedTaskEvent)="selectTask($event)"
							[sync]="!disableButton"
							[project]="selectedProject"
							[tasks]="availableTasks$ | async"
						></ga-tasks-sprint-view>
					</div>
					<ng-container
						*ngTemplateOutlet="
							paginationTemplate;
							context: {
								pagination: pagination
							}
						"
					></ng-container>
				</ng-template>
			</ng-template>
		</ng-container>
	</nb-card-body>
</nb-card>
<ng-template #paginationTemplate let-pagination="pagination">
	<div class="pagination-container">
		<ng-container *ngIf="smartTableSource">
			<ngx-pagination [source]="smartTableSource"></ngx-pagination>
		</ng-container>
	</div>
</ng-template>
<ng-template #actionButtons>
	<ng-template
		[ngxPermissionsOnly]="['ALL_ORG_EDIT', 'ALL_ORG_VIEW', 'ORG_TASK_VIEW', 'ORG_TASK_EDIT', 'ORG_TASK_DELETE']"
	>
		<div class="btn-group actions">
			<ngx-favorite-toggle
				*ngIf="selectedTask"
				[entityType]="'Task'"
				[entityId]="selectedTask.id"
				[entityName]="selectedTask.title"
				size="small"
				status="basic"
				spacing="list"
				[disabled]="disableButton"
				[showLabel]="false"
				(favoriteToggled)="onTaskFavoriteToggled($event)"
			></ngx-favorite-toggle>
			<ng-template [ngxPermissionsOnly]="['ALL_ORG_VIEW', 'ORG_TASK_VIEW']">
				<button
					nbButton
					status="basic"
					class="action secondary"
					[disabled]="disableButton"
					size="small"
					underConstruction
				>
					<nb-icon icon="eye-outline" pack="eva"></nb-icon>
					{{ 'BUTTONS.VIEW' | translate }}
				</button>
			</ng-template>
			<ng-template [ngxPermissionsOnly]="['ALL_ORG_EDIT', 'ORG_TASK_EDIT']">
				<button
					nbButton
					status="basic"
					(click)="editTaskDialog(selectedItem)"
					[disabled]="disableButton"
					class="action primary"
					size="small"
				>
					<nb-icon icon="edit-outline"></nb-icon>
					{{ 'BUTTONS.EDIT' | translate }}
				</button>
			</ng-template>
			<ng-template [ngxPermissionsOnly]="['ALL_ORG_EDIT', 'ORG_TASK_EDIT']">
				<button
					nbButton
					status="basic"
					(click)="duplicateTaskDialog(selectedItem)"
					[disabled]="disableButton"
					class="action primary"
					size="small"
				>
					<nb-icon icon="copy-outline"></nb-icon>
					{{ 'BUTTONS.DUPLICATE' | translate }}
				</button>
			</ng-template>
			<ng-template [ngxPermissionsOnly]="['ALL_ORG_EDIT', 'ORG_TASK_DELETE']">
				<button
					nbButton
					status="basic"
					(click)="deleteTask(selectedItem)"
					[disabled]="disableButton"
					class="action"
					size="small"
					[nbTooltip]="'BUTTONS.DELETE' | translate"
				>
					<nb-icon status="danger" icon="trash-2-outline"> </nb-icon>
				</button>
			</ng-template>
		</div>
	</ng-template>
</ng-template>
<ng-template #visibleButton>
	<ng-template [ngxPermissionsOnly]="['ALL_ORG_EDIT', 'ORG_TASK_ADD']">
		<button nbButton status="success" (click)="createTaskDialog()" size="small">
			<nb-icon icon="plus-outline"></nb-icon>
			{{ 'BUTTONS.ADD' | translate }}
		</button>
	</ng-template>
</ng-template>
