<div class="d-flex settings">
	<div class="settings__name">
		<span>{{ 'TASKS_PAGE.TASK_VIEW_MODE' | translate }}:</span>
	</div>
	<div class="settings__value">
		<ng-select
			[items]="taskViewModeList"
			bindLabel="type"
			binValue="name"
			(change)="setTaskViewMode($event)"
			[(ngModel)]="selectedTaskViewMode"
			placeholder="{{ 'TASKS_PAGE.TASK_VIEW_MODE' | translate }}"
			appendTo="body"
			[clearable]="false"
		>
			<ng-template ng-option-tmp let-item="item" let-index="index">
				<nb-icon
					[icon]="item.icon"
					class="mr-1"
					style="font-size: 1rem"
				></nb-icon>
				<span>{{ item.name }}</span>
			</ng-template>
			<ng-template ng-label-tmp let-item="item">
				<div class="selector-template">
					<nb-icon
						[icon]="item.icon"
						class="mr-1"
						style="font-size: 1rem"
					></nb-icon>
					<span>{{ item.name }}</span>
				</div>
			</ng-template>
		</ng-select>
	</div>
</div>
<hr/>
<ng-container *ngIf="selectedTaskViewMode.type === taskViewModeType.SPRINT">
	<ngx-tasks-sprint-settings-view
		[project]="project"
	></ngx-tasks-sprint-settings-view>
</ng-container>
