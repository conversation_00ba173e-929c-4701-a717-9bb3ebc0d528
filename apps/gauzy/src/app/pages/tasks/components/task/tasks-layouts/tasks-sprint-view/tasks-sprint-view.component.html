<div class="sprints">
	<nb-accordion multi>
		<nb-accordion-item [expanded]="false" *ngFor="let sprint of sprints">
			<nb-accordion-item-header
				class="d-flex flex-column align-items-start"
			>
				<div
					class="sprint__title w-100 d-flex flex-row justify-content-between"
				>
					<div class="h6">{{ sprint.name }}</div>
					<div class="sprint__actions mr-5">
						<button
							nbButton
							size="tiny"
							*ngIf="sprint.isActive"
							(click)="completeSprint(sprint, $event)"
						>
							{{ 'TASKS_PAGE.COMPLETE_SPRINT' | translate }}
						</button>
					</div>
				</div>

				<div class="sprint__details row">
					<div class="col">Tasks: {{ sprint.tasks?.length }}</div>
					<div class="col" *ngIf="sprint.startDate">
						<div class="d-flex flex-column">
							<span class="date"
								>{{
									'TASKS_PAGE.DATE_START' | translate
								}}:</span
							>
							{{ sprint.startDate | date: 'short' }}
						</div>
					</div>
					<div class="col" *ngIf="sprint.endDate">
						<div class="d-flex flex-column">
							<span class="date"
								>{{ 'TASKS_PAGE.DATE_END' | translate }}:</span
							>
							{{ sprint.endDate | date: 'short' }}
						</div>
					</div>
				</div>
			</nb-accordion-item-header>
			<nb-accordion-item-body *ngIf="sprint?.tasks?.length > 0">
				<ng-container *ngTemplateOutlet="taskHeader"></ng-container>
				<div
					class="tasks"
					id="{{ sprint.id }}"
					cdkDropList
					[cdkDropListData]="sprint.tasks"
					[cdkDropListConnectedTo]="sprintIds"
					(cdkDropListDropped)="drop($event)"
				>
					<ga-sprint-task
						(taskActionEvent)="taskAction($event)"
						(changeStatusEvent)="changeTaskStatus($event)"
						(click)="toggleItemSelection(task)"
						*ngFor="let task of sprint.tasks; trackBy: trackByFn"
						[isSelected]="sync && task === selectedTask"
						[task]="task"
					></ga-sprint-task>
				</div>
			</nb-accordion-item-body>
		</nb-accordion-item>
		<nb-accordion-item [expanded]="true">
			<nb-accordion-item-header class="backlog-header">
				{{ 'TASKS_PAGE.BACKLOG' | translate }}
			</nb-accordion-item-header>
			<nb-accordion-item-body>
				<ng-container *ngTemplateOutlet="taskHeader"></ng-container>
				<div
					id="backlog"
					cdkDropList
					[cdkDropListData]="backlogTasks"
					[cdkDropListConnectedTo]="sprintIds"
					(cdkDropListDropped)="drop($event)"
				>
					<ga-sprint-task
						(changeStatusEvent)="changeTaskStatus($event)"
						(taskActionEvent)="taskAction($event)"
						(click)="toggleItemSelection(task)"
						*ngFor="let task of backlogTasks"
						[isSelected]="sync && task === selectedTask"
						[task]="task"
					></ga-sprint-task>
				</div>
			</nb-accordion-item-body>
		</nb-accordion-item>
	</nb-accordion>
</div>

<ng-template #taskHeader>
	<div class="row header">
		<div class="col-1">{{ 'TASKS_PAGE.TASK_ID' | translate }}</div>
		<div class="col">{{ 'TASKS_PAGE.TASKS_TITLE' | translate }}</div>
		<div class="col">{{ 'TASKS_PAGE.TASKS_PROJECT' | translate }}</div>
		<div class="col">{{ 'TASKS_PAGE.TASKS_CREATOR' | translate }}</div>
		<div class="col">
			{{ 'TASKS_PAGE.TASK_MEMBERS' | translate }}/
			{{ 'TASKS_PAGE.TASK_TEAMS' | translate }}
		</div>
		<div class="col">{{ 'TASKS_PAGE.DUE_DATE' | translate }}</div>
		<div class="col">{{ 'TASKS_PAGE.TASKS_STATUS' | translate }}</div>
	</div>
</ng-template>
