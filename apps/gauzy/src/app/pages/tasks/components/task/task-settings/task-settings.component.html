<ng-template [ngxPermissionsOnly]="['ALL_ORG_EDIT', 'ORG_PROJECT_EDIT']">
	<nb-card class="h-100 task-settings" *ngIf="project$ | async as project">
		<nb-card-header class="header align-items-center task-settings__title">
			<ngx-back-navigation></ngx-back-navigation>
			<span class="bold">
				{{ 'TASKS_PAGE.PROJECT' | translate }}:
			</span>
			{{ project.name }} {{ 'TASKS_PAGE.SETTINGS' | translate }}
		</nb-card-header>
		<nb-card-body class="task-settings__projects">
			<ngx-project-view
				[project]="project"
				(changeEvent)="changeProject($event)"
			></ngx-project-view>
		</nb-card-body>
	</nb-card>
</ng-template>