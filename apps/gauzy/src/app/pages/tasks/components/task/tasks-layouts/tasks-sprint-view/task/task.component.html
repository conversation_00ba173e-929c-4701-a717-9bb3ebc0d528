<div [cdkDragData]="task" [cdkDragLockAxis]="'y'" cdkDrag class="task">
	<nb-card>
		<nb-card-body [ngClass]="{ selected: isSelected }">
			<div class="row">
				<span class="col-1">
					{{ task?.taskNumber }}
				</span>
				<span class="col">
					<ga-notes-with-tags [rowData]="task"></ga-notes-with-tags>
				</span>
				<span class="col">
					<ngx-project [rowData]="task"></ngx-project>
				</span>
				<span class="col">
					<ngx-created-by-user [rowData]="task"></ngx-created-by-user>
				</span>
				<span class="col">
					<ngx-employee-with-links
						[value]="task?.employees || []"
					></ngx-employee-with-links>
				</span>
				<span class="col">
					<ngx-date-view [value]="task?.dueDate"></ngx-date-view>
				</span>
				<span class="col">
					<ngx-status-view
						[nbPopover]="contextMenu"
						nbPopoverTrigger="hover"
						[rowData]="task"
						[value]="task?.status"
						class="mr-2 status-view"
					></ngx-status-view>
				</span>
			</div>
		</nb-card-body>
	</nb-card>
</div>
<ng-template #contextMenu>
	<div class="view">
		<ng-container *ngFor="let taskStatus of statuses$ | async">
			<ngx-status-view
				(click)="updateStatus(taskStatus)"
				[rowData]="{taskStatus}"
				[value]="taskStatus.name"
				class="status-view"
			></ngx-status-view>
		</ng-container>
	</div>
</ng-template>
