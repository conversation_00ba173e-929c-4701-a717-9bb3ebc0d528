.tasks {}

.task {
  display: flex;
  flex-direction: row;

  &__title {}
}

.h6 {
  font-size: 15px;
  font-weight: 600;
  line-height: 18px;
  letter-spacing: 0em;
  color: var(--gauzy-text-color-2) !important;
}

.col {
  color: var(--gauzy-text-color-1);
}

.sprint__details {
  min-width: 60%;
}

.sprint {
  font-size: 14px;
  font-weight: 400;
  line-height: 17px;
  letter-spacing: 0em;
  color: var(--gauzy-text-color-1);
}

.sprints {
  height: calc(100vh - 24rem);
  overflow: auto;
  border-radius: var(--border-radius);
}

.header {
  padding: 1rem 14px;
  margin: 4px 0;
  background-color: var(--gauzy-card-1);
  border-radius: var(--border-radius);
  font-size: 12px;
  font-weight: 600;
  line-height: 15px;
  letter-spacing: 0em;

  >div {
    color: var(--gauzy-text-color-2);
  }
}

.date {
  color: var(--gauzy-text-color-2);
}

nb-accordion-item-header.backlog-header {
  background-color: var(--gauzy-sidebar-background-3);
  color: var(--gauzy-text-color-2);

  &.accordion-item-header-collapsed {
    border-radius: 0 0 var(--border-radius) var(--border-radius);
  }
}

nb-accordion {
  box-shadow: none;
}