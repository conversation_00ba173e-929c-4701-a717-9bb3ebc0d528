nb-card-body {
	box-shadow: var(--gauzy-shadow);
	border-radius: var(--border-radius);
	background-color: var(--gauzy-card-1);
}

nb-card {
	margin-bottom: 8px;
}

.selected {
	background-color: var(--gauzy-sidebar-background-3);
	box-shadow: -6px 0 0 0 rgba(0 0 0 / 10%);
}

.view {
	display: flex;
	flex-direction: column;
	gap: 4px;
	padding: 4px;
}
.status-view {
	cursor: pointer;
}

::ng-deep nb-popover > span.arrow {
	display: none;
}
