<div class="editable">
	<div class="container">
		<div class="row">
			<div class="col-sm-12 d-flex justify-content-end">
				<i class="fas fa-times" (click)="cancel()"></i>
			</div>
		</div>
		<div class="row mb-3">
			<h5 class="title mr-3 ml-3">
				{{ (team ? 'POP_UPS.EDIT' : 'POP_UPS.ADD') | translate }}
			</h5>
		</div>
		<form [formGroup]="form">
			<div class="team-photo mb-3">
				<ngx-image-uploader
					[folder]="'team_avatars'"
					(changeHoverState)="hoverState = $event"
					(uploadedImageAsset)="updateImageAsset($event)"
					(uploadImageAssetError)="handleImageUploadError($event)"
				></ngx-image-uploader>
				<svg
					xmlns="http://www.w3.org/2000/svg"
					xmlns:xlink="http://www.w3.org/1999/xlink"
					width="48"
					height="48"
					viewBox="0 0 68 68"
					[ngStyle]="{
						opacity: hoverState ? '1' : '0.3'
					}"
					*ngIf="form && form.get('imageUrl').value"
				>
					<defs>
						<path
							id="a"
							d="M28.667 31.333a2 2 0 1 0-.002-4.001 2 2 0 0 0 .002 4.001m13.333 12H26.748l9.34-7.793c.328-.279.923-.277 1.244-.001l6.001 5.12V42c0 .736-.597 1.333-1.333 1.333M26 24.667h16c.736 0 1.333.597 1.333 1.333v11.152l-4.27-3.643c-1.32-1.122-3.386-1.122-4.694-.008l-9.702 8.096V26c0-.736.597-1.333 1.333-1.333M42 22H26c-2.205 0-4 1.795-4 4v16c0 2.205 1.795 4 4 4h16c2.205 0 4-1.795 4-4V26c0-2.205-1.795-4-4-4"
						/>
					</defs>
					<g fill="none" fill-rule="evenodd">
						<circle cx="34" cy="34" r="34" fill="#0091FF" opacity=".3" />
						<circle cx="34" cy="34" r="26" fill="#0091FF" opacity=".9" />
						<use fill="#FFF" fill-rule="nonzero" xlink:href="#a" />
					</g>
				</svg>
				<div class="image-overlay" [ngStyle]="{ opacity: hoverState ? '0.2' : '0.1' }"></div>
				<ng-template [ngIf]="form.get('imageUrl').value" [ngIfElse]="noImageTemplate">
					<img
						[src]="form.get('imageUrl').value"
						alt="Team Photo"
						(mouseenter)="hoverState = true"
						(mouseleave)="hoverState = false"
					/>
				</ng-template>
				<ng-template #noImageTemplate>
					<div class="no-image">
						<i class="fas fa-image"></i>
					</div>
				</ng-template>
			</div>

			<div class="row mb-3">
				<div class="col-sm-12 d-flex flex-column">
					<div class="form-group">
						<label class="label">
							{{ 'FORM.PLACEHOLDERS.TEAM_NAME' | translate }}
						</label>
						<input
							formControlName="name"
							type="text"
							nbInput
							fullWidth
							[placeholder]="'FORM.PLACEHOLDERS.TEAM_NAME' | translate"
						/>
					</div>
				</div>
			</div>
			<div class="row mb-3">
				<div class="form-group col-12">
					<ga-project-selector
						formControlName="projects"
						[shortened]="true"
						[skipGlobalChange]="true"
						[multiple]="true"
						[showAllOption]="false"
						[placeholder]="'FORM.PLACEHOLDERS.ADD_REMOVE_PROJECTS' | translate"
						[defaultSelected]="false"
						[label]="'FORM.PLACEHOLDERS.ADD_REMOVE_PROJECTS' | translate"
						(onChanged)="onProjectsSelected($event)"
					></ga-project-selector>
				</div>
				<div class="form-group col-12">
					<ga-employee-multi-select
						[label]="'FORM.LABELS.ADD_REMOVE_MANAGERS' | translate"
						[placeholder]="'FORM.PLACEHOLDERS.ADD_REMOVE_MANAGERS' | translate"
						[selectedEmployeeIds]="form.get('managerIds').value"
						[allEmployees]="employees"
						(selectedChange)="onManagersSelected($event)"
					></ga-employee-multi-select>
				</div>
				<div class="form-group col-12">
					<ga-employee-multi-select
						[label]="'FORM.LABELS.ADD_REMOVE_MEMBERS' | translate"
						[placeholder]="'FORM.PLACEHOLDERS.ADD_REMOVE_MEMBERS' | translate"
						[selectedEmployeeIds]="form.get('memberIds').value"
						[allEmployees]="employees"
						(selectedChange)="onMembersSelected($event)"
					></ga-employee-multi-select>
				</div>
			</div>
			<div class="row mb-3">
				<div class="col-sm-12 d-flex flex-column">
					<div class="form-group">
						<ga-tags-color-input
							[selectedTags]="form.get('tags').value"
							(selectedTagsEvent)="selectedTagsEvent($event)"
							[isOrgLevel]="true"
						></ga-tags-color-input>
					</div>
				</div>
			</div>
		</form>
		<div class="row mb-3">
			<button (click)="cancel()" nbButton status="basic" outline class="delete mr-3 ml-3">
				{{ 'BUTTONS.CANCEL' | translate }}
			</button>
			<button [disabled]="form.invalid" (click)="addOrEditTeams()" nbButton status="success">
				{{ 'BUTTONS.SAVE' | translate }}
			</button>
		</div>
	</div>
</div>
