<nb-card [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
	<nb-card-header class="card-custom-header">
		<div class="card-header-title">
			<h4>
				<ngx-header-title>
					{{ 'ORGANIZATIONS_PAGE.TEAMS' | translate }}
				</ngx-header-title>
			</h4>
		</div>
		<div class="gauzy-button-container">
			<ngx-gauzy-button-action
				[isDisable]="disableButton"
				[buttonTemplate]="actionButtons"
				[componentName]="viewComponentName"
				[buttonTemplateVisible]="visibleButton"
			></ngx-gauzy-button-action>
		</div>
	</nb-card-header>
	<nb-card-body>
		<ng-container *ngIf="dataLayoutStyle === componentLayoutStyleEnum.TABLE">
			<div class="table-scroll-container">
				<angular2-smart-table
					style="cursor: pointer"
					[settings]="smartTableSettings"
					[source]="smartTableSource"
					(userRowSelect)="selectTeam($event)"
				></angular2-smart-table>
			</div>
			<div class="pagination-container">
				<ng-container *ngIf="smartTableSource">
					<ngx-pagination [source]="smartTableSource"></ngx-pagination>
				</ng-container>
			</div>
		</ng-container>
		<ng-container *ngIf="dataLayoutStyle === componentLayoutStyleEnum.CARDS_GRID">
			<ga-card-grid
				[totalItems]="pagination?.totalItems"
				[settings]="smartTableSettings"
				[source]="teams"
				(onSelectedItem)="selectTeam($event)"
				(scroll)="onScroll()"
			></ga-card-grid>
		</ng-container>
	</nb-card-body>
</nb-card>
<ng-template #visibleButton>
	<ng-template [ngxPermissionsOnly]="['ORG_TEAM_ADD', 'ALL_ORG_EDIT']">
		<ng-container>
			<button (click)="openDialog(addEditTemplate, false)" nbButton status="success" class="action" size="small">
				<nb-icon icon="plus-outline"></nb-icon>
				{{ 'BUTTONS.ADD' | translate }}
			</button>
		</ng-container>
	</ng-template>
</ng-template>
<ng-template #actionButtons let-buttonSize="buttonSize" let-selectedItem="selectedItem">
	<ngx-favorite-toggle
		*ngIf="selectedTeam"
		[entityType]="'OrganizationTeam'"
		[entityId]="selectedTeam.id"
		[entityName]="selectedTeam.name"
		size="small"
		status="basic"
		spacing="list"
		[disabled]="disableButton"
		[showLabel]="false"
		(favoriteToggled)="onTeamFavoriteToggled($event)"
	></ngx-favorite-toggle>
	<ng-template [ngxPermissionsOnly]="['ORG_TEAM_EDIT', 'ORG_TEAM_DELETE', 'ALL_ORG_EDIT']">
		<div class="btn-group actions">
			<ng-template [ngxPermissionsOnly]="['ORG_TEAM_EDIT', 'ALL_ORG_EDIT']">
				<button
					nbButton
					status="basic"
					class="action primary"
					[disabled]="disableButton"
					(click)="openDialog(addEditTemplate, true)"
					size="small"
				>
					<nb-icon icon="edit-outline"></nb-icon>
					{{ 'BUTTONS.EDIT' | translate }}
				</button>
			</ng-template>
			<ng-template [ngxPermissionsOnly]="['ORG_TEAM_DELETE', 'ALL_ORG_EDIT']">
				<button
					nbButton
					status="basic"
					class="action"
					[disabled]="disableButton"
					(click)="removeTeam(selectedItem?.id, selectedItem?.name)"
					size="small"
					[nbTooltip]="'BUTTONS.DELETE' | translate"
				>
					<nb-icon status="danger" icon="trash-2-outline"></nb-icon>
				</button>
			</ng-template>
		</div>
	</ng-template>
</ng-template>
<ng-template #addEditTemplate let-ref="dialogRef">
	<ga-teams-mutation
		[employees]="employees"
		[team]="selectedTeam"
		[projects]="projects"
		(canceled)="clearItem()"
		(addOrEditTeam)="addOrEditTeam($event)"
	></ga-teams-mutation>
</ng-template>
