<div *ngIf="rowData?.isDeleted">
	<div class="badge badge-danger">
		{{ 'EMPLOYEES_PAGE.DELETED' | translate }}
	</div>
</div>
<div *ngIf="rowData?.isActive">
	<div class="badge badge-success">
		{{ 'EMPLOYEES_PAGE.ACTIVE' | translate }}
	</div>
</div>
<div *ngIf="!rowData.startedWorkOn" [nbTooltip]="'EMPLOYEES_PAGE.NOT_STARTED_HELP' | translate">
	<div class="badge badge-disabled">
		{{ 'EMPLOYEES_PAGE.NOT_STARTED' | translate }}
	</div>
</div>
<div *ngIf="rowData.workStatus" class="text-center d-block">
	<div class="badge badge-danger">
		{{ 'EMPLOYEES_PAGE.WORK_ENDED' | translate }}
	</div>
	{{ rowData.workStatus }}
</div>
