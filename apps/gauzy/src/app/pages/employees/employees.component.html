<nb-card [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
	<nb-card-header class="d-flex flex-column pb-0">
		<div class="card-header-title">
			<div class="card-header-title">
				<h4>
					<ngx-header-title [allowEmployee]="false">
						{{ 'EMPLOYEES_PAGE.HEADER' | translate }}
					</ngx-header-title>
				</h4>
			</div>
			<div class="card-header-title">
				<div class="mr-2">
					<ng-template [ngxPermissionsOnly]="[PermissionsEnum.ORG_EMPLOYEES_VIEW]">
						<ng-template [ngxPermissionsOnly]="[PermissionsEnum.ORG_INVITE_VIEW]">
							<ng-template [ngIf]="organization?.invitesAllowed">
								<button nbButton status="primary" [routerLink]="'/pages/employees/invites'">
									{{ 'BUTTONS.MANAGE_INVITES' | translate }}
								</button>
							</ng-template>
						</ng-template>
					</ng-template>
				</div>
			</div>
		</div>
		<div class="gauzy-button-container">
			<ngx-gauzy-button-action
				[hasLayoutSelector]="false"
				[isDisable]="disableButton"
				[hidden]="haveBtnActionPermissions()"
			>
				<ng-container
					buttonTemplate
					[ngTemplateOutlet]="actionButtons"
					[ngTemplateOutletContext]="{ $implicit: selectedEmployee }"
				></ng-container>
				<ng-container buttonTemplateVisible [ngTemplateOutlet]="visibleButtons"></ng-container>
			</ngx-gauzy-button-action>
			<div class="custom-toggle">
				<nb-toggle (checkedChange)="changeIncludeDeleted($event)" status="danger">
					{{ 'FORM.CHECKBOXES.INCLUDE_DELETED' | translate }}
				</nb-toggle>
			</div>
			<ga-layout-selector [componentName]="viewComponentName"></ga-layout-selector>
		</div>
	</nb-card-header>
	<nb-card-body>
		<ng-template [ngxPermissionsOnly]="[PermissionsEnum.ALL_ORG_VIEW, PermissionsEnum.ORG_EMPLOYEES_VIEW]">
			<ng-container [ngSwitch]="dataLayoutStyle">
				<!-- Table View -->
				<ng-template [ngSwitchCase]="componentLayoutStyleEnum.TABLE">
					<div class="table-scroll-container">
						<angular2-smart-table
							style="cursor: pointer"
							[settings]="settingsSmartTable"
							[source]="smartTableSource"
							(userRowSelect)="selectEmployee($event)"
						></angular2-smart-table>
					</div>
					<div class="pagination-container">
						<ng-container *ngIf="smartTableSource">
							<ngx-pagination [source]="smartTableSource"></ngx-pagination>
						</ng-container>
					</div>
				</ng-template>

				<!-- Card Grid View -->
				<ng-template [ngSwitchCase]="componentLayoutStyleEnum.CARDS_GRID">
					<div class="grid">
						<ga-card-grid
							[totalItems]="pagination?.totalItems"
							[settings]="settingsSmartTable"
							[source]="employees"
							(onSelectedItem)="selectEmployee($event)"
							(scroll)="onScroll()"
							#grid
						></ga-card-grid>
					</div>
				</ng-template>

				<!-- Optional: Default case if no specific layout matches -->
				<ng-template *ngSwitchDefault>
					<p>{{ 'SETTINGS_MENU.NO_LAYOUT' | translate }}</p>
				</ng-template>
			</ng-container>
		</ng-template>
		<ng-template [ngxPermissionsExcept]="['ORG_EMPLOYEES_VIEW']">
			<div>
				<!-- Content to display if the user does not have 'canEditComponent' permission -->
			</div>
		</ng-template>
	</nb-card-body>
</nb-card>

<!-- Visible Buttons -->
<ng-template #visibleButtons>
	<ng-template [ngxPermissionsOnly]="[PermissionsEnum.ORG_EMPLOYEES_EDIT]">
		<ng-template [ngxPermissionsOnly]="[PermissionsEnum.ORG_INVITE_EDIT]">
			<ng-template [ngIf]="organization?.invitesAllowed">
				<button nbButton status="info" class="action" (click)="invite()" size="small">
					<nb-icon class="mr-1" icon="email-outline"></nb-icon>
					{{ 'BUTTONS.INVITE' | translate }}
				</button>
			</ng-template>
		</ng-template>
		<button nbButton size="small" status="success" (click)="add()">
			<nb-icon class="mr-1" icon="plus-outline"></nb-icon>
			{{ 'BUTTONS.ADD' | translate }}
		</button>
	</ng-template>
</ng-template>

<!-- Start Work Button -->
<ng-template #startWork>
	<button (click)="startEmployeeWork()" class="action text-info" nbButton size="small" status="basic">
		<nb-icon icon="person-done-outline"></nb-icon>
		{{ 'BUTTONS.START_WORK' | translate }}
	</button>
</ng-template>

<!-- Action Buttons -->
<ng-template #actionButtons let-selectedItem>
	<div class="btn-group actions">
		<!-- Favorite Button: Add or remove employee from favorites -->
		<ngx-favorite-toggle
			*ngIf="selectedEmployee"
			[entityType]="'Employee'"
			[entityId]="selectedEmployee.id"
			[entityName]="getEmployeeDisplayName(selectedEmployee)"
			size="small"
			status="basic"
			spacing="list"
			[disabled]="disabledButton"
			[showLabel]="false"
			(favoriteToggled)="onEmployeeFavoriteToggled($event)"
		></ngx-favorite-toggle>
		<ng-template [ngxPermissionsOnly]="[PermissionsEnum.ORG_EMPLOYEES_EDIT, PermissionsEnum.ORG_EMPLOYEES_VIEW]">
			<ng-template ngxPermissionsOnly="ORG_EMPLOYEES_VIEW">
				<button status="basic" class="action secondary" size="small" nbButton underConstruction>
					<nb-icon icon="eye-outline" pack="eva"></nb-icon>
					<span> {{ 'BUTTONS.VIEW' | translate }} </span>
				</button>
			</ng-template>
			<ng-container *ngIf="selectedItem && selectedItem?.isDeleted">
				<button
					nbButton
					size="small"
					debounceClick
					(throttledClick)="restoreToWork(selectedItem)"
					status="info"
					class="action"
				>
					<nb-icon class="mr-1" icon="edit-outline"></nb-icon>
					{{ 'BUTTONS.RESTORE' | translate }}
				</button>
			</ng-container>
			<ng-container
				*ngIf="
					selectedEmployee?.startedWorkOn && selectedEmployee?.isActive && !selectedEmployee?.workStatus;
					else startWork
				"
			>
				<ng-container *ngIf="selectedItem">
					<button
						(throttledClick)="timeTrackingAction(selectedItem)"
						class="action warning"
						debounceClick
						nbButton
						size="small"
						status="basic"
					>
						<nb-icon icon="edit-outline"></nb-icon>
						{{
							(selectedItem?.isTrackingEnabled
								? 'BUTTONS.TIME_TRACKING_DISABLE'
								: 'BUTTONS.TIME_TRACKING_ENABLE'
							) | translate
						}}
					</button>
				</ng-container>
				<button
					(click)="selectedItem?.endWork ? backToWork(selectedItem) : endWork(selectedItem)"
					[disabled]="disableButton || !selectedItem?.isActive"
					class="action orange"
					nbButton
					size="small"
					status="basic"
				>
					<nb-icon icon="person-delete-outline"></nb-icon>
					{{
						(selectedItem && selectedItem?.endWork
							? 'EMPLOYEES_PAGE.BACK_TO_WORK'
							: 'EMPLOYEES_PAGE.END_WORK'
						) | translate
					}}
				</button>
			</ng-container>
			<button
				nbButton
				[disabled]="disableButton"
				size="small"
				(click)="edit(selectedItem)"
				status="basic"
				class="action primary"
			>
				<nb-icon class="mr-1" icon="edit-outline"></nb-icon>
				{{ 'BUTTONS.EDIT' | translate }}
			</button>
			<ng-container *ngIf="selectedItem && !selectedItem?.isDeleted">
				<button
					nbButton
					size="small"
					(click)="delete(selectedItem)"
					status="basic"
					class="action"
					[nbTooltip]="'BUTTONS.DELETE' | translate"
				>
					<nb-icon status="danger" icon="trash-2-outline"></nb-icon>
				</button>
			</ng-container>
		</ng-template>
	</div>
</ng-template>
