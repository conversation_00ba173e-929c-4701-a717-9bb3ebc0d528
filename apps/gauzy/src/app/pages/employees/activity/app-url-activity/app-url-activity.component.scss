@forward '../time-activities/time-activities/time-activities.component';

:host {
  .percentage-col {
    width: 90px;
  }
  .custom-card-body {
    height: calc(100vh - 21.5rem) !important;
  }
  ::ng-deep {
    nb-progress-bar {
      .progress-container {
        height: 5px !important;
      }
      .progress-value {
        span {
          display: none;
        }
      }
    }
  }
  .screenshot-container {
    height: calc(100% - 3.5rem);
    .no-data {
      margin-top: 12px;
      height: 100%;
    }
  }
}
.mini-card {
  background-color: nb-theme(background-basic-color-1);
  padding: 20px;
  margin-bottom: 5px;
  border-radius: nb-theme(border-radius);
  &.head {
    background-color: rgba($color: white, $alpha: 0.5);
    padding: 12px 20px;
  }
  .times {
    font-size: 12px;
    font-weight: 400;
    line-height: 15px;
    letter-spacing: 0em;
    text-align: left;
  }
}
.arrow {
  width: 32px;
}
