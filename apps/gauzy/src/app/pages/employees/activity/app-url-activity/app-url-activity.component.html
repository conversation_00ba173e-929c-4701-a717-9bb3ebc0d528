<div class="custom-card">
	<div class="filters mb-2 mt-0">
		<ngx-gauzy-filters
			[isTimeFormat]="true"
			[saveFilters]="(datePickerConfig$ | async).isSaveDatePicker"
			(filtersChange)="filtersChange($event)"
		></ngx-gauzy-filters>
	</div>
	<div [nbSpinner]="loading" nbSpinnerSize="giant" nbSpinnerStatus="primary" class="custom-card-body">
		<div class="screenshot-container pb-3">
			<div class="mini-card head align-items-center">
				<div class="row">
					<div class="col-sm-2 py-2">
						{{ (type == 'apps' ? 'ACTIVITY.APPS' : 'ACTIVITY.VISITED_SITES') | translate }}
					</div>
					<div class="col-sm-3 py-2">
						{{ 'ACTIVITY.VISITED_DATES' | translate }}
					</div>
					<div class="col-sm-5 py-2">
						{{ 'ACTIVITY.PERCENT_USED' | translate }}
					</div>
					<div class="col-sm-2 text-right py-2">
						{{ 'ACTIVITY.TIME_SPENT' | translate }}
					</div>
				</div>
			</div>
			<div class="mini-card" *ngFor="let values of apps">
				<div *ngFor="let app of values.activities">
					<ngx-activity-item
						(loadChild)="loadChild($event)"
						[allowChild]="type === 'urls'"
						[item]="app"
						[visitedDate]="values.hour | dateTimeFormat"
					></ngx-activity-item>
				</div>
			</div>
			<div *ngIf="!loading && !apps?.length" class="no-data">
				<ngx-no-data-message [message]="'ACTIVITY.NO_ACTIVITIES' | translate"></ngx-no-data-message>
			</div>
		</div>
	</div>
</div>
