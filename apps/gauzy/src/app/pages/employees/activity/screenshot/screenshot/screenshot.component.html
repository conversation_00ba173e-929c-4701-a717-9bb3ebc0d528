<div class="custom-card">
	<div class="filters mt-1 mb-3">
		<ngx-gauzy-filters
			[isTimeFormat]="true"
			[saveFilters]="(datePickerConfig$ | async).isSaveDatePicker"
			(filtersChange)="filtersChange($event)"
		></ngx-gauzy-filters>
		<ga-daily-statistics class="mt-3" [filters]="filters"></ga-daily-statistics>
	</div>
	<div class="custom-card-body">
		<div
			[ngClass]="{
				'px-3': timeSlots?.length > 0
			}"
			[nbSpinner]="loading"
			nbSpinnerSize="giant"
			nbSpinnerStatus="primary"
			class="h-100"
		>
			<ng-template [ngIf]="timeSlots?.length > 0" [ngIfElse]="notFound">
				<div class="row hour-slot-group" *ngFor="let hourSlot of timeSlots; let i = index">
					<div class="col p-0">
						<div class="mb-4">
							<h6 class="hour-label">
								{{ hourSlot.startTime | timeFormat : filters?.timeFormat }} -
								{{ hourSlot.endTime | timeFormat : filters?.timeFormat }}
							</h6>
						</div>
						<div class="row grid-container">
							<div *ngFor="let timeSlot of hourSlot.timeSlots">
								<ngx-screenshots-item
									[timeFormat]="filters?.timeFormat"
									[timezone]="filters?.timeZone"
									[employees]="timeSlot?.employees"
									[timeSlot]="timeSlot"
									[selectionMode]="selectedIdsCount > 0"
									[isSelected]="selectedIds[timeSlot?.id]"
									(delete)="deleteSlot()"
									(toggle)="toggleSelect($event)"
								></ngx-screenshots-item>
							</div>
						</div>
					</div>
				</div>
			</ng-template>
			<ng-template #notFound>
				<ng-container *ngIf="!request.employeeIds; else noFoundScreenshot">
					<ngx-no-data-message [message]="'ACTIVITY.NO_RECORD_FOUND' | translate"></ngx-no-data-message>
				</ng-container>
				<ng-template #noFoundScreenshot>
					<ngx-no-data-message [message]="'ACTIVITY.NO_SCREENSHOTS' | translate"></ngx-no-data-message>
				</ng-template>
			</ng-template>
		</div>
		<div class="selected-items-action" *ngIf="selectedIdsCount > 0">
			<div class="card p-4 w-100">
				<div class="row align-items-center">
					<div class="col">{{ this.selectedIdsCount }} Screen selected</div>
					<div class="col-auto">
						<button
							class="ml-auto mr-2 select-hidden"
							status="info"
							nbButton
							size="small"
							(click)="toggleAllSelect()"
						>
							{{ this.allSelected ? 'Unselect All' : 'Select All' }}
						</button>
						<button
							class="ml-auto select-hidden"
							status="danger"
							nbButton
							size="small"
							(click)="deleteSlots()"
						>
							<nb-icon icon="trash-2-outline"></nb-icon>
						</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
