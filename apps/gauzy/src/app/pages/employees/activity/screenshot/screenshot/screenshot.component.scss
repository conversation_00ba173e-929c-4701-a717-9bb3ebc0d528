@forward 'report';
@forward '../../time-activities/time-activities/time-activities.component';

:host {
  ::ng-deep {
    nb-progress-bar.size-tiny .progress-container {
      height: 4px;
    }
  }
  .curser-pointer {
    cursor: pointer;
  }
  .activity {
    .image-container {
      background: rgba($color: #000000, $alpha: 0.1);
      min-height: 130px;
      position: relative;
      .hover {
        padding: 5px;
        display: flex;
        flex-direction: column;
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba($color: #000000, $alpha: 0.7);
        opacity: 0;
        z-index: 9;
        align-items: center;
        justify-content: center;
        transition: opacity 0.5s;
      }
      .view-button {
        height: 100%;
        flex-grow: 1;
        display: flex;
        align-items: center;
      }

      &.select-mode {
        .hover {
          justify-content: normal;
        }
        .select-hidden {
          display: none;
        }
      }

      &.select-mode,
      &:hover {
        .hover {
          opacity: 1;
        }
      }

      .no-image {
        padding: 8px;
        background: rgba($color: #000000, $alpha: 0.6);
        color: #fff;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        border-radius: 5px;
        text-align: center;
      }

      img {
        width: 100%;
        height: 130px;
        object-fit: cover;
        &.default-image {
          object-fit: contain;
        }
      }
    }
  }
  .selected-items-action {
    max-width: 500px;
    width: 90%;
    position: fixed;
    bottom: 0;
    display: flex;
    flex-direction: row;
    left: 0;
    right: 0;
    margin: auto;
    z-index: 99;
  }
  ::ng-deep {
    .progress-value {
      span {
        display: none;
      }
    }
  }
  .custom-card {
    .custom-card-body {
      height: calc(100vh - 31rem);
    }
  }
}
.hour-slot-group {
  margin-bottom: 1rem;
  padding: 15px;
  border-radius: nb-theme(border-radius);
  background-color: nb-theme(background-basic-color-1);
}
.grid-container {
  display: grid;
  -webkit-box-align: center;
  align-items: center;
  -webkit-box-pack: start;
  justify-content: start;
  column-gap: 1rem;
  grid-gap: 1rem;
  grid-template-columns: repeat(auto-fill, minmax(12rem, 1fr));
  padding: 0 1rem;
}
.hour-label {
  font-size: 14px;
  font-weight: 600;
  line-height: 17px;
  letter-spacing: 0em;
  text-align: left;
  text-transform: none;
}
@media screen and (min-width: 1690px) {
  .grid-container {
    grid-template-columns: repeat(6, 1fr);
  }
}
