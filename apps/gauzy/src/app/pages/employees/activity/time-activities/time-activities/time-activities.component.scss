@use 'themes' as *;

:host .custom-card {
  border-radius: 0 nb-theme(border-radius) nb-theme(border-radius) nb-theme(border-radius);
  background-color: nb-theme(gauzy-card-2);
  @include nb-ltr(padding, 1rem 0.5rem 1rem 18px);
  @include nb-rtl(padding, 1rem 18px 1rem 0.5rem);
  .custom-card-body {
    overflow-y: overlay;
    overflow-x: hidden;
    padding-right: 11px;
    height: calc(100vh - 31.25rem);
  }
}

:host .filters {
  @include nb-ltr(padding-right, 0.5rem);
  @include nb-rtl(padding-left, 0.5rem);
}

:host ga-daily-grid ::ng-deep {
  .no-data {
    min-height: 10rem;
    height: calc(100vh - 34rem) !important;
  }
}
