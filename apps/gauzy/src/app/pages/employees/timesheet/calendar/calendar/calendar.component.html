<nb-card [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
	<nb-card-header class="p-0">
		<div class="filters">
			<div class="add-button">
				<ng-template [ngxPermissionsOnly]="PermissionsEnum.ALLOW_MANUAL_TIME">
					<ng-template ngxTimeTrackingAuthorized [permission]="PermissionsEnum.ALLOW_MANUAL_TIME">
						<button nbButton status="success" size="small" (click)="openDialog()">
							<nb-icon icon="plus-outline"></nb-icon>
							{{ 'TIMESHEET.ADD_TIME' | translate }}
						</button>
					</ng-template>
				</ng-template>
			</div>
			<ngx-gauzy-filters
				[isTimeFormat]="true"
				[filters]="filters"
				[saveFilters]="(datePickerConfig$ | async).isSaveDatePicker"
				(filtersChange)="filtersChange($event)"
			></ngx-gauzy-filters>
		</div>
	</nb-card-header>
	<nb-card-body>
		<full-calendar
			#calendar
			class="custom-calendar"
			[options]="calendarOptions"
			[nbSpinner]="loading"
			nbSpinnerSize="giant"
			nbSpinnerStatus="primary"
		></full-calendar>
	</nb-card-body>
</nb-card>
