<nb-card>
	<nb-card-header class="p-0">
		<div class="filters">
			<div class="gauzy-action">
				<ngx-gauzy-button-action
					[hasLayoutSelector]="false"
					[isDisable]="isRowSelected() ? false : !isCheckboxSelected()"
				>
					<ng-container
						buttonTemplate
						[ngTemplateOutlet]="actionButtons"
						[ngTemplateOutletContext]="{ $implicit: selectedLog?.data }"
					></ng-container>
					<ng-container buttonTemplateVisible [ngTemplateOutlet]="visibleButtons"></ng-container>
				</ngx-gauzy-button-action>
			</div>
			<ngx-gauzy-filters
				[isTimeFormat]="true"
				[saveFilters]="(datePickerConfig$ | async).isSaveDatePicker"
				(filtersChange)="filtersChange($event)"
			></ngx-gauzy-filters>
		</div>
	</nb-card-header>
	<nb-card-body>
		<div class="log-container">
			<div class="custom-header-container">
				<div class="row custom-header m-0 align-items-center">
					<ng-template [ngxPermissionsOnly]="PermissionsEnum.ALLOW_DELETE_TIME">
						<ng-template ngxTimeTrackingAuthorized [permission]="PermissionsEnum.ALLOW_DELETE_TIME">
							<div class="col-auto">
								<nb-checkbox
									status="basic"
									[checked]="allChecked"
									[indeterminate]="isIndeterminate()"
									(checkedChange)="checkedAll($event)"
								></nb-checkbox>
							</div>
						</ng-template>
					</ng-template>
					<div class="col-3">{{ 'TIMESHEET.PROJECT' | translate }} / {{ 'TIMESHEET.TODO' | translate }}</div>
					<div class="col" *ngxPermissionsOnly="[PermissionsEnum.CHANGE_SELECTED_EMPLOYEE]">
						{{ 'TIMESHEET.EMPLOYEE' | translate }}
					</div>
					<div class="col">
						{{ 'TIMESHEET.LOG_TYPE' | translate }}
					</div>
					<div class="col">
						{{ 'TIMESHEET.SOURCE' | translate }}
					</div>
					<div class="col">
						{{ 'TIMESHEET.DURATION' | translate }}
					</div>
					<div class="col-2">
						{{ 'TIMESHEET.TIME_SPAN' | translate }}
					</div>
					<div class="col">
						{{ 'TIMESHEET.NOTES' | translate }}
					</div>
				</div>
			</div>
			<div [nbSpinner]="loading" nbSpinnerSize="giant" nbSpinnerStatus="primary" class="custom-body">
				<ng-container *ngIf="logs$ | async">
					<ng-template [ngIf]="logs?.length > 0" [ngIfElse]="notFound">
						<div class="content" style="cursor: pointer">
							<ng-container *ngFor="let log of logs; let last = last">
								<div
									[ngClass]="{ 'border-bottom': !last, selected: log?.isSelected }"
									class="row m-0 py-3 align-items-center"
									(click)="userRowSelect(log)"
								>
									<ng-template [ngxPermissionsOnly]="PermissionsEnum.ALLOW_DELETE_TIME">
										<ng-template
											ngxTimeTrackingAuthorized
											[permission]="PermissionsEnum.ALLOW_DELETE_TIME"
										>
											<div class="col-auto">
												<nb-checkbox
													[checked]="log.checked"
													(click)="$event.stopPropagation()"
													(checkedChange)="toggleCheckbox($event, log)"
													status="basic"
													[disabled]="log.isRunning"
												></nb-checkbox>
											</div>
										</ng-template>
									</ng-template>
									<div class="col-3">
										<ngx-project *ngIf="log?.project; else noProject" [rowData]="log"></ngx-project>
										<ng-template #noProject>
											<span>
												{{ 'TIMESHEET.NO_PROJECT' | translate }}
											</span>
										</ng-template>
										<div class="mt-2 small">
											<span *ngIf="log?.task; else noToDo" [nbTooltip]="log?.task?.title">
												<strong>{{ 'TIMESHEET.TODO' | translate }}:</strong>
												{{ log?.task?.title | truncate : 40 }}
											</span>
											<ng-template #noToDo>
												<span>{{ 'TIMESHEET.NO_TODO' | translate }}</span>
											</ng-template>
										</div>
									</div>
									<div
										class="col employee-name"
										*ngxPermissionsOnly="[PermissionsEnum.CHANGE_SELECTED_EMPLOYEE]"
									>
										<div class="name-container">
											<ngx-avatar
												class="report-table"
												[name]="log?.employee?.user?.name"
												[src]="log?.employee?.user?.imageUrl"
												[id]="log?.employee?.id"
												[employee]="log?.employee"
											></ngx-avatar>
										</div>
									</div>
									<div class="col">
										<span class="log">
											{{ log.logType | titlecase }}
										</span>
									</div>
									<div class="col source-version">
										<ngx-badge-label
											[text]="log?.source | replace : '_' : ' ' | titlecase"
											class="badge-item"
										></ngx-badge-label>

										<ngx-badge-label
											*ngIf="log?.version"
											[text]="log?.version"
											status="info"
											class="badge-item"
										></ngx-badge-label>
									</div>
									<div class="col">
										{{ log.duration | durationFormat }}
									</div>
									<div class="col-2">
										<span class="start-timer">
											{{
												log.startedAt
													| utcToTimezone : filters?.timeZone
													| timeFormat : filters?.timeFormat
											}}
										</span>
										-
										<span *ngIf="!log.isRunning; else runningTimer" class="end-timer">
											{{
												log.stoppedAt
													| utcToTimezone : filters?.timeZone
													| timeFormat : filters?.timeFormat
											}}
										</span>
										<ng-template #runningTimer>
											<span class="running-timer">
												{{ 'TIMESHEET.TILL_NOW' | translate }}
											</span>
										</ng-template>
									</div>
									<div class="col">
										{{ log?.description }}
									</div>
								</div>
							</ng-container>
						</div>
					</ng-template>
					<ng-template #notFound>
						<div class="h-100">
							<ngx-no-data-message>
								<div
									message
									[innerHTML]="'REPORT_PAGE.NO_DATA.DAILY_TIME_AND_ACTIVITY' | translate"
								></div>
							</ngx-no-data-message>
						</div>
					</ng-template>
				</ng-container>
			</div>
		</div>
	</nb-card-body>
</nb-card>
<ng-template #actionButtons let-selectedItem>
	<div class="actions">
		<ng-template [ngxPermissionsOnly]="PermissionsEnum.ALLOW_DELETE_TIME">
			<ng-template ngxTimeTrackingAuthorized [permission]="PermissionsEnum.ALLOW_DELETE_TIME">
				<ng-template [ngIf]="isCheckboxSelected() && contextMenus?.length > 0">
					<button
						outline
						nbButton
						status="primary"
						class="action"
						size="small"
						[title]="'TIMESHEET.BULK_ACTION' | translate"
						[nbContextMenu]="contextMenus"
						nbContextMenuTag="time-logs-bulk-action"
					>
						{{ 'TIMESHEET.BULK_ACTION' | translate }}
						<nb-icon icon="chevron-down-outline"></nb-icon>
					</button>
				</ng-template>
			</ng-template>
		</ng-template>
		<ng-container *ngIf="selectedItem">
			<button
				nbButton
				status="basic"
				class="action secondary"
				size="small"
				[disabled]="disableButton"
				(click)="openView(selectedItem)"
			>
				<nb-icon icon="eye-outline"></nb-icon>
				{{ 'TIMESHEET.VIEW' | translate }}
			</button>
			<ng-template [ngxPermissionsOnly]="PermissionsEnum.ALLOW_MODIFY_TIME">
				<ng-template ngxTimeTrackingAuthorized [permission]="PermissionsEnum.ALLOW_MODIFY_TIME">
					<button
						[disabled]="selectedItem?.isRunning"
						nbButton
						status="basic"
						class="action primary"
						[disabled]="selectedItem?.isRunning || disableButton"
						size="small"
						(click)="openEdit(selectedItem)"
					>
						<nb-icon icon="edit"></nb-icon>
						{{ 'TIMESHEET.EDIT' | translate }}
					</button>
				</ng-template>
			</ng-template>
			<ng-template [ngxPermissionsOnly]="PermissionsEnum.ALLOW_DELETE_TIME">
				<ng-template ngxTimeTrackingAuthorized [permission]="PermissionsEnum.ALLOW_DELETE_TIME">
					<button
						[disabled]="selectedItem?.isRunning"
						nbButton
						status="basic"
						size="small"
						class="action"
						[disabled]="selectedItem?.isRunning || disableButton"
						ngxConfirmDialog
						[message]="'TIMESHEET.DELETE_TIMELOG' | translate"
						(confirm)="onDeleteConfirm(selectedItem)"
						[nbTooltip]="'TIMESHEET.DELETE' | translate"
					>
						<nb-icon status="danger" icon="trash-2-outline"></nb-icon>
					</button>
				</ng-template>
			</ng-template>
		</ng-container>
	</div>
</ng-template>
<ng-template #visibleButtons>
	<ng-template [ngxPermissionsOnly]="PermissionsEnum.ALLOW_MANUAL_TIME">
		<ng-template ngxTimeTrackingAuthorized [permission]="PermissionsEnum.ALLOW_MANUAL_TIME">
			<button class="action" size="small" nbButton status="success" (click)="openAdd()">
				<nb-icon icon="plus-outline"></nb-icon>
				{{ 'TIMESHEET.ADD_TIME' | translate }}
			</button>
		</ng-template>
	</ng-template>
</ng-template>
