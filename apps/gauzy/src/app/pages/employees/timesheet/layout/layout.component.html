<nb-card>
	<nb-card-header class="header">
		<div class="col-auto">
			<h4>
				<ngx-header-title>
					{{ 'MENU.TIMESHEETS' | translate }}
				</ngx-header-title>
			</h4>
			<ng-container *ngIf="selectedDateRange$ | async as selectedDateRange">
				<ngx-date-range-title
					[start]="selectedDateRange?.startDate"
					[end]="selectedDateRange?.endDate"
					[format]="'dddd, LL'"
				></ngx-date-range-title>
			</ng-container>
		</div>
	</nb-card-header>
	<nb-card-body class="p-0">
		<gz-dynamic-tabs [tabsetId]="tabsetId"></gz-dynamic-tabs>
	</nb-card-body>
</nb-card>
