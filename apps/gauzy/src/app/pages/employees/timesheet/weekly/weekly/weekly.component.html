<nb-card>
	<nb-card-header class="p-0">
		<div class="filters">
			<div class="add-button">
				<ng-template
					[ngxPermissionsOnly]="PermissionsEnum.ALLOW_MANUAL_TIME"
				>
					<ng-template
						ngxTimeTrackingAuthorized
						[permission]="PermissionsEnum.ALLOW_MANUAL_TIME"
					>
						<button
							nbButton
							status="success"
							size="small"
							(click)="openAddEdit()"
						>
							<nb-icon icon="plus-outline"></nb-icon>
							{{ 'TIMESHEET.ADD_TIME' | translate }}
						</button>
					</ng-template>
				</ng-template>
			</div>
			<ngx-gauzy-filters
				[saveFilters]="(datePickerConfig$ | async).isSaveDatePicker"
				(filtersChange)="filtersChange($event)"
			></ngx-gauzy-filters>
		</div>
	</nb-card-header>
	<nb-card-body class="weekly-logs row-table timesheet-weekly-container">
		<div class="custom-header-container">
			<div class="row custom-header m-0 align-items-center">
				<div class="col-sm-3 project-name">
					{{ 'TIMESHEET.PROJECT' | translate }} /
					{{ 'TIMESHEET.TODO' | translate }}
				</div>
				<div
					class="col text-center day-col"
					*ngFor="let date of weekDayList"
				>
					{{ date | dateFormat }}
				</div>
			</div>
		</div>

		<div
			[nbSpinner]="loading"
			nbSpinnerSize="giant"
			nbSpinnerStatus="primary"
			class="custom-body"
		>
			<ng-template [ngIf]="weekData?.length > 0" [ngIfElse]="notFound">
				<div class="content">
					<div
						[class]="
							weekData.at(-1) == day
								? 'row m-0 py-3 align-items-center'
								: 'row border-bottom m-0 py-3 align-items-center'
						"
						*ngFor="let day of weekData"
					>
						<div class="col-sm-3 project-name">
							<ngx-project
								*ngIf="day?.project; else noProject"
								[rowData]="day"
							></ngx-project>
							<ng-template #noProject>
								<span>
									{{ 'TIMESHEET.NO_PROJECT' | translate }}
								</span>
							</ng-template>
						</div>
						<div
							class="col text-center day-col"
							*ngFor="let date of weekDayList"
						>
							<button
								*ngIf="day.dates[date]?.sum > 0; else noLogsCol"
								nbButton
								ghost
								status="basic"
								[nbPopover]="viewTimeLogComponent"
								[nbPopoverContext]="{
									timeLogs: day.dates[date]?.logs,
									callback: addTimeCallback,
									close: onClose
								}"
								#popover
							>
								{{ day.dates[date]?.sum | durationFormat }}
							</button>
							<ng-template #noLogsCol>
								<ng-template
									[ngxPermissionsOnly]="
										PermissionsEnum.ALLOW_MANUAL_TIME
									"
									[ngxPermissionsOnlyElse]="
										notAllowAddTimeBlock
									"
								>
									<ng-template
										ngxTimeTrackingAuthorized
										[permission]="
											PermissionsEnum.ALLOW_MANUAL_TIME
										"
										[permissionElse]="notAllowAddTimeBlock"
									>
										<button
											nbButton
											outline
											size="small"
											status="primary"
											(click)="
												openAddByDateProject(
													date,
													day?.project
												)
											"
											*ngIf="
												allowAdd(date);
												else notAllowAddTimeBlock
											"
										>
											<nb-icon
												icon="plus-outline"
											></nb-icon>
										</button>
									</ng-template>
								</ng-template>
								<ng-template #notAllowAddTimeBlock>
									-
								</ng-template>
							</ng-template>
						</div>
					</div>
				</div>
			</ng-template>
		</div>
		<ng-template #notFound>
			<ngx-no-data-message
				[message]="'TIMESHEET.NO_DATA.WEEKLY_TIMESHEET' | translate"
			></ngx-no-data-message>
		</ng-template>
	</nb-card-body>
</nb-card>
