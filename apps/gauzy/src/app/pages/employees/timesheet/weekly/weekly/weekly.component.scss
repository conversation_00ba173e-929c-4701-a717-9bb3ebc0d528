@use 'gauzy/_gauzy-table' as *;
@forward '../../calendar/calendar/calendar.component';

:host {
  .filters {
    .form-control {
      min-height: 40px;
    }
  }

  .week-date-input {
    position: relative;

    input {
      opacity: 0;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
    }
  }

  .weekly-logs-table {
    td,
    th {
      padding: 16px;
    }

    .day-col {
      text-align: left;
    }
  }

  .custom-header {
    font-size: 12px;
    font-weight: 600;
    line-height: 15px;
    letter-spacing: 0em;
    text-align: left;
    color: nb-theme(gauzy-text-color-2);
    background: var(--gauzy-card-4);
    border-radius: nb-theme(border-radius);
    padding: 10px;
    padding-left: 12px;
    flex-wrap: nowrap;
  }

  nb-card-body {
    .custom-header-container {
      @include nb-ltr(padding, 0 0.5rem 0 0);
      @include nb-rtl(padding, 0 0 0 0.5rem);
    }
    .custom-body {
      border-radius: nb-theme(border-radius);
      margin-top: 6px;
      overflow: auto;
	    height: 100%;
      @include nb-ltr(padding, 0 0.5rem 0 0);
      @include nb-rtl(padding, 0 0 0 0.5rem);
      .content {
        background-color: var(--gauzy-card-3);
        border-radius: var(--border-radius);
        @include nb-ltr(padding-left, 12px);
        @include nb-rtl(padding-right, 12px);
      }
      .border-bottom {
        border-bottom: 1px solid rgba(126, 126, 143, 0.1) !important;
      }

      [nbButton].appearance-outline.status-primary {
        border: none;
      }

      .item {
        cursor: pointer;
      }

      .selected {
        background-color: var(--gauzy-sidebar-background-3);
        box-shadow: -6px 0 0 0 rgba(48, 48, 120, 0.2);
        border-radius: var(--border-radius) 0 0 var(--border-radius);

        &.border-bottom {
          border-bottom: none;
        }
      }
    }
  }

  .project-name {
    flex: 0 0 20%;
    max-width: 20%;
  }

  nb-checkbox ::ng-deep .custom-checkbox {
    border-width: 2px;
  }

  [nbButton].appearance-outline.status-primary {
    border-style: none;
  }

  [nbButton].appearance-ghost.status-basic {
    background-color: nb-theme(gauzy-sidebar-background-4);
    font-size: 12px;
    font-weight: 600;
    line-height: 15px;
    letter-spacing: 0em;
    text-align: left;
    border: none;
  }

  .gauzy-action {
    position: absolute;
    @include nb-rtl(left, 1rem);
    @include nb-ltr(right, 1rem);
    top: -0.75rem;
  }
}

@media screen and (max-width: 1280px) {
  .content .project-name {
    font-size: 14px;
  }

  .content .text-center button {
    padding: 8px 8px;
    font-size: 10px;
  }
}
