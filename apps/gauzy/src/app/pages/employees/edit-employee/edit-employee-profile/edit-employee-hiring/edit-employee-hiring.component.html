<nb-card class="m-3">
	<nb-card-body>
		<form [formGroup]="form">
			<div class="row">
				<div class="col">
					<div class="form-group">
						<label for="offerDateInput" class="label">{{
							'FORM.LABELS.OFFER_DATE' | translate
						}}</label>
						<input
							fullWidth
							id="offerDateInput"
							formControlName="offerDate"
							nbInput
							[nbDatepicker]="offerDatePicker"
							[placeholder]="'POP_UPS.PICK_DATE' | translate"
							autocomplete="off"
						/>
						<nb-datepicker #offerDatePicker></nb-datepicker>
					</div>
				</div>
				<div class="col">
					<div class="form-group">
						<label for="acceptDateInput" class="label">{{
							'FORM.LABELS.ACCEPT_DATE' | translate
						}}</label>
						<input
							fullWidth
							id="acceptDateInput"
							formControlName="acceptDate"
							nbInput
							[nbDatepicker]="acceptDatePicker"
							[placeholder]="'POP_UPS.PICK_DATE' | translate"
							autocomplete="off"
							[status]="
								form.get('acceptDate').invalid
									? 'danger'
									: 'basic'
							"
						/>
						<nb-datepicker #acceptDatePicker></nb-datepicker>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col">
					<div class="form-group">
						<label for="rejectDateInput" class="label">{{
							'FORM.LABELS.REJECT_DATE' | translate
						}}</label>
						<input
							fullWidth
							id="rejectDateInput"
							formControlName="rejectDate"
							nbInput
							[nbDatepicker]="rejectDatePicker"
							[placeholder]="'POP_UPS.PICK_DATE' | translate"
							autocomplete="off"
							[status]="
								form.get('rejectDate').invalid
									? 'danger'
									: 'basic'
							"
						/>
						<nb-datepicker #rejectDatePicker></nb-datepicker>
					</div>
				</div>
				<div class="col"></div>
			</div>
			<div class="actions">
				<button
					nbButton
					status="success"
					[disabled]="form.invalid"
					(click)="submitForm()"
				>
					{{ 'BUTTONS.SAVE' | translate }}
				</button>
			</div>
		</form>
	</nb-card-body>
</nb-card>
