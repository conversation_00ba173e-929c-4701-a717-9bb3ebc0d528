@use 'gauzy/_gauzy-cards' as *;

$radius: nb-theme(border-radius);

.notes {
  margin-top: 16px;
  padding: 10px;
  border-radius: var(--border-radius);
  background-color: var(--color-warning-transparent-100);
}
.notes p {
  margin: 0;
  color: var(--color-warning-default);
  font-size: 0.75rem;
  font-weight: 600;
  text-align: justify;
}
.card {
  background-color: var(--gauzy-card-2);
  border: unset;
}
:host {
  background: var(--gauzy-card-2);
  overflow-y: auto;

  @include nb-card_overrides(
    auto,
    calc($default-card-height + 0.25rem),
    $default-radius
  );

  nb-card {
    border-radius: 0;
  }

  nb-card-body {
    background-color: var(--gauzy-card-2);
    padding: 1rem;

    .form-container {
      background-color: var(--gauzy-sidebar-background-2);
      border-radius: var(--border-radius);
      padding: 1rem;
    }
  }
}
