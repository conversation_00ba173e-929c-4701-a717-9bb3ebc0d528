<div class="content">
	<div class="organization-container w-25 d-flex flex-column">
		<div class="organization-photo">
			<ngx-image-uploader
				(changeHoverState)="hoverState = $event"
				(uploadedImageAsset)="updateImageAsset($event)"
				(uploadImageAssetError)="handleImageUploadError($event)"
			></ngx-image-uploader>
			<svg
				xmlns="http://www.w3.org/2000/svg"
				xmlns:xlink="http://www.w3.org/1999/xlink"
				width="68"
				height="68"
				viewBox="0 0 68 68"
				[ngStyle]="{ opacity: hoverState ? '1' : '0.3' }"
			>
				<defs>
					<path
						id="a"
						d="M28.667 31.333a2 2 0 1 0-.002-4.001 2 2 0 0 0 .002 4.001m13.333 12H26.748l9.34-7.793c.328-.279.923-.277 1.244-.001l6.001 5.12V42c0 .736-.597 1.333-1.333 1.333M26 24.667h16c.736 0 1.333.597 1.333 1.333v11.152l-4.27-3.643c-1.32-1.122-3.386-1.122-4.694-.008l-9.702 8.096V26c0-.736.597-1.333 1.333-1.333M42 22H26c-2.205 0-4 1.795-4 4v16c0 2.205 1.795 4 4 4h16c2.205 0 4-1.795 4-4V26c0-2.205-1.795-4-4-4"
					/>
				</defs>
				<g fill="none" fill-rule="evenodd">
					<circle
						cx="34"
						cy="34"
						r="34"
						fill="#0091FF"
						opacity=".3"
					/>
					<circle
						cx="34"
						cy="34"
						r="26"
						fill="#0091FF"
						opacity=".9"
					/>
					<use fill="#FFF" fill-rule="nonzero" xlink:href="#a" />
				</g>
			</svg>
			<div [ngStyle]="{ opacity: hoverState ? '0.2' : '0' }"></div>
			<img
				*ngIf="form"
				[src]="form.get('imageUrl').value"
				alt="Profile Photo"
				(mouseenter)="hoverState = true"
				(mouseleave)="hoverState = false"
			/>
		</div>
		<nb-badge
			*ngIf="selectedEmployee && selectedEmployee.isVetted"
			status="success"
			style="position: relative; margin-top: 20px"
			[text]="'EMPLOYEES_PAGE.EDIT_EMPLOYEE.VETTED' | translate"
			position="bottom-left"
		>
		</nb-badge>
		<div *ngIf="selectedEmployee">
			<div
				class="employee-optional-info"
				*ngIf="selectedEmployee.jobSuccess"
			>
				{{ 'EMPLOYEES_PAGE.EDIT_EMPLOYEE.JOB_SUCCESS' | translate }}:
				{{ selectedEmployee.jobSuccess }}%
			</div>
			<div
				class="employee-optional-info"
				*ngIf="selectedEmployee.totalJobs"
			>
				{{ 'EMPLOYEES_PAGE.EDIT_EMPLOYEE.TOTAL_JOBS' | translate }}:
				{{ selectedEmployee.totalJobs }}
			</div>
			<div
				class="employee-optional-info"
				*ngIf="selectedEmployee.totalWorkHours"
			>
				{{ 'EMPLOYEES_PAGE.EDIT_EMPLOYEE.TOTAL_HOURS' | translate }}:
				{{ selectedEmployee.totalWorkHours }}
			</div>
			<div
				class="employee-optional-info"
				*ngIf="
					selectedEmployee.billRateCurrency &&
					selectedEmployee.billRateValue
				"
			>
				{{ 'EMPLOYEES_PAGE.EDIT_EMPLOYEE.RATE' | translate }}:
				{{ selectedEmployee.billRateCurrency }}
				{{ selectedEmployee.billRateValue }} /
				{{ 'EMPLOYEES_PAGE.EDIT_EMPLOYEE.HR' | translate }}
			</div>
		</div>
	</div>

	<div class="employee-form w-75">
		<form [formGroup]="form">
			<div class="row">
				<div class="col">
					<div class="form-group">
						<label class="label" for="usernameInput">{{
							'FORM.USERNAME' | translate
						}}</label>
						<input
							fullWidth
							id="usernameInput"
							type="text"
							nbInput
							formControlName="username"
							placeholder="{{ 'FORM.USERNAME' | translate }}"
						/>
					</div>
				</div>
				<div class="col">
					<div class="form-group">
						<label for="emailInput" class="label">{{
							'FORM.EMAIL' | translate
						}}</label>
						<input
							fullWidth
							id="emailInput"
							type="email"
							nbInput
							formControlName="email"
							placeholder="{{ 'FORM.EMAIL' | translate }}"
						/>
					</div>
				</div>
			</div>

			<div class="row">
				<div class="col">
					<div class="form-group">
						<label class="label" for="firstNameInput">{{
							'FORM.LABELS.FIRST_NAME' | translate
						}}</label>
						<input
							fullWidth
							id="firstNameInput"
							type="text"
							nbInput
							formControlName="firstName"
							placeholder="{{
								'FORM.PLACEHOLDERS.FIRST_NAME' | translate
							}}"
						/>
					</div>
				</div>
				<div class="col">
					<div class="form-group">
						<label for="lastNameInput" class="label">{{
							'FORM.LABELS.LAST_NAME' | translate
						}}</label>
						<input
							fullWidth
							id="lastNameInput"
							type="text"
							nbInput
							formControlName="lastName"
							placeholder="{{
								'FORM.PLACEHOLDERS.LAST_NAME' | translate
							}}"
						/>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-sm-6">
					<div class="form-group">
						<label for="preferredLanguage" class="label">{{
							'FORM.LABELS.PREFERRED_LANGUAGE' | translate
						}}</label>
						<ngx-language-selector
							[placeholder]="'FORM.PLACEHOLDERS.PREFERRED_LANGUAGE'| translate"
							class="d-block"
							selectBy="object"
							[template]="'ng-select'"
							formControlName="preferredLanguage"
						>
						</ngx-language-selector>
					</div>
				</div>
				<div class="col-sm-6">
					<div class="form-group">
						<label for="profileLinkInput" class="label">{{
							'FORM.LABELS.PROFILE_LINK' | translate
						}}</label>
						<input
							fullWidth
							id="profileLinkInput"
							type="text"
							nbInput
							formControlName="profile_link"
							placeholder="{{
								'FORM.PLACEHOLDERS.PROFILE_LINK' | translate
							}}"
						/>
					</div>
				</div>
			</div>

			<div class="actions">
				<button
					[disabled]="form.invalid"
					(click)="submitForm()"
					nbButton
					status="success"
				>
					{{ 'BUTTONS.SAVE' | translate }}
				</button>
			</div>
		</form>
	</div>
</div>
