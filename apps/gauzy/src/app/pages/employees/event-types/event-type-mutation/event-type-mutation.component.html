<nb-card class="main">
	<nb-card-header class="d-flex flex-column">
		<span class="cancel"
			><i class="fas fa-times" (click)="dialogRef.close()"></i
		></span>
		<h4 class="title" *ngIf="eventType; else headerAdd">
			{{ 'POP_UPS.EDIT_EVENT_TYPE' | translate }}
		</h4>
		<ng-template #headerAdd>
			<h4 class="title">{{ 'POP_UPS.ADD_EVENT_TYPE' | translate }}</h4>
		</ng-template>
	</nb-card-header>
	<nb-card-body class="body">
		<form [formGroup]="form">
			<div class="row">
				<div class="col">
					<div class="form-group">
						<label for="employee" class="label">{{
							'FORM.LABELS.EMPLOYEE' | translate
						}}</label>
						<ga-employee-multi-select
							id="employee"
							[multiple]="false"
							[label]="false"
							[placeholder]="
								'FORM.PLACEHOLDERS.ALL_EMPLOYEES' | translate
							"
							[selectedEmployeeIds]="form.get('employeeId').value"
							(selectedChange)="onEmployeeChange($event)"
							formControlName="employeeId"
						>
						</ga-employee-multi-select>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col">
					<div class="form-group">
						<label
							for="title"
							class="label"
							[innerHtml]="'FORM.LABELS.TITLE' | translate"
						></label>
						<input
							fullWidth
							id="title"
							type="text"
							nbInput
							formControlName="title"
							[placeholder]="
								'FORM.PLACEHOLDERS.TITLE' | translate
							"
						/>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col">
					<div class="form-group">
						<label
							for="description"
							class="label"
							[innerHtml]="'FORM.LABELS.DESCRIPTION' | translate"
						></label>
						<input
							fullWidth
							id="description"
							type="text"
							nbInput
							formControlName="description"
							[placeholder]="
								'FORM.PLACEHOLDERS.DESCRIPTION' | translate
							"
						/>
					</div>
				</div>
			</div>
			<div>
				<label for="title" class="label">{{
					'FORM.LABELS.DURATION' | translate
				}}</label>
				<div class="row">
					<div class="col-3">
						<div class="form-group">
							<input
								[placeholder]="
									'FORM.PLACEHOLDERS.DURATION' | translate
								"
								fullWidth
								id="durationInput"
								type="number"
								[min]="0"
								nbInput
								formControlName="duration"
							/>
						</div>
					</div>
					<div class="col-3">
						<nb-select
							class="d-block"
							formControlName="durationUnit"
						>
							<nb-option
								*ngFor="let unit of durationUnits"
								[value]="unit"
							>
								{{
									'EVENT_TYPE_PAGE.DURATION_UNIT'
										| translate: { unit: unit }
								}}
							</nb-option>
						</nb-select>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col">
					<div class="form-group">
						<ga-tags-color-input
							[selectedTags]="form.get('tags').value"
							(selectedTagsEvent)="selectedTagsEvent($event)"
							[isOrgLevel]="true"
						>
						</ga-tags-color-input>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col">
					<div class="form-group">
						<nb-checkbox formControlName="isActive">
							<span
								[nbTooltip]="
									'EVENT_TYPE_PAGE.ACTIVE' | translate
								"
								[innerHtml]="
									'EVENT_TYPE_PAGE.ACTIVE' | translate
								"
							></span>
						</nb-checkbox>
					</div>
				</div>
			</div>
		</form>
	</nb-card-body>
	<nb-card-footer class="text-left">
		<button
			(click)="dialogRef.close()"
			status="basic"
			outline
			class="mr-2"
			nbButton
		>
			{{ 'BUTTONS.CANCEL' | translate }}
		</button>
		<button
			[disabled]="form.invalid"
			(click)="addOrEditEventType()"
			status="success"
			nbButton
		>
			{{ 'BUTTONS.SAVE' | translate }}
		</button>
	</nb-card-footer>
</nb-card>
