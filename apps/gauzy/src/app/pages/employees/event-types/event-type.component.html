<nb-card
	[nbSpinner]="loading"
	nbSpinnerStatus="primary"
	nbSpinnerSize="large"
	class="card-scroll"
>
	<nb-card-header class="d-flex flex-column pb-0">
		<div class="d-flex">
			<ngx-back-navigation></ngx-back-navigation>
			<h4>
				<ngx-header-title>
					{{ 'EVENT_TYPE_PAGE.EVENT_TYPE' | translate }}
				</ngx-header-title>
			</h4>
		</div>
		<div class="align-self-end">
			<ngx-gauzy-button-action
				[componentName]="viewComponentName"
				[buttonTemplateVisible]="visible"
				[buttonTemplate]="actionButtons"
				[isDisable]="disableButton"
			></ngx-gauzy-button-action>
		</div>
	</nb-card-header>
	<nb-card-body>
		<ng-template
			[ngIf]="dataLayoutStyle === componentLayoutStyleEnum.TABLE"
			[ngIfElse]="gridLayout"
		>
			<div class="table-scroll-container custom-table">
				<angular2-smart-table
					style="cursor: pointer"
					[settings]="smartTableSettings"
					[source]="smartTableSource"
					(userRowSelect)="selectEventType($event)"
				></angular2-smart-table>
			</div>
			<div class="pagination-container">
				<ng-container *ngIf="smartTableSource">
					<ngx-pagination
						[source]="smartTableSource"
					></ngx-pagination>
				</ng-container>
			</div>
		</ng-template>
		<ng-template #gridLayout>
			<ga-card-grid
				[totalItems]="pagination?.totalItems"
				[settings]="smartTableSettings"
				[source]="eventTypes"
				(onSelectedItem)="selectEventType($event)"
				(scroll)="onScroll()"
			></ga-card-grid>
		</ng-template>
	</nb-card-body>
</nb-card>
<ng-template #actionButtons let-selectedItem="selectedItem">
	<div class="btn-group actions">
		<button
			nbButton
			[disabled]="!selectedItem && disableButton"
			(click)="openEditEventTypeDialog(selectedItem)"
			status="basic"
			class="action primary"
			size="small"
		>
			<nb-icon class="mr-1" icon="edit-outline"></nb-icon
			>{{ 'BUTTONS.EDIT' | translate }}
		</button>
		<button
			nbButton
			[disabled]="!selectedItem && disableButton"
			(click)="deleteEventType(selectedItem)"
			status="basic"
			class="action"
			size="small"
		>
			<nb-icon status="danger" icon="trash-2-outline"></nb-icon>
		</button>
	</div>
</ng-template>
<ng-template #visible>
	<button
		nbButton
		status="success"
		(click)="openAddEventTypeDialog()"
		class="action"
		size="small"
	>
		<nb-icon class="mr-1" icon="plus-outline"></nb-icon
		>{{ 'BUTTONS.ADD' | translate }}
	</button>
</ng-template>
