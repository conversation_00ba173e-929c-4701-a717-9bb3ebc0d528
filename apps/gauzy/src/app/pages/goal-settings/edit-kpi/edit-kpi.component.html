<nb-card class="min-width-60vw">
	<nb-card-header class="d-flex flex-column">
		<span class="cancel"
			><i class="fas fa-times" (click)="closeDialog(null)"></i
		></span>
		<h6 class="title">
			{{
				(selectedKPI
					? 'GOALS_PAGE.SETTINGS.EDIT_KPI'
					: 'GOALS_PAGE.SETTINGS.ADD_KPI'
				) | translate
			}}
		</h6>
	</nb-card-header>
	<nb-card-body>
		<form [formGroup]="kpiForm">
			<div
				class="row"
				(mouseenter)="helper = 'kpi-title'"
				(mouseleave)="helperText(null)"
			>
				<div class="col-md-7">
					<label for="kpi-title" class="label">
						{{ 'FORM.LABELS.NAME' | translate }}
					</label>
					<input
						type="text"
						id="kpi-title"
						nbInput
						fullWidth
						formControlName="name"
						placeholder="{{
							'GOALS_PAGE.FORM.PLACEHOLDERS.KPI_NAME' | translate
						}}"
					/>
				</div>
				<div class="col-md-5 mt-3 position-relative">
					<p
						class="mt-3 position-absolute"
						*ngIf="helper === ''"
						innerHtml="{{
							'GOALS_PAGE.HELPER_TEXT.KPI_GENERAL' | translate
						}}"
					></p>
					<div
						class="mt-3 position-absolute"
						*ngIf="helper === 'kpi-title'"
						innerHtml="{{
							'GOALS_PAGE.HELPER_TEXT.KPI_NAME' | translate
						}}"
					></div>
				</div>
			</div>
			<div class="row">
				<div class="col-md-7">
					<label for="kpi-description" class="label mt-3">
						{{ 'FORM.LABELS.DESCRIPTION_OPTIONAL' | translate }}
					</label>
					<textarea
						(mouseenter)="helperText($event)"
						(mouseleave)="helperText(null)"
						id="kpi-description"
						nbInput
						fullWidth
						formControlName="description"
						placeholder="{{
							'GOALS_PAGE.FORM.PLACEHOLDERS.KPI_DESCRIPTION'
								| translate
						}}"
					></textarea>
				</div>
				<div class="col-md-5 mt-3 position-relative">
					<div
						class="mt-3 position-absolute"
						*ngIf="helper === 'kpi-description'"
					>
						<p>
							{{
								'GOALS_PAGE.HELPER_TEXT.KPI_DESCRIPTION'
									| translate
							}}
						</p>
					</div>
				</div>
			</div>

			<div class="row">
				<div
					class="col-md-7"
					(mouseenter)="helper = 'kpi-type'"
					(mouseleave)="helperText(null)"
				>
					<div class="row mt-3">
						<div
							[ngClass]="{
								'col-md-6':
									kpiForm.value.type ==
										kpiMetricEnum.NUMERICAL ||
									kpiForm.value.type ==
										kpiMetricEnum.CURRENCY,
								'col-md-12':
									kpiForm.value.type !=
										kpiMetricEnum.NUMERICAL &&
									kpiForm.value.type != kpiMetricEnum.CURRENCY
							}"
						>
							<label for="kpi-type" class="label">
								{{
									'GOALS_PAGE.FORM.LABELS.KPI_METRIC'
										| translate
								}}
							</label>
							<nb-select
								id="kpi-type"
								fullWidth
								formControlName="type"
							>
								<nb-option
									*ngFor="
										let type of kpiMetricEnum | keyvalue
									"
									[value]="type.value"
									>{{
										'GOALS_PAGE.KPI_METRIC.' + type.key
											| translate
									}}</nb-option
								>
							</nb-select>
						</div>
						<div
							class="col-md-6"
							*ngIf="
								kpiForm.value.type == kpiMetricEnum.NUMERICAL ||
								kpiForm.value.type == kpiMetricEnum.CURRENCY
							"
						>
							<ga-goal-custom-unit-select
								[parentFormGroup]="kpiForm"
								[numberUnits]="numberUnitsEnum"
							></ga-goal-custom-unit-select>
						</div>
					</div>
				</div>
				<div class="col-md-5 mt-3 position-relative">
					<div
						class="mt-3 position-absolute"
						*ngIf="helper === 'kpi-type'"
					>
						<p>
							{{
								'GOALS_PAGE.HELPER_TEXT.KPI_METRIC' | translate
							}}
						</p>
					</div>
				</div>
			</div>

			<div class="row">
				<div
					class="col-md-7"
					(mouseenter)="helper = 'kpi-lead'"
					(mouseleave)="helperText(null)"
				>
					<label for="kpi-lead" class="label mt-3">
						{{ 'GOALS_PAGE.FORM.LABELS.LEAD' | translate }}</label
					>
					<ga-employee-multi-select
						[multiple]="false"
						[allEmployees]="employees"
						[selectedEmployeeIds]="kpiForm.value.lead"
						[label]="false"
						(selectedChange)="selectEmployee($event, 'lead')"
						id="kpi-lead"
						placeholder="{{
							'GOALS_PAGE.FORM.LABELS.LEAD' | translate
						}}"
						class="header-selector employee-selector"
					></ga-employee-multi-select>
				</div>
				<div class="mt-3 col-md-5 position-relative">
					<div
						class="mt-3 position-absolute"
						*ngIf="helper === 'kpi-lead'"
					>
						<p>
							{{ 'GOALS_PAGE.HELPER_TEXT.KPI_LEAD' | translate }}
						</p>
					</div>
				</div>
			</div>

			<div class="row">
				<div class="col-md-7">
					<div class="row mt-3">
						<div class="col-md-4">
							<label for="current-value" class="label">
								{{
									'GOALS_PAGE.FORM.LABELS.CURRENT_VALUE'
										| translate
								}}
							</label>
							<input
								type="number"
								id="current-value"
								nbInput
								fullWidth
								formControlName="currentValue"
								placeholder="{{
									'KEY_RESULT_PAGE.FORM.LABELS.INITIAL_VALUE'
										| translate
								}}"
							/>
						</div>
						<div class="col-md-4">
							<label for="kpi-operator" class="label">
								{{
									'GOALS_PAGE.FORM.LABELS.KPI_SHOULD_BE'
										| translate
								}}
							</label>
							<nb-select
								fullWidth
								id="kpi-operator"
								formControlName="operator"
							>
								<nb-option
									*ngFor="
										let operator of kpiOperatorEnum
											| keyvalue
									"
									[value]="operator.value"
									>{{
										'GOALS_PAGE.KPI_OPERATOR.' +
											operator.key | translate
									}}
								</nb-option>
							</nb-select>
						</div>
						<div class="col-md-4">
							<label for="target-value" class="label">
								{{
									'KEY_RESULT_PAGE.FORM.LABELS.TARGET_VALUE'
										| translate
								}}
							</label>
							<input
								type="number"
								id="target-value"
								nbInput
								fullWidth
								formControlName="targetValue"
								placeholder="{{
									'KEY_RESULT_PAGE.FORM.LABELS.TARGET_VALUE'
										| translate
								}}"
							/>
						</div>
					</div>
				</div>
			</div>
		</form>
	</nb-card-body>
	<nb-card-footer class="text-left">
		<button
			class="mr-3"
			nbButton
			outline
			status="basic"
			(click)="closeDialog(null)"
		>
			{{ 'BUTTONS.CANCEL' | translate }}
		</button>
		<button
			[disabled]="kpiForm.invalid"
			nbButton
			status="success"
			(click)="saveKeyResult()"
		>
			{{ 'BUTTONS.SAVE' | translate }}
		</button>
	</nb-card-footer>
</nb-card>
