<nb-card class="card-scroll">
	<nb-card-header class="card-header-title">
		<div class="card-header-title">
			<h4>
				<ngx-header-title [allowEmployee]="false">
					{{ 'GOALS_PAGE.GOAL_SETTINGS' | translate }}
				</ngx-header-title>
			</h4>
		</div>
	</nb-card-header>
	<nb-card-body class="p-0">
		<div *ngIf="selectedTab != 'general'" class="gauzy-button-container">
			<ngx-gauzy-button-action
				[buttonTemplateVisible]="visibleButton"
				[isDisable]="!selectedItem && disableButton"
				[buttonTemplate]="actionButtons"
				[componentName]="viewComponentName"
			>
			</ngx-gauzy-button-action>
		</div>
		<nb-tabset class="nb-tabset" (changeTab)="tabChange($event)">
			<nb-tab
				tabId="general"
				tabTitle="{{ 'GOALS_PAGE.SETTINGS.GENERAL' | translate }}"
			>
				<div class="card-general-container">
					<nb-card>
						<nb-card-body class="card-general">
							<form
								[formGroup]="generalSettingsForm"
								(change)="saveGeneralSettings()"
							>
								<div class="row">
									<div class="col-md-4">
										<p class="font-weight-bold">
											{{
												'GOALS_PAGE.SETTINGS.MAX_ENTITIES'
													| translate
											}}
										</p>
										<div class="row">
											<div class="col-md-4">
												<label
													class="label"
													for="max-goals"
												>
													{{
														'GOALS_PAGE.FORM.LABELS.OBJECTIVE'
															| translate
													}}
												</label>
												<input
													nbInput
													id="max-goals"
													min="5"
													fullWidth
													formControlName="maxObjectives"
													type="number"
												/>
											</div>
											<div class="col-md-4">
												<label
													class="label"
													for="max-key-result"
												>
													{{
														'GOALS_PAGE.FORM.LABELS.KEY_RESULT'
															| translate
													}}
												</label>
												<input
													nbInput
													id="max-key-result"
													fullWidth
													formControlName="maxKeyResults"
													min="5"
													type="number"
												/>
											</div>
										</div>
									</div>
									<div class="col-md-4">
										<p class="font-weight-bold">
											{{
												'GOALS_PAGE.SETTINGS.EMPLOYEE_OBJECTIVES'
													| translate
											}}
										</p>
										<nb-toggle
											formControlName="employeeCanCreateObjective"
										></nb-toggle>
									</div>
								</div>

								<div class="row mt-3">
									<div class="col-md-4">
										<p class="mt-3 font-weight-bold">
											{{
												'GOALS_PAGE.SETTINGS.WHO_CAN_OWN_OBJECTIVES'
													| translate
											}}
										</p>
										<nb-select
											fullWidth
											(selectedChange)="
												saveGeneralSettings()
											"
											formControlName="canOwnObjectives"
										>
											<nb-option
												*ngFor="
													let level of goalOwnershipEnum
														| keyvalue
												"
												[value]="level.value"
											>
												{{
													'GOALS_PAGE.OWNERSHIP.' +
														level.key | translate
												}}
											</nb-option>
										</nb-select>
									</div>
									<div class="col-md-4">
										<p class="mt-3 font-weight-bold">
											{{
												'GOALS_PAGE.SETTINGS.WHO_CAN_OWN_KEY_RESULTS'
													| translate
											}}
										</p>
										<nb-select
											fullWidth
											(selectedChange)="
												saveGeneralSettings()
											"
											formControlName="canOwnKeyResult"
										>
											<nb-option
												*ngFor="
													let level of goalOwnershipEnum
														| keyvalue
												"
												[value]="level.value"
											>
												{{
													'GOALS_PAGE.OWNERSHIP.' +
														level.key | translate
												}}
											</nb-option>
										</nb-select>
									</div>
								</div>
								<div class="row mt-3">
									<div class="col-md-4">
										<p class="mt-3 font-weight-bold">
											{{
												'GOALS_PAGE.SETTINGS.ADD_KPI_TO_KEY_RESULT'
													| translate
											}}
										</p>
										<nb-toggle
											status="basic"
											formControlName="krTypeKPI"
										></nb-toggle>
									</div>
									<div class="col-md-4">
										<p class="mt-3 font-weight-bold">
											{{
												'GOALS_PAGE.SETTINGS.ADD_TASK_TO_KEY_RESULT'
													| translate
											}}
										</p>
										<nb-toggle
											status="basic"
											formControlName="krTypeTask"
										></nb-toggle>
									</div>
								</div>
							</form>
						</nb-card-body>
						<!--Please do not remove this comment section-->
						<!-- <nb-card-footer>
							<button
								nbButton
								status="success"
								(click)="addTemplate()"
							>
								Add Template
							</button>
						</nb-card-footer> -->
					</nb-card>
				</div>
			</nb-tab>
			<nb-tab
				[nbSpinner]="loading"
				nbSpinnerStatus="primary"
				nbSpinnerSize="large"
				tabId="timeframe"
				tabTitle="{{
					'GOALS_PAGE.SETTINGS.TIME_FRAME_PAGE_TITLE' | translate
				}}"
			>
				<ng-container>
					<ng-template
						[ngTemplateOutlet]="
							dataLayoutStyle === 'TABLE'
								? tableLayout
								: gridLayout
						"
					></ng-template>
				</ng-container>
			</nb-tab>
			<nb-tab
				tabId="kpi"
				tabTitle="{{ 'GOALS_PAGE.SETTINGS.KPI' | translate }}"
			>
				<ng-container>
					<ng-template
						[ngTemplateOutlet]="
							dataLayoutStyle === 'TABLE'
								? tableLayout
								: gridLayout
						"
					></ng-template>
				</ng-container>
			</nb-tab>
		</nb-tabset>
	</nb-card-body>
</nb-card>
<ng-template #visibleButton>
	<button
		nbButton
		status="success"
		*ngIf="selectedTab === 'timeframe'"
		(click)="editTimeFrame('add')"
		size="small"
	>
		<nb-icon class="mr-1" icon="plus-outline"></nb-icon
		>{{ 'BUTTONS.ADD' | translate }}
	</button>
	<button
		nbButton
		*ngIf="selectedTab === 'kpi'"
		status="success"
		(click)="editKPI('add')"
		size="small"
	>
		<nb-icon class="mr-1" icon="plus-outline"></nb-icon
		>{{ 'BUTTONS.ADD' | translate }}
	</button>
</ng-template>
<ng-template #actionButtons let-selectedItem="selectedItem">
	<div class="btn-group actions">
		<button
			nbButton
			status="basic"
			class="action secondary"
			[disabled]="disableButton"
			size="small"
			*ngIf="selectedTab === 'kpi'"
			underConstruction
		>
			<nb-icon icon="eye-outline" pack="eva"></nb-icon>
			{{ 'BUTTONS.VIEW' | translate }}
		</button>
		<button
			nbButton
			status="basic"
			class="action secondary"
			[disabled]="disableButton"
			size="small"
			*ngIf="selectedTab !== 'kpi'"
			underConstruction
		>
			<nb-icon icon="eye-outline" pack="eva"></nb-icon>
			{{ 'BUTTONS.VIEW' | translate }}
		</button>
		<button
			*ngIf="selectedTab !== 'kpi'"
			nbButton
			status="basic"
			(click)="editTimeFrame('edit', selectedItem)"
			class="action primary"
			[disabled]="!selectedItem && disableButton"
			size="small"
		>
			<nb-icon class="mr-1" icon="edit-outline"></nb-icon>
			{{ 'BUTTONS.EDIT' | translate }}
		</button>
		<button
			*ngIf="selectedTab !== 'kpi'"
			nbButton
			(click)="deleteTimeFrame(selectedItem)"
			class="action"
			[disabled]="!selectedItem && disableButton"
			size="small"
		>
			<nb-icon status="danger" icon="trash-2-outline"></nb-icon>
		</button>

		<button
			*ngIf="selectedTab === 'kpi'"
			nbButton
			status="basic"
			(click)="editKPI('edit', selectedItem)"
			class="action primary"
			[disabled]="!selectedItem && disableButton"
			size="small"
		>
			<nb-icon icon="edit-outline"></nb-icon>
			{{ 'BUTTONS.EDIT' | translate }}
		</button>
		<button
			*ngIf="selectedTab === 'kpi'"
			nbButton
			status="basic"
			(click)="deleteKPI(selectedItem)"
			class="action"
			[disabled]="!selectedItem && disableButton"
			size="small"
			[nbTooltip]="'BUTTONS.DELETE' | translate"
		>
			<nb-icon status="danger" icon="trash-2-outline"></nb-icon>
		</button>
	</div>
</ng-template>
<ng-template #tableLayout>
	<div class="table-scroll-container">
		<angular2-smart-table
			[settings]="smartTableSettings"
			[source]="smartTableData"
			(userRowSelect)="selectRow($event)"
			style="cursor: pointer"
		></angular2-smart-table>
	</div>
	<div class="pagination-container">
		<ng-container *ngIf="smartTableSource">
			<ngx-pagination
				[source]="smartTableSource"
			></ngx-pagination>
		</ng-container>
	</div>
</ng-template>
<ng-template #gridLayout>
	<ga-card-grid
		[totalItems]="pagination?.totalItems"
		[settings]="smartTableSettings"
		[source]="goalTimeFrames"
		(onSelectedItem)="selectRow($event)"
		(scroll)="onScroll()"
	></ga-card-grid>
</ng-template>
