<nb-card>
	<nb-card-header>
		<div class="d-flex flex-column">
			<span class="cancel"
				><i class="fas fa-times" (click)="closeDialog(null)"></i
			></span>
			<h5 class="title">
				{{
					(timeFrame
						? 'GOALS_PAGE.SETTINGS.EDIT_TIME_FRAME_TITLE'
						: 'GOALS_PAGE.SETTINGS.ADD_TIME_FRAME_TITLE'
					) | translate
				}}
			</h5>
		</div>
	</nb-card-header>
	<nb-card-body>
		<div class="row">
			<div class="col-md-4">
				<h6 class="subtitle">
					{{
						'GOALS_PAGE.SETTINGS.PREDEFINED_TIME_FRAMES' | translate
					}}
				</h6>
				<nb-list>
					<nb-list-item
						style="cursor: pointer; font-weight: bold"
						*ngFor="let timeFrame of preDefinedTimeFrames"
						(click)="updateTimeFrameValues(timeFrame, $event)"
					>
						{{ timeFrame.name }}
					</nb-list-item>
				</nb-list>
			</div>
			<div class="col-md-8">
				<form [formGroup]="timeFrameForm">
					<label for="time-frame-title" class="label">
						{{ 'FORM.LABELS.NAME' | translate }}
					</label>
					<input
						type="text"
						id="time-frame-title"
						nbInput
						fullWidth
						formControlName="name"
						placeholder="{{
							'GOALS_PAGE.FORM.PLACEHOLDERS.TIME_FRAME_NAME'
								| translate
						}}"
					/>

					<label for="time-frame-status" class="label mt-3">
						{{ 'GOALS_PAGE.FORM.LABELS.STATUS' | translate }}
					</label>
					<nb-select
						id="time-frame-status"
						fullWidth
						filled
						[status]="
							timeFrameForm.value.status ===
							timeFrameStatusEnum.ACTIVE
								? 'success'
								: 'danger'
						"
						formControlName="status"
					>
						<nb-option
							*ngFor="
								let status of timeFrameStatusEnum | keyvalue
							"
							[value]="status.value"
						>
							{{
								'GOALS_PAGE.TIME_FRAME_STATUS.' + status.key
									| translate
							}}
						</nb-option>
					</nb-select>

					<div class="row mt-3">
						<div class="col-md-6">
							<label for="start-date" class="label">
								{{ 'SM_TABLE.START_DATE' | translate }}
							</label>
							<input
								nbInput
								id="start-date"
								fullWidth
								placeholder="{{
									'SM_TABLE.START_DATE' | translate
								}}"
								[nbDatepicker]="startDatePicker"
								formControlName="startDate"
							/>
							<nb-datepicker #startDatePicker></nb-datepicker>
						</div>
						<div class="col-md-6">
							<label for="end-date" class="label">
								{{ 'SM_TABLE.END_DATE' | translate }}
							</label>
							<input
								nbInput
								id="end-date"
								fullWidth
								placeholder="{{
									'SM_TABLE.END_DATE' | translate
								}}"
								[nbDatepicker]="endDatePicker"
								formControlName="endDate"
							/>
							<nb-datepicker #endDatePicker></nb-datepicker>
						</div>
					</div>
					<div
						class="text-danger mt-2 text-center"
						*ngIf="timeFrameForm.controls['endDate'].errors"
					>
						{{
							timeFrameForm.controls['endDate'].errors
								.beforeRequestDayMsg
						}}
					</div>
				</form>
			</div>
		</div>
	</nb-card-body>
	<nb-card-footer class="text-left">
		<button
			class="mr-3"
			nbButton
			status="basic"
			outline
			(click)="closeDialog(null)"
		>
			{{ 'BUTTONS.CANCEL' | translate }}
		</button>
		<button
			[disabled]="timeFrameForm.invalid"
			nbButton
			status="success"
			(click)="saveTimeFrame()"
		>
			{{ 'BUTTONS.SAVE' | translate }}
		</button>
	</nb-card-footer>
</nb-card>
