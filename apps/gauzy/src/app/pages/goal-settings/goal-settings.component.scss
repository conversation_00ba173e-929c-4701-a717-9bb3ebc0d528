@use 'gauzy/_gauzy-table' as *;

:host {
  nb-tab.content-active {
    @include nb-ltr(padding, 1rem 0.5rem 1rem 18px);
    @include nb-rtl(padding, 1rem 18px 1rem 0.5rem);
    overflow: unset;
    border-radius: 0 0 var(--border-radius) var(--border-radius);
    display: flex;
    flex-direction: column;
    height: calc(100vh - 18.5rem);
    overflow: unset;
    .table-scroll-container {
      flex-grow: 10;
      max-height: unset;
    }
  }
  nb-card,
  nb-tab {
    background-color: var(--gauzy-card-2);
    margin: 0;
  }
  nb-card-body {
    overflow: unset;
    margin: 0;
    border-radius: 0 0 var(--border-radius) var(--border-radius);
  }
}

.nb-tabset {
  margin-top: 27px;
}

nb-tab {
  height: calc(100vh - 18.5rem);
}
.card-general {
  background: var(--gauzy-card-3);
  border-radius: nb-theme(border-radius);
}

:host .card-general-container {
  @include nb-ltr(padding, 0 0.5rem 0 0);
  @include nb-rtl(padding, 0 0 0 0.5rem);
  overflow: auto;
  height: 100%;
}
:host .gauzy-button-container {
  position: absolute;
  @include nb-ltr(right, 1rem);
  @include nb-rtl(left, 1rem);
  top: 0;
}
