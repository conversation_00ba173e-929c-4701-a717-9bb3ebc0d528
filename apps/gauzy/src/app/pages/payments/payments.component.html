<nb-card [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
	<nb-card-header class="card-custom-header">
		<div class="card-header-title">
			<h4>
				<ngx-header-title [allowEmployee]="false">
					{{ 'PAYMENTS_PAGE.HEADER' | translate }}
				</ngx-header-title>
			</h4>
		</div>
		<div class="gauzy-button-container">
			<ngx-gauzy-button-action
				[isDisable]="disableButton"
				[buttonTemplate]="actionButtons"
				[componentName]="viewComponentName"
				[buttonTemplateVisible]="visibleButton"
			></ngx-gauzy-button-action>
		</div>
	</nb-card-header>
	<nb-card-body>
		<ng-template
			[ngIf]="dataLayoutStyle === componentLayoutStyleEnum.TABLE"
			[ngIfElse]="gridLayout"
		>
			<div class="table-scroll-container">
				<angular2-smart-table
					style="cursor: pointer"
					[settings]="settingsSmartTable"
					[source]="smartTableSource"
					(userRowSelect)="selectPayment($event)"
				></angular2-smart-table>
			</div>
			<div class="pagination-container">
				<ng-container *ngIf="smartTableSource">
					<ngx-pagination
						[source]="smartTableSource"
					></ngx-pagination>
				</ng-container>
			</div>
		</ng-template>
		<ng-template #gridLayout>
			<ga-card-grid
				[totalItems]="pagination?.totalItems"
				[settings]="settingsSmartTable"
				[source]="payments"
				(onSelectedItem)="selectPayment($event)"
				(scroll)="onScroll()"
			></ga-card-grid>
		</ng-template>
	</nb-card-body>
</nb-card>

<ng-template
	#actionButtons
	let-buttonSize="buttonSize"
	let-selectedItem="selectedItem"
>
	<div class="btn-group actions">
		<ng-template ngxPermissionsOnly="ORG_PAYMENT_ADD_EDIT">
			<button
				nbButton
				[disabled]="!selectedItem && disableButton"
				status="basic"
				class="action primary"
				(click)="editPayment(selectedItem)"
				size="small"
			>
				<nb-icon icon="edit-outline"></nb-icon>
				{{ 'BUTTONS.EDIT' | translate }}
			</button>
			<button
				nbButton
				[disabled]="!selectedItem && disableButton"
				status="basic"
				(click)="deletePayment(selectedItem)"
				class="action"
				size="small"
				[nbTooltip]="'BUTTONS.DELETE' | translate"
			>
				<nb-icon status="danger" icon="trash-2-outline"> </nb-icon>
			</button>
		</ng-template>
	</div>
</ng-template>
<ng-template #visibleButton>
	<ng-template ngxPermissionsOnly="ORG_PAYMENT_ADD_EDIT">
		<button
			nbButton
			status="success"
			size="small"
			(click)="recordPayment()"
		>
			<nb-icon icon="plus-outline"> </nb-icon>
			{{ 'BUTTONS.ADD' | translate }}
		</button>
	</ng-template>
</ng-template>
