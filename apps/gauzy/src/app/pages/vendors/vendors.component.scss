@use 'gauzy/_gauzy-cards' as *;
@forward '../expenses/expense-categories/expense-categories.component';

.editable {
  width: 525px;
}

:host nb-card-body {
  @include nb-ltr(padding, 1rem 0.5rem 1rem 18px);
  @include nb-rtl(padding, 1rem 18px 1rem 0.5rem);
  .no-data {
    @include nb-ltr(padding, 0 0.5rem 0 0);
    @include nb-rtl(padding, 0 0 0 0.5rem);
    height: 100%;
  }
  .table-scroll-container {
    max-height: 100%;
  }
}

.columns-header {
  background-color: nb-theme(gauzy-card-2);
  border-radius: 8px;
  margin-bottom: 10px;
  padding-top: 12px;
  padding-bottom: 12px;
  padding-left: 12px;
  font-size: 12px;
  font-style: normal;
  font-weight: 600;
  line-height: 15px;
  letter-spacing: 0em;
}
