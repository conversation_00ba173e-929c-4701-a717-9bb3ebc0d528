@use '@shared/_pg-card' as *;

nb-radio-group {
  display: flex;
  flex-direction: row;
}

.row {
  display: flex;
  justify-content: space-between;
}

.actions-container {
  justify-content: flex-start;
  margin-bottom: 1rem;
}

::ng-deep {
  .toggle-label {
    margin-bottom: 0;
  }
}
:host {
  .custom-toggle {
    ::ng-deep {
      span.text {
        color: nb-theme(text-primary-color);
      }
    }
  }
  .custom-grid {
    flex-grow: 10;
    max-height: unset;
    overflow-y: auto;
  }
  nb-card-body {
    height: calc($card-height - 0.5rem) !important;
  }
}

.filter {
  background-color: var(--gauzy-card-2);
  width: fit-content;
  display: flex;
  padding: 6px 12px;
  margin-right: 16px;
  border-radius: nb-theme(border-radius);
}
