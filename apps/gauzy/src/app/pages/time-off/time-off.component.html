<nb-card [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
	<nb-card-header class="d-flex flex-column pb-0">
		<div class="card-header-title">
			<h4>
				<ngx-header-title>
					{{ 'TIME_OFF_PAGE.HEADER' | translate }}
				</ngx-header-title>
			</h4>
			<ng-template [ngxPermissionsOnly]="[PermissionsEnum.ALL_ORG_VIEW, PermissionsEnum.TIME_OFF_POLICY_VIEW]">
				<button
					nbButton
					type="button"
					status="basic"
					class="action p-2"
					routerLink="/pages/employees/time-off/settings"
				>
					<i class="fas fa-cog"></i>
				</button>
			</ng-template>
		</div>
		<div class="align-self-end d-flex align-items-center">
			<!-- Action Buttons -->
			<ngx-gauzy-button-action
				[buttonTemplate]="actionButtons"
				[buttonTemplateVisible]="visibleButton"
				[hasLayoutSelector]="false"
				[isDisable]="disableButton"
			></ngx-gauzy-button-action>

			<!-- Display Holidays Toggle -->
			<ng-template [ngxPermissionsOnly]="[PermissionsEnum.ALL_ORG_VIEW, PermissionsEnum.TIME_OFF_VIEW]">
				<nb-toggle
					[(ngModel)]="displayHolidays"
					(checkedChange)="changeDisplayHolidays($event)"
					class="custom-toggle mr-3 ml-3"
					status="primary"
					checked
				>
					{{ 'TIME_OFF_PAGE.DISPLAY_HOLIDAYS' | translate }}
				</nb-toggle>
			</ng-template>

			<!-- Layout Selector -->
			<ga-layout-selector [componentName]="viewComponentName"></ga-layout-selector>
		</div>
	</nb-card-header>
	<nb-card-body>
		<ng-template [ngIf]="showFilter">
			<div class="row mb-3 justify-content-end">
				<div class="filter">
					<!-- Statuses Radio Group -->
					<nb-radio-group
						class="align-center radio-group mr-5"
						[(ngModel)]="selectedStatus"
						(ngModelChange)="detectStatusChange($event)"
					>
						<nb-radio class="d-flex" *ngFor="let status of timeOffStatuses" [value]="status">
							{{ 'TIME_OFF_PAGE.STATUSES.' + status | translate }}
						</nb-radio>
					</nb-radio-group>

					<!-- Include Archived Toggle -->
					<nb-toggle
						[(ngModel)]="includeArchived"
						(checkedChange)="changeIncludeArchived($event)"
						status="warning"
						class="my-auto align-center"
					>
						{{ 'FORM.CHECKBOXES.INCLUDE_ARCHIVED' | translate }}
					</nb-toggle>
				</div>
			</div>
		</ng-template>

		<!-- Check if the user has the 'TIME_OFF_VIEW' permission -->
		<ng-template [ngxPermissionsOnly]="[PermissionsEnum.ALL_ORG_VIEW, PermissionsEnum.TIME_OFF_VIEW]">
			<ng-container [ngSwitch]="dataLayoutStyle">
				<!-- Table View -->
				<ng-template [ngSwitchCase]="componentLayoutStyleEnum.TABLE">
					<div class="table-scroll-container custom-table">
						<angular2-smart-table
							class="time-off-table"
							style="cursor: pointer"
							[settings]="settingsSmartTable"
							[source]="sourceSmartTable"
							(userRowSelect)="selectTimeOff($event)"
							#timeOffTable
						></angular2-smart-table>
					</div>
					<ng-container *ngIf="smartTableSource">
						<ngx-pagination [source]="smartTableSource"></ngx-pagination>
					</ng-container>
				</ng-template>

				<!-- Grid Card View -->
				<ng-template [ngSwitchCase]="componentLayoutStyleEnum.CARDS_GRID">
					<div class="custom-grid">
						<ga-card-grid
							[totalItems]="pagination?.totalItems"
							(onSelectedItem)="selectTimeOff($event)"
							(scroll)="onScroll()"
							[settings]="settingsSmartTable"
							[source]="timeOffs"
						></ga-card-grid>
					</div>
				</ng-template>

				<!-- Optional: Default case if no specific layout matches -->
				<ng-template *ngSwitchDefault>
					<p>{{ 'SETTINGS_MENU.NO_LAYOUT' | translate }}</p>
				</ng-template>
			</ng-container>
		</ng-template>

		<!-- If the user does not have the 'TIME_OFF_VIEW' permission -->
		<ng-template [ngxPermissionsExcept]="[PermissionsEnum.ALL_ORG_VIEW, PermissionsEnum.TIME_OFF_VIEW]">
			<div>
				<!-- Content to display if the user does not have the 'TIME_OFF_VIEW' permission -->
				<p>You don't have permission to view this page</p>
			</div>
		</ng-template>
	</nb-card-body>
</nb-card>

<!-- Action Buttons -->
<ng-template #actionButtons let-selectedItem="selectedItem">
	<ng-template [ngxPermissionsOnly]="['ALL_ORG_EDIT', 'TIME_OFF_EDIT']">
		<div class="btn-group actions">
			<ng-container>
				<button
					*ngIf="!selectedTimeOffRecord?.isHoliday"
					(click)="updateTimeOffRecord()"
					nbButton
					status="basic"
					size="small"
					class="action primary"
				>
					<nb-icon class="mr-1" icon="edit-outline"></nb-icon>
					{{ 'BUTTONS.EDIT' | translate }}
				</button>
				<button
					(click)="deleteRequest(selectedItem)"
					nbButton
					status="basic"
					size="small"
					class="action"
					[nbTooltip]="'BUTTONS.DELETE' | translate"
				>
					<nb-icon status="danger" icon="trash-2-outline"></nb-icon>
				</button>

				<!-- Show Actions Button -->
				<ng-container *ngIf="!showActions">
					<button nbButton (click)="showActions = true" status="basic" size="small" class="action">
						<nb-icon icon="more-horizontal-outline" cursor="pointer"></nb-icon>
					</button>
				</ng-container>
			</ng-container>
		</div>

		<ng-template [ngIf]="showActions">
			<div class="btn-group actions ml-2">
				<button
					nbButton
					type="button"
					(click)="approveDaysOff(selectedItem)"
					status="success"
					size="small"
					class="action"
				>
					<nb-icon class="mr-1" icon="checkmark-circle-outline"></nb-icon>
					{{ 'BUTTONS.APPROVE' | translate }}
				</button>
				<button
					nbButton
					type="button"
					(click)="denyDaysOff(selectedItem)"
					status="warning"
					size="small"
					class="action"
				>
					<nb-icon class="mr-1" icon="close-outline"></nb-icon>
					{{ 'BUTTONS.DENY' | translate }}
				</button>
				<button
					nbButton
					type="button"
					(click)="archive()"
					status="basic"
					size="small"
					class="action secondary"
					[disabled]="selectedTimeOffRecord ? selectedTimeOffRecord.isArchived : true"
				>
					<nb-icon class="mr-1" icon="archive-outline"></nb-icon>
					{{ 'BUTTONS.ARCHIVE' | translate }}
				</button>
				<button nbButton (click)="showActions = false" status="basic" size="small" class="action">
					<nb-icon icon="close-outline" cursor="pointer"></nb-icon>
				</button>
			</div>
		</ng-template>
	</ng-template>
</ng-template>

<!-- Visible Buttons -->
<ng-template #visibleButton>
	<ng-template [ngxPermissionsOnly]="['ALL_ORG_EDIT', 'TIME_OFF_ADD']">
		<button nbButton type="button" size="small" class="action" status="success" (click)="requestDaysOff()">
			<nb-icon class="mr-1" icon="calendar-outline"></nb-icon>
			{{ 'BUTTONS.ADD' | translate }}
		</button>
		<button nbButton type="button" size="small" status="basic" class="action info" (click)="addHolidays()">
			<nb-icon class="mr-1" icon="plus-outline"></nb-icon>
			{{ 'TIME_OFF_PAGE.ADD_HOLIDAYS' | translate }}
		</button>
	</ng-template>

	<button (click)="showFilter = !showFilter" nbButton status="basic" size="small" class="action primary">
		<nb-icon icon="funnel-outline"></nb-icon>
		{{ 'BUTTONS.FILTER' | translate }}
	</button>
</ng-template>
