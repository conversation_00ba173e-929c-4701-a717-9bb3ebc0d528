<nb-card [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
	<nb-card-header class="d-flex flex-column pb-0">
		<div class="card-header-title d-flex justify-content-start">
			<ngx-back-navigation></ngx-back-navigation>
			<h4>
				<ngx-header-title [allowEmployee]="false">
					{{ 'TIME_OFF_PAGE.POLICY.HEADER' | translate }}
				</ngx-header-title>
			</h4>
		</div>
		<div class="align-self-end">
			<ngx-gauzy-button-action
				[isDisable]="disableButton"
				[buttonTemplate]="actionButtons"
				[buttonTemplateVisible]="visibleButton"
				[componentName]="viewComponentName"
			></ngx-gauzy-button-action>
		</div>
	</nb-card-header>
	<nb-card-body>
		<!-- Check if the user has the 'TIME_OFF_POLICY_VIEW' permission -->
		<ng-template [ngxPermissionsOnly]="[PermissionsEnum.ALL_ORG_VIEW, PermissionsEnum.TIME_OFF_POLICY_VIEW]">
			<ng-container [ngSwitch]="dataLayoutStyle">
				<!-- Table View -->
				<ng-template [ngSwitchCase]="componentLayoutStyleEnum.TABLE">
					<div class="table-scroll-container custom-table">
						<angular2-smart-table
							style="cursor: pointer"
							[settings]="smartTableSettings"
							[source]="smartTableSource"
							(userRowSelect)="selectTimeOffPolicy($event)"
						></angular2-smart-table>
					</div>
					<ng-container *ngIf="smartTableSource">
						<ngx-pagination [source]="smartTableSource"></ngx-pagination>
					</ng-container>
				</ng-template>

				<!-- Card Grid View -->
				<ng-template [ngSwitchCase]="componentLayoutStyleEnum.CARDS_GRID">
					<ga-card-grid
						[totalItems]="pagination?.totalItems"
						[settings]="smartTableSettings"
						[source]="timeOffPolicies"
						(onSelectedItem)="selectTimeOffPolicy($event)"
						(scroll)="onScroll()"
					></ga-card-grid>
				</ng-template>

				<!-- Optional: Default case if no specific layout matches -->
				<ng-template *ngSwitchDefault>
					<p>{{ 'SETTINGS_MENU.NO_LAYOUT' | translate }}</p>
				</ng-template>
			</ng-container>
		</ng-template>

		<!-- If the user does not have the 'ORG_JOB_EMPLOYEE_VIEW' permission -->
		<ng-template [ngxPermissionsExcept]="[PermissionsEnum.ALL_ORG_VIEW, PermissionsEnum.TIME_OFF_POLICY_VIEW]">
			<div>
				<!-- Content to display if the user does not have the 'TIME_OFF_POLICY_VIEW' permission -->
				<p>You don't have permission to view this page</p>
			</div>
		</ng-template>
	</nb-card-body>
</nb-card>

<!-- Action Buttons -->
<ng-template #actionButtons let-buttonSize="buttonSize" let-selectedItem="selectedItem">
	<div class="btn-group actions">
		<ng-template [ngxPermissionsOnly]="[PermissionsEnum.ALL_ORG_EDIT, PermissionsEnum.TIME_OFF_POLICY_EDIT]">
			<button
				nbButton
				type="button"
				status="basic"
				class="action primary"
				[disabled]="disableButton"
				(click)="openEditPolicyDialog(selectedItem)"
				size="small"
			>
				<nb-icon class="mr-1" icon="edit-outline"></nb-icon>
				{{ 'BUTTONS.EDIT' | translate }}
			</button>
			<button
				nbButton
				type="button"
				status="basic"
				class="action"
				size="small"
				[disabled]="disableButton"
				(click)="openDeletePolicyDialog(selectedItem)"
				[nbTooltip]="'BUTTONS.DELETE' | translate"
			>
				<nb-icon status="danger" icon="trash-2-outline"></nb-icon>
			</button>
		</ng-template>
	</div>
</ng-template>

<!-- Visible Buttons -->
<ng-template #visibleButton>
	<ng-template [ngxPermissionsOnly]="[PermissionsEnum.ALL_ORG_EDIT, PermissionsEnum.TIME_OFF_POLICY_ADD]">
		<button nbButton status="success" size="small" (click)="openAddPolicyDialog()">
			<nb-icon class="mr-1" icon="plus-outline"></nb-icon>
			{{ 'BUTTONS.ADD' | translate }}
		</button>
	</ng-template>
</ng-template>
