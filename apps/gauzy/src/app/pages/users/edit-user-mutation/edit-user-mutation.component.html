<nb-card>
	<nb-card-header
		>{{ 'USERS_PAGE.ADD_EXISTING_USER' | translate }}
		<span
			class="label info-font"
			[nbTooltip]="'USERS_PAGE.ADD_EXISTING_USER_TOOLTIP' | translate"
		>
			&nbsp;
			<nb-icon icon="info-outline"></nb-icon>
		</span>
	</nb-card-header>
	<nb-card-body>
		<div class="form-group m-0">
			<div
				class="form-group d-flex flex-row justify-content-between align-items-center m-0"
			>
				<div class="form-group w-100 p-0">
					<form [formGroup]="form" *ngIf="form">
						<ga-user-multi-select
							[selectedUserIds]="selectedUsersIds"
							[allUsers]="users"
							(selectedChange)="onUsersSelected($event)"
						>
						</ga-user-multi-select>
					</form>
				</div>
				<div class="form-group d-flex flex-row add-organization-action">
					<button
						class="mr-3"
						(click)="cancel()"
						nbButton
						outline
						status="basic"
					>
						{{ 'BUTTONS.CANCEL' | translate }}
					</button>
					<button
						nbButton
						(click)="submitForm()"
						status="success"
						[disabled]="disableButton"
					>
						{{ 'BUTTONS.SAVE' | translate }}
					</button>
				</div>
			</div>
		</div>
	</nb-card-body>
</nb-card>
