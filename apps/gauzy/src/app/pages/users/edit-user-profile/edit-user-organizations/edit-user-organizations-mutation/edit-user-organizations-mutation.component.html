<nb-card class="m-0 pb-0">
	<nb-card-header>
		{{ 'USERS_PAGE.ADD_EXISTING_ORGANIZATION' | translate }}
	</nb-card-header>
	<nb-card-body>
		<div class="form-group">
			<div
				class="form-group d-flex flex-row justify-content-between align-items-center m-0"
			>
				<div class="form-group w-100 p-0">
					<form [formGroup]="form" *ngIf="form">
						<ga-user-organizations-multi-select
							[selectedOrganizationsId]="selectedOrganizationsId"
							[allOrganizations]="organizations"
							(selectedChange)="onOrganizationsSelected($event)"
						>
						</ga-user-organizations-multi-select>
					</form>
				</div>
				<div class="form-group d-flex flex-row ml-3 add-organization-action">
					<button
						(click)="cancel()"
						nbButton
						outline
						status="basic"
					>
						{{ 'BUTTONS.CANCEL' | translate }}
					</button>
					<button nbButton (click)="submitForm()" status="success">
						{{ 'BUTTONS.SAVE' | translate }}
					</button>
				</div>
			</div>
		</div>
	</nb-card-body>
</nb-card>
