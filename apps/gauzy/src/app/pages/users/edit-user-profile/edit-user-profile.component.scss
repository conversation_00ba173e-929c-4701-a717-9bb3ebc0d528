@use 'gauzy/_gauzy-overrides' as *;
@forward '@shared/_edit-profile-form';

:host {
  nb-card {
    nb-card-body {
      background-color: nb-theme(gauzy-card-2);
      padding: 1rem;
    }
  }
  @include input-appearance(42px, var(--gauzy-card-1));

  ::ng-deep .route-tabset .route-tab {
    &.active {
      svg {
        fill: nb-theme(text-primary-color);
      }
    }

    a.tab-link {
      border-radius: nb-theme(border-radius) nb-theme(border-radius) 0 0;
      display: flex !important;

      span.tab-text {
        text-transform: lowercase;
        display: block;

        &:first-letter {
          text-transform: uppercase;
        }
      }
    }
  }
  .gap-1 {
    @include nb-rtl(gap, 1rem);
    @include nb-ltr(gap, 0);
  }
}
