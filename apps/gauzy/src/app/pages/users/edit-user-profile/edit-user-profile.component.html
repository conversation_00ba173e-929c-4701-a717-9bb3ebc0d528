<nb-card class="card-scroll">
	<nb-card-header class="d-flex gap-1">
		<ngx-back-navigation></ngx-back-navigation>
		<h4>
			{{ 'USERS_PAGE.EDIT_USER.HEADER' | translate }}
			<ng-container *ngIf="user?.name">
				<span> {{ user.name }} </span>
			</ng-container>
			<span *ngIf="user?.username">
				<ng-container
					*ngIf="user?.username; then withBraces; else withQuotes"
				>
				</ng-container>
				<ng-template #withBraces> ({{ user.username }}) </ng-template>
				<ng-template #withQuotes> '{{ user.username }}' </ng-template>
			</span>
		</h4>
	</nb-card-header>
	<nb-card-body>
		<nb-route-tabset [tabs]="tabs"></nb-route-tabset>
	</nb-card-body>
</nb-card>
