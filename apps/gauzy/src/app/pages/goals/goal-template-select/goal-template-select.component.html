<nb-card size="large" class="max-width-50vw">
	<nb-card-header>
		<nb-icon
			class="ml-auto mt-1 close"
			icon="close-outline"
			(click)="closeDialog(null)"
		></nb-icon
	></nb-card-header>
	<nb-card-body class="mt-3 margin-stepper">
		<nb-stepper #stepper orientation="horizontal" [linear]="true">
			<nb-step label="Select a Preset">
				<nb-accordion
					class="mt-3"
					*ngFor="let template of goalTemplates; let index = index"
				>
					<nb-accordion-item [expanded]="index == 0 ? true : false">
						<nb-accordion-item-header>
							{{ template.name }}
						</nb-accordion-item-header>
						<nb-accordion-item-body>
							<p *ngFor="let keyResult of template.keyResults">
								{{ keyResult.name }}
							</p>
							<div class="text-right">
								<button
									nbButton
									status="success"
									(click)="nextStep(template)"
								>
									{{
										'BUTTONS.SELECT_AND_CONTINUE'
											| translate
									}}
								</button>
							</div>
						</nb-accordion-item-body>
					</nb-accordion-item>
				</nb-accordion>
			</nb-step>
			<nb-step label="Additional Details">
				<form [formGroup]="form">
					<div style="padding: 0 3vw">
						<label for="objective-deadline" class="label mt-3">
							{{ 'GOALS_PAGE.FORM.LABELS.DEADLINE' | translate }}
						</label>
						<nb-select
							*ngIf="timeFrames.length > 0"
							id="objective-deadline"
							placeholder="{{
								'GOALS_PAGE.FORM.LABELS.DEADLINE' | translate
							}}"
							fullWidth
							formControlName="deadline"
						>
							<nb-option
								*ngFor="let deadline of timeFrames"
								[value]="deadline.name"
							>
								{{ deadline.name }}
							</nb-option>
						</nb-select>
						<p>
							<button
								id="objective-deadline"
								*ngIf="timeFrames.length == 0"
								status="primary"
								nbButton
								(click)="openSetTimeFrame()"
							>
								{{
									'GOALS_PAGE.BUTTONS.ADD_TIME_FRAME'
										| translate
								}}
							</button>
						</p>
						<ga-goal-level-select
							[orgId]="orgId"
							[orgName]="orgName"
							[parentFormGroup]="form"
							[enableHelperText]="false"
							[employees]="employees"
						>
						</ga-goal-level-select>
					</div>
				</form>
			</nb-step>
		</nb-stepper>
	</nb-card-body>
	<nb-card-footer class="text-right" [hidden]="stepper.selectedIndex == 0">
		<button
			class="mr-3"
			[hidden]="stepper.selectedIndex == 0"
			nbButton
			(click)="previousStep()"
		>
			{{ 'BUTTONS.BACK' | translate }}
		</button>
		<button type="submit" nbButton status="success" (click)="createGoal()">
			{{ 'BUTTONS.CREATE' | translate }}
		</button>
	</nb-card-footer>
</nb-card>
