<nb-card class="min-width-60vw card-scroll">
	<nb-card-header>
		<div class="d-flex flex-column">
			<span class="cancel"
				><i class="fas fa-times" (click)="closeDialog(false)"></i
			></span>
			<h4 class="subtitle">{{ goal.name }}</h4>
		</div>
		<div class="row mt-3 d-flex align-items-center">
			<ngx-avatar class="ml-3" [src]="src" [name]="ownerName">
			</ngx-avatar>
			<span class="smallText ml-3 mt-1"
				>{{ 'GOALS_PAGE.SESSION' | translate }}:
				{{ goal.deadline }}</span
			>
			<span class="ml-5 mt-1">
				<div class="button-container">
					<button
						status="basic"
						nbButton
						size="tiny"
						class="action"
						(click)="deleteGoal()"
						[nbTooltip]="'BUTTONS.DELETE' | translate"
					>
						<nb-icon
							status="danger"
							icon="trash-2-outline"
						></nb-icon>
					</button>
				</div>
			</span>
		</div>
		<div class="row mt-3">
			<p class="ml-3">
				{{
					goal.description != ''
						? goal.description
						: ('GOALS_PAGE.NO_DESCRIPTION' | translate)
				}}
			</p>
		</div>
		<div class="row mt-3 align-items-center">
			<div class="col-md-12 d-flex align-items-center">
				<span class="mr-3 smallText">{{
					'GOALS_PAGE.PROGRESS' | translate
				}}</span>
				<span>{{ goal.progress }}%</span>
				<nb-progress-bar
					class="ml-3"
					nbTooltip="{{ 'GOALS_PAGE.TOOLTIPS.PROGRESS' | translate }}"
					[value]="goal.progress"
					[status]="
						goal.progress < 50
							? 'warning'
							: goal.progress < 75
							? 'info'
							: 'success'
					"
					size="small"
				>
				</nb-progress-bar>
			</div>
		</div>
	</nb-card-header>
	<nb-card-body class="p-0 card-body-content">
		<nb-tabset>
			<nb-tab tabTitle="{{ 'GOALS_PAGE.KEY_RESULTS' | translate }}">
				<div
					class="card-key-result"
					å
					*ngIf="goal.keyResults.length == 0"
				>
					{{ 'GOALS_PAGE.MESSAGE.NO_KEY_RESULT' | translate }}
				</div>
				<ng-container
					*ngFor="let keyResult of goal.keyResults; let i = index"
				>
					<div class="card-key-result">
						<div
							class="w-100 d-flex align-items-center justify-content-between keyResult"
						>
							<span class="p-1 smallText flex-fill">{{
								keyResult.name
							}}</span>
							<div class="w-25 d-flex align-items-center">
								<span class="mr-1"
									>{{ keyResult.progress }}%</span
								>
								<nb-progress-bar
									nbTooltip="{{
										'KEY_RESULT_PAGE.TOOLTIPS.PROGRESS'
											| translate
												: {
														weight: calculateKeyResultWeight(
															keyResult.weight
														)
												  }
									}}"
									class="mt-1"
									[status]="
										keyResult.progress < 50
											? 'warning'
											: keyResult.progress < 75
											? 'info'
											: 'success'
									"
									[value]="keyResult.progress"
									size="tiny"
								></nb-progress-bar>
							</div>
							<div class="border ml-1">
								<nb-actions>
									<nb-action
										nbTooltip="{{
											'KEY_RESULT_PAGE.TOOLTIPS.DETAILS'
												| translate
										}}"
										(click)="keyResultDetails(i, keyResult)"
										icon="eye"
										size="tiny"
									></nb-action>
								</nb-actions>
							</div>
						</div>
					</div>
				</ng-container>
			</nb-tab>
			<nb-tab tabTitle="{{ 'GOALS_PAGE.UPDATES' | translate }}">
				<div class="card-key-result" *ngIf="updates.length == 0">
					{{ 'GOALS_PAGE.MESSAGE.NO_UPDATES' | translate }}
				</div>
				<div *ngIf="updates.length > 0">
					<nb-card class="custom-card" *ngFor="let update of updates">
						<nb-card-header class="custom-header">
							<div
								class="d-flex align-items-center justify-content-between"
							>
								<ngx-avatar [src]="src" [name]="ownerName">
								</ngx-avatar>
								<span class="smallText">{{
									update.createdAt | date
								}}</span>
							</div>
						</nb-card-header>
						<nb-card-footer class="custom-footer pt-2">
							<div
								class="d-flex align-items-center justify-content-between"
							>
								<span>
									{{ 'GOALS_PAGE.UPDATE' | translate }}:
									<span class="font-weight-bold">{{
										update.update
									}}</span>
								</span>
								<span>
									{{ update.status }}
								</span>
								<span>
									{{ 'GOALS_PAGE.PROGRESS' | translate }} :
									<span class="font-weight-bold"
										>{{ update.progress }}%</span
									>
								</span>
							</div>
						</nb-card-footer>
					</nb-card>
				</div>
			</nb-tab>
			<nb-tab tabTitle="Alignments">
				<div class="card-key-result" *ngIf="!goal.alignedKeyResult">
					{{ 'GOALS_PAGE.MESSAGE.NO_ALIGNMENT' | translate }}
				</div>
				<nb-card *ngIf="!!goal.alignedKeyResult">
					<nb-card-header
						class="w-100 d-flex align-items-center justify-content-between"
					>
						<span class="smallText"
							>{{ goal.alignedKeyResult.goal.name }}
						</span>
						<span class="w-25 d-flex align-items-center">
							<span
								>{{
									goal.alignedKeyResult.goal.progress
								}}%</span
							>
							<nb-progress-bar
								class="mt-1"
								[status]="
									goal.alignedKeyResult.goal.progress < 50
										? 'warning'
										: goal.alignedKeyResult.progress < 75
										? 'info'
										: 'success'
								"
								[value]="goal.alignedKeyResult.goal.progress"
								size="tiny"
							></nb-progress-bar></span
					></nb-card-header>
					<nb-card-body>
						<nb-card class="ml-2">
							<nb-card-header class="row"
								><span class="col-md-6">{{
									goal.alignedKeyResult.name
								}}</span
								><span class="col-md-4"
									><nb-progress-bar
										class="mt-1"
										[status]="
											goal.alignedKeyResult.progress < 50
												? 'warning'
												: goal.alignedKeyResult
														.progress < 75
												? 'info'
												: 'success'
										"
										[value]="goal.alignedKeyResult.progress"
										size="tiny"
										>{{
											goal.alignedKeyResult.progress
										}}%</nb-progress-bar
									>
								</span>
								<span class="col-md-2">
									<nb-actions>
										<nb-action
											(click)="
												keyResultDetails(
													null,
													goal.alignedKeyResult
												)
											"
											icon="eye"
										></nb-action>
									</nb-actions> </span
							></nb-card-header>
							<nb-card-body>
								<nb-card class="ml-2">
									<nb-card-header class="row"
										><span class="col-md-6">{{
											goal.name
										}}</span>
										<span class="col-md-6"
											><nb-progress-bar
												class="mt-1"
												[status]="
													goal.progress < 50
														? 'warning'
														: goal.progress < 75
														? 'info'
														: 'success'
												"
												[value]="goal.progress"
												size="tiny"
												>{{
													goal.progress
												}}%</nb-progress-bar
											></span
										></nb-card-header
									>
								</nb-card>
							</nb-card-body>
						</nb-card>
					</nb-card-body>
				</nb-card>
			</nb-tab>
		</nb-tabset>
	</nb-card-body>
	<nb-card-footer>
		<nb-card>
			<nb-card-body class="p-0 row">
				<div class="col-9">
					<label class="smallText">
						{{ 'GOALS_PAGE.COMMENTS' | translate }}
					</label>
					<textarea
						nbInput
						fullWidth
						placeholder="Add Comment"
					></textarea>
				</div>
				<div class="col-3 btn-comment">
					<button nbButton status="info">
						{{ 'BUTTONS.ADD_COMMENT' | translate }}
					</button>
				</div>
			</nb-card-body>
		</nb-card>
	</nb-card-footer>
</nb-card>
