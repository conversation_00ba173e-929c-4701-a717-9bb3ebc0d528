<nb-card class="max-width-30vw">
	<nb-card-header>
		<div class="d-flex flex-column">
			<span class="cancel"
				><i class="fas fa-times" (click)="closeDialog()"></i
			></span>
			<h4 class="title">
				{{ 'KEY_RESULT_PAGE.UPDATE_KEY_RESULT' | translate }}
			</h4>
		</div>
	</nb-card-header>
	<nb-card-body>
		<h6 class="subtitle">{{ keyResult.name }}</h6>
		<div
			*ngIf="
				keyResult.type == keyResultTypeEnum.NUMERICAL ||
				keyResult.type == keyResultTypeEnum.KPI ||
				keyResult.type == keyResultTypeEnum.CURRENCY
			"
		>
			<div class="row p-2">
				<div class="col-md-6">
					<p>
						<span class="label mr-2">
							{{
								'KEY_RESULT_PAGE.FORM.LABELS.INITIAL_VALUE'
									| translate
							}}:</span
						><span class="font-weight-bold"
							>{{ keyResult.initialValue }}
							{{ keyResult.unit }}</span
						>
					</p>
				</div>
				<div class="col-md-6">
					<p>
						<span class="label mr-2">
							{{
								'KEY_RESULT_PAGE.FORM.LABELS.TARGET_VALUE'
									| translate
							}}:</span
						><span class="font-weight-bold"
							>{{ keyResult.targetValue }}
							{{ keyResult.unit }}</span
						>
					</p>
				</div>
			</div>
		</div>
		<form [formGroup]="keyResultUpdateForm">
			<label for="updated-value" class="label">
				{{ 'KEY_RESULT_PAGE.FORM.LABELS.UPDATED_VALUE' | translate }}
			</label>
			<input
				*ngIf="
					keyResult.type == keyResultTypeEnum.NUMERICAL ||
					keyResult.type == keyResultTypeEnum.KPI ||
					keyResult.type == keyResultTypeEnum.CURRENCY
				"
				type="number"
				id="updated-value"
				nbInput
				[min]="keyResult.initialValue"
				[max]="keyResult.targetValue"
				fullWidth
				formControlName="newValueNumber"
			/>
			<div class="row">
				<nb-toggle
					id="updated-value"
					class="row ml-3"
					*ngIf="keyResult.type == keyResultTypeEnum.TRUE_OR_FALSE"
					formControlName="newValueBoolean"
					status="primary"
				>
					{{
						'KEY_RESULT_PAGE.FORM.LABELS.MARK_COMPLETE' | translate
					}}
				</nb-toggle>
			</div>

			<label
				*ngIf="!hideStatus"
				for="updated-value-status"
				class="label mt-3"
			>
				{{ 'KEY_RESULT_PAGE.FORM.LABELS.STATUS' | translate }}
			</label>
			<nb-select
				*ngIf="!hideStatus"
				id="updated-value-status"
				filled
				[status]="
					keyResultUpdateForm.value.newStatus ==
					updateStatusEnum.ON_TRACK
						? 'success'
						: keyResultUpdateForm.value.newStatus ==
						  updateStatusEnum.NEEDS_ATTENTION
						? 'warning'
						: 'danger'
				"
				fullWidth
				formControlName="newStatus"
			>
				<nb-option
					[value]="status.value"
					*ngFor="let status of updateStatusEnum | keyvalue"
					>{{
						'KEY_RESULT_PAGE.UPDATE.STATUS.' + status.key
							| translate
					}}</nb-option
				>
			</nb-select>
		</form>
	</nb-card-body>
	<nb-card-footer class="text-left">
		<button
			class="mr-3"
			status="basic"
			nbButton
			outline
			(click)="closeDialog()"
		>
			{{ 'BUTTONS.CANCEL' | translate }}
		</button>
		<button
			[disabled]="!keyResultUpdateForm.valid"
			nbButton
			status="success"
			(click)="updateKeyResult()"
		>
			{{ 'BUTTONS.UPDATE' | translate }}
		</button>
	</nb-card-footer>
</nb-card>
