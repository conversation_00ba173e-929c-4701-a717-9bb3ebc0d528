<nb-card
	class="minmax-width-60vw"
	[nbSpinner]="loading"
	nbSpinnerStatus="primary"
	nbSpinnerSize="large"
>
	<nb-card-header>
		<div class="d-flex flex-column">
			<span class="cancel"
				><i class="fas fa-times" (click)="closeDialog(false)"></i
			></span>
			<h4 class="subtitle">{{ keyResult.name }}</h4>
		</div>
		<div class="row mt-3 d-flex align-items-center">
			<ngx-avatar [src]="src" [name]="ownerName"> </ngx-avatar>
			<p class="smallText mt-3 ml-3">
				{{ 'KEY_RESULT_PAGE.FORM.LABELS.DEADLINE' | translate }}:
				<span class="font-weight-bold">{{ endDate | date }}</span>
			</p>
		</div>
	</nb-card-header>
	<nb-card-body>
		<div class="row" *ngIf="!loading">
			<div class="col-md-12">
				<div class="row">
					<div class="col-md-12">
						<ga-keyresult-progress-chart
							[organization]="organization"
							[keyResult]="keyResult"
							[kpi]="kpi"
						></ga-keyresult-progress-chart>
					</div>
				</div>
				<div class="p-3 smallText border">
					<div
						class="row mt-2"
						*ngIf="
							keyResult.type !== keyResultTypeEnum.TRUE_OR_FALSE
						"
					>
						<div class="col-md-4 font-weight-bold">
							{{
								'KEY_RESULT_PAGE.FORM.LABELS.INITIAL_VALUE'
									| translate
							}}
						</div>
						<div class="col-md-8">
							{{ keyResult.initialValue }}
							{{
								keyResult.type == keyResultTypeEnum.KPI
									? kpi?.unit
									: keyResult.unit
							}}
						</div>
					</div>
					<div
						class="row mt-2"
						*ngIf="
							keyResult.type !== keyResultTypeEnum.TRUE_OR_FALSE
						"
					>
						<div class="col-md-4 font-weight-bold">
							{{
								'KEY_RESULT_PAGE.FORM.LABELS.TARGET_VALUE'
									| translate
							}}
						</div>
						<div class="col-md-8">
							{{ keyResult.targetValue }}
							{{
								keyResult.type == keyResultTypeEnum.KPI
									? kpi?.unit
									: keyResult.unit
							}}
						</div>
					</div>
					<div class="row mt-2">
						<div class="col-md-4 font-weight-bold">
							{{ 'GOALS_PAGE.PROGRESS' | translate }}
						</div>
						<div class="col-md-8">{{ keyResult.progress }} %</div>
					</div>
					<div class="row mt-2">
						<div class="col-md-4 font-weight-bold">
							{{ 'GOALS_PAGE.GOAL' | translate }}
						</div>
						<div class="col-md-8">{{ keyResult.goal.name }}</div>
					</div>
				</div>
				<div class="main-header">
					<h6 class="subtitle mt-3">
						{{ 'GOALS_PAGE.UPDATES' | translate }}
					</h6>
					<div class="button-container">
						<button
							nbButton
							size="small"
							[hidden]="!isUpdatable"
							status="success"
							(click)="keyResultUpdate()"
						>
							{{ 'BUTTONS.ADD_NEW' | translate }}
						</button>
					</div>
				</div>
				<div class="mt-2" *ngIf="!isUpdatable">
					<nb-alert
						*ngIf="endDate.getTime() < today.getTime()"
						status="warning"
					>
						{{
							'KEY_RESULT_PAGE.MESSAGE.TIME_FRAME_ENDED'
								| translate: { date: endDate | date }
						}}
					</nb-alert>
					<nb-alert
						*ngIf="startDate.getTime() > today.getTime()"
						status="warning"
					>
						{{
							'KEY_RESULT_PAGE.MESSAGE.TIME_FRAME_NOT_STARTED'
								| translate: { date: startDate | date }
						}}
					</nb-alert>
				</div>
				<div class="section mt-3 overflow-scroll" *ngIf="!!updates">
					<nb-card
						class="custom-card"
						style="position: relative"
						*ngFor="let update of updates; let index = index"
					>
						<nb-card-header class="custom-header">
							<nb-badge
								style="font-size: 0.8rem !important"
								class="custom-badge"
								*ngIf="
									index < updates.length - 1 &&
									!relativeProgress(
										update,
										updates[index + 1]
									).zero
								"
								[text]="
									relativeProgress(update, updates[index + 1])
										.progressText
								"
								[status]="
									relativeProgress(update, updates[index + 1])
										.status
								"
								position="top right"
							>
							</nb-badge>
							<div class="main-header">
								<ngx-avatar [src]="src" [name]="ownerName">
								</ngx-avatar>
								<p class="mt-3">
									{{ update.createdAt | date }}
								</p>
							</div>
						</nb-card-header>
						<nb-card-footer class="custom-footer pt-3">
							<div
								class="d-flex align-items-center justify-content-between"
							>
								<p>
									{{ 'GOALS_PAGE.UPDATE' | translate }} :
									<span class="font-weight-bold">{{
										update.update
									}}</span>
								</p>
								<p>
									{{ update.status }}
								</p>
								<p>
									{{ 'GOALS_PAGE.PROGRESS' | translate }} :
									<span class="font-weight-bold"
										>{{ update.progress }}%</span
									>
								</p>
							</div>
						</nb-card-footer>
					</nb-card>
				</div>
			</div>
		</div>
	</nb-card-body>
	<nb-card-footer>
		<div class="d-flex float-left">
			<div class="button-container">
				<button
					class="mr-3"
					status="basic"
					nbButton
					size="small"
					class="action"
					(click)="deleteKeyResult()"
					[nbTooltip]="'BUTTONS.DELETE' | translate"
				>
					<nb-icon status="danger" icon="trash-2-outline"></nb-icon>
				</button>
			</div>
			<button nbButton status="success" (click)="closeDialog(true)">
				{{ 'BUTTONS.SAVE' | translate }}
			</button>
		</div>
	</nb-card-footer>
</nb-card>
