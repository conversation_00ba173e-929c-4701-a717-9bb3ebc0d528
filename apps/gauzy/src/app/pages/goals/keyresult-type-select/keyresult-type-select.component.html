<div [formGroup]="parentFormGroup">
	<div [ngClass]="{ row: enableHelperText }">
		<div [ngClass]="{ 'col-md-7': enableHelperText }">
			<label for="key-result-type" class="label mt-3">
				{{ 'KEY_RESULT_PAGE.FORM.LABELS.KEY_RESULT_TYPE' | translate }}
			</label>
			<nb-select
				(selectedChange)="taskTypeValidators()"
				id="key-result-type"
				fullWidth
				formControlName="type"
			>
				<nb-option
					*ngFor="let type of keyResultTypeEnum | keyvalue"
					[hidden]="
						type.value == keyResultTypeEnum.KPI
							? !settings?.krTypeKPI
							: type.value == keyResultTypeEnum.TASK
							? !settings?.krTypeTask
							: false
					"
					[value]="type.value"
				>
					{{ 'KEY_RESULT_PAGE.TYPE.' + type.key | translate }}
				</nb-option>
			</nb-select>
		</div>
		<div
			*ngIf="enableHelperText"
			class="col-md-5 position-relative mt-3"
		></div>
	</div>

	<div
		[ngClass]="{ row: enableHelperText }"
		*ngIf="parentFormGroup.value.type === keyResultTypeEnum.TASK"
	>
		<div [ngClass]="{ 'col-md-7': enableHelperText }">
			<div class="row">
				<div class="col-md-12">
					<label class="label mt-3" for="project-selector">{{
						'KEY_RESULT_PAGE.FORM.LABELS.SELECT_PROJECT' | translate
					}}</label>
					<ga-project-selector
						id="project-selector"
						formControlName="projectId"
					>
					</ga-project-selector>
				</div>
				<div class="col-md-12">
					<label class="label mt-3" for="task-selector">{{
						'KEY_RESULT_PAGE.FORM.LABELS.SELECT_TASK' | translate
					}}</label>
					<ga-task-selector
						id="task-selector"
						formControlName="taskId"
						[(projectId)]="parentFormGroup.value.projectId"
					>
					</ga-task-selector>
				</div>
			</div>
		</div>
		<div
			*ngIf="enableHelperText"
			class="col-md-5 position-relative mt-3"
		></div>
	</div>

	<div [ngClass]="{ row: enableHelperText }">
		<div [ngClass]="{ 'col-md-7': enableHelperText }">
			<div *ngIf="parentFormGroup.value.type === keyResultTypeEnum.KPI">
				<label for="kpi" class="label mt-3">
					{{ 'KEY_RESULT_PAGE.FORM.LABELS.SELECT_KPI' | translate }}
				</label>
				<nb-select
					*ngIf="KPIs.length > 0"
					id="kpi"
					fullWidth
					formControlName="kpiId"
				>
					<nb-option *ngFor="let kpi of KPIs" [value]="kpi.id">
						{{ kpi.name }}
					</nb-option>
				</nb-select>
				<p *ngIf="KPIs.length === 0">
					<button
						nbButton
						status="primary"
						id="kpi"
						(click)="openEditKPI()"
					>
						{{ 'BUTTONS.ADD_KPI' | translate }}
					</button>
				</p>
			</div>
		</div>
		<div
			*ngIf="enableHelperText"
			class="col-md-5 position-relative mt-3"
		></div>
	</div>

	<div
		[ngClass]="{ row: enableHelperText }"
		*ngIf="
			parentFormGroup.value.type == keyResultTypeEnum.NUMERICAL ||
			parentFormGroup.value.type == keyResultTypeEnum.CURRENCY
		"
	>
		<div [ngClass]="{ 'col-md-7': enableHelperText }">
			<div class="row mt-2">
				<div class="col-md-4">
					<label for="initial-value" class="label">
						{{
							'KEY_RESULT_PAGE.FORM.LABELS.INITIAL_VALUE'
								| translate
						}}
					</label>
					<input
						type="number"
						[min]="0"
						id="initial-value"
						nbInput
						fullWidth
						formControlName="initialValue"
						placeholder="{{
							'KEY_RESULT_PAGE.FORM.LABELS.INITIAL_VALUE'
								| translate
						}}"
					/>
				</div>

				<div class="col-md-4">
					<label for="target-value" class="label">
						{{
							'KEY_RESULT_PAGE.FORM.LABELS.TARGET_VALUE'
								| translate
						}}
					</label>

					<input
						class="d-flex space-between"
						type="number"
						id="target-value"
						nbInput
						[min]="0"
						fullWidth
						formControlName="targetValue"
						placeholder="{{
							'KEY_RESULT_PAGE.FORM.LABELS.TARGET_VALUE'
								| translate
						}}"
					/>
				</div>
				<div class="col-md-4">
					<ga-goal-custom-unit-select
						[parentFormGroup]="parentFormGroup"
						[numberUnits]="numberUnits"
					></ga-goal-custom-unit-select>
				</div>
			</div>
		</div>
		<div
			*ngIf="enableHelperText"
			class="col-md-5 position-relative mt-3"
		></div>
	</div>
</div>
