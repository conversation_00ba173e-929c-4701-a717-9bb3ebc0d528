<nb-card class="max-width-50vw">
	<nb-card-header class="d-flex flex-column">
		<span class="cancel"
			><i class="fas fa-times" (click)="closeDialog()"></i
		></span>
		<h4 class="title">
			{{
				(!!data
					? 'GOALS_PAGE.EDIT_OBJECTIVE'
					: 'GOALS_PAGE.ADD_NEW_OBJECTIVE'
				) | translate
			}}
		</h4>
	</nb-card-header>

	<nb-card-body>
		<form [formGroup]="form">
			<div class="row">
				<div
					class="col-md-7"
					(mouseenter)="helperText = 'objective-title'"
					(mouseleave)="helperText = ''"
				>
					<label for="objective-title" class="label">
						{{ 'FORM.LABELS.NAME' | translate }}
					</label>
					<input
						type="text"
						id="objective-title"
						nbInput
						fullWidth
						formControlName="name"
						[placeholder]="
							'GOALS_PAGE.FORM.PLACEHOLDERS.NAME' | translate
						"
					/>
				</div>
				<div class="mt-3 col-md-5 position-relative helper-text">
					<p
						*ngIf="helperText == ''"
						class="mt-3 position-absolute"
						[innerHtml]="
							'GOALS_PAGE.HELPER_TEXT.OBJECTIVE_GENERAL'
								| translate
						"
					></p>
					<div
						*ngIf="helperText == 'objective-title'"
						class="mt-3 position-absolute"
						[innerHtml]="
							'GOALS_PAGE.HELPER_TEXT.OBJECTIVE_TITLE' | translate
						"
					></div>
				</div>
			</div>

			<div class="row">
				<div
					class="col-md-7"
					(mouseenter)="helperText = 'objective-description'"
					(mouseleave)="helperText = ''"
				>
					<label for="objective-description" class="label mt-3">
						{{ 'FORM.LABELS.DESCRIPTION_OPTIONAL' | translate }}
					</label>
					<textarea
						id="objective-description"
						nbInput
						fullWidth
						formControlName="description"
						[placeholder]="
							'GOALS_PAGE.FORM.PLACEHOLDERS.DESCRIPTION'
								| translate
						"
					></textarea>
				</div>
				<div class="col-md-5 mt-3 position-relative helper-text">
					<div
						*ngIf="helperText == 'objective-description'"
						class="mt-3 position-absolute"
					>
						<p>
							{{
								'GOALS_PAGE.HELPER_TEXT.OBJECTIVE_DESCRIPTION'
									| translate
							}}
						</p>
					</div>
				</div>
			</div>

			<ga-goal-level-select
				[parentFormGroup]="form"
				[orgId]="orgId"
				[teams]="teams"
				[hideOrg]="hideOrg"
				[hideEmployee]="hideEmployee"
				[hideTeam]="hideTeam"
				[helperText]="helperText"
				[employees]="employees"
				[orgName]="orgName"
				[enableHelperText]="true"
			>
			</ga-goal-level-select>

			<div class="row">
				<div
					class="col-md-7"
					(mouseenter)="helperText = 'objective-deadline'"
					(mouseleave)="helperText = ''"
				>
					<label for="objective-deadline" class="label mt-3">
						{{ 'GOALS_PAGE.FORM.LABELS.DEADLINE' | translate }}
					</label>
					<nb-select
						*ngIf="timeFrames.length > 0"
						id="objective-deadline"
						formControlName="deadline"
						placeholder="{{
							'GOALS_PAGE.FORM.LABELS.DEADLINE' | translate
						}}"
						fullWidth
					>
						<nb-option
							*ngFor="let deadline of timeFrames"
							[value]="deadline.name"
							>{{ deadline.name }}</nb-option
						>
					</nb-select>
					<p>
						<button
							id="objective-deadline"
							*ngIf="timeFrames.length == 0"
							status="primary"
							nbButton
							(click)="openSetTimeFrame()"
						>
							{{
								'GOALS_PAGE.BUTTONS.ADD_TIME_FRAME' | translate
							}}
						</button>
					</p>
				</div>
				<div class="col-md-5 mt-3 position-relative helper-text">
					<div
						class="mt-3 position-absolute"
						*ngIf="helperText == 'objective-deadline'"
					>
						<p>
							{{
								'GOALS_PAGE.HELPER_TEXT.OBJECTIVE_TIMEFRAME'
									| translate
							}}
						</p>
					</div>
				</div>
			</div>
		</form>
	</nb-card-body>
	<nb-card-footer class="text-left">
		<button class="mr-3" nbButton outline status="basic" (click)="closeDialog()">
			{{ 'BUTTONS.CANCEL' | translate }}
		</button>
		<button
			nbButton
			[disabled]="
				form.invalid || hideOrg
					? true
					: this.form.value.level === goalLevelEnum.ORGANIZATION
					? false
					: true
			"
			status="success"
			(click)="saveObjective()"
		>
			{{ 'BUTTONS.SAVE' | translate }}
		</button>
	</nb-card-footer>
</nb-card>
