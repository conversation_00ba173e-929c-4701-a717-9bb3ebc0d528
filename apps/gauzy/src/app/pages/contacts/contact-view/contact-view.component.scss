nb-accordion {
  box-shadow: var(--gauzy-shadow);

  nb-accordion-item-header {
    font-size: 15px;
    font-weight: 600;
    line-height: 18px;
    letter-spacing: 0em;
    text-align: left;
    color: var(--gauzy-text-color-2);

    &.accordion-item-header-expanded {
      background-color: var(--gauzy-sidebar-background-3);
      border-radius: var(--border-radius) var(--border-radius) 0 0;
    }
  }

  nb-accordion-item-body {
    background-color: var(--gauzy-card-3);

    label {
      color: var(--gauzy-text-color-2);
      font-size: 12px;
      font-weight: 600;
      line-height: 15px;
      letter-spacing: 0em;
      text-align: left;
    }
  }
}

nb-card {
  background-color: var(--gauzy-card-2);
  margin: 0;
}

.profile-userpic {
  display: flex;
  align-items: center;

  img {
    width: 100px;
    height: 100px;
    object-fit: cover;
  }
}

.profile-userpic {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.profile-sidebar {
  display: flex;
  flex-direction: column;
  row-gap: 2.25rem;
}

.profile-usertitle {
  display: flex;
  flex-direction: column;
  row-gap: 10px;

  .profile-user-title-name {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    padding: 5px 14px 5px 7px;
    width: fit-content;
    background: rgba(110, 73, 232, 0.05);
    border-radius: 20px;
    color: rgba(110, 73, 232, 1);
    font-size: 16px;
    font-weight: 400;
    line-height: 19px;
    letter-spacing: 0em;
    text-align: left;
  }
}

.text-caption.caption {
  color: var(--gauzy-text-color-1);
  font-size: 14px;
  font-weight: 400;
  line-height: 17px;
  letter-spacing: 0em;
  text-align: left;
}

nb-tab.content-active {
  background-color: var(--gauzy-card-2);
  margin: 0 -24px -16px 0;
  border-radius: 0 0 var(--border-radius) 0;
  height: calc(100vh - 17.25rem);
  padding: 16px 20px 0 20px;

  .map {
    margin-bottom: -10px;
  }
}

:host ga-leaflet-map ::ng-deep .leaflet-container {
  height: calc(100vh - 30.5rem) !important;
}
