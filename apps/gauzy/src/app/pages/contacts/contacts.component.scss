@use '@shared/_pg-card' as *;

.contact-list {
  display: flex;
  flex-wrap: wrap;

  .member-card {
    display: flex;
    flex-wrap: wrap;
    max-width: 340px;
    width: 100%;
  }

  .contact-info {
    padding-right: 12px;
    display: flex;
    flex-direction: column;

    .info-line {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      font-size: 0.7em;
      color: darkgray;

      .info-value {
        display: flex;
        justify-content: flex-end;
        text-align: end;

        .info-list-item:not(:last-child)::after {
          content: ',';
          margin-right: 5px;
        }
      }
    }
  }
}

.main-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

:host {
  nb-card-body.mutation {
    height: calc($card-height + 4.75rem) !important;
  }
}
