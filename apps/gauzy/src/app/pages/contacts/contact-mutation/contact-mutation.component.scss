@use 'gauzy/_gauzy-dialogs' as *;

.contact-container {
  transition: transform 150ms ease-in-out;
  display: flex;
  flex-direction: column;
  position: relative;
  margin-right: 3rem;

  .contact-photo {
    width: fit-content;
    height: 294px;
    position: relative;

    .image-overlay {
      pointer-events: none;
      background: black;
      position: absolute;
      height: 100%;
      width: 100%;
      border-radius: nb-theme(border-radius);
    }

    img,
    .image {
      width: 299px;
      height: 294px;
      object-fit: cover;
      border-radius: nb-theme(border-radius);
    }

    .image {
      background-color: rgba(126, 126, 143, 0.1);

      i {
        margin: 5px;
      }

      > span {
        color: rgba(126, 126, 143, 1);
        z-index: 2;
        transition: opacity 0.2s ease-in;
        position: absolute;
        top: calc(50% - 16px / 2);
        left: calc(50% - 141px / 2);
      }
    }

    svg {
      z-index: 2;
      transition: opacity 0.2s ease-in;
      opacity: 0.3;
      position: absolute;
      top: calc(50% - 68px / 2);
      left: calc(50% - 68px / 2);

      g circle {
        fill: nb-theme(text-primary-color);
      }
    }
  }
}

.contact-form {
  display: flex;
}

.team-card {
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  .name-container {
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    .image-container {
      width: 70px;
      height: 50px;
      display: flex;

      img {
        height: 100%;
        max-width: 70px;
      }
    }
  }

  .button-container {
    display: flex;
    width: 160px;
    justify-content: space-between;
    margin-top: 15px;
    margin-left: 9px;
  }
}

.buttons {
  position: absolute;
  left: 2.75rem;
  bottom: 1.4rem;
}

h4 {
  font-size: 24px;
  font-weight: 600;
  line-height: 30px;
  letter-spacing: 0em;
}

:host {
  height: 100%;
  nb-card,
  nb-card-body,
  nb-card-header {
    background-color: unset;
  }
  nb-card,
  nb-card-body,
  nb-stepper {
    height: 100%;
    padding-bottom: 0 !important;
  }

  nb-card {
    margin: -1rem;
    border: var(--gauzy-card-2);
  }

  nb-stepper ::ng-deep .step-content {
    overflow-y: auto;
    height: calc(100vh - 20.75rem);
  }

  .custom-card {
    height: 100%;
    width: 100%;
  }

  ::ng-deep ngx-image-uploader input {
    height: 100% !important;
  }

  @include dialog(transparent, var(--gauzy-card-1));
}
