<nb-card>
	<nb-card-header class="d-flex">
		<ngx-back-navigation [haveLink]="true" (click)="cancel()"></ngx-back-navigation>
		<h4>
			{{
				(organizationContact
					? 'ORGANIZATIONS_PAGE.EDIT.EDIT_CONTACT'
					: 'ORGANIZATIONS_PAGE.EDIT.ADD_NEW_CONTACT'
				) | translate
			}}
		</h4>
	</nb-card-header>
	<nb-card-body>
		<nb-stepper #stepper disableStepNavigation>
			<!-- Step 1 Contact Main Form -->
			<nb-step [stepControl]="contMainForm" [label]="'CONTACTS_PAGE.MAIN' | translate">
				<ng-template #mainStep>
					{{ 'CONTACTS_PAGE.MAIN' | translate }}
				</ng-template>
				<div class="custom-card">
					<form
						[formGroup]="contMainForm"
						(ngSubmit)="contMainForm.markAsDirty()"
						class="contact-form d-flex flex-nowrap justify-content-between"
					>
						<div class="contact-container">
							<div class="contact-photo">
								<ngx-image-uploader
									(changeHoverState)="hoverState = $event"
									(uploadedImageAsset)="updateImageAsset($event)"
									(uploadImageAssetError)="handleImageUploadError($event)"
								></ngx-image-uploader>
								<svg
									*ngIf="contMainForm && contMainForm.get('imageUrl').value"
									xmlns="http://www.w3.org/2000/svg"
									xmlns:xlink="http://www.w3.org/1999/xlink"
									width="68"
									height="68"
									viewBox="0 0 68 68"
									[ngStyle]="{
										opacity: hoverState ? '1' : '0.3'
									}"
								>
									<defs>
										<path
											id="a"
											d="M28.667 31.333a2 2 0 1 0-.002-4.001 2 2 0 0 0 .002 4.001m13.333 12H26.748l9.34-7.793c.328-.279.923-.277 1.244-.001l6.001 5.12V42c0 .736-.597 1.333-1.333 1.333M26 24.667h16c.736 0 1.333.597 1.333 1.333v11.152l-4.27-3.643c-1.32-1.122-3.386-1.122-4.694-.008l-9.702 8.096V26c0-.736.597-1.333 1.333-1.333M42 22H26c-2.205 0-4 1.795-4 4v16c0 2.205 1.795 4 4 4h16c2.205 0 4-1.795 4-4V26c0-2.205-1.795-4-4-4"
										/>
									</defs>
									<g fill="none" fill-rule="evenodd">
										<circle cx="34" cy="34" r="34" fill="#0091FF" opacity=".3" />
										<circle cx="34" cy="34" r="26" fill="#0091FF" opacity=".9" />
										<use fill="#FFF" fill-rule="nonzero" xlink:href="#a" />
									</g>
								</svg>
								<div
									class="image-overlay"
									[ngStyle]="{
										opacity: hoverState ? '0.2' : '0'
									}"
								></div>

								<img
									*ngIf="contMainForm && contMainForm.get('imageUrl').value"
									[src]="contMainForm.get('imageUrl').value"
									alt="Contact Photo"
									(mouseenter)="hoverState = true"
									(mouseleave)="hoverState = false"
								/>
								<div *ngIf="!contMainForm || !contMainForm.get('imageUrl').value" class="image">
									<span><i class="fas fa-image"></i>Add or Drop Image</span>
								</div>
							</div>
						</div>
						<div class="fields container">
							<div class="row">
								<div class="form-group col-7">
									<label class="label">{{ 'ORGANIZATIONS_PAGE.EDIT.NAME' | translate }}</label>
									<input
										id="name"
										type="text"
										#name
										nbInput
										fullWidth
										formControlName="name"
										[placeholder]="'ORGANIZATIONS_PAGE.EDIT.NAME' | translate"
										[status]="
											FormHelpers.isInvalidControl(contMainForm, 'name') ? 'danger' : 'basic'
										"
									/>
									<ng-container *ngIf="FormHelpers.isInvalidControl(contMainForm, 'name')">
										<p class="caption status-danger mt-1">
											{{ 'TOASTR.MESSAGE.NAME_REQUIRED' | translate }}
										</p>
									</ng-container>
								</div>
							</div>
							<div class="row">
								<div class="form-group col-7">
									<label class="label">{{
										'ORGANIZATIONS_PAGE.EDIT.PRIMARY_EMAIL' | translate
									}}</label>
									<input
										id="email"
										autofocus
										pattern=".+@.+\..+"
										type="email"
										#email
										nbInput
										fullWidth
										formControlName="primaryEmail"
										[placeholder]="'ORGANIZATIONS_PAGE.EDIT.PRIMARY_EMAIL' | translate"
										[status]="
											FormHelpers.isInvalidControl(contMainForm, 'primaryEmail')
												? 'danger'
												: 'basic'
										"
									/>
									<ng-container *ngIf="FormHelpers.isInvalidControl(contMainForm, 'primaryEmail')">
										<p
											class="caption status-danger mt-1"
											*ngIf="contMainForm.controls.primaryEmail.errors?.pattern"
										>
											{{ 'TOASTR.MESSAGE.EMAIL_SHOULD_BE_REAL' | translate }}
										</p>
									</ng-container>
								</div>
								<div class="form-group col-5">
									<label class="label">{{ 'ORGANIZATIONS_PAGE.EDIT.PHONE' | translate }}</label>
									<input
										type="text"
										#phone
										nbInput
										fullWidth
										formControlName="primaryPhone"
										[placeholder]="'ORGANIZATIONS_PAGE.EDIT.PHONE' | translate"
									/>
								</div>
							</div>
							<div class="row">
								<div class="form-group col-7">
									<label class="label">{{ 'ORGANIZATIONS_PAGE.EDIT.WEBSITE' | translate }}</label>
									<input
										type="text"
										#website
										nbInput
										fullWidth
										formControlName="website"
										[placeholder]="'ORGANIZATIONS_PAGE.EDIT.WEBSITE' | translate"
									/>
								</div>
								<div class="form-group col-5">
									<label class="label">{{ 'ORGANIZATIONS_PAGE.EDIT.FAX' | translate }}</label>
									<input
										type="text"
										#fax
										nbInput
										fullWidth
										formControlName="fax"
										[placeholder]="'ORGANIZATIONS_PAGE.EDIT.FAX' | translate"
									/>
								</div>
							</div>
							<div class="row">
								<div class="form-group col-7">
									<label class="label">{{
										'ORGANIZATIONS_PAGE.EDIT.FISCAL_INFORMATION' | translate
									}}</label>
									<input
										type="text"
										#fiscalInformation
										nbInput
										fullWidth
										formControlName="fiscalInformation"
										[placeholder]="'ORGANIZATIONS_PAGE.EDIT.FISCAL_INFORMATION' | translate"
									/>
								</div>
							</div>
							<div class="row">
								<div class="form-group col-7">
									<label class="label">{{ 'ORGANIZATIONS_PAGE.EDIT.PROJECTS' | translate }}</label>
									<ng-select
										[addTag]="addNewProject"
										[items]="projects"
										bindLabel="name"
										[multiple]="true"
										[searchable]="true"
										formControlName="projects"
										[placeholder]="'ORGANIZATIONS_PAGE.EDIT.PROJECTS' | translate"
										appendTo="body"
									>
									</ng-select>
								</div>
								<div class="form-group col-5">
									<label class="label">{{ 'CONTACTS_PAGE.CONTACT_TYPE' | translate }}</label>
									<ng-select
										formControlName="contactType"
										[clearable]="false"
										[searchable]="false"
										[placeholder]="'CONTACTS_PAGE.CONTACT_TYPE' | translate"
										appendTo="body"
									>
										<ng-option [value]="type" *ngFor="let type of contactTypes">{{
											type
										}}</ng-option>
									</ng-select>
								</div>
							</div>
							<div class="row">
								<div class="form-group col-12">
									<ga-tags-color-input
										[selectedTags]="tags"
										(selectedTagsEvent)="selectedTagsEvent($event)"
										[isOrgLevel]="true"
									>
									</ga-tags-color-input>
								</div>
							</div>
						</div>
						<div class="buttons">
							<div class="d-flex">
								<button
									[disabled]="contMainForm.invalid"
									nbButton
									(click)="nextStep()"
									class="green mr-2"
									status="basic"
									outline
								>
									{{ 'BUTTONS.NEXT' | translate }}
								</button>
								<button nbButton (click)="cancel()" status="basic" outline>
									{{ 'BUTTONS.CANCEL' | translate }}
								</button>
							</div>
						</div>
					</form>
				</div>
			</nb-step>
			<!-- Step 1 Contact Main Form End -->

			<!-- Step 2 Address Form -->
			<nb-step [stepControl]="locationForm" [label]="'CONTACTS_PAGE.ADDRESS' | translate">
				<ng-template #addressStep>{{ 'CONTACTS_PAGE.ADDRESS' | translate }}</ng-template>
				<form
					class="step-container custom-card"
					[formGroup]="locationForm"
					*ngIf="locationForm"
					(ngSubmit)="locationForm.markAsDirty()"
				>
					<div class="fields h-100">
						<div class="row h-100">
							<div class="col-5 h-100 d-flex flex-column justify-content-between">
								<ga-location-form
									#locationFormDirective
									[form]="locationForm"
									[showAutocompleteSearch]="true"
									[showCoordinateInput]="true"
									(mapCoordinatesEmitter)="onCoordinatesChanges($event)"
									(mapGeometryEmitter)="onGeometrySend($event)"
								>
								</ga-location-form>
								<div>
									<button nbButton nbStepperPrevious class="gray mr-2" status="basic" outline>
										{{ 'BUTTONS.PREVIOUS' | translate }}
									</button>
									<button
										[disabled]="locationForm.invalid"
										nbButton
										class="green mr-2"
										status="basic"
										outline
										nbStepperNext
									>
										{{ 'BUTTONS.NEXT' | translate }}
									</button>
									<button nbButton (click)="cancel()" class="mr-2" status="basic" outline>
										{{ 'BUTTONS.CANCEL' | translate }}
									</button>
								</div>
							</div>
							<div class="col-7">
								<ng-container *ngIf="stepper._selectedIndex === 1">
									<ga-leaflet-map #leafletTemplate (mapClicked)="onMapClicked($event)">
									</ga-leaflet-map>
								</ng-container>
							</div>
						</div>
					</div>
				</form>
			</nb-step>
			<!-- Step 2 Address Form End -->

			<!-- Step 3 Budget Form -->
			<nb-step
				*ngIf="budgetForm"
				[stepControl]="budgetForm"
				[label]="'CONTACTS_PAGE.BUDGET' | translate"
				(ngSubmit)="budgetForm.markAsDirty()"
			>
				<form class="custom-card" [formGroup]="budgetForm">
					<div class="row">
						<div class="form-group col-3">
							<label class="label" [innerHtml]="'FORM.PLACEHOLDERS.BUDGET_TYPE' | translate"></label>
							<nb-select
								fullWidth
								[placeholder]="'FORM.PLACEHOLDERS.BUDGET_TYPE' | translate"
								formControlName="budgetType"
							>
								<nb-option
									*ngFor="let budgetType of organizationContactBudgetTypeEnum | keyvalue"
									[value]="budgetType.value"
								>
									{{ budgetType.value | titlecase }}
								</nb-option>
							</nb-select>
						</div>
						<ng-container *ngIf="budgetType === organizationContactBudgetTypeEnum.HOURS">
							<div class="form-group col-3">
								<label class="label" [innerHtml]="'FORM.PLACEHOLDERS.HOURS' | translate"></label>
								<input
									nbInput
									type="number"
									[min]="0"
									fullWidth
									formControlName="budget"
									[placeholder]="'FORM.PLACEHOLDERS.HOURS' | translate"
								/>
							</div>
						</ng-container>
						<ng-container *ngIf="budgetType === organizationContactBudgetTypeEnum.COST">
							<div class="form-group col-3">
								<label class="label" [innerHtml]="'FORM.PLACEHOLDERS.COST' | translate"></label>
								<input
									nbInput
									type="number"
									[min]="0"
									fullWidth
									formControlName="budget"
									[placeholder]="'FORM.PLACEHOLDERS.COST' | translate"
								/>
							</div>
						</ng-container>
					</div>
					<div class="buttons">
						<button nbButton nbStepperPrevious class="gray mr-2" status="basic" outline>
							{{ 'BUTTONS.PREVIOUS' | translate }}
						</button>
						<button
							[disabled]="budgetForm.invalid"
							nbButton
							class="green mr-2"
							status="basic"
							outline
							nbStepperNext
						>
							{{ 'BUTTONS.NEXT' | translate }}
						</button>
						<button nbButton (click)="cancel()" class="mr-2" status="basic" outline>
							{{ 'BUTTONS.CANCEL' | translate }}
						</button>
					</div>
				</form>
			</nb-step>
			<!-- Step 3 Budget Form End -->

			<!-- Step 4 Members Form -->
			<nb-step [label]="'CONTACTS_PAGE.MEMBERS' | translate">
				<ng-template #membersStep>{{ 'CONTACTS_PAGE.MEMBERS' | translate }}</ng-template>
				<div class="fields custom-card">
					<div class="row">
						<div class="col-6">
							<div class="form-group">
								<ga-employee-multi-select
									[selectedEmployeeIds]="selectedEmployeeIds"
									(onLoadEmployees)="onLoadEmployees($event)"
									(selectedChange)="onMembersSelected($event)"
								>
								</ga-employee-multi-select>
							</div>
						</div>
						<div class="col-6" *ngIf="selectedMembers">
							<div class="team-card">
								<div class="name-container" *ngFor="let member of selectedMembers">
									<ngx-avatar
										class="report-table"
										[src]="member.user.imageUrl"
										[name]="member.user.name"
										[id]="member.id"
										[employee]="member"
										[nbTooltip]="'ORGANIZATIONS_PAGE.EDIT.CLICK_EMPLOYEE' | translate"
									></ngx-avatar>
								</div>
							</div>
						</div>
					</div>
					<div class="buttons">
						<button class="gray mr-2" status="basic" outline nbButton nbStepperPrevious>
							{{ 'BUTTONS.PREVIOUS' | translate }}
						</button>
						<button (click)="submitForm()" nbButton status="success" class="mr-2 ml-2" nbStepperNext>
							{{ 'BUTTONS.SAVE' | translate }}
						</button>
					</div>
				</div>
			</nb-step>
			<!-- Step 4 Members Form End -->
		</nb-stepper>
	</nb-card-body>
</nb-card>
