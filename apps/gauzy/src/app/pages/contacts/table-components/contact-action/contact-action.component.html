<button
	size="small"
	class="action"
	(click)="invite(rowData)"
	*ngIf="!rowData?.inviteStatus || rowData?.inviteStatus === 'NOT_INVITED'"
	nbButton
	status="success"
>
	<nb-icon class="mr-1" icon="email-outline"></nb-icon
	>{{ 'BUTTONS.INVITE' | translate }}
</button>
<button
	size="small"
	class="action info-text-1"
	status="basic"
	(click)="invite(rowData)"
	*ngIf="rowData?.inviteStatus === 'INVITED'"
	nbButton
>
	<nb-icon class="mr-1" icon="email-outline"></nb-icon
	>{{ 'BUTTONS.INVITE_AGAIN' | translate }}
</button>
