<nb-card [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large">
	<nb-card-header class="d-flex flex-column pb-0">
		<div class="main-header">
			<h4>
				<ngx-header-title>
					{{ contactType + 's' | titlecase }}
				</ngx-header-title>
			</h4>
		</div>
		<div *ngIf="!showAddCard" class="align-self-end">
			<ngx-gauzy-button-action
				[componentName]="viewComponentName"
				[isDisable]="disableButton"
				[buttonTemplate]="actionButtons"
				[buttonTemplateVisible]="visible"
			></ngx-gauzy-button-action>
		</div>
	</nb-card-header>
	<nb-card-body [ngClass]="{ mutation: showAddCard }" class="custom-card">
		<ng-container *ngIf="showAddCard; else layout">
			<ga-contact-mutation
				class="w-100"
				[contactType]="contactType"
				[organizationContact]="selectedOrganizationContact"
				[projectsWithoutOrganizationContacts]="projectsWithoutOrganizationContacts"
				(canceled)="cancel()"
				(addOrEditOrganizationContact)="addOrEditOrganizationContact($event)"
			></ga-contact-mutation>
		</ng-container>
		<ng-template #layout>
			<ng-template [ngIf]="dataLayoutStyle === componentLayoutStyleEnum.TABLE" [ngIfElse]="gridLayout">
				<div class="table-scroll-container">
					<angular2-smart-table
						style="cursor: pointer"
						(userRowSelect)="selectContact($event)"
						[settings]="settingsSmartTable"
						[source]="smartTableSource"
					></angular2-smart-table>
				</div>
				<div class="pagination-container align-self-end">
					<ng-container *ngIf="smartTableSource">
						<ngx-pagination [source]="smartTableSource"></ngx-pagination>
					</ng-container>
				</div>
			</ng-template>
			<ng-template #gridLayout>
				<ga-card-grid
					[totalItems]="pagination?.totalItems"
					(onSelectedItem)="selectContact($event)"
					[settings]="settingsSmartTable"
					[source]="organizationContacts"
					(scroll)="onScroll()"
				></ga-card-grid>
			</ng-template>
		</ng-template>
	</nb-card-body>
</nb-card>

<ng-template #actionButtons>
	<ngx-favorite-toggle
		*ngIf="selectedOrganizationContact"
		[entityType]="'OrganizationContact'"
		[entityId]="selectedOrganizationContact.id"
		[entityName]="selectedOrganizationContact.name"
		size="small"
		status="basic"
		spacing="list"
		[disabled]="disableButton"
		[showLabel]="false"
		(favoriteToggled)="onContactFavoriteToggled($event)"
	></ngx-favorite-toggle>
	<ng-template ngxPermissionsOnly="ORG_CONTACT_EDIT">
		<div class="btn-group actions">
			<ngx-contact-action
				[rowData]="selectedOrganizationContact"
				(updateResult)="onUpdateResult($event)"
			></ngx-contact-action>

			<button
				nbButton
				class="action secondary"
				status="basic"
				size="small"
				[disabled]="!selectedOrganizationContact && disableButton"
				(click)="navigateToContact(selectedOrganizationContact)"
			>
				<nb-icon class="mr-1" icon="eye-outline"></nb-icon>
				{{ 'BUTTONS.VIEW' | translate }}
			</button>
			<button
				nbButton
				class="action primary"
				status="basic"
				size="small"
				[disabled]="!selectedOrganizationContact && disableButton"
				(click)="editOrganizationContact(selectedOrganizationContact)"
			>
				<nb-icon class="mr-1" icon="edit-outline"></nb-icon>
				{{ 'BUTTONS.EDIT' | translate }}
			</button>
			<button
				nbButton
				class="action"
				status="basic"
				size="small"
				[disabled]="!selectedOrganizationContact && disableButton"
				(click)="removeOrganizationContact(selectedOrganizationContact?.id, selectedOrganizationContact?.name)"
				size="small"
				[nbTooltip]="'BUTTONS.DELETE' | translate"
			>
				<nb-icon status="danger" icon="trash-2-outline"></nb-icon>
			</button>
		</div>
	</ng-template>
</ng-template>
<ng-template #visible>
	<ng-template ngxPermissionsOnly="ORG_CONTACT_EDIT">
		<button (click)="invite()" nbButton class="action info" size="small" status="basic">
			<nb-icon class="mr-1" icon="email-outline"></nb-icon>
			{{ 'BUTTONS.INVITE' | translate }}
		</button>
		<button (click)="add()" nbButton class="action" size="small" status="success">
			<nb-icon class="mr-1" icon="plus-outline"></nb-icon>
			{{ 'BUTTONS.ADD' | translate }}
		</button>
	</ng-template>
</ng-template>
