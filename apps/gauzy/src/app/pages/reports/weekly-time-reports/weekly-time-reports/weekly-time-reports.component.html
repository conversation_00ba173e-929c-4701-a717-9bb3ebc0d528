<nb-card [nbSpinner]="loading" nbSpinnerStatus="primary" nbSpinnerSize="large" class="card-scroll">
	<nb-card-header>
		<div class="row">
			<div class="col-auto">
				<h4>
					<ngx-header-title>
						{{ 'REPORT_PAGE.WEEKLY_TIME_AND_ACTIVITY_REPORT' | translate }}
					</ngx-header-title>
				</h4>
				<ng-container *ngIf="request?.startDate && request?.endDate">
					<ngx-date-range-title
						[start]="request?.startDate"
						[end]="request?.endDate"
						[format]="'dddd, LL'"
					></ngx-date-range-title>
				</ng-container>
			</div>
		</div>
		<div class="row">
			<div class="col-auto ml-auto">
				<ngx-gauzy-filters
					[filters]="filters"
					[saveFilters]="(datePickerConfig$ | async).isSaveDatePicker"
					(filtersChange)="filtersChange($event)"
				></ngx-gauzy-filters>
			</div>
		</div>
	</nb-card-header>
	<nb-card-body class="report-body">
		<div class="report-container">
			<ga-daily-statistics [filters]="filters"></ga-daily-statistics>
			<div class="daily-time-report">
				<div class="daily-time-report">
					<nb-card>
						<nb-card-body class="chart" *ngIf="organization">
							<ngx-line-chart
								[data]="charts"
								[standardWorkHours]="organization?.standardWorkHoursPerDay"
								[enableAnnotations]="true"
							></ngx-line-chart>
						</nb-card-body>
					</nb-card>
				</div>
			</div>
			<div class="table card" [ngClass]="{ 'not-found': !weekLogs?.length }">
				<div
					class="weekly-logs row-table weekly-time-grid"
					[nbSpinner]="loading"
					nbSpinnerSize="giant"
					nbSpinnerStatus="primary"
				>
					<nb-card class="m-0" *ngIf="weekLogs?.length > 0; else notFound">
						<nb-card-header>
							{{ 'MENU.REPORTS' | translate }}
						</nb-card-header>
						<nb-card-body class="weekly-time-container">
							<div class="columns-header weekly-main-header">
								<div class="table-inner-wrapper">
									<div class="responsive-table-row employee-column">
										{{ 'REPORT_PAGE.EMPLOYEE' | translate }}
									</div>
									<ng-container *ngFor="let date of weekDays">
										<div class="responsive-table-row day-column">
											{{ date | dateFormat }}
										</div>
									</ng-container>
									<div class="responsive-table-row day-column">
										{{ 'REPORT_PAGE.WEEKLY_TOTAL' | translate }}
									</div>
									<div class="responsive-table-row day-column">
										{{ 'REPORT_PAGE.ACTIVITY' | translate }}
									</div>
									<div class="responsive-table-row day-column">
										{{ 'REPORT_PAGE.SPENT' | translate }}
									</div>
								</div>
							</div>
							<div class="table-row" *ngFor="let log of weekLogs">
								<div class="table-inner-wrapper">
									<div class="responsive-table-row employee-column">
										<div class="responsive-table-header">
											{{ 'REPORT_PAGE.EMPLOYEE' | translate }}
										</div>
										<div class="responsive-table-content mobile-padding">
											<div class="avatar-wrapper">
												<ngx-avatar
													class="report-table"
													[src]="log?.employee?.user?.imageUrl"
													[name]="log?.employee?.user?.name"
													[id]="log?.employee?.id"
													[employee]="log?.employee"
												></ngx-avatar>
												<ng-template #noProjects>
													<span>
														{{ 'REPORT_PAGE.NO_EMPLOYEE' | translate }}
													</span>
												</ng-template>
											</div>
										</div>
									</div>
									<ng-container *ngFor="let date of weekDays">
										<div class="responsive-table-row day-column">
											<div class="responsive-table-header">
												{{ date | date : 'E d MM, y' }}
											</div>
											<div class="responsive-table-content mobile-padding">
												<span *ngIf="log.dates[date]; else noLogsCol">
													{{ log.dates[date]?.sum | durationFormat }}
												</span>
												<ng-template #noLogsCol> &#8212; </ng-template>
											</div>
										</div>
									</ng-container>
									<div class="responsive-table-row day-column">
										<div class="responsive-table-header">
											{{ 'REPORT_PAGE.WEEKLY_TOTAL' | translate }}
										</div>
										<div class="responsive-table-content mobile-padding">
											<span>
												{{ log?.sum | durationFormat }}
											</span>
										</div>
									</div>
									<div class="responsive-table-row day-column">
										<div class="responsive-table-header">
											{{ 'REPORT_PAGE.ACTIVITY' | translate }}
										</div>
										<div class="responsive-table-content mobile-padding">
											<span>
												<nb-badge
													[status]="getStatus(log?.activity || 0)"
													[text]="(log?.activity || 0) + '%'"
												></nb-badge>
											</span>
										</div>
									</div>
									<div class="responsive-table-row day-column">
										<div class="responsive-table-header">
											{{ 'REPORT_PAGE.SPENT' | translate }}
										</div>
										<div class="responsive-table-content mobile-padding">
											<span> &#8212; </span>
										</div>
									</div>
								</div>
							</div>
						</nb-card-body>
					</nb-card>
					<ng-template #notFound>
						<ngx-no-data-message
							[message]="'REPORT_PAGE.NO_DATA.WEEKLY_TIME_AND_ACTIVITY' | translate"
						></ngx-no-data-message>
					</ng-template>
				</div>
			</div>
		</div>
	</nb-card-body>
</nb-card>
