<nb-card class="card-scroll">
	<nb-card-header>
		<div class="row">
			<div class="col-auto">
				<h4>
					<ngx-header-title>
						{{ 'REPORT_PAGE.APPS_AND_URLS_REPORT' | translate }}
					</ngx-header-title>
				</h4>
				<ng-container *ngIf="request?.startDate && request?.endDate">
					<ngx-date-range-title
						[start]="request?.startDate"
						[end]="request?.endDate"
						[format]="'dddd, LL'"
					></ngx-date-range-title>
				</ng-container>
			</div>
		</div>
		<div class="row">
			<div class="col-auto ml-auto">
				<ngx-gauzy-filters
					[filters]="filters"
					[saveFilters]="(datePickerConfig$ | async).isSaveDatePicker"
					(filtersChange)="filtersChange($event)"
				></ngx-gauzy-filters>
			</div>
		</div>
	</nb-card-header>
	<nb-card-body class="report-body">
		<div class="report-container">
			<ga-activities-report-grid
				[filters]="filters"
			></ga-activities-report-grid>
		</div>
	</nb-card-body>
</nb-card>
