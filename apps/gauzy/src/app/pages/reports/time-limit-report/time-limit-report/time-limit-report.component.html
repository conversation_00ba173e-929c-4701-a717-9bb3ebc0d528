<nb-card class="card-scroll">
	<nb-card-header>
		<div class="row">
			<div class="col-auto">
				<h4>
					<ngx-header-title>
						{{ title | translate }}
					</ngx-header-title>
				</h4>
				<ng-container *ngIf="request?.startDate && request?.endDate">
					<ngx-date-range-title
						[start]="request?.startDate"
						[end]="request?.endDate"
						[format]="'dddd, LL'"
					></ngx-date-range-title>
				</ng-container>
			</div>
		</div>
		<div class="row">
			<div class="col-auto ml-auto">
				<ngx-gauzy-filters
					[filters]="filters"
					[saveFilters]="(datePickerConfig$ | async).isSaveDatePicker"
					(filtersChange)="filtersChange($event)"
				></ngx-gauzy-filters>
			</div>
		</div>
	</nb-card-header>
	<nb-card-body class="report-body">
		<div class="report-container">
			<div class="table">
				<div
					class="time-logs row-table"
					*ngIf="loading"
					[nbSpinner]="loading"
					[nbSpinnerSize]="'giant'"
					[nbSpinnerStatus]="'primary'"
				></div>
				<ng-template [ngIf]="dailyData?.length > 0" [ngIfElse]="notFound">
					<nb-card class="card" *ngFor="let day of dailyData">
						<nb-card-header class="card-title">
							{{ day.date | dateFormat }}
						</nb-card-header>
						<nb-card-body class="budget-container">
							<div class="columns-header" *ngIf="day.employees?.length > 0; else noEmployees">
								<div class="table-inner-wrapper font-weight-bold align-items-center">
									<ng-template [ngIf]="duration === 'week'">
										<ng-container *ngTemplateOutlet="weeklyHeadings"></ng-container>
									</ng-template>
									<ng-template [ngIf]="duration === 'day'">
										<ng-container *ngTemplateOutlet="dailyHeadings"></ng-container>
									</ng-template>
								</div>
							</div>
							<ng-template #noEmployees>
								<div class="ml-3">
									{{ 'REPORT_PAGE.NO_EMPLOYEES_WORKED' | translate }}
								</div>
							</ng-template>
							<div class="cart-body project-row">
								<div class="table-row" *ngFor="let timeLogRow of day.employees">
									<div class="table-inner-wrapper">
										<ng-template [ngIf]="duration === 'week'">
											<ng-container
												*ngTemplateOutlet="weeklyTable; context: { $implicit: timeLogRow }"
											>
											</ng-container>
										</ng-template>
										<ng-template [ngIf]="duration === 'day'">
											<ng-container
												*ngTemplateOutlet="dailyTable; context: { $implicit: timeLogRow }"
											>
											</ng-container>
										</ng-template>
									</div>
								</div>
							</div>
						</nb-card-body>
					</nb-card>
				</ng-template>
				<ng-template #notFound>
					<nb-card>
						<nb-card-body>
							<div class="row font-weight-bold py-5 align-items-center">
								<div class="col text-center">
									{{ 'REPORT_PAGE.NO_EMPLOYEES' | translate }}
								</div>
							</div>
						</nb-card-body>
					</nb-card>
				</ng-template>
			</div>
		</div>
	</nb-card-body>
</nb-card>

<ng-template let-project #projectEl>
	<ga-project-column-view *ngIf="project; else noProjects" [project]="project"></ga-project-column-view>
	<ng-template #noProjects>
		<span>{{ 'REPORT_PAGE.NO_PROJECT' | translate }}</span>
	</ng-template>
</ng-template>

<ng-template let-task #taskEl>
	<span *ngIf="task; else noTaSK">{{ task?.title }} </span>
	<ng-template #noTaSK>
		<span>{{ 'REPORT_PAGE.NO_TASK' | translate }}</span>
	</ng-template>
</ng-template>

<ng-template let-employee #employeeEl>
	<div class="avatar-wrapper-outer">
		<ngx-avatar
			class="report-table"
			*ngIf="employee"
			[src]="employee?.user?.imageUrl"
			[name]="employee?.user?.name"
			[id]="employee?.id"
			[employee]="employee"
		>
		</ngx-avatar>
	</div>
	<ng-template #noEmployee>
		<span>{{ 'REPORT_PAGE.NO_EMPLOYEE' | translate }}</span>
	</ng-template>
</ng-template>

<ng-template #weeklyHeadings>
	<div class="responsive-table-row employee-column">
		{{ 'REPORT_PAGE.EMPLOYEE' | translate }}
	</div>
	<div class="responsive-table-row limit-column">
		{{ 'REPORT_PAGE.LIMIT' | translate }}
	</div>
	<div class="responsive-table-row spent-column">
		{{ 'REPORT_PAGE.SPENT_HOURS' | translate }}
	</div>
	<div class="responsive-table-row activity-column">
		{{ 'REPORT_PAGE.ACTIVITY' | translate }}
	</div>
	<div class="responsive-table-row remaining-column">
		{{ 'REPORT_PAGE.REMAINING_HOURS' | translate }}
	</div>
</ng-template>
<ng-template let-timeLogRow #weeklyTable>
	<div class="responsive-table-row employee-column">
		<div class="responsive-table-header">
			{{ 'REPORT_PAGE.EMPLOYEE' | translate }}
		</div>
		<div class="responsive-table-content project-name">
			<ng-container *ngTemplateOutlet="employeeEl; context: { $implicit: timeLogRow?.employee }"> </ng-container>
		</div>
	</div>
	<div class="responsive-table-row limit-column">
		<div class="responsive-table-header">{{ 'REPORT_PAGE.LIMIT' | translate }}</div>
		<div class="responsive-table-content project-name">
			{{ timeLogRow.limit | durationFormat }}
		</div>
	</div>
	<div class="responsive-table-row spent-column">
		<div class="responsive-table-header">{{ 'REPORT_PAGE.SPENT_HOURS' | translate }}</div>
		<div class="responsive-table-content project-name">
			<span class="duration-span">{{ timeLogRow.duration | durationFormat }}</span>
		</div>
	</div>
	<div class="responsive-table-row activity-column">
		<div class="responsive-table-header">{{ 'REPORT_PAGE.ACTIVITY' | translate }}</div>
		<div class="responsive-table-content project-name">
			<ngx-progress-status
				class="w-100 report-progress"
				defaultStatus="success"
				[percentage]="timeLogRow.durationPercentage"
			>
			</ngx-progress-status>
		</div>
	</div>
	<div class="responsive-table-row remaining-column remaining-content">
		<div class="responsive-table-header">{{ 'REPORT_PAGE.TO_DO' | translate }}</div>
		<div class="responsive-table-content day-col">
			{{
				timeLogRow.limit - timeLogRow.duration > 0
					? (timeLogRow.limit - timeLogRow.duration | durationFormat)
					: 0
			}}
		</div>
	</div>
</ng-template>

<ng-template #dailyHeadings>
	<div class="responsive-table-row employee-column">
		{{ 'REPORT_PAGE.EMPLOYEE' | translate }}
	</div>
	<div class="responsive-table-row activity-column">
		{{ 'REPORT_PAGE.ACTIVITY' | translate }}
	</div>
	<div class="responsive-table-row spent-column">
		8
		{{ 'REPORT_PAGE.SPENT_HOURS' | translate }}
	</div>
	<div class="responsive-table-row remaining-column">
		{{ 'REPORT_PAGE.REMAINING_HOURS' | translate }}
	</div>
	<div class="responsive-table-row limit-column">
		{{ 'REPORT_PAGE.LIMIT' | translate }}
	</div>
</ng-template>
<ng-template let-timeLogRow #dailyTable>
	<div class="responsive-table-row employee-column">
		<div class="responsive-table-header">
			{{ 'REPORT_PAGE.EMPLOYEE' | translate }}
		</div>
		<div class="responsive-table-content project-name">
			<ng-container *ngTemplateOutlet="employeeEl; context: { $implicit: timeLogRow?.employee }"> </ng-container>
		</div>
	</div>
	<div class="responsive-table-row activity-column">
		<div class="responsive-table-header">{{ 'REPORT_PAGE.ACTIVITY' | translate }}</div>
		<div class="responsive-table-content project-name">
			<ngx-progress-status
				class="w-100 report-progress"
				defaultStatus="success"
				[percentage]="timeLogRow.durationPercentage"
			>
			</ngx-progress-status>
		</div>
	</div>
	<div class="responsive-table-row spent-column">
		<div class="responsive-table-header">{{ 'REPORT_PAGE.SPENT_HOURS' | translate }}</div>
		<div class="responsive-table-content project-name">
			<span class="duration-span">{{ timeLogRow.duration | durationFormat }}</span>
		</div>
	</div>
	<div class="responsive-table-row remaining-column">
		<div class="responsive-table-header">{{ 'REPORT_PAGE.TO_DO' | translate }}</div>
		<div class="responsive-table-content day-col">
			{{
				timeLogRow.limit - timeLogRow.duration > 0
					? (timeLogRow.limit - timeLogRow.duration | durationFormat)
					: 0
			}}
		</div>
	</div>
	<div class="responsive-table-row limit-column">
		<div class="responsive-table-header">{{ 'REPORT_PAGE.LIMIT' | translate }}</div>
		<div class="responsive-table-content project-name">
			{{ timeLogRow.limit | durationFormat }}
		</div>
	</div>
</ng-template>
