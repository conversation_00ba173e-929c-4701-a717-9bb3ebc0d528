<ng-template ngxPermissionsOnly="APPROVALS_POLICY_VIEW">
	<nb-card
		[nbSpinner]="loading"
		nbSpinnerStatus="primary"
		nbSpinnerSize="large"
		class="card-scroll"
	>
		<nb-card-header class="d-flex flex-column pb-0">
			<div class="card-header-title align-self-start">
				<ngx-back-navigation></ngx-back-navigation>
				<h4>
					<ngx-header-title>
						{{ 'APPROVAL_POLICY_PAGE.HEADER' | translate }}
					</ngx-header-title>
				</h4>
			</div>
			<div class="gauzy-button-container">
				<ngx-gauzy-button-action
					[buttonTemplate]="actionButtons"
					[buttonTemplateVisible]="visibleButton"
					[isDisable]="disableButton"
					[componentName]="viewComponentName"
				></ngx-gauzy-button-action>
			</div>
		</nb-card-header>
		<nb-card-body>
			<ng-container
				*ngIf="dataLayoutStyle === componentLayoutStyleEnum.TABLE"
			>
				<div class="table-scroll-container custom-table">
					<angular2-smart-table
						style="cursor: pointer"
						(userRowSelect)="selectApprovalPolicy($event)"
						[settings]="settingsSmartTable"
						[source]="smartTableSource"
					></angular2-smart-table>
				</div>
				<div class="pagination-container">
					<ng-container *ngIf="smartTableSource">
						<ngx-pagination
							[source]="smartTableSource"
						></ngx-pagination>
					</ng-container>
				</div>
			</ng-container>
			<ga-card-grid
				[totalItems]="pagination?.totalItems"
				*ngIf="dataLayoutStyle === componentLayoutStyleEnum.CARDS_GRID"
				[settings]="settingsSmartTable"
				[source]="approvalPolicies"
				(onSelectedItem)="selectApprovalPolicy($event)"
				(scroll)="onScroll()"
			></ga-card-grid>
		</nb-card-body>
	</nb-card>
	<ng-template
		#actionButtons
		let-buttonSize="buttonSize"
		let-selectedItem="selectedItem"
	>
		<div class="btn-group actions">
			<ng-template ngxPermissionsOnly="APPROVALS_POLICY_EDIT">
				<button
					(click)="edit(selectedItem)"
					nbButton
					status="basic"
					class="action primary"
					[disabled]="!selectedItem && disableButton"
					size="small"
				>
					<nb-icon class="mr-1" icon="edit-outline"></nb-icon>
					{{ 'BUTTONS.EDIT' | translate }}
				</button>
				<button
					(click)="delete(selectedItem)"
					nbButton
					status="basic"
					class="action"
					[disabled]="!selectedItem && disableButton"
					size="small"
					[nbTooltip]="'BUTTONS.DELETE' | translate"
				>
					<nb-icon status="danger" icon="trash-2-outline"></nb-icon>
				</button>
			</ng-template>
		</div>
	</ng-template>
	<ng-template #visibleButton>
		<ng-template ngxPermissionsOnly="APPROVALS_POLICY_EDIT">
			<button
				(click)="add()"
				nbButton
				size="small"
				status="success"
			>
				<nb-icon class="mr-1" icon="plus-outline"></nb-icon>
				{{ 'BUTTONS.ADD' | translate }}
			</button>
		</ng-template>
	</ng-template>
</ng-template>
