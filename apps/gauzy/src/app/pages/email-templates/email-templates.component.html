<form class="main-form" [formGroup]="form" *ngIf="form">
	<nb-card>
		<nb-card-header>
			<h4>
				<ngx-header-title [allowEmployee]="false">
					<span class="menu-setting">
						{{ 'MENU.SETTINGS' | translate }}/
					</span>
					{{ 'EMAIL_TEMPLATES_PAGE.HEADER' | translate }}
				</ngx-header-title>
			</h4>
		</nb-card-header>
		<nb-card-body>
			<div class="row">
				<div class="col-6 email-template-column">
					<nb-card>
						<nb-card-header class="editor-header">
							{{ 'EMAIL_TEMPLATES_PAGE.HTML_EDITOR' | translate }}
						</nb-card-header>
						<nb-card-body class="editor">
							<div class="form-group">
								<label class="label" for="subject">
									{{
										'EMAIL_TEMPLATES_PAGE.LABELS.SUBJECT'
											| translate
									}}
								</label>
								<ace-editor
									id="subject"
									#subjectEditor
									style="height: 10vh; font-size: medium"
									[durationBeforeCallback]="400"
									[mode]="'handlebars'"
									(textChange)="onSubjectChange($event)"
								>
								</ace-editor>
							</div>
							<div class="form-group">
								<label class="label" for="email">
									{{
										'EMAIL_TEMPLATES_PAGE.LABELS.EMAIL_BODY'
											| translate
									}}
								</label>
								<ace-editor
									#emailEditor
									id="email"
									[mode]="'handlebars'"
									class="ace-editor"
									[durationBeforeCallback]="400"
									(textChange)="onEmailChange($event)"
								></ace-editor>
							</div>
						</nb-card-body>
					</nb-card>
				</div>
				<!-- Live Preview column -->
				<div class="col-6 email-template-column">
					<nb-card class="email-template-right">
						<nb-card-header class="email-column-header">
							<div class="select">
								<div class="select-block">
									<label class="label" for="languageCode">
										{{
											'EMAIL_TEMPLATES_PAGE.LABELS.LANGUAGE'
												| translate
										}}
									</label>
									<ngx-language-selector
										class="d-block"
										size="small"
										template="ng-select"
										formControlName="languageCode"
										(selectedLanguageEvent)="subject$.next(true)"
									></ngx-language-selector>
								</div>
								<div class="select-block">
									<label class="label" for="templateName">
										{{
											'EMAIL_TEMPLATES_PAGE.LABELS.TEMPLATE_NAME'
												| translate
										}}
									</label>
									<nb-select
										id="templateName"
										class="d-block"
										size="small"
										formControlName="name"
										[placeholder]="'EMAIL_TEMPLATES_PAGE.LABELS.TEMPLATE_NAME' | translate"
										(selectedChange)="subject$.next(true)"
									>
										<nb-option
											*ngFor="let name of templates"
											[value]="name"
										>
											{{
												'EMAIL_TEMPLATES_PAGE.TEMPLATE_NAMES.' +
													name | translate
											}}
										</nb-option>
									</nb-select>
								</div>
							</div>
							<div class="template-save">
								<button
									[disabled]="form.invalid"
									(click)="submitForm()"
									nbButton
									status="success"
								>
									{{
										'EMAIL_TEMPLATES_PAGE.SAVE' | translate
									}}
								</button>
							</div>
						</nb-card-header>
						<nb-card-body>
							<div class="email-column-sub-header">
								<div class="form-group preview-subject">
									<label class="label" for="previewSubject"
										>{{
											'EMAIL_TEMPLATES_PAGE.LABELS.SUBJECT_PREVIEW'
												| translate
										}}:
									</label>
									<div
										id="previewSubject"
										[innerHtml]="previewSubject"
									></div>
								</div>
								<label class="label" for="previewEmail"
									>{{
										'EMAIL_TEMPLATES_PAGE.LABELS.EMAIL_PREVIEW'
											| translate
									}}
								</label>
							</div>
							<div class="form-group">
								<div class="custom-parent-email-content">
									<div
										id="previewEmail"
										[innerHtml]="previewEmail"
										class="email-content"
									></div>
								</div>
							</div>
						</nb-card-body>
					</nb-card>
				</div>
			</div>
		</nb-card-body>
	</nb-card>
</form>
