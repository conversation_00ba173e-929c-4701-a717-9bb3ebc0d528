@use '@angular/material' as mat;

 @mixin angular-material() {
   @include mat.elevation-classes();
   @include mat.app-background();

   @include nb-for-theme(material-dark) {
     $custom-dark-theme: mat.m2-define-dark-theme(
         mat.m2-define-palette(mat.$m2-pink-palette),
         mat.m2-define-palette(mat.$m2-blue-grey-palette));
     @include mat.all-component-themes($custom-dark-theme);
   }

   @include nb-for-theme(material-light) {
     $custom-light-theme: mat.m2-define-light-theme(
         mat.m2-define-palette(mat.$m2-indigo-palette),
         mat.m2-define-palette(mat.$m2-pink-palette));
     @include mat.all-component-themes($custom-light-theme);
   }
 }
