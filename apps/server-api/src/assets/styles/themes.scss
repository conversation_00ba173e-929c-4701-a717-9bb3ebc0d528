@use "sass:string";
// @nebular theming framework
@import '@nebular/theme/styles/theming';
// @nebular out of the box themes
@import '@nebular/theme/styles/themes';
// material themes
@import './material/material-dark';
@import './material/material-light';


$nb-themes: nb-register-theme(
		(
			font-family-secondary: font-family-primary,
			layout-padding-top: 24px,
			layout-window-mode-padding-top: 0,
			menu-item-icon-margin: 0 0.5rem 0 0,
			card-height-tiny: 13.5rem,
			card-height-small: 21.1875rem,
			card-height-medium: 28.875rem,
			card-height-large: 36.5625rem,
			card-height-giant: 44.25rem,
			card-margin-bottom: 0,
			card-header-with-select-padding-top: 0.5rem,
			card-header-with-select-padding-bottom: 0.5rem,
			select-min-width: 6rem,
			slide-out-background: #f7f9fc,
			slide-out-shadow-color: 0 4px 14px 0 #8f9bb3,
			slide-out-shadow-color-rtl: 0 4px 14px 0 #8f9bb3,
			gauzy-text-contact: #323232,
			accordion-padding: 0.625rem,
			actions-divider-style: none,
			sidebar-padding: 0.625rem,
			header-shadow: none,
			gauzy-card-1: card-background-color,
			gauzy-card-2: rgba(50, 50, 50, 0.03),
			gauzy-card-3: rgba(255, 255, 255, 0.5),
			gauzy-card-4: rgba(255, 255, 255, 0.75),
			gauzy-card-5: rgba(248, 246, 254, 1),
			gauzy-card-6: rgba(249, 249, 249, 1),
			gauzy-input-background: rgba(255, 255, 255, 1),
			gauzy-sidebar-background-1: #ebebeb,
			gauzy-sidebar-background-2: rgba(251, 251, 251, 1),
			accordion-item-background-color: #fbfbfb,
			gauzy-background-transparent: color-primary-transparent-default,
			gauzy-background-transparent-1: color-primary-transparent-default,
			gauzy-border-default-color: rgba(126, 126, 143, 0.1),
			gauzy-border-table: gauzy-border-default-color,
			gauzy-sidebar-background-3: rgba(126, 126, 143, 0.1),
			gauzy-sidebar-background-4: rgba(126, 126, 143, 0.05),
			gauzy-text-color-1: rgba(66, 66, 66, 1),
			gauzy-text-color-2: rgba(126, 126, 143, 1),
			gauzy-siderbar-background-5: rgb(237, 237, 238),
			gauzy-shadow: 0px 1px 1px 0px rgb(0 0 0 / 15%),
			gauzy-scrollbar: rgba(126, 126, 143, 0.1),
			gauzy-primary-background: rgb(246, 250, 254),
			layout-padding: 24px 24px 0 12px,
			header-height: 4.5rem,
			button-filled-medium-padding: 0.4375rem 0.875rem,
			button-outline-medium-padding: 0.4375rem 0.875rem,
			button-filled-small-padding: 0.375rem 0.875rem,
			button-outline-small-padding: 0.375rem 0.875rem,
		),
		default,
		default
);

$nb-themes: nb-register-theme(
		(
			font-family-secondary: font-family-primary,
			layout-window-mode-padding-top: 0,
			layout-padding-top: 24px,
			menu-item-icon-margin: 0 0.5rem 0 0,
			card-height-tiny: 13.5rem,
			card-height-small: 21.1875rem,
			card-height-medium: 28.875rem,
			card-height-large: 36.5625rem,
			card-height-giant: 44.25rem,
			card-margin-bottom: 0,
			card-header-with-select-padding-top: 0.5rem,
			card-header-with-select-padding-bottom: 0.5rem,
			select-min-width: 6rem,
			slide-out-background: #252547,
			slide-out-shadow-color: 2px 0 3px #29157a,
			slide-out-shadow-color-rtl: -2px 0 3px #29157a,
			accordion-padding: 0.625rem,
			button-filled-medium-padding: 0.4375rem 0.875rem,
			button-outline-medium-padding: 0.4375rem 0.875rem,
			sidebar-padding: 0.625rem,
			header-shadow: none,
			gauzy-card-2: rgba(39, 39, 78, 0.5),
			gauzy-card-1: card-background-color,
			gauzy-card-3: rgba(5, 5, 5, 0.5),
			gauzy-card-4: rgba(5, 5, 5, 0.25),
			gauzy-card-5: rgb(4, 1, 9),
			gauzy-input-background: rgba(230, 225, 254, 0.25),
			gauzy-background-transparent: color-primary-transparent-default,
			gauzy-siderbar-background-5: rgb(18, 18, 17),
			gauzy-sidebar-background-1: rgb(39, 39, 85),
			gauzy-sidebar-background-2: rgb(45 47 86),
			gauzy-border-default-color: rgba(255, 255, 255, 0.1),
			gauzy-border-table: rgb(26 26 69 / 62%),
			gauzy-sidebar-background-3: rgba(134, 126, 143, 0.1),
			gauzy-sidebar-background-4: rgba(125, 114, 140, 0.05),
			gauzy-shadow: 0px 1px 1px 0px rgb(0 0 0 / 35%),
			gauzy-scrollbar: rgba(137, 126, 143, 0.25),
			gauzy-primary-background: rgb(214, 200, 255),
			gauzy-card-6: rgb(57, 52, 62),
			layout-padding: 24px 24px 0 12px,
			header-height: 4.5rem,
			gauzy-text-color-1: rgba(255, 255, 255, 1),
			gauzy-text-color-2: rgba(255, 255, 255, 0.5),
		),
		cosmic,
		cosmic
);

$nb-themes: nb-register-theme(
		(
			font-family-secondary: font-family-primary,
			layout-padding-top: 24px,
			layout-window-mode-padding-top: 0,
			menu-item-icon-margin: 0 0.5rem 0 0,
			card-height-tiny: 13.5rem,
			card-height-small: 21.1875rem,
			card-height-medium: 28.875rem,
			card-height-large: 36.5625rem,
			card-height-giant: 44.25rem,
			card-margin-bottom: 0,
			card-header-with-select-padding-top: 0.5rem,
			card-header-with-select-padding-bottom: 0.5rem,
			select-min-width: 6rem,
			slide-out-background: linear-gradient(270deg, #edf1f7 0%, #e4e9f2 100%),
			slide-out-shadow-color: 0 4px 14px 0 #8f9bb3,
			slide-out-shadow-color-rtl: 0 4px 14px 0 #8f9bb3,
			accordion-padding: 0.625rem,
			button-filled-medium-padding: 0.4375rem 0.875rem,
			button-outline-medium-padding: 0.4375rem 0.875rem,
			sidebar-padding: 0.625rem,
			gauzy-card-1: rgba(255, 255, 255, 1),
			gauzy-card-2: rgba(50, 50, 50, 0.03),
			gauzy-input-background: rgba(255, 255, 255, 1),
			gauzy-background-transparent: color-primary-transparent-default,
			gauzy-border-default-color: rgba(126, 126, 143, 0.1),
			gauzy-border-table: gauzy-border-default-color,
			gauzy-sidebar-background-1: #ebebeb,
			gauzy-sidebar-background-2: rgba(251, 251, 251, 1),
			gauzy-sidebar-background-3: rgba(126, 126, 143, 0.1),
			gauzy-sidebar-background-4: rgba(126, 126, 143, 0.05),
			gauzy-card-5: rgba(248, 246, 254, 1),
			gauzy-text-color-1: rgba(66, 66, 66, 1),
			gauzy-text-color-2: rgba(126, 126, 143, 1),
			gauzy-card-3: rgba(255, 255, 255, 0.5),
			gauzy-card-4: rgba(255, 255, 255, 0.75),
			gauzy-siderbar-background-5: rgb(237, 237, 238),
			gauzy-shadow: 0px 1px 1px 0px rgb(0 0 0 / 15%),
			gauzy-scrollbar: rgba(126, 126, 143, 0.1),
			gauzy-primary-background: rgb(246, 247, 254),
			gauzy-card-6: rgba(249, 249, 249, 1),
			layout-padding: 24px 24px 0 12px,
			header-height: 4.5rem
		),
		corporate,
		corporate
);

$nb-themes: nb-register-theme(
		(
			font-family-secondary: font-family-primary,
			layout-padding-top: 24px,
			layout-window-mode-padding-top: 0,
			menu-item-icon-margin: 0 0.5rem 0 0,
			card-height-tiny: 13.5rem,
			card-height-small: 21.1875rem,
			card-height-medium: 28.875rem,
			card-height-large: 36.5625rem,
			card-height-giant: 44.25rem,
			card-margin-bottom: 0,
			card-header-with-select-padding-top: 0.5rem,
			card-header-with-select-padding-bottom: 0.5rem,
			select-min-width: 6rem,
			slide-out-background: linear-gradient(270deg, #222b45 0%, #151a30 100%),
			slide-out-shadow-color: 0 4px 14px 0 #8f9bb3,
			slide-out-shadow-color-rtl: 0 4px 14px 0 #8f9bb3,
			gauzy-text-contact: #eeeeee,
			accordion-padding: 0.625rem,
			actions-divider-style: none,
			button-filled-medium-padding: 0.4375rem 0.875rem,
			button-outline-medium-padding: 0.4375rem 0.875rem,
			sidebar-padding: 0.625rem,
			gauzy-card-2: rgba(16, 16, 20, 0.3),
			gauzy-card-1: card-background-color,
			gauzy-input-background: rgba(255, 255, 255, 0.15),
			gauzy-sidebar-background-1: #131e34,
			gauzy-sidebar-background-2: rgb(32 41 67),
			accordion-item-background-color: gauzy-sidebar-background-2,
			gauzy-background-transparent: color-primary-transparent-default,
			gauzy-border-default-color: rgba(255, 255, 255, 0.1),
			gauzy-border-table: rgba(16, 16, 20, 0.5),
			gauzy-sidebar-background-3: rgba(126, 126, 143, 0.1),
			gauzy-sidebar-background-4: rgba(126, 126, 143, 0.05),
			gauzy-text-color-1: rgba(255, 255, 255, 1),
			gauzy-text-color-2: rgba(255, 255, 255, 0.5),
			gauzy-card-3: rgba(5, 5, 5, 0.5),
			gauzy-card-4: rgba(5, 5, 5, 0.25),
			gauzy-card-5: rgba(7, 9, 1, 1),
			gauzy-siderbar-background-5: rgb(18, 18, 17),
			gauzy-shadow: 0px 1px 1px 0px rgb(0 0 0 / 35%),
			gauzy-scrollbar: rgba(126, 126, 143, 0.25),
			gauzy-primary-background: rgb(58, 62, 75),
			gauzy-card-6: rgba(52, 52, 62, 1),
			layout-padding: 24px 24px 0 12px,
			header-height: 4.5rem
		),
		dark,
		dark
);

$nb-themes: nb-register-theme(
		(
			layout-padding-top: 24px,
			menu-item-icon-margin: 0 0.5rem 0 0,
			card-height-tiny: 13.5rem,
			card-height-small: 21.1875rem,
			card-height-medium: 28.875rem,
			card-height-large: 36.5625rem,
			card-height-giant: 44.25rem,
			card-margin-bottom: 0,
			card-header-with-select-padding-top: 0.5rem,
			card-header-with-select-padding-bottom: 0.5rem,
			select-min-width: 6rem,
			option-list-border-radius: 0,
			slide-out-background: linear-gradient(270deg, #e0e0e0 0%, #ebebeb 100%),
			slide-out-shadow-color: 0 4px 14px 0 #ebebeb,
			slide-out-shadow-color-rtl: 0 4px 14px 0 #ebebeb,
			accordion-padding: 0.625rem,
			button-filled-medium-padding: 0.4375rem 0.875rem,
			button-outline-medium-padding: 0.4375rem 0.875rem,
			gauzy-primary-background: rgb(240, 235, 246)
		),
		material-light,
		material-light
);

$nb-themes: nb-register-theme(
		(
			layout-padding-top: 24px,
			menu-item-icon-margin: 0 0.5rem 0 0,
			card-height-tiny: 13.5rem,
			card-height-small: 21.1875rem,
			card-height-medium: 28.875rem,
			card-height-large: 36.5625rem,
			card-height-giant: 44.25rem,
			card-margin-bottom: 0,
			card-header-with-select-padding-top: 0.5rem,
			card-header-with-select-padding-bottom: 0.5rem,
			select-min-width: 6rem,
			option-list-border-radius: 0,
			slide-out-background: linear-gradient(270deg, #1f1f1f 0%, #292929 100%),
			slide-out-shadow-color: 0 4px 14px 0 #292929,
			slide-out-shadow-color-rtl: 0 4px 14px 0 #292929,
			accordion-padding: 0.625rem,
			button-filled-medium-padding: 0.4375rem 0.875rem,
			button-outline-medium-padding: 0.4375rem 0.875rem,
			gauzy-primary-background: rgba(40, 10, 10, 1),
			gauzy-sidebar-background-1: #2a2323,
			gauzy-sidebar-background-2: rgb(51 51 51),
			gauzy-border-table: rgb(37 37 59 / 16%)
		),
		material-dark,
		material-dark
);

$nb-themes: nb-register-theme(
		(
			// Status colors: primary, success, info, warning, danger - for colored elements (buttons, etc)
			color-primary-100: #f2f6ff,
			color-primary-200: #dbd3f7,
			color-primary-300: #ab92fd,
			color-primary-400: #7d56fd,
			color-primary-500: #6e49e8,
			color-primary-600: #5037a3,
			color-primary-700: #3d2a7a,
			color-primary-800: #291d52,
			color-primary-900: #1a1038,
			color-primary-transparent-100: rgba(110, 73, 232, 0.1),
			// Basic colors: background
			layout-background-color: #ffffff,
			background-basic-color-3: #f9f9f9,
			// Supporting variables - border radius, outline, shadow, divider
			border-radius: 0.625rem,
			outline-width: 0.375rem,
			outline-color: color-basic-transparent-200,
			scrollbar-color: background-basic-color-4,
			scrollbar-background-color: background-basic-color-2,
			button-rectangle-border-radius: 2rem,
			scrollbar-width: 0.3125rem,
			shadow: rgba(9, 30, 66, 0.25) 0px 4px 8px -2px,
			divider-color: border-basic-color-3,
			checkbox-border-radius: 1rem,
			divider-width: 0,
			tabset-tab-active-underline-color: none,
			tabset-tab-active-background-color: rgb(249, 249, 249),
			card-background-color: rgb(249, 249, 249),
			tabset-border-radius: 0.5rem,
			select-rectangle-border-radius: 2rem,
			tabset-tab-hover-underline-color: none,
			tabset-tab-focus-underline-color: none,
			tabset-tab-text-transform: capitalize,
			button-filled-text-transform: none,
			font-family-primary: string.unquote('Inter, sans-serif'),
			font-family-secondary: string.unquote('Inter, sans-serif'),
			button-outline-text-transform: none,
			gauzy-text-contact: #323232,
			button-filled-medium-padding: 0.4375rem 0.875rem,
			button-outline-medium-padding: 0.4375rem 0.875rem,
			button-filled-small-padding: 0.375rem 0.875rem,
			button-outline-small-padding: 0.375rem 0.875rem,
			toggle-height: 16px,
			toggle-switcher-size: 12px,
			toggle-border-width: 2px,
			toggle-basic-border-color: color-basic-transparent-default,
			toggle-width: 32px,
			toggle-switcher-icon-size: 0,
			header-shadow: none,
			card-border-style: none,
			tabset-divider-style: none,
			route-tabset-divider-style: none,
			tabset-divider-width: 0,
			route-tabset-divider-width: 0,
			route-tabset-tab-underline-width: 0,
			footer-shadow: none,
			footer-divider-style: none,
			sidebar-shadow: none,
			tabset-tab-underline-width: 0,
			tabset-tab-text-font-size: 1rem,
			route-tabset-tab-text-font-size: 1rem,
			tabset-tab-text-font-weight: 400,
			route-tabset-tab-text-font-weight: 400,
			color-success-default: rgba(37, 184, 105, 1),
			color-info-default: rgba(0, 136, 254, 1),
			color-danger-default: #ff0000,
			color-warning-default: rgba(255, 171, 45, 1),
			gauzy-background-transparent: rgba(110, 73, 232, 0.5),
			gauzy-background-transparent-1: rgba(110, 73, 232, 0.1),
			tag-filled-basic-background-color: rgba(126, 126, 143, 0.1),
			toggle-basic-background-color: rgba(126, 126, 143, 1),
			background-basic-color-1: gauzy-card-1,
			gauzy-primary-background: rgba(248, 246, 254, 1),
			input-basic-placeholder-text-color: rgba(126, 126, 143, 0.5),
			input-basic-background-color: gauzy-card-1,
			select-outline-basic-background-color: gauzy-card-1,
			input-border-width: 0,
			select-outline-border-width: 0,
			select-outline-basic-border-color: transparent,
			// Gauzy
			gauzy-card-1: rgba(255, 255, 255, 1),
		),
		gauzy-light,
		default
);

$nb-themes: nb-register-theme(
		(
			// Status colors: primary, success, info, warning, danger - for colored elements (buttons, etc)
			color-primary-100: #f2f6ff,
			color-primary-200: #dbd3f7,
			color-primary-300: #ab92fd,
			color-primary-400: #7d56fd,
			color-primary-500: #6e49e8,
			color-primary-600: #5037a3,
			color-primary-700: #3d2a7a,
			color-primary-800: #291d52,
			color-primary-900: #1a1038,
			color-primary-transparent-100: rgba(110, 73, 232, 0.1),
			// Supporting variables - border radius, outline, shadow, divider
			border-radius: 0.625rem,
			outline-width: 0.375rem,
			outline-color: color-basic-transparent-200,
			scrollbar-color: background-basic-color-4,
			scrollbar-background-color: background-basic-color-2,
			scrollbar-width: 0.3125rem,
			shadow: rgba(0, 0, 0, 0.733) 0px 4px 8px -2px,
			divider-color: border-basic-color-3,
			button-rectangle-border-radius: 2rem,
			checkbox-border-radius: 1rem,
			divider-width: 0,
			tabset-tab-active-underline-color: none,
			tabset-border-radius: 0.5rem,
			select-rectangle-border-radius: 2rem,
			tabset-tab-hover-underline-color: none,
			tabset-tab-focus-underline-color: none,
			tabset-tab-text-transform: capitalize,
			button-filled-text-transform: none,
			font-family-primary: string.unquote('Inter, sans-serif'),
			font-family-secondary: string.unquote('Inter, sans-serif'),
			button-outline-text-transform: none,
			button-filled-medium-padding: 0.4375rem 0.875rem,
			button-outline-medium-padding: 0.4375rem 0.875rem,
			toggle-height: 16px,
			toggle-switcher-size: 12px,
			toggle-border-width: 2px,
			toggle-basic-border-color: color-basic-transparent-default,
			toggle-width: 32px,
			toggle-switcher-icon-size: 0,
			header-shadow: none,
			card-border-style: none,
			tabset-divider-style: none,
			route-tabset-divider-style: none,
			tabset-divider-width: 0,
			route-tabset-divider-width: 0,
			route-tabset-tab-underline-width: 0,
			footer-shadow: none,
			footer-divider-style: none,
			sidebar-shadow: none,
			layout-background-color: gauzy-card-1,
			footer-background-color: gauzy-card-1,
			header-background-color: gauzy-card-1,
			tabset-tab-underline-width: 0,
			tabset-tab-text-font-size: 1rem,
			route-tabset-tab-text-font-size: 1rem,
			background-basic-color-1: gauzy-card-1,
			input-basic-background-color: gauzy-card-1,
			background-basic-color-2: rgba(32, 32, 35, 1),
			smart-table-bg-active: rgba(126, 126, 143, 0.05),
			background-basic-color-3: gauzy-card-3,
			gauzy-primary-background: rgb(86, 82, 99),
			input-basic-placeholder-text-color: rgba(126, 126, 143, 0.5),
			select-outline-basic-background-color: gauzy-card-1,
			input-border-width: 0,
			select-outline-border-width: 0,
			select-outline-basic-border-color: transparent,
			color-success-default: rgba(37, 184, 105, 1),
			color-info-default: rgba(0, 136, 254, 1),
			color-danger-default: #ff0000,
			color-warning-default: rgba(255, 171, 45, 1),
			gauzy-background-transparent: rgba(110, 73, 232, 0.5),
			gauzy-card-1: rgba(32, 32, 35, 1),
			gauzy-sidebar-background-1: #181818,
			gauzy-sidebar-background-2: rgba(38, 38, 46, 1),
		),
		gauzy-dark,
		dark
);
