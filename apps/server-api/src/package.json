{"name": "gauzy-api-server", "productName": "Ever Gauzy API Server", "version": "0.1.0", "description": "Ever Gauzy API Server", "license": "AGPL-3.0", "homepage": "https://gauzy.co", "repository": {"type": "git", "url": "https://github.com/ever-co/ever-gauzy.git"}, "bugs": {"url": "https://github.com/ever-co/ever-gauzy/issues"}, "private": true, "author": {"name": "Ever Co. LTD", "email": "<EMAIL>", "url": "https://ever.co"}, "main": "index.js", "bin": "api/main.js", "workspaces": {"packages": ["../../../dist/packages/auth", "../../../dist/packages/common", "../../../dist/packages/config", "../../../dist/packages/constants", "../../../dist/packages/contracts", "../../../dist/packages/core", "../../../dist/packages/desktop-core", "../../../dist/packages/desktop-activity", "../../../dist/packages/desktop-lib", "../../../dist/packages/desktop-window", "../../../dist/packages/plugin", "../../../dist/packages/plugins/integration-ai", "../../../dist/packages/plugins/job-proposal", "../../../dist/packages/plugins/videos", "../../../dist/packages/plugins/integration-zapier", "../../../dist/packages/plugins/posthog", "../../../dist/packages/plugins/registry", "../../../dist/packages/plugins/camshot", "../../../dist/packages/plugins/soundshot", "../../../dist/packages/plugins/registry", "../../../dist/packages/ui-config", "../../../dist/packages/utils"]}, "build": {"npmArgs": ["--production"], "appId": "com.ever.gauzyapiserver", "artifactName": "${name}-${version}.${ext}", "productName": "Ever Gauzy API Server", "copyright": "Copyright © 2019-Present. Ever Co. LTD", "afterSign": "tools/notarize.js", "dmg": {"sign": false}, "asar": true, "npmRebuild": true, "asarUnpack": ["node_modules/@sentry/electron", "node_modules/better-sqlite3", "node_modules/@sentry/profiling-node/lib"], "directories": {"buildResources": "icons", "output": "../desktop-packages"}, "publish": [{"provider": "github", "repo": "ever-gauzy-api-server", "releaseType": "release"}, {"provider": "spaces", "name": "ever", "region": "sfo3", "path": "/ever-gauzy-api-server", "acl": "public-read"}], "mac": {"category": "public.app-category.developer-tools", "icon": "icon.icns", "target": ["zip", "dmg"], "asarUnpack": "**/*.node", "artifactName": "${name}-${version}.${ext}", "hardenedRuntime": true, "gatekeeperAssess": false, "entitlements": "tools/build/entitlements.mas.plist", "entitlementsInherit": "tools/build/entitlements.mas.plist", "extendInfo": {"NSAppleEventsUsageDescription": "Please allow access to script browser applications to detect the current URL when triggering instant lookup.", "NSMicrophoneUsageDescription": "This app requires microphone access when recording sounds or detecting loudness.", "NSCameraUsageDescription": "This app requires camera access when using the video sensing blocks."}}, "win": {"publisherName": "Ever", "target": [{"target": "nsis", "arch": ["x64"]}], "icon": "icon.ico", "verifyUpdateCodeSignature": false, "requestedExecutionLevel": "requireAdministrator"}, "linux": {"icon": "linux", "target": ["AppImage", "deb", "tar.gz"], "executableName": "gauzy-api-server", "artifactName": "${name}-${version}.${ext}", "synopsis": "Server", "category": "Development"}, "nsis": {"oneClick": false, "perMachine": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "allowToChangeInstallationDirectory": true, "allowElevation": true, "installerIcon": "icon.ico", "artifactName": "${name}-${version}.${ext}", "deleteAppDataOnUninstall": true, "menuCategory": true}, "extraResources": ["./data/**/*"]}, "dependencies": {"@datorama/akita-ngdevtools": "^7.0.0", "@datorama/akita": "^8.0.1", "@electron/remote": "^2.0.8", "@gauzy/auth": "file:../../../dist/packages/auth", "@gauzy/config": "file:../../../dist/packages/config", "@gauzy/constants": "file:../../../dist/packages/constants", "@gauzy/contracts": "file:../../../dist/packages/contracts", "@gauzy/core": "file:../../../dist/packages/core", "@gauzy/desktop-core": "file:../../../dist/packages/desktop-core", "@gauzy/desktop-activity": "file:../../../dist/packages/desktop-activity", "@gauzy/desktop-lib": "file:../../../dist/packages/desktop-lib", "@gauzy/desktop-window": "file:../../../dist/packages/desktop-window", "@gauzy/plugin": "file:../../../dist/packages/plugin", "@gauzy/plugin-changelog": "file:../../../dist/packages/plugins/changelog", "@gauzy/plugin-integration-ai": "file:../../../dist/packages/plugins/integration-ai", "@gauzy/plugin-integration-github": "file:../../../dist/packages/plugins/integration-github", "@gauzy/plugin-integration-hubstaff": "file:../../../dist/packages/plugins/integration-hubstaff", "@gauzy/plugin-integration-jira": "file:../../../dist/packages/plugins/integration-jira", "@gauzy/plugin-integration-make-com": "file:../../../dist/packages/plugins/integration-make-com", "@gauzy/plugin-integration-upwork": "file:../../../dist/packages/plugins/integration-upwork", "@gauzy/plugin-integration-wakatime": "file:../../../dist/packages/plugins/integration-wakatime", "@gauzy/plugin-jitsu-analytics": "file:../../../dist/packages/plugins/jitsu-analytics", "@gauzy/plugin-job-proposal": "file:../../../dist/packages/plugins/job-proposal", "@gauzy/plugin-job-search": "file:../../../dist/packages/plugins/job-search", "@gauzy/plugin-knowledge-base": "file:../../../dist/packages/plugins/knowledge-base", "@gauzy/plugin-product-reviews": "file:../../../dist/packages/plugins/product-reviews", "@gauzy/plugin-sentry": "file:../../../dist/packages/plugins/sentry-tracing", "@gauzy/plugin-videos": "file:../../../dist/packages/plugins/videos", "@gauzy/plugin-registry": "file:../../../dist/packages/plugins/registry", "@gauzy/plugin-integration-zapier": "file:../../../dist/packages/plugins/integration-zapier", "@gauzy/plugin-posthog": "file:../../../dist/packages/plugins/posthog", "@gauzy/plugin-camshot": "file:../../../dist/packages/plugins/camshot", "@gauzy/plugin-soundshot": "file:../../../dist/packages/plugins/soundshot", "@gauzy/ui-config": "file:../../../dist/packages/ui-config", "@gauzy/utils": "file:../../../dist/packages/utils", "@sentry/electron": "^4.18.0", "@sentry/node": "^7.101.1", "@sentry/profiling-node": "^7.101.1", "@sentry/replay": "^7.101.1", "@sentry/tracing": "^7.101.1", "@sentry/types": "^7.101.1", "auto-launch": "5.0.5", "consolidate": "^0.16.0", "electron-log": "^4.4.8", "electron-store": "^8.1.0", "electron-updater": "^6.1.7", "electron-util": "^0.18.1", "form-data": "^4.0.1", "htmlparser2": "^8.0.2", "knex": "^3.1.0", "libsql": "^0.3.16", "locutus": "^2.0.30", "moment": "^2.30.1", "node-fetch": "^2.6.7", "node-notifier": "^8.0.0", "node-static": "^0.7.11", "pdfmake": "^0.2.0", "pg-query-stream": "^4.7.1", "pg": "^8.13.1", "sound-play": "1.1.0", "squirrelly": "^8.0.8", "tslib": "^2.6.2", "twing": "^5.0.2", "underscore": "^1.13.3", "undici": "^6.10.2", "custom-electron-titlebar": "^4.2.8"}, "optionalDependencies": {"node-linux": "^0.1.12", "node-mac": "^1.0.1", "node-windows": "^1.0.0-beta.8"}, "pkg": {"assets": ["api/assets/**/*", "node_modules/bcrypt/lib/binding/napi-v3/bcrypt_lib.node", "node_modules/linebreak/src/classes.trie"], "targets": ["node20-linux-x64", "node20-mac-x64", "node20-win-x64"]}}