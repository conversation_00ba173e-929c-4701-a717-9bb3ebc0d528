{"name": "gauzy-api-server", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "prefix": "gauzy", "sourceRoot": "apps/server-api/src", "tags": [], "implicitDependencies": [], "generators": {"@nx/angular:component": {"style": "scss"}}, "targets": {"build": {"executor": "@angular-builders/custom-webpack:browser", "outputs": ["{options.outputPath}"], "options": {"customWebpackConfig": {"path": "apps/server-api/config/custom-webpack.config.js"}, "outputPath": "dist/apps/gauzy-api-server", "index": "apps/server-api/src/index.html", "main": "apps/server-api/src/main.ts", "polyfills": "apps/server-api/src/polyfills.ts", "tsConfig": "apps/server-api/tsconfig.app.json", "aot": true, "stylePreprocessorOptions": {"includePaths": ["apps/server-api/src/assets/styles", "packages/ui-core/static/styles"]}, "assets": ["apps/server-api/src/favicon.ico", "apps/server-api/src/assets", {"glob": "**/*", "input": "apps/server-api/src/", "ignore": ["**/*.ts"], "output": ""}], "styles": ["node_modules/@nebular/theme/styles/prebuilt/default.css", "node_modules/bootstrap/dist/css/bootstrap.css", "node_modules/typeface-exo/index.css", "node_modules/roboto-fontface/css/roboto/roboto-fontface.css", "node_modules/ionicons/dist/scss/ionicons.scss", "node_modules/@fortawesome/fontawesome-free/css/all.css", "node_modules/socicon/css/socicon.css", "node_modules/nebular-icons/scss/nebular-icons.scss", "node_modules/@ali-hm/angular-tree-component/css/angular-tree-component.css", "node_modules/leaflet/dist/leaflet.css", "apps/server-api/src/styles.css", "apps/server-api/src/assets/styles/styles.scss", "node_modules/@ng-select/ng-select/themes/default.theme.css"], "scripts": [], "allowedCommonJsDependencies": ["dayjs", "eva-icons", "localforage", "moment", "moment-duration-format", "moment-range", "moment-timezone", "rfdc", "rxjs", "rxjs/operators", "slugify"]}, "configurations": {"production": {"fileReplacements": [{"replace": "apps/server-api/src/environments/environment.ts", "with": "apps/server-api/src/environments/environment.prod.ts"}, {"replace": "packages/ui-config/src/lib/environments/environment.ts", "with": "packages/ui-config/src/lib/environments/environment.prod.ts"}], "optimization": true, "sourceMap": true, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "20mb", "maximumError": "40mb"}, {"type": "anyComponentStyle", "maximumWarning": "60kb", "maximumError": "120kb"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"executor": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "server-api:build:production"}, "development": {"buildTarget": "server-api:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"executor": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "server-api:build"}}, "lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/server-api/jest.config.ts"}}}}