{"name": "@gauzy/api", "version": "0.1.0", "description": "Gauzy API", "license": "AGPL-3.0", "homepage": "https://gauzy.co", "repository": {"type": "git", "url": "https://github.com/ever-co/ever-gauzy.git"}, "bugs": {"url": "https://github.com/ever-co/ever-gauzy/issues"}, "private": true, "author": {"name": "Ever Co. LTD", "email": "<EMAIL>", "url": "https://ever.co"}, "scripts": {"typeorm": "yarn ts-node -r tsconfig-paths/register --project apps/api/tsconfig.app.json node_modules/.bin/typeorm", "typeorm:sync": "yarn typeorm schema:sync", "typeorm:seeds": "yarn typeorm migration:run", "typeorm:flush": "yarn typeorm migration:revert", "typeorm:create": "yarn typeorm migration:create", "typeorm:preserve": "yarn typeorm:sync -- -f=ormconfig && yarn typeorm:seeds -- -f=ormconfig", "migration:run": "yarn ts-node -r tsconfig-paths/register src/migration.ts migration:run", "migration:revert": "yarn ts-node -r tsconfig-paths/register src/migration.ts migration:revert", "migration:generate": "yarn ts-node -r tsconfig-paths/register src/migration.ts migration:generate", "start": "yarn ts-node -r tsconfig-paths/register --project apps/api/tsconfig.app.json src/main.ts", "start:debug": "nodemon --config nodemon-debug.json", "build": "yarn nx build api --configuration=development", "build:prod": "yarn nx build api --configuration=production", "seed": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=14000 yarn ts-node -r tsconfig-paths/register --project apps/api/tsconfig.app.json src/seed.ts", "seed:build": "yarn ng run api:seed", "seed:all": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=14000 yarn ts-node -r tsconfig-paths/register --project apps/api/tsconfig.app.json src/seed-all.ts", "seed:module": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=14000 yarn ts-node -r tsconfig-paths/register --project apps/api/tsconfig.app.json src/seed-module.ts --name", "seed:all:build": "yarn ng run api:seed-all", "seed:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=14000 yarn ts-node -r tsconfig-paths/register --project apps/api/tsconfig.app.json src/seed.ts", "seed:prod:build": "yarn ng run api:seed -c=production"}, "dependencies": {"@gauzy/core": "^0.1.0", "@gauzy/plugin-changelog": "^0.1.0", "@gauzy/plugin-integration-activepieces": "^0.1.0", "@gauzy/plugin-integration-ai": "^0.1.0", "@gauzy/plugin-integration-github": "^0.1.0", "@gauzy/plugin-integration-hubstaff": "^0.1.0", "@gauzy/plugin-integration-make-com": "^0.1.0", "@gauzy/plugin-integration-zapier": "^0.1.0", "@gauzy/plugin-integration-jira": "^0.1.0", "@gauzy/plugin-integration-upwork": "^0.1.0", "@gauzy/plugin-jitsu-analytics": "^0.1.0", "@gauzy/plugin-job-proposal": "^0.1.0", "@gauzy/plugin-job-search": "^0.1.0", "@gauzy/plugin-knowledge-base": "^0.1.0", "@gauzy/plugin-product-reviews": "^0.1.0", "@gauzy/plugin-sentry": "^0.1.0", "@gauzy/plugin-posthog": "^0.1.0", "@gauzy/plugin-videos": "^0.1.0", "@gauzy/plugin-camshot": "^0.1.0", "@gauzy/plugin-soundshot": "^0.1.0", "@gauzy/plugin-registry": "^0.1.0", "dotenv": "^16.0.3", "yargs": "^17.5.0"}, "devDependencies": {"@nestjs/cli": "^11.0.7", "@nestjs/schematics": "^11.0.5", "@nestjs/testing": "^11.0.0", "nodemon": "^3.1.0", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}