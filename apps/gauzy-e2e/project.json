{"name": "gauzy-e2e", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "sourceRoot": "apps/gauzy-e2e/src", "targets": {"e2e": {"executor": "@nx/cypress:cypress", "options": {"cypressConfig": "apps/gauzy-e2e/cypress.json", "tsConfig": "apps/gauzy-e2e/tsconfig.e2e.json", "devServerTarget": "gauzy:serve"}, "configurations": {"production": {"devServerTarget": "gauzy:serve:production"}}}, "lint": {"executor": "@nx/eslint:lint"}}}