{"name": "@gauzy/e2e", "version": "0.1.0", "description": "Gauzy e2e tests", "license": "GPL-3.0", "homepage": "https://gauzy.co", "repository": {"type": "git", "url": "https://github.com/ever-co/ever-gauzy.git"}, "bugs": {"url": "https://github.com/ever-co/ever-gauzy/issues"}, "private": true, "author": {"name": "Ever Co. LTD", "email": "<EMAIL>", "url": "https://ever.co"}, "scripts": {"config": "yarn ts-node ./.scripts/configure.ts", "config:dev": "yarn run config -- --environment=dev", "build:package:all": "yarn run build:package:contracts && yarn run build:package:common && yarn run build:package:config && yarn run build:package:plugin && yarn run build:package:plugins && yarn run build:package:auth && yarn run build:package:core && yarn run build:package:plugin:knowledge-base && yarn run build:package:plugin:changelog && yarn build:package:desktop-lib && yarn build:package:plugin:integration-wakatime && yarn ng build desktop-ui-lib", "start:api": "yarn ng serve api", "build:package:contracts": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=14000 yarn --cwd ./packages/contracts build", "build:package:config": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=14000 yarn --cwd ./packages/config build", "build:package:common": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=14000 yarn --cwd ./packages/common build", "build:package:utils": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=14000 yarn --cwd ./packages/utils build", "build:package:plugin": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=14000 yarn --cwd ./packages/plugin build", "build:package:plugin:integration-wakatime": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=14000 yarn --cwd ./packages/plugins/integration-wakatime build", "build:package:desktop-lib": "yarn run build:package:desktop-window && cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=14000 yarn nx build desktop-lib --configuration=development", "build:package:desktop-lib:prod": "yarn run build:package:desktop-window:prod && cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=14000 yarn nx build desktop-lib --configuration=production", "build:package:desktop-window": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=14000 yarn nx build desktop-window --configuration=development", "build:package:desktop-window:prod": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=14000 yarn nx build desktop-window --configuration=production", "build:package:plugin:changelog": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=14000 yarn --cwd ./packages/plugins/changelog build", "build:package:plugin:knowledge-base": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=14000 yarn --cwd ./packages/plugins/knowledge-base build", "build:package:core": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=14000 yarn --cwd ./packages/core build", "build:package:auth": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=14000 yarn --cwd ./packages/auth build", "build:package:plugins": "yarn run build:package:plugin:integration-ai && yarn run build:package:plugin:integration-hubstaff && yarn run build:package:plugin:integration-upwork && yarn run build:package:plugin:integration-github && yarn run build:package:plugin:integration-jira && yarn run build:package:plugin:product-reviews", "build": "yarn build:package:all && concurrently --raw \"yarn build:api\" \"yarn build:gauzy\"", "build:api": "yarn ng build api", "build:gauzy": "yarn run postinstall.web && yarn run config:dev && yarn ng build gauzy", "build:package:plugin:integration-ai": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=14000 yarn --cwd ./packages/plugins/integration-ai build", "build:package:plugin:integration-hubstaff": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=14000 yarn --cwd ./packages/plugins/integration-hubstaff build", "build:package:plugin:integration-upwork": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=14000 yarn --cwd ./packages/plugins/integration-upwork build", "build:package:plugin:integration-github": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=14000 yarn --cwd ./packages/plugins/integration-github build", "build:package:plugin:integration-jira": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=14000 yarn --cwd ./packages/plugins/integration-jira build", "build:package:plugin:product-reviews": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=14000 yarn --cwd ./packages/plugins/product-reviews build", "postinstall.web": "yarn node ./decorate-angular-cli.js && yarn node tools/web/postinstall"}, "dependencies": {"@faker-js/faker": "^9.8.0", "dayjs": "^1.11.4", "resolve": "^1.20.0"}, "devDependencies": {"@4tw/cypress-drag-drop": "^2.1.0", "@cypress/browserify-preprocessor": "^3.0.2", "@types/jest": "29.5.14", "cypress": "^9.4.1", "cypress-cucumber-preprocessor": "^4.3.1", "cypress-file-upload": "^5.0.8", "jasmine-core": "^3.6.0", "jasmine-spec-reporter": "^6.0.0", "jest": "^29.7.0", "jest-preset-angular": "14.5.5", "karma": "^6.4.4", "karma-chrome-launcher": "^3.2.0", "karma-cli": "^2.0.0", "karma-coverage-istanbul-reporter": "^3.0.3", "karma-jasmine": "^5.1.0", "karma-jasmine-html-reporter": "^1.7.0"}, "cypress-cucumber-preprocessor": {"nonGlobalStepDefinitions": true, "step_definitions": "src/support/step_definitions/"}}