{"projectId": "2n3iur", "fileServerFolder": ".", "watchForFileChanges": true, "fixturesFolder": "./src/fixtures", "integrationFolder": "./src/support", "pluginsFile": "./src/plugins/index.ts", "supportFile": "./src/support/index.ts", "video": false, "videoUploadOnPasses": false, "videosFolder": "../../dist/cypress/apps/gauzy-e2e/videos", "screenshotsFolder": "../../dist/cypress/apps/gauzy-e2e/screenshots", "chromeWebSecurity": false, "viewportWidth": 1920, "viewportHeight": 1080, "baseUrl": "http://localhost:4200", "ignoreTestFiles": "*.ts", "testFiles": "**/*.feature", "defaultCommandTimeout": 24000, "pageLoadTimeout": 60000, "taskTimeout": 60000, "requestTimeout": 24000, "execTimeout": 60000, "responseTimeout": 30000, "retries": {"runMode": 1, "openMode": 0}, "env": {"coverage": false, "codeCoverage": {"url": "http://localhost:3001/__coverage__"}}, "experimentalStudio": true}