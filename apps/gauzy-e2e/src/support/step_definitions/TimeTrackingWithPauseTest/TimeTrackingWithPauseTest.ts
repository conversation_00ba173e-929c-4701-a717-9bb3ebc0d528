import { faker } from '@faker-js/faker';
import { Given, Then, When, And } from 'cypress-cucumber-preprocessor/steps';
import { CustomCommands } from '../../commands';
import * as loginPage from '../../Base/pages/Login.po';
import { LoginPageData } from '../../Base/pagedata/LoginPageData';
import * as dashboardPage from '../../Base/pages/Dashboard.po';
import * as logoutPage from '../../Base/pages/Logout.po';
import * as manageEmployeesPage from '../../Base/pages/ManageEmployees.po';
import * as timeTrackingWithPausePage from '../../Base/pages/TimeTrackingWithPause.po';
import { waitUntil } from '../../Base/utils/util';
import { TimeTrackingWithPausePageData } from '../../Base/pagedata/TimeTracingWithPausePageData';


const pageLoadTimeout = Cypress.config('pageLoadTimeout');


let email = faker.internet.exampleEmail();
let fullName = faker.person.firstName() + ' ' + faker.person.lastName();
let city = faker.location.city();
let postcode = faker.location.zipCode();
let street = faker.location.streetAddress();
let website = faker.internet.url();


let firstName = faker.person.firstName();
let lastName = faker.person.lastName();
let username = faker.internet.userName();
let password = faker.internet.password();
let imgUrl = faker.image.avatar();
let employeeEmail = faker.internet.exampleEmail();

let employeeFullName = `${firstName} ${lastName}`;

// Login with email

Given('Login with default credentials', () => {
	CustomCommands.login(loginPage, LoginPageData, dashboardPage)
})

//Add employee
Then('User can add new employee', () => {
	CustomCommands.logout(dashboardPage, logoutPage, loginPage);
	CustomCommands.clearCookies();
	CustomCommands.login(loginPage, LoginPageData, dashboardPage);
	CustomCommands.addEmployee(
		manageEmployeesPage,
		firstName,
		lastName,
		username,
		employeeEmail,
		password,
		imgUrl
	);
});

//Logout
And('User can logout', () => {
	CustomCommands.logout(dashboardPage, logoutPage, loginPage);
	CustomCommands.clearCookies();
});

//Login as employee

And('Employee can see login page', () => {
	loginPage.verifyLoginText();
});

And('Employee can see email input', () => {
	loginPage.clearEmailField();
});

And('Employee can enter value for employee email', () => {
	loginPage.enterEmail(employeeEmail);
});

And('Employee can see password input', () => {
	loginPage.clearPasswordField();
});

And('Employee can enter value for employee password', () => {
	loginPage.enterPassword(password);
});

When('Employee click on login button', () => {
	loginPage.clickLoginButton();
});

Then('Employee will see Create button', () => {
	dashboardPage.verifyCreateButton();
});

//Generate work time

And('Employee can see timer', () => {
	timeTrackingWithPausePage.waitMainDashboard(TimeTrackingWithPausePageData.urlConfirmDashboardLoad);
	timeTrackingWithPausePage.timerVisible();
});

When('Employee click on timer', () => {
	timeTrackingWithPausePage.clickTimer();
});

Then('Employee can see timer button', () => {
	timeTrackingWithPausePage.timerBtnVisible();
});

When('Employee click on timer button', () => {
	timeTrackingWithPausePage.clickTimerBtn(1);
});

And('Employee can see start timer button', () => {
	timeTrackingWithPausePage.startTimerBtnVisible();
});

When('Employee click on start timer button', () => {
	timeTrackingWithPausePage.clickStartTimerBtn();
});

Then('Employee can let timer work for 5 seconds', () => {
	waitUntil(5000);
})

And('Employee can see stop timer button', () => {
	timeTrackingWithPausePage.stopTimerBtnVisible();
});

When('Employee click on stop timer button', () => {
	timeTrackingWithPausePage.clickStopTimerBtn();
});

And('Employee wait button to change', () => {
	waitUntil(3000);
});

Then('Employee can see again start timer button', () => {
	timeTrackingWithPausePage.startTimerBtnVisible();
});

When('Employee click on start timer button', () => {
	timeTrackingWithPausePage.clickStartTimerBtn();
});

Then('Employee can let timer work for 5 seconds', () => {
	waitUntil(5000);
})

And('Employee can see stop timer button again', () => {
	timeTrackingWithPausePage.stopTimerBtnVisible();
});

When('Employee click on stop timer button', () => {
	timeTrackingWithPausePage.clickStopTimerBtn();
});

Then('Employee can see again start timer button', () => {
	timeTrackingWithPausePage.startTimerBtnVisible();
});


//Check the recorded time

Then('Employee can see view timesheet button', () => {
	timeTrackingWithPausePage.viewTimesheetBtnVisible();
});

When('Employee click on view timesheet button', () => {
	timeTrackingWithPausePage.clickViewTimesheetBtn();
});

Then('Employee verify first time record', () => {
	timeTrackingWithPausePage.verifyWorkTimeRecorded(TimeTrackingWithPausePageData.firstTimeTable, TimeTrackingWithPausePageData.tracked)
});

And('Employee verify second time record', () => {
	timeTrackingWithPausePage.verifyWorkTimeRecorded(TimeTrackingWithPausePageData.secondTimeTable, TimeTrackingWithPausePageData.tracked)
});

Then('Employee can see first delete button', () => {
	timeTrackingWithPausePage.viewRecordedTimeDeleteBtn(TimeTrackingWithPausePageData.firstDeleteBtn)
});

Then('Employee can see second delete button', () => {
	timeTrackingWithPausePage.viewRecordedTimeDeleteBtn(TimeTrackingWithPausePageData.secondDeleteBtn)
});

When('Employee click on delete button', () => {
	timeTrackingWithPausePage.clickRecordedTimeDeleteBtn(TimeTrackingWithPausePageData.firstDeleteBtn)
});

Then('Employee can see confirm dialog', () => {
	timeTrackingWithPausePage.notificationDialogVisible()
});

When('Employee can click confirm dialog button', () => {
	timeTrackingWithPausePage.clickNotificationButton()
});

Then('Employee refresh', () => {
	CustomCommands.logout(dashboardPage, logoutPage, loginPage);
	CustomCommands.clearCookies();
	CustomCommands.login(loginPage, { email: employeeEmail, password: password }, dashboardPage);

	cy.visit('/#/pages/employees/timesheets/daily', { timeout: pageLoadTimeout });

});


Then('Employee can verify time', () => {
	timeTrackingWithPausePage.verifyTimerTime(TimeTrackingWithPausePageData.timerTime)

});
