Feature: Time tracking with pause test
  Sc<PERSON>rio: Login with email
    Given Login with default credentials
  Sc<PERSON><PERSON>: Add employee
    Then User can add new employee
  <PERSON><PERSON><PERSON>: Logout
    And Use<PERSON> can logout
  Scenario: Login as employee
    And Em<PERSON>loyee can see login page
    And Em<PERSON>loyee can see email input
    And Em<PERSON>loyee can enter value for employee email
    And Em<PERSON>loyee can see password input
    And Em<PERSON>loyee can enter value for employee password
    When <PERSON><PERSON>loyee click on login button
    Then Em<PERSON>loyee will see Create button
  Scenario: Add time
    And <PERSON><PERSON>loy<PERSON> can see timer
    When Employee click on timer
    Then Em<PERSON>loyee can see timer button
    When Employee click on timer button
    And Em<PERSON>loyee can see start timer button
    When Employee click on start timer button
    Then Em<PERSON>loyee can let timer work for 5 seconds
    And Em<PERSON>loyee can see stop timer button
    When Employee click on stop timer button
    And Em<PERSON>loyee wait button to change
    Then Em<PERSON>loyee can see again start timer button
    When Employee click on start timer button
    Then Em<PERSON>loyee can let timer work for 5 seconds
    And Em<PERSON>loyee can see stop timer button again
    When Employee click on stop timer button
    Then Em<PERSON>loyee can see again start timer button
    Then Em<PERSON>loyee can see view timesheet button
    When Em<PERSON>loyee click on view timesheet button
    Then Employee verify first time record
    And Em<PERSON>loyee verify second time record
    Then Em<PERSON>loyee can see first delete button
    And <PERSON><PERSON>loy<PERSON> can see second delete button
    When Em<PERSON>loyee click on delete button
    Then Employee can see confirm dialog
    When Employee can click confirm dialog button
    Then Employee refresh
    Then Employee can verify time

    
  