import * as loginPage from '../../Base/pages/Login.po';
import { LoginPageData } from '../../Base/pagedata/LoginPageData';
import * as logoutPage from '../../Base/pages/Logout.po';
import * as addEmployeePositionPage from '../../Base/pages/AddEmployeePosition.po';
import { AddEmployeePositionPageData } from '../../Base/pagedata/AddEmployeePositionPageData';
import * as dashboardPage from '../../Base/pages/Dashboard.po';
import { CustomCommands } from '../../commands';
import * as organizationTagsUserPage from '../../Base/pages/OrganizationTags.po';
import { OrganizationTagsPageData } from '../../Base/pagedata/OrganizationTagsPageData';

import { Given, Then, When, And } from 'cypress-cucumber-preprocessor/steps';

const pageLoadTimeout = Cypress.config('pageLoadTimeout');

// Login with email
Given('Login with default credentials', () => {
	CustomCommands.login(loginPage, LoginPageData, dashboardPage);
});

// Add new tag
Then('User can add new tag', () => {
	//dashboardPage.verifyAccountingDashboardIfVisible();
	CustomCommands.addTag(organizationTagsUserPage, OrganizationTagsPageData);
});

// Add new employee position
Then('User can go to Employee positions page', () => {
	CustomCommands.logout(dashboardPage, logoutPage, loginPage);
	CustomCommands.clearCookies();
	CustomCommands.login(loginPage, LoginPageData, dashboardPage);
	cy.visit('/#/pages/employees/positions', { timeout: pageLoadTimeout });
});

And('User will see grid button', () => {
	addEmployeePositionPage.gridBtnExists();
});

And('User can click on second grid button to change view', () => {
	addEmployeePositionPage.gridBtnClick(1);
});

And('User can see Add new position button', () => {
	addEmployeePositionPage.addNewPositionButtonVisible();
});

When('User click on Add new position button', () => {
	addEmployeePositionPage.clickAddNewPositionButton();
});

Then('User can see new position input', () => {
	addEmployeePositionPage.newPositionInputVisible();
});

And('User can add data for new position', () => {
	addEmployeePositionPage.enterNewPositionData(
		AddEmployeePositionPageData.fullStackDeveloper
	);
});

And('User can see tag multi-select', () => {
	addEmployeePositionPage.tagsMultiSelectVisible();
});

When('User click on tag multi-select', () => {
	addEmployeePositionPage.clickTagsMultiSelect();
});

Then('User can pick tag from dropdown menu', () => {
	addEmployeePositionPage.selectTagsFromDropdown(0);
	addEmployeePositionPage.clickKeyboardButtonByKeyCode(9);
});

And('User can see save position button', () => {
	addEmployeePositionPage.savePositionButtonVisible();
});

When('User click on save position button', () => {
	addEmployeePositionPage.clickSavePositionButton();
});

Then('Notification message will appear', () => {
	addEmployeePositionPage.waitMessageToHide();
});

When('User selects position to edit', () => {
	addEmployeePositionPage.selectPositionToEdit();
})

And('User can see edit position button', () => {
	addEmployeePositionPage.editEmployeePositionButtonVisible();
});

When('User click on edit position button', () => {
	addEmployeePositionPage.clickEditEmployeePositionButton();
});

Then('User can verify position was created', () => {
	addEmployeePositionPage.verifyTitleExists(
		AddEmployeePositionPageData.fullStackDeveloper
	);
});

And('User can see cancel edit button', () => {
	addEmployeePositionPage.cancelButtonVisible();
});

And('User can click on cancel edit button', () => {
	addEmployeePositionPage.clickCancelButton();
});

// Еdit employee position
When('User selects position to edit', () => {
	addEmployeePositionPage.selectPositionToEdit();
})

When('User click on the created Employee Level Twice', () => {
	addEmployeePositionPage.clickRowEmployeeLevelTwice(); //there is a minor bug that requires to double click the level after cancelling an edit
});

Then('User can see edit newly position button', () => {
	addEmployeePositionPage.clickEditEmployeePositionButton();
});

When('User click on edit new position button', () => {
	addEmployeePositionPage.editEmployeePositionInputVisible();
});

Then('User can edit previously created position', () => {
	addEmployeePositionPage.enterEditPositionData(
		AddEmployeePositionPageData.midLevelWebDeveloper
	);
});

And('User can see tag multi-select', () => {
	addEmployeePositionPage.tagsMultiSelectVisible();
});

When('User click on tag multi-select', () => {
	addEmployeePositionPage.clickTagsMultiSelect();
});

Then('User can pick tag from dropdown menu', () => {
	addEmployeePositionPage.selectTagsFromDropdown(0);
	addEmployeePositionPage.clickKeyboardButtonByKeyCode(9);
});

And('User can see update position button', () => {
	addEmployeePositionPage.updatePositionButtonVisible();
});

When('User click on update position button', () => {
	addEmployeePositionPage.clickUpdatePositionButton();
});

Then('Notification message will appear', () => {
	addEmployeePositionPage.waitMessageToHide();
});

When('User selects position to edit', () => {
	addEmployeePositionPage.selectPositionToEdit();
})

And('User can see edit position button', () => {
	addEmployeePositionPage.editEmployeePositionButtonVisible();
});

When('User click on edit position button', () => {
	addEmployeePositionPage.clickEditEmployeePositionButton();
});

Then('User can verify position was edited', () => {
	addEmployeePositionPage.verifyTitleExists(
		AddEmployeePositionPageData.midLevelWebDeveloper
	);
});

And('User can see cancel edit button', () => {
	addEmployeePositionPage.cancelButtonVisible();
});

And('User can click on cancel edit button', () => {
	addEmployeePositionPage.clickCancelButton();
});

// Delete employee position
And('User can see Add new position button', () => {
	addEmployeePositionPage.addNewPositionButtonVisible();
});

When('User click on Add new position button', () => {
	addEmployeePositionPage.clickAddNewPositionButton();
});

Then('User can see new position input', () => {
	addEmployeePositionPage.newPositionInputVisible();
});

And('User can add data for new position', () => {
	addEmployeePositionPage.enterNewPositionData(
		AddEmployeePositionPageData.deleteThisPosition
	);
});

And('User can see tag multi-select', () => {
	addEmployeePositionPage.tagsMultiSelectVisible();
});

When('User click on tag multi-select', () => {
	addEmployeePositionPage.clickTagsMultiSelect();
});

Then('User can pick tag from dropdown menu', () => {
	addEmployeePositionPage.selectTagsFromDropdown(0);
	addEmployeePositionPage.clickKeyboardButtonByKeyCode(9);
});

And('User can see save position button', () => {
	addEmployeePositionPage.savePositionButtonVisible();
});

When('User click on save position button', () => {
	addEmployeePositionPage.clickSavePositionButton();
});

Then('Notification message will appear', () => {
	addEmployeePositionPage.waitMessageToHide();
});

When('User selects position to delete', () => {
	addEmployeePositionPage.selectPositionToDelete();
});

And('User can see delete position button', () => {
	addEmployeePositionPage.deletePositionButtonVisible();
});

When('User click on delete position button', () => {
	addEmployeePositionPage.clickDeletePositionButton();
});

Then('User can see confirm delete button', () => {
	addEmployeePositionPage.confirmDeleteButtonVisible();
});

When('User click on confirm delete button', () => {
	addEmployeePositionPage.clickConfirmDeletePositionButton();
});

Then('User can verify that position was deleted', () => {
	addEmployeePositionPage.verifyElementIsDeleted(
		AddEmployeePositionPageData.deleteThisPosition
	);
});

And('User will see a notification message', () => {
	addEmployeePositionPage.waitMessageToHide();
});