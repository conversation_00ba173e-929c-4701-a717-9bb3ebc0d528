Feature: Project tracked in timesheets test
  Scenario: Login with email
    Given <PERSON>gin with default credentials
  Sc<PERSON><PERSON>: Add employee
    Then User can add new employee
  <PERSON><PERSON><PERSON>: Add new project
    And User can visit Organization projects page
    And User can see grid button
    And User can click on second grid button to change view
    And User can see request project button
    When User click on request project button
    Then User can see name input field
    And User can enter value for name
    And User can see employee dropdown
    When User click on employee dropdown
    Then User can select employee from dropdown options
    And User can see save project button
    When User click on save project button
    Then Notification message will appear
  Scenario: Logout
    And User can logout
  Scenario: Login as employee
    And Em<PERSON>loyee can see login page
    And Em<PERSON>loyee can see email input
    And Employee can enter value for employee email
    And Em<PERSON>loyee can see password input
    And Employee can enter value for employee password
    When Employee click on login button
    Then Em<PERSON>loyee will see Create button
  Scenario: Record time with the new project
    And Em<PERSON>loyee can see timer
    When Employee click on timer
    Then Em<PERSON>loyee can see timer button
    And Em<PERSON>loyee can see project select
    When Employee click on project select
    Then Em<PERSON>loyee can select project from dropdown options
    When Employee click on start timer button
    Then Em<PERSON>loyee can let timer work for 5 seconds
    And Em<PERSON>loyee can see stop timer button
    When Em<PERSON>loyee click on stop timer button
    Then Em<PERSON>loyee can see view timesheet button
    When Em<PERSON>loyee click on view timesheet button
    Then Employee verify project name is the same
