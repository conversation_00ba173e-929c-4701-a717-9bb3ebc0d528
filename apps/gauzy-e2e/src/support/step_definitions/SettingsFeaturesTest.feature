Feature: Settings features test
  Scenario: Login with email
    Given Login with default credentials and visit Settings features page
  Scenario: Task Dashboard features
    And User can see tenant tab button
    When User click on tenant tab button
    Then User can see Task dashboard features
  Scenario: Payment features
    And User can verify Payment features
  Scenario: Proposal features
    And User can verify Proposal features
  Scenario: Expense features
    And User can verify Expense features
  Scenario: Invoice features
    And User can verify Invoice features
  Scenario: Jobs features
    And User can verify Jobs features
  Scenario: Time Activity features
    And user can verify Time Activity features
  Scenario: Appointment and Schedule features
    And User can verify Appointment and Schedule features
  Scenario: Manage Organization features
    And User can verify Manage Organization features
  Scenario: Project features
    And User can verify Project features
  Scenario: Organization Document features
    And User can verify Organization Document features
  Scenario: Goal and Objective features
    And User can verify Goal and Objective features
  Scenario: Users features
    And User can verify Users features
  Scenario: Apps and Integrations features
    And User can verify Apps and Integrations features
  Scenario: Setting features
    And User can verify Setting features
  Scenario: Custom SMTP features
    And User can verify Custom SMTP features
  Scenario: Time Tracking features
    And User can verify Time Tracking features
  Scenario: Estimate features
    And User can verify Estimate features
  Scenario: Income features
    And User can verify Income features
  Scenario: Dashboard features
    And User can verify Dashboard features
  Scenario: Sales Pipeline features
    And User can verify Sales Pipeline features
  Scenario: Employees features
    And User can verify Employees features
  Scenario: Timesheet features
    And User can verify Timesheet features
  Scenario: Candidate features
    And User can verify Candidate features
  Scenario: Product Inventory features
    And User can verify Product Inventory features
  Scenario: Organization Team features
    And User can verify Organization Team features
  Scenario: Lead, Customer and Client features
    And User can verify Lead, Customer and Client features
  Scenario: All Report features
    And User can verify All Report features
  Scenario: Organizations features
    And User can verify Organizations features
  Scenario: Email History features
    And User can verify Email History features
  Scenario: Entity Import and Export features
    User can verify Entity Import and Export features
  Scenario: Roles and Permissions features
    User can verify Roles and Permissions features