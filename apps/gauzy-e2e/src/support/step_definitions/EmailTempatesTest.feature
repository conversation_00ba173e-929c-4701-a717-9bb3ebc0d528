Feature: Email templates test
  Scenario: Login with email
    Given Login with default credentials and visit Email templates page
  Scenario: Password Reset template
    Then User can see select language button
    When User click on select language button
    Then User can select English language option from dropdown
    And User can verify Password Reset template in English
    When User click on select language button
    Then User can select Bulgarian language option from dropdown
    And User can verify Password Reset template in Bulgarian
    When User click on select language button
    Then User can select Hebrew language option from dropdown
    And User can verify Password Reset template in Hebrew
    When User click on select language button
    Then User can select Russian language option from dropdown
    And User can verify Password Reset template in Russian
  Scenario: Appointment Confirmation template
    Then User can see select template button
    When User click on select template button
    Then User can select Appointment Confirmation template option
    When User click on select language button
    Then User can select English language option from dropdown
    Then User can verify Appointment Confirmation template in English
    When User click on select language button
    Then User can select Bulgarian language option from dropdown
    And User can verify Appointment Confirmation template in Bulgarian
    When User click on select language button
    Then User can select Hebrew language option from dropdown
    And User can verify Appointment Confirmation template in Hebrew
    When User click on select language button
    Then User can select Russian language option from dropdown
    And User can verify Appointment Confirmation template in Russian
  Scenario: Appointment Cancellation template
    Then User can see select template button
    When User click on select template button
    Then User can select Appointment Cancellation template option
    When User click on select language button
    Then User can select English language option from dropdown
    And User can verify Appointment Cancellation template in English
    When User click on select language button
    Then User can select Bulgarian language option from dropdown
    And User can verify Appointment Cancellation template in Bulgarian
    When User click on select language button
    Then User can select Hebrew language option from dropdown
    And User can verify Appointment Cancellation template in Hebrew
    When User click on select language button
    Then User can select Russian language option from dropdown
    And User can verify Appointment Cancellation template in Russian
  Scenario: Time Off Policy template
    Then User can see select template button
    When User click on select template button
    Then User can select Time Off Policy template option
    When User click on select language button
    Then User can select English language option from dropdown
    And User can verify Time Off Policy template in English
    When User click on select language button
    Then User can select Bulgarian language option from dropdown
    And User can verify Time Off Policy template in Bulgarian
    When User click on select language button
    Then User can select Hebrew language option from dropdown
    And User can verify Time Off Policy template in Hebrew
    When User click on select language button
    Then User can select Russian language option from dropdown
    And User can verify Time Off Policy template in Russian
  Scenario: Task Update template
    Then User can see select template button
    When User click on select template button
    Then User can select Task Update template option
    When User click on select language button
    Then User can select English language option from dropdown
    And User can verify Task Update template in English
    When User click on select language button
    Then User can select Bulgarian language option from dropdown
    And User can verify Task Update template in Bulgarian
    When User click on select language button
    Then User can select Hebrew language option from dropdown
    And User can verify Task Update template in Hebrew
    When User click on select language button
    Then User can select Russian language option from dropdown
    And User can verify Task Update template in Russian
  Scenario: Equipment Create template
    Then User can see select template button
    When User click on select template button
    Then User can select Equipment Create template option
    When User click on select language button
    Then User can select English language option from dropdown
    And User can verify Equipment Create template in English
    When User click on select language button
    Then User can select Bulgarian language option from dropdown
    And User can verify Equipment Create template in Bulgarian
    When User click on select language button
    Then User can select Hebrew language option from dropdown
    And User can verify Equipment Create template in Hebrew
    When User click on select language button
    Then User can select Russian language option from dropdown
    And User can verify Equipment Create template in Russian
  Scenario: Equipment Request template
    Then User can see select template button
    When User click on select template button
    Then User can select Equipment Request template option
    When User click on select language button
    Then User can select English language option from dropdown
    And User can verify Equipment Request template in English
    When User click on select language button
    Then User can select Bulgarian language option from dropdown
    And User can verify Equipment Request template in Bulgarian
    When User click on select language button
    Then User can select Hebrew language option from dropdown
    And User can verify Equipment Request template in Hebrew
    When User click on select language button
    Then User can select Russian language option from dropdown
    And User can verify Equipment Request template in Russian
  Scenario: Time Sheet Overview template
    Then User can see select template button
    When User click on select template button
    Then User can select Time Sheet Overview template option
    When User click on select language button
    Then User can select English language option from dropdown
    And User can verify Time Sheet Overview template in English
    When User click on select language button
    Then User can select Bulgarian language option from dropdown
    And User can verify Time Sheet Overview template in Bulgarian
    When User click on select language button
    Then User can select Hebrew language option from dropdown
    And User can verify Time Sheet Overview template in Hebrew
    When User click on select language button
    Then User can select Russian language option from dropdown
    And User can verify Time Sheet Overview template in Russian
  Scenario: Time Sheet Submit template
    Then User can see select template button
    When User click on select template button
    Then User can select Time Sheet Submit template option
    When User click on select language button
    Then User can select English language option from dropdown
    And User can verify Time Sheet Submit template in English
    When User click on select language button
    Then User can select Bulgarian language option from dropdown
    And User can verify Time Sheet Submit template in Bulgarian
    When User click on select language button
    Then User can select Hebrew language option from dropdown
    And User can verify Time Sheet Submit template in Hebrew
    When User click on select language button
    Then User can select Russian language option from dropdown
    And User can verify Time Sheet Submit template in Russian
  Scenario: Time Sheet Actions template
    Then User can see select template button
    When User click on select template button
    Then User can select Time Sheet Actions template option
    When User click on select language button
    Then User can select English language option from dropdown
    And User can verify Time Sheet Actions template in English
    When User click on select language button
    Then User can select Bulgarian language option from dropdown
    And User can verify Time Sheet Actions template in Bulgarian
    When User click on select language button
    Then User can select Hebrew language option from dropdown
    And User can verify Time Sheet Actions template in Hebrew
    When User click on select language button
    Then User can select Russian language option from dropdown
    And User can verify Time Sheet Actions template in Russian
  Scenario: Time Sheet Delete template
    Then User can see select template button
    When User click on select template button
    Then User can select Time Sheet Delete template option
    When User click on select language button
    Then User can select English language option from dropdown
    And User can verify Time Sheet Delete template in English
    When User click on select language button
    Then User can select Bulgarian language option from dropdown
    And User can verify Time Sheet Delete template in Bulgarian
    When User click on select language button
    Then User can select Hebrew language option from dropdown
    And User can verify Time Sheet Delete template in Hebrew
    When User click on select language button
    Then User can select Russian language option from dropdown
    And User can verify Time Sheet Delete template in Russian
  Scenario: Candidate Interview Schedule template
    Then User can see select template button
    When User click on select template button
    Then User can select Candidate Interview Schedule template option
    When User click on select language button
    Then User can select English language option from dropdown
    And User can verify Candidate Interview Schedule template in English
    When User click on select language button
    Then User can select Bulgarian language option from dropdown
    And User can verify Candidate Interview Schedule template in Bulgarian
    When User click on select language button
    Then User can select Hebrew language option from dropdown
    And User can verify Candidate Interview Schedule template in Hebrew
    When User click on select language button
    Then User can select Russian language option from dropdown
    And User can verify Candidate Interview Schedule template in Russian
  Scenario: Interviewer Schedule template
    Then User can see select template button
    When User click on select template button
    Then User can select Interviewer Schedule template option
    When User click on select language button
    Then User can select English language option from dropdown
    And User can verify Interviewer Schedule template in English
    When User click on select language button
    Then User can select Bulgarian language option from dropdown
    And User can verify Interviewer Schedule template in Bulgarian
    When User click on select language button
    Then User can select Hebrew language option from dropdown
    And User can verify Interviewer Schedule template in Hebrew
    When User click on select language button
    Then User can select Russian language option from dropdown
    And User can verify Interviewer Schedule template in Russian
  Scenario: Welcome User template
    Then User can see select template button
    When User click on select template button
    Then User can select Welcome User template option
    When User click on select language button
    Then User can select English language option from dropdown
    And User can verify Welcome User template in English
    When User click on select language button
    Then User can select Bulgarian language option from dropdown
    And User can verify Welcome User template in Bulgarian
    When User click on select language button
    Then User can select Hebrew language option from dropdown
    And User can verify Welcome User template in Hebrew
    When User click on select language button
    Then User can select Russian language option from dropdown
    And User can verify Welcome User template in Russian
  Scenario: Invite Organization Client template
    Then User can see select template button
    When User click on select template button
    Then User can select Invite Organization Client template option
    When User click on select language button
    Then User can select English language option from dropdown
    And User can verify Invite Organization Client template in English
    When User click on select language button
    Then User can select Bulgarian language option from dropdown
    And User can verify Invite Organization Client template in Bulgarian
    When User click on select language button
    Then User can select Hebrew language option from dropdown
    And User can verify Invite Organization Client template in Hebrew
    When User click on select language button
    Then User can select Russian language option from dropdown
    And User can verify Invite Organization Client template in Russian
  Scenario: Invite Employee template
    Then User can see select template button
    When User click on select template button
    Then User can select Invite Employee template option
    When User click on select language button
    Then User can select English language option from dropdown
    And User can verify Invite Employee template in English
    When User click on select language button
    Then User can select Bulgarian language option from dropdown
    And User can verify Invite Employee template in Bulgarian
    When User click on select language button
    Then User can select Hebrew language option from dropdown
    And User can verify Invite Employee template in Hebrew
    When User click on select language button
    Then User can select Russian language option from dropdown
    And User can verify Invite Employee template in Russian
  Scenario: Invite User template
    Then User can see select template button
    When User click on select template button
    Then User can select Invite User template option
    When User click on select language button
    Then User can select English language option from dropdown
    And User can verify Invite User template in English
    When User click on select language button
    Then User can select Bulgarian language option from dropdown
    And User can verify Invite User template in Bulgarian
    When User click on select language button
    Then User can select Hebrew language option from dropdown
    And User can verify Invite User template in Hebrew
    When User click on select language button
    Then User can select Russian language option from dropdown
    And User can verify Invite User template in Russian
  Scenario: Email Invoice
    Then User can see select template button
    When User click on select template button
    Then User can select Email Invoice template option
    When User click on select language button
    Then User can select English language option from dropdown
    And User can verify Email Invoice template in English
  Scenario: Email Estimate
    Then User can see select template button
    When User click on select template button
    Then User can select Email Estimate template option
    When User click on select language button
    Then User can select English language option from dropdown
    And User can verify Email Estimate template in English