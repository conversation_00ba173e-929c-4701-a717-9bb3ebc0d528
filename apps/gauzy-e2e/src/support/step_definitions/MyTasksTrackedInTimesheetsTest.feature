Feature: Time tracking with pause test
  Scenario: Login with email
    Given Login with default credentials
  Sc<PERSON><PERSON>: Add new tag
    And User can add new tag
  Sc<PERSON><PERSON>: Add employee
    Then User can add new employee
  <PERSON><PERSON><PERSON>: Add project
    And User can add new project
  Scenario: Logout
    And User can logout
  Scenario: Login as employee
    And <PERSON><PERSON>loyee can see login page
    And Em<PERSON>loyee can see email input
    And <PERSON><PERSON>loyee can enter value for employee email
    And <PERSON><PERSON>loyee can see password input
    And <PERSON><PERSON>loyee can enter value for employee password
    When Employee click on login button
    Then Em<PERSON>loyee will see Create button
  Scenario: Create task and verify
    When Employee go to my tasks
    Then Em<PERSON>loyee can see add button
    When Employee click on add button
    Then Em<PERSON>loyee can see project dropdown
    When Employee click on project dropdown
    Then Employee can select project from dropdown options
    And Em<PERSON>loyee can see status dropdown
    When Employee click on status dropdown
    Then Em<PERSON>loyee can select status from dropdown options
    And Em<PERSON>loyee can see title input field
    And Em<PERSON>loyee can enter title
    And Em<PERSON>loyee can see tags dropdown
    When Employee click on tags dropdown
    Then Em<PERSON>loyee can select tag from dropdown options
    And Em<PERSON>loyee can see due date input field
    And Em<PERSON>loyee can enter due date
    And Em<PERSON>loyee can see estimate days input field
    And Em<PERSON>loyee can enter estimate days
    And Em<PERSON>loyee can see estimate hours input field
    And Em<PERSON>loyee can enter estimate hours
    And <PERSON><PERSON>loy<PERSON> can see estimate minutes input field
    And Em<PERSON>loyee can enter estimate minutes
    And Employee can see task description input field
    And Employee can enter task description
    And Employee can see task save button
    When Employee click on save task button
    Then Notification message will appear
  Scenario: Record time with the new task
    And Employee can see timer
    When Employee click on timer
    Then Employee can see timer button
    And Employee can see task select
    When Employee click on task select
    Then Employee can select task from dropdown options
    When Employee click on start timer button
    Then Employee can let timer work for 5 seconds
    And Employee can see stop timer button
    When Employee click on stop timer button
    Then Employee can see view timesheet button
    When Employee click on view timesheet button
    Then Employee verify project name is the same