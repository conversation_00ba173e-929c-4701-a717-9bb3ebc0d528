Feature: Time tracking for client test
  Scenario: Login with email
    Given Login with default credentials
  Sc<PERSON><PERSON>: Add employee
    Then User can add new employee
  <PERSON><PERSON><PERSON>: Add new client
    And User can add new client
  Scenario: Logout
    And User can logout
  Scenario: Login as employee
    And Em<PERSON>loyee can see login page
    And Em<PERSON>loyee can see email input
    And <PERSON><PERSON>loyee can enter value for employee email
    And <PERSON><PERSON>loyee can see password input
    And <PERSON><PERSON>loyee can enter value for employee password
    When Em<PERSON>loyee click on login button
    Then Em<PERSON>loyee will see Create button
  Scenario: Record time for the new client and verify
    And Em<PERSON>loyee can see timer
    When Employee click on timer
    Then Em<PERSON>loyee can see timer button
    And Em<PERSON>loyee can see client select
    When Employee click on client select
    Then Em<PERSON>loyee can select client from dropdown options
    When Employee click on start timer button
    Then Em<PERSON>loyee can let timer work for 5 seconds
    And <PERSON><PERSON>loyee can see stop timer button
    When Employee click on stop timer button
    Then Em<PERSON>loyee can see view timesheet button
    When Em<PERSON>loyee click on view timesheet button
    Then Em<PERSON>loyee can see view button
    When Employee click on view button
    Then Em<PERSON>loyee can verify the client name is recorded
