import dayjs from 'dayjs';
import {
	verifyElementIsVisible,
	clickButtonByIndex,
	clickButton,
	clearField,
	enterInput,
	clickKeyboardBtnByKeycode,
	clickElementByText,
	waitElementToHide,
	verifyText,
	verifyTextNotExisting,
	verifyByLength,
	wait

} from '../utils/util';
import { AddTaskPage } from '../pageobjects/AddTasksPageObject';

export const gridBtnExists = () => {
	verifyElementIsVisible(AddTaskPage.gridButtonCss);
};

export const gridBtnClick = (index) => {
	clickButtonByIndex(AddTaskPage.gridButtonCss, index);
};

export const addTaskButtonVisible = () => {
	verifyElementIsVisible(AddTaskPage.addTaskButtonCss);
};

export const clickAddTaskButton = () => {
	clickButton(AddTaskPage.addTaskButtonCss);
};

export const selectProjectDropdownVisible = () => {
	verifyElementIsVisible(AddTaskPage.selectProjectDropdownCss);
};

export const clickSelectProjectDropdown = () => {
	clickButton(AddTaskPage.selectProjectDropdownCss);
};

export const selectProjectOptionDropdown = (text) => {
	clickElementByText(AddTaskPage.selectProjectDropdownOptionCss, text);
};

export const selectEmployeeDropdownVisible = () => {
	verifyElementIsVisible(AddTaskPage.selectEmployeeMultiSelectCss);
};

export const clickSelectEmployeeDropdown = () => {
	clickButton(AddTaskPage.selectEmployeeMultiSelectCss);
};

export const selectEmployeeDropdownOption = (index) => {
	clickButtonByIndex(AddTaskPage.selectEmployeeDropdownOptionCss, index);
};

export const selectEmployeeFromDropdownByName = (name) => {
	clickElementByText(
		AddTaskPage.selectEmployeeDropdownOptionCss,
		name
	);
};

export const addTitleInputVisible = () => {
	verifyElementIsVisible(AddTaskPage.addTitleInputCss);
};

export const enterTitleInputData = (data) => {
	clearField(AddTaskPage.addTitleInputCss);
	enterInput(AddTaskPage.addTitleInputCss, data);
};

export const tagsMultiSelectVisible = () => {
	verifyElementIsVisible(AddTaskPage.tagsSelectCss);
};

export const clickTagsMultiSelect = () => {
	clickButton(AddTaskPage.tagsSelectCss);
};

export const selectTagsFromDropdown = (index) => {
	clickButtonByIndex(AddTaskPage.tagsSelectOptionCss, index);
};

export const closeTagsMultiSelectDropdownButtonVisible = () => {
	verifyElementIsVisible(AddTaskPage.closeTagsMultiSelectDropdownCss);
};

export const clickCloseTagsMultiSelectDropdownButton = () => {
	clickButton(AddTaskPage.closeTagsMultiSelectDropdownCss);
};

export const clickKeyboardButtonByKeyCode = (keycode) => {
	clickKeyboardBtnByKeycode(keycode);
};

export const dueDateInputVisible = () => {
	verifyElementIsVisible(AddTaskPage.dueDateInputCss);
};

export const enterDueDateData = () => {
	clearField(AddTaskPage.dueDateInputCss);
	const date = dayjs().add(1, 'd').format('MMM D, YYYY');
	enterInput(AddTaskPage.dueDateInputCss, date);
};

export const estimateDaysInputVisible = () => {
	verifyElementIsVisible(AddTaskPage.estimateDaysInputCss);
};

export const enterEstimateDaysInputData = (days) => {
	clearField(AddTaskPage.estimateDaysInputCss);
	enterInput(AddTaskPage.estimateDaysInputCss, days);
};

export const estimateHoursInputVisible = () => {
	verifyElementIsVisible(AddTaskPage.estimateHoursInputCss);
};

export const enterEstimateHoursInputData = (hours) => {
	clearField(AddTaskPage.estimateHoursInputCss);
	enterInput(AddTaskPage.estimateHoursInputCss, hours);
};

export const estimateMinutesInputVisible = () => {
	verifyElementIsVisible(AddTaskPage.estimateMinsInputCss);
};

export const enterEstimateMinutesInputData = (mins) => {
	clearField(AddTaskPage.estimateMinsInputCss);
	enterInput(AddTaskPage.estimateMinsInputCss, mins);
};

export const taskDescriptionTextareaVisible = () => {
	verifyElementIsVisible(AddTaskPage.descriptionTextareaCss);
};

export const enterTaskDescriptionTextareaData = (data) => {
	clearField(AddTaskPage.descriptionTextareaCss);
	enterInput(AddTaskPage.descriptionTextareaCss, data);
};

export const saveTaskButtonVisible = () => {
	verifyElementIsVisible(AddTaskPage.saveNewTaskButtonCss);
};

export const clickSaveTaskButton = () => {
	clickButton(AddTaskPage.saveNewTaskButtonCss);
};

export const tasksTableVisible = () => {
	verifyElementIsVisible(AddTaskPage.selectTableRowCss);
};

export const selectTasksTableRow = (index) => {
	clickButtonByIndex(AddTaskPage.selectTableRowCss, index);
};

export const selectFirstTaskTableRow = (index) => {
	clickButtonByIndex(AddTaskPage.selectTableFirstRowCss, index);
};

export const deleteTaskButtonVisible = () => {
	verifyElementIsVisible(AddTaskPage.deleteTaskButtonCss);
};

export const clickDeleteTaskButton = () => {
	clickButton(AddTaskPage.deleteTaskButtonCss);
};

export const confirmDeleteTaskButtonVisible = () => {
	verifyElementIsVisible(AddTaskPage.confirmDeleteTaskButtonCss);
};

export const clickConfirmDeleteTaskButton = () => {
	clickButton(AddTaskPage.confirmDeleteTaskButtonCss);
};

export const duplicateTaskButtonVisible = () => {
	verifyElementIsVisible(AddTaskPage.duplicateTaskButtonCss);
};

export const clickDuplicateTaskButton = (index) => {
	clickButtonByIndex(AddTaskPage.duplicateTaskButtonCss, index);
};

export const confirmDuplicateTaskButtonVisible = () => {
	verifyElementIsVisible(AddTaskPage.confirmDuplicateOrEditTaskButtonCss);
};

export const clickConfirmDuplicateTaskButton = () => {
	clickButton(AddTaskPage.confirmDuplicateOrEditTaskButtonCss);
};

export const editTaskButtonVisible = () => {
	wait(500);
	verifyElementIsVisible(AddTaskPage.editTaskButtonCss);
};

export const clickEditTaskButton = (index) => {
	clickButtonByIndex(AddTaskPage.editTaskButtonCss, index);
};

export const confirmEditTaskButtonVisible = () => {
	verifyElementIsVisible(AddTaskPage.confirmDuplicateOrEditTaskButtonCss);
};

export const clickConfirmEditTaskButton = () => {
	clickButton(AddTaskPage.confirmDuplicateOrEditTaskButtonCss);
};

export const waitMessageToHide = () => {
	waitElementToHide(AddTaskPage.toastrMessageCss);
};

export const verifyTaskExists = (text) => {
	verifyText(AddTaskPage.verifyTextCss, text);
};

export const verifyElementIsDeleted = (text) => {
	verifyTextNotExisting(AddTaskPage.verifyTextCss, text);
};

export const verifyTitleInput = () => {
	verifyElementIsVisible(AddTaskPage.searchTitleInputCss);
};

export const searchTitleName = (name: string) => {
	clearField(AddTaskPage.searchTitleInputCss);
	enterInput(AddTaskPage.searchTitleInputCss, name);
};

export const clearSearchInput = () => {
	clearField(AddTaskPage.searchTitleInputCss);
};

export const verifySearchResult = (length: number) => {
	verifyByLength(AddTaskPage.selectTableRowCss, length);
};
