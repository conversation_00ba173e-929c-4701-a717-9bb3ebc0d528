export const AddOrganizationPage = {
	organizationNameFieldCss: '[placeholder="Organization Name"]',
	currencyFieldCss: 'div.ng-select-container.ng-has-value',
	dropdownOptionCss: 'ng-dropdown-panel[role="listbox"]',
	startOfWeekOptionCss: 'ul.option-list',
	bonusDropdownOptionCss: 'ul.option-list',
	dateDropdownOptionCss: 'ul.option-list',
	regionDropdownOptionCss: 'ul.option-list',
	dateFormatOptionCss: 'ul.option-list',
	numberFormatDropdownOptionCss: 'ul.option-list',
	officialNameFieldCss: '#officialNameInput',
	gridButtonCss: 'div.layout-switch > button',
	taxFieldCss: '#taxIdInput',
	nextButtonCss: 'button[type="submit"]',
	addButtonCss: 'button[status="success"]',
	verifyOrganizationCss: 'div.d-block',
	toastrMessageCss: 'nb-toast.ng-tns-c214-120.ng-trigger',
	countryDropdownCss: 'div.ng-select-container.ng-has-value',
	cityInputCss: '#cityInput',
	postCodeInputCss: '#postcodeInput',
	streetInputCss: '#addressInput',
	bonusTypeDropdownCss: '[formcontrolname="bonusType"]',
	bonusPercentageCss: '[formcontrolname="bonusPercentage"]',
	expiryPeriodInputCss: '[formcontrolname="inviteExpiryPeriod"]',
	dateTypeDropdownCss: '[formcontrolname="defaultValueDateType"]',
	startOfWeekDropdownCss: '#startWeekOnSelect',
	regionCodeDropdownCss: '[formcontrolname="regionCode"]',
	numberFormatDropdownCss: '[formcontrolname="numberFormat"]',
	dateFormatDropdownCss: '[formcontrolname="dateFormat"]',
	timeZoneDropdownCss: 'div[role="combobox"] > input[type="text"]',
	timeZoneDropdownOptionCss: 'ng-dropdown-panel[role="listbox"]',
	tableRowCss: 'table > tbody > tr.angular2-smart-row'
};
