export const TimesheetsPage = {
	addTimeButtonCss: 'div.text-left > button[status="primary"]',
	selectEmployeeCss:
		'ga-employee-multi-select[formcontrolname="employeeId"] > nb-select',
	selectEmployeeDropdownOptionCss: '.option-list nb-option',
	dateInputCss: '[name="date"]',
	projectDropdownCss: '[formcontrolname="projectId"]',
	dropdownOptionCss: 'div.ng-option',
	clientDropdownCss: '[formcontrolname="organizationContactId"]',
	taskDropdownCss: '[formcontrolname="taskId"]',
	descriptionTextareaCss: 'textarea[name="description"]',
	saveTimeButtonCss: 'nb-card-footer > button[status="primary"]',
	viewEmployeeTimeCss: 'div.action-button > button[status="primary"]',
	editEmployeeTimeCss: 'div.action-button > button[status="success"]',
	deleteEmployeeTimeCss: 'div.action-button > button[status="danger"]',
	closeAddTimeLogPopoverCss: 'g[data-name="close"]',
	confirmDeleteButtonCss: 'nb-card-footer > button[status="primary"]',
	startTimeDropdownCss: 'ga-timer-picker[name="start_time"]',
	closeButtonCss: 'button.close-button',
	toastrMessageCss: 'nb-toast.ng-trigger',
	verifyTimeCss: 'ngx-avatar > div > div.col > div.ng-star-inserted'
};
