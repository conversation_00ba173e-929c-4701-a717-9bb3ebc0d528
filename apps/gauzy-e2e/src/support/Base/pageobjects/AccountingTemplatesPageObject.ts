export const AccountingTemplatesPage = {
	leftTableDataCss:
		'table[role="presentation"] > tbody > tr > td[align="left"] > div',
	rightTableDataCss:
		'table[role="presentation"] > tbody > tr > td[align="left"] > div > div > span',
    receiptNumberAndPaymentMethodDataCss: 'table[role="presentation"] > tbody > tr > td[align="right"] > div > div > span',
	languageSelectCss: 'ngx-language-selector > ng-select',
	templateSelectCss: 'nb-select[id="templateName"]',
	languageDropdownOptionCss: 'div.ng-option',
	templateDropdownOptionCss: '.option-list nb-option',
	logoCss:
		'table[role="presentation"] > tbody > tr > td[align="right"] > table > tbody > tr > td > img[src="assets/images/logos/ever-large.jpg"]',
	saveBtnCss:
		'div[class="col-2 d-flex justify-content-end"] > button[status="success"]'
};
