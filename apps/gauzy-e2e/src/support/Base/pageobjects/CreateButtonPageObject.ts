export const CreateButton = {
	createButtonCss: 'nb-action.show-large-up > button[status="success"]',
	createButtonOptionCss:
		'nb-menu.context-menu > ul.menu-items > li.menu-item > a',
	titleTextCss: 'nb-menu.context-menu > ul.menu-items span.menu-title',
	cancelButtonCss: 'nb-card-footer.text-right > button[status="danger"]',
	closeButtonCss: 'nb-icon[icon="close-outline"]',
	nbCardh4Css: 'nb-card-header.d-flex > h4',
	nbCardh5Css: 'nb-card-header.d-flex > h5',
	divh4Css: 'div.main-header > h4',
	timeLogHeaderTextCss: 'nb-card-header.header > div.row > div.col',
	proposalHeaderTextCss: 'nb-card-header > h4',
	contactHeaderTextCss: 'nb-card-header',
	teamHeaderCss: 'div.card-header-title > h4',
	projectHeaderCss: 'nb-card-header'
};
