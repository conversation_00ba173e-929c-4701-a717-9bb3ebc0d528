export const EmployeeDashboardPage = {
	addNewExpenseButtonCss: 'nb-card > nb-card-body > button[status="success"]',
	employeeDropdownCss: '[placeholder="Employee"]',
	dropdownOptionCss: 'div.ng-option',
	expenseDropdownCss: '#positionInput',
	valueInputCss: '#valueInput',
	saveExpenseButtonCss:
		'nb-card-footer.text-right > button[status="success"]',
	toastrMessageCss: 'nb-toast.ng-trigger',
	menuButtonsCss: 'nb-menu > ul.menu-items > li',
	employeeSelectorCss: 'ga-employee-selector',
	selectEmployeeDropdownOptionCss: 'ng-dropdown-panel > div[class="ng-dropdown-panel-items scroll-host"] > div > div.ng-star-inserted',
	salaryCss: 'div.header-container > div > div[class="employee-details employee-salary ng-star-inserted"]',
	incomeBtn: 'a[title="Income"]',
	addNewIncomeBtnCss: 'nb-card-body > div > button[status="success"]',
	gridButtonCss: 'div.layout-switch > button',
	addIncomeButtonCss: 'div.mb-3 > button[status="success"]',
	selectEmployeeDropdownCss: 'ga-employee-selector.employees',
	selectEmployeeDropdownOptCss: 'div.ng-option',
	dateInputCss: '[formcontrolname="valueDate"]',
	organizationContactCss: '[formcontrolname="organizationContact"]',
	amountInputCss: '[formcontrolname="amount"]',
	addTagsDropdownCss: '#addTags',
	tagsDropdownOption: 'div.ng-option',
	notesInputCss: '[formcontrolname="notes"]',
	saveIncomeButtonCss: 'nb-card-footer > button[status="success"]',
	verifyIncomeCss: 'ga-notes-with-tags > div > div.ng-star-inserted',
	verifyDashboardIncomeCss: 'ga-info-block[ng-reflect-title="Total Income"]',
	verifyDashboardBonusCss: 'div[class="bonus ng-star-inserted"]',
	currencyFieldCss: 'nb-select[id="currencySelect"]',
	currencyOptionCss: 'ul.option-list > nb-option'

};
