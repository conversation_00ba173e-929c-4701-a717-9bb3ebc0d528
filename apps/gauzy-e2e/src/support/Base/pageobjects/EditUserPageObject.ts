export const EditUserPage = {
	gridButtonCss: 'div.layout-switch > button',
	selectTableRowCss: 'table > tbody > tr.angular2-smart-row',
	editButtonCss: '[data-name="edit"]',
	orgTabButtonCss: 'ul.route-tabset > li > a.tab-link',
	addOrgButtonCss: 'nb-card-header > button[status="success"]',
	removeOrgButtonCss: 'g[data-name="close"]',
	confirmRemoveOrgButtonCss: 'nb-card-footer > button[status="danger"]',
	selectOrgMultiSelectCss: 'button[class="select-button placeholder"]',
	selectOrgDropdownOptionCss: '.option-list nb-option',
	saveSelectedOrgButton: 'div.form-group > button[status="success"]',
	firstNameInputCss: '#firstName',
	lastNameInputCss: '#lastName',
	passwordInputCss: 'input[placeholder="Password"]',
	repeatPasswordInputCss: 'input#reset-password',
	emailInputCss: '#email',
	tagsSelectCss: '#addTags',
	tagsSelectOptionCss: 'div.ng-dropdown-panel-items.scroll-host',
	roleSelectCss: 'nb-select#role>button',
	roleSelectOptionCss: '.option-list',
	preferredLanguageCss: 'div.ng-select-container.ng-has-value',
	preferredLanguageOptionCss: 'div.ng-dropdown-panel-items.scroll-host',
	saveButtonCss: 'button[status="success"]',
	verifyUserCss: 'div.names-wrapper',
	toastrMessageCss: 'nb-toast.ng-trigger'
};
