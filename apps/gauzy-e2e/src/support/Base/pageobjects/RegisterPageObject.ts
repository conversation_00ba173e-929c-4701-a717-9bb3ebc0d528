export const RegisterPage = {
	registerLinkCss: 'section.another-action > a.text-link',
	fullNameFieldCss: '#input-name',
	emailAddressFieldCss: '#input-email',
	passwordFieldCss: '#input-password',
	confirmPassFieldCss: '#input-re-password',
	termAndConditionCheckboxCss: 'span.custom-checkbox',
	registerButtonCss: 'ngx-register > form > button',
	organizationNameFieldCss: '[placeholder="Organization Name"]',
	currencyFieldCss: '[class="select-button placeholder"]',
	dropdownOptionCss: '.option-list nb-option',
	officialNameFieldCss: '#officialNameInput',
	taxFieldCss: '#taxIdInput',
	nextButtonCss: 'button[status="success"]',
	verifyOrganizationCss: 'div.d-block',
	toastrMessageCss: 'nb-toast.ng-trigger',
	countryDropdownCss: 'ga-country div.form-group nb-select',
	cityInputCss: '#cityInput',
	postCodeInputCss: '#postcodeInput',
	streetInputCss: '#addressInput',
	bonusTypeDropdownCss: '[formcontrolname="bonusType"]',
	bonusPercentageCss: '[formcontrolname="bonusPercentage"]',
	expiryPeriodInputCss: '[formcontrolname="inviteExpiryPeriod"]',
	dateTypeDropdownCss: '[formcontrolname="defaultValueDateType"]',
	startOfWeekDropdownCss: '#startWeekOnSelect',
	regionCodeDropdownCss: '[formcontrolname="regionCode"]',
	numberFormatDropdownCss: '[formcontrolname="numberFormat"]',
	dateFormatDropdownCss: '[formcontrolname="dateFormat"]',
	timeZoneDropdownCss: '[formcontrolname="timeZone"]',
	timeZoneDropdownOptionCss: 'div.ng-option',
	tableRowCss: 'table > tbody > tr.angular2-smart-row',
	verifyLogoCss: 'div.logo-container'
};
