export const ManageCandidatesInvitesPage = {
	inviteButtonCss: 'div.mb-3 > button[status="primary"]',
	emailInputCss: '#emails',
	dateInputCss: 'input[formcontrolname="appliedDate"]',
	saveButtonCss: 'nb-card-footer.text-right > button[status="success"]',
	selectTableRowCss: 'table > tbody > tr.angular2-smart-row',
	resendInviteButtonCss: 'div.mb-3 > button[status="warning"]',
	deleteInviteButtonCss: 'div.mb-3 > button[status="danger"]',
	confirmDeleteButtonCss: 'nb-card-footer > button[status="danger"]',
	confirmResendInviteButtonCss: 'nb-card-footer > button[status="success"]',
	toastrMessageCss: 'nb-toast.ng-trigger',
	verifyEmailCss: 'table-cell-view-mode > div > div.ng-star-inserted',
	headerPageCss: 'ngx-header-title',
	emailPlaceholderCss: 'input-filter > input[placeholder="Email"]'
};
