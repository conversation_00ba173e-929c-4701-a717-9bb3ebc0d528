export const AddEmployeePositionPage = {
	gridButtonCss: 'div.layout-switch > button',
	addNewPositionButtonCss: 'button[status="success"]',
	newPositionInputCss: '[placeholder="Position name"]',
	tagsSelectCss: '#addTags',
	tagsSelectOptionCss: '[type="checkbox"]',
	saveNewPositionButtonCss: 'button[ng-reflect-disabled="false"]',
	updatePositionButtonCss: '.appearance-filled.size-medium',
	cancelNewPositionButtonCss: 'button.delete.mr-3',
	editEmployeePositionButtonCss: 'button.action.primary',
	removeEmployeePositionButtonCss: 'button.action.appearance-filled',
	confirmDeletePositionButtonCss: 'button.mr-3.ml-3',
	editPositionInputCss: 'div.d-flex > input[type="text"]',
	verifyTextCss: 'ga-notes-with-tags > div > div > div.ng-star-inserted',
	cardBodyCss: 'nb-card-body',
	toastrMessageCss: 'nb-toast.ng-trigger',
	cancelButtonCss: 'button.delete.mr-3',
	selectPositionToEditCss: 'ga-notes-with-tags',
	selectPositionToDeleteCss: 'ga-notes-with-tags'
};
