export const EditProfilePage = {
	firstNameInputCss: '#firstName',
	lastNameInputCss: '#lastName',
	passwordInputCss: '#password',
	repeatPasswordInputCss: '[placeholder="Repeat Password"]',
	emailInputCss: '#email',
	preferredLanguageCss: '#preferredLanguage',
	languageSelectCss: 'div > div.form-group > ngx-language-selector > ng-select',
	preferredLanguageOptionCss: 'ng-dropdown-panel > div[class="ng-dropdown-panel-items scroll-host"] > div > div[class="ng-option ng-star-inserted"] > span[class="ng-option-label ng-star-inserted"]',
	saveButtonCss: 'div.actions > button[status="success"]'
};
