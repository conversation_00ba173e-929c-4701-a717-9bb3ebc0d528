export const PipelinesPage = {
	gridButtonCss: 'div.layout-switch > button',
	addPipelineButtonCss: 'div.mb-3 > button[status="success"]',
	pipelineNameInputCss: '[formcontrolname="name"]',
	descriptionInputCss: '[formcontrolname="description"]',
	createPipelineButtonCss: 'nb-card-footer > button[status="primary"]',
	selectTableRowCss: 'table > tbody > tr.angular2-smart-row',
	editPipelineButtonCss: 'div.mb-3 > button[status="info"]',
	updateButtonCss: 'nb-card-footer > button[status="info"]',
	deletePipelineButtonCss: 'div.mb-3 > button[status="danger"]',
	confirmDeleteButtonCss: 'nb-card-footer > button[status="danger"]',
	toastrMessageCss: 'nb-toast.ng-trigger',
	verifyPipelineCss: 'div.ng-star-inserted',
	stageButtonCss: 'nb-card-header > button',
	namePlaceholderCss: 'input[placeholder="Name"]',
	detailsButtonCss: 'div.mb-3 > button[status="basic"]',
	titleInputCss: 'input[placeholder="Title"]',
	createDealButtonCss: 'nb-card-footer > button[ng-reflect-status="primary"]',
	addDealPipelineButtonCss: 'div.mb-3 > div > button[status="success"]',
	probabilityInputCss: 'nb-select[formcontrolname="probability"]',
	dropdownOptionCss: 'ul.option-list > nb-option',
	backButtonCss: 'ngx-back-navigation > div.main > button'
};
