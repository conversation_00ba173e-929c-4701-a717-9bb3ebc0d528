export const ProjectTrackedInTimesheetPage = {
    gridButtonCss: 'div.layout-switch > button',
    requestNewProjectButtonCss: 'div.mb-3 > button[status="success"]',
    projectNameInputCss: '#name',
    selectEmployeeMultiSelectCss: 'button[class="select-button placeholder"]',
    selectEmployeeDropdownOptionCss: '.option-list nb-option',
    saveProjectButtonCss: 'div.form-group > button[status="success"]',
    toastrMessageCss: 'nb-toast.ng-trigger',
    timerCss: 'div.header-container > nb-actions > nb-action.timer-action',
    timerBtnCss: 'nb-icon[icon="clock-outline"] > svg > g[data-name="Layer 2"] > g[data-name="clock"]',
    projectSelectCss: 'div.form-group > ga-project-selector > ng-select[ng-reflect-placeholder="Select Project"]',
    dropdownOptionCss: 'div.ng-option',
    startTimerBtnCss: 'div.actions > div.toggle > button[class="btn btn-rounded btn-success"]',
    stopTimerBtnCss: 'div.actions > div.toggle > button[class="btn btn-rounded btn-danger"]',
    viewTimesheetBtnCss: 'div.view-log-button > a[href="#/pages/employees/timesheets"]',
    projectNameCss: 'div[class="col project-name"] > span.ng-star-inserted',
    headerImgCss: 'div.header-container'
}
