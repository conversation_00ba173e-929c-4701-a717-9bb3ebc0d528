export const OrganizationDocumentsPage = {
	gridButtonCss: 'div.layout-switch > button',
	addButtonCss: 'div.pb-4 > button[status="success"]',
	nameInputCss: '#documentName',
	urlInputCss: '#inputDocUrl',
	selectTableRowCss: 'table > tbody > tr.angular2-smart-row',
	saveButtonCss: 'span.col-1 > button[status="success"]',
	editButtonCss: 'nb-actions g[data-name="edit"]',
	deleteButtonCss: 'nb-actions g[data-name="close"]',
	confirmDeleteButtonCss: 'nb-card-footer > button[status="danger"]',
	cardBodyCss: 'nb-card-body',
	toastrMessageCss: 'nb-toast.ng-trigger',
	verifyDocumentCss: 'div > a'
};
