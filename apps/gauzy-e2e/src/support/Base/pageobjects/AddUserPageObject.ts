export const AddUserPage = {
	addUserButtonCss: 'button.action.status-success',
	firstNameInputCss: '#firstName',
	lastNameInputCss: '#lastName',
	usernameInputCss: '#username',
	emailInputCss: '#email',
	selectRoleDropdownCss: 'nb-select#role>button',
	selectRoleDropdownOptionCss: '.option-list',
	passwordInputCss: 'input#password',
	imageInputUrlCss: '[placeholder="Image"]',
	confirmAddUserButtonCss:
		'button.green.appearance-outline',
	verifyUserCss: 'div.names-wrapper',
	toastrMessageCss: 'nb-toast.ng-trigger',
	endOfUserListCss: 'ul > li:nth-of-type(9)'
};
