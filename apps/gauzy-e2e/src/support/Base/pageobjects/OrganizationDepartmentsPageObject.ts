export const OrganizationDepartmentsPage = {
	gridButtonCss: 'div.layout-switch > button',
	addDepartmentButtonCss: '.status-success',
	nameInputCss: '.input-full-width',
	selectEmployeeDropdownCss: 'button[class="select-button placeholder"]',
	selectEmployeeDropdownOptionCss: '.option-list nb-option',
	addTagsDropdownCss: 'form [aria-autocomplete]',
	tagsDropdownOption: '.ng-dropdown-panel > div > div > div > div > span',
	selectTableRowCss: 'table > tbody > tr.angular2-smart-row',
	saveDepartmentButtonCss: '[type="submit"]',
	editDepartmentButtonCss: '.primary',
	deleteDepartmentButtonCss: '.icon-end.ng-star-inserted',
	confirmDeleteButtonCss:
		'[class="mr-3 ml-3 appearance-filled size-medium shape-rectangle status-danger nb-transition"]',
	footerCss: '.editable',
	verifyDepartmentCss:
		'tbody > tr > td > angular2-smart-table-cell > table-cell-view-mode > div > div.ng-star-inserted',
	toastrMessageCss: 'nb-toast.ng-trigger',
	departmentListCss: 'tbody > tr > td > angular2-smart-table-cell > table-cell-view-mode > div > div.ng-star-inserted'
};
