export const JobSearchPage = {
	searchInputCss: '[placeholder="Job Search"]',
	filterButtonCss: 'div.col-auto > button[status="info"]',
	hideAllButtonCss: 'div.col-auto > button[status="danger"]',
	refreshButtonCss: 'div.col-auto > button[status="success"]',
	nbToggleCss: 'div.toggle',
	inputCheckBoxCss: 'input.native-input',
	viewButtonCss: 'ng2-st-tbody-custom > a > span.btn-primary',
	applyButtonCss: 'ng2-st-tbody-custom > a > span.btn-success',
	hideButtonCs: 'ng2-st-tbody-custom > a > span.btn-danger',
	confirmHideJobsButtonCss: 'nb-card-footer > button[status="primary"]',
	toastrMessageCss: 'nb-toast.ng-trigger'
};
