export const TimeTrackingForClient = {
    timerCss: 'div.header-container > nb-actions > nb-action.timer-action',
    timerBtnCss: 'nb-icon[icon="clock-outline"] > svg > g[data-name="Layer 2"] > g[data-name="clock"]',
    clientSelectCss: 'div.form-group > ga-contact-selector > ng-select[ng-reflect-placeholder="Select Client"]',
    dropdownOptionCss: 'div.ng-option',
    startTimerBtnCss: 'div.actions > div.toggle > button[class="btn btn-rounded btn-success"]',
	stopTimerBtnCss: 'div.actions > div.toggle > button[class="btn btn-rounded btn-danger"]',
    viewTimesheetBtnCss: 'div.view-log-button > a[href="#/pages/employees/timesheets"]',
    viewViewBtnCss: 'div.action-button > button[status="primary"]',
    clientNameCss: 'nb-card-body.custom-scroll > div > div',
    headerImgCss: 'div.header-container'

};