export const ProposalsPage = {
	gridButtonCss: 'div.layout-switch > button',
	registerProposalButtonCss: 'div.mb-3 > button[status="success"]',
	selectEmployeeDropdownCss: 'ga-employee-selector.employees',
	selectEmployeeDropdownOptionCss: 'div.ng-option',
	jobPostUrlInputCss: '[formcontrolname="jobPostUrl"]',
	dateInputCss: '[formcontrolname="valueDate"]',
	jobPostContentInputCss: '[formControlName="jobPostContent"]',
	proposalContentInputCss: '[formControlName="proposalContent"]',
	addTagsDropdownCss: '#addTags',
	tagsDropdownOption: 'div.ng-option',
	saveProposalButtonCss: 'nb-card-footer > button[status="success"]',
	selectTableRowCss: 'table > tbody > tr.angular2-smart-row',
	detailsButtonCss: 'div.mb-3 > button[status="info"]',
	editProposalButtonCss: 'button[status="info"]',
	markAsStatusButtonCss: 'div.mb-3 > button[status="warning"]',
	deleteProposalButtonCss: 'div.mb-3 > button[status="danger"]',
	confirmDeleteButtonCss: 'nb-card-footer > button[status="danger"]',
	confirmStatusButtonCss: 'nb-card-footer > button[status="success"]',
	cardBodyCss: 'nb-card-footer.text-right',
	backButtonCss: 'div.main > button[status="primary"]',
	toastrMessageCss: 'nb-toast.ng-trigger',
	verifyProposalCss: 'div.ng-star-inserted',
	acceptedProposalCss: 'ga-status-badge > div.badge-success',
	manageTemplatesBtnCss: 'div.mb-3 > button[status="info"]',
	addProposalTemplateBtnCss: 'div.mb-3 > button[status="success"]',
	editProposalTemplateBtnCss: 'div.mb-3 > button[status="info"]',
	deleteProposalTemplateBtnCss: 'div.mb-3 > button[status="danger"]',
	templateNameInputCss: 'div.form-group > input[formcontrolname="name"]',
	saveTemplateBtnCss: 'nb-card-footer.text-right > button[status="success"]',
	confirmDeleteTemplateBtnCss: 'nb-card-footer > button[status="primary"]',
	rejectDeleteBtnCss: 'nb-card-footer > button[status="warn"]',
	verifyProposalTemplateCss: 'div.ng-star-inserted',
	employeeMultiSelectCss: 'ga-employee-multi-select > nb-select > button.select-button',
	employeeMultiSelectDropdownOptionCss: '.option-list nb-option',
	headerTitleCss: 'ngx-header-title'
};
