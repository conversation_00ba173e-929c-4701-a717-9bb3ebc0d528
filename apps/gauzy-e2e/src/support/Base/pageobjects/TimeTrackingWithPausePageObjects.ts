export const TimeTrackingWithPausePage = {
	headerTextCss: 'nb-card-header > h4',
	topCardHeaderTextCss: 'div.col-sm-4 > nb-card > nb-card-body > p',
	bottomCardHeaderTextCss: 'div.col-sm-6 > nb-card > nb-card-header',
	recentActivitiesEmployeeCss: 'div.hour-lable > ngx-avatar > div.row > div.col > div.ng-star-inserted',
	projectsCss: 'nb-list > nb-lit-item > div.w-100 > div.row > div[class="col text-left"]',
	membersInfoCss: 'div.col > ngx-avatar > div[class="row align-items-center"] > div.col > div.ng-star-inserted',
	badgeCss: 'div.activity-percentage > badge[class="status-danger position-top position-right"]',
	timerCss: 'div.header-container > nb-actions > nb-action.timer-action',
	timerBtnCss: 'nb-icon[icon="clock-outline"] > svg > g[data-name="Layer 2"] > g[data-name="clock"]',
	startTimerBtnCss: 'div.actions > div.toggle > button[class="btn btn-rounded btn-success"]',
	stopTimerBtnCss: 'div.actions > div.toggle > button[class="btn btn-rounded btn-danger"]',
	clientSelectCss: 'div.form-group > ga-contact-selector > ng-select[ng-reflect-placeholder="Select Client"]',
	projectSelectCss: 'div.form-group > ga-project-selector > ng-select[ng-reflect-placeholder="Select Project"]',
	taskSelectCss: 'div.form-group > ga-task-selector > div > ng-select[ng-reflect-placeholder="Select Task"]',
	descriptionInputCss: 'div.form-group > textarea[name="description"]',
	dropdownOptionCss: 'div.ng-option',
	closeBtnCss: 'nb-icon > svg > g[data-name="Layer 2"] > g[data-name="close"]',
	viewTimesheetBtnCss: 'div.view-log-button > a[href="#/pages/employees/timesheets"]',
	manualBtnCss: 'nb-icon[icon="menu-outline"] > svg > g[data-name="Layer 2"] > g[data-name="menu"]',
	dateInputCss: 'div.form-group > input[name="date"]',
	startTimeSelectCss: 'ga-timer-picker[name="start_time"]',
	endTimeSelectCss: 'ga-timer-picker[name="end_time"]',
	addTimeBtnCss: 'div.time-manual > button[class="btn btn-success"]',
	todaySessionCss: 'div.time-count > span[class="today-time mt-2"]',
	tabBtnCss: 'li.route-tab > a.tab-link',
	verifyWorkCss: 'nb-list[role="list"] > nb-list-item[role="listitem"] > div.w-100 > div[class="row align-items-center"] > div[class="col text-left"]',
	verifyTimeCss: 'div[class="col-sm-4 col-md-3 col-lg"] > nb-card > nb-card-body > div.h1',
	toastrMessageCss: 'nb-toast.ng-trigger',
	verifyManualTimeCss: 'div[class="row border-bottom py-3 align-items-center ng-star-inserted"] > div.col',
    verifyTrackedTimeCss: 'div[class="row border-bottom py-3 align-items-center ng-star-inserted"] > div.col',
	deleteTimeBtnCss: 'div[class="action-button"] > button > nb-icon > svg[class="eva eva-trash-2-outline"]',
	confirmDialogBtnCss: 'nb-dialog-container > ngx-confirm > nb-card > nb-card-footer > button[class="mr-2 appearance-filled size-medium shape-rectangle status-primary nb-transition"]',
	headerImgCss: 'div.header-container'

};
