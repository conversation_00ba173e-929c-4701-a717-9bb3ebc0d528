export const JobsProposalsPage = {
	addButtonCss: 'div.mb-3 > button[status="success"]',
	selectEmployeeDropdownCss: 'button[class="select-button placeholder"]',
	selectEmployeeDropdownOptionCss: '.option-list nb-option',
	nameInputCss: '[formcontrolname="name"]',
	contentInputCss: '[formcontrolname="content"]',
	saveButtonCss: 'nb-card-footer.text-right > button[status="success"]',
	selectTableRowCss: 'table > tbody > tr.angular2-smart-row',
	editButtonCss: 'div.mb-3 > button[status="info"]',
	makeDefaultButtonCss: 'div.mb-3 > button[status="info"]',
	deleteButtonCss: 'div.mb-3 > button[status="danger"]',
	confirmDeleteButtonCss: 'nb-card-footer > button[status="primary"]',
	toastrMessageCss: 'nb-toast.ng-trigger',
	verifyProposalCss: 'div.ng-star-inserted'
};
