export const HumanResourcesPage = {
	employeeCss: 'div.block-info > div.row > div.col',
	employeeNameCss: 'span.employee-name',
	infoTextCss: 'div.info-text',
	chartDropdownCss: '[placeholder="Select chart"]',
	dropdownOptionCss: '.option-list nb-option',
	popupHeaderCss: 'div.records > h5',
	popupProfitHeaderCss: 'div.profit-history > h5',
	popupTableHederCss: 'div.angular2-smart-title > angular2-smart-table-title',
	cardBodyCss: 'nb-layout-column.ng-star-inserted'
};
