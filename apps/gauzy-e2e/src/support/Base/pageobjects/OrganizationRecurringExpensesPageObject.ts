export const OrganizationRecurringExpensesPage = {
	addButtonCss: 'nb-card-body > div > button[status="success"]',
	expenseDropdownCss: '#positionInput',
	dropdownOptionCss: 'div.ng-option',
	valueInputCss: '#valueInput',
	saveButtonCss: 'nb-card-footer.text-right > button[status="success"]',
	settingsButtonCss: 'g[data-name="settings-2"]',
	editButtonCss: 'div.block-settings g[data-name="edit"]',
	deleteButtonCss: 'g[data-name="close"]',
	deleteOnlyThisRadioButtonCss: 'nb-radio[value="current"]',
	confirmDeleteExpenseButtonCss: 'nb-card-footer > button[status="danger"]',
	toastrMessageCss: 'nb-toast.ng-trigger',
	verifyExpenseCss: 'div.expense > div.block-item'
};
