export const OrganizationEquipmentPage = {
	gridButtonCss: 'div.layout-switch > button',
	addButtonCss: 'div.mb-3 > button[status="success"]',
	nameInputCss: '[formcontrolname="name"]',
	typeInputCss: '[formcontrolname="type"]',
	serialNumberInputCss: '[formcontrolname="serialNumber"]',
	manufacturedYearInputCss: '[formcontrolname="manufacturedYear"]',
	initialCostInputCss: '[formcontrolname="initialCost"]',
	maxSharePeriodInputCss: '[formcontrolname="maxSharePeriod"]',
	selectEmployeeDropdownCss: '[formcontrolname="employees"]',
	selectEmployeeDropdownOptionCss: '.option-list nb-option',
	addTagsDropdownCss: '#addTags',
	tagsDropdownOption: 'div.ng-option',
	selectTableRowCss: 'table > tbody > tr.angular2-smart-row',
	saveButtonCss: 'nb-card-footer.text-right > button[status="success"]',
	editEquipmentButtonCss: 'div.mb-3 > button[status="info"]',
	deleteEquipmentButtonCss: 'div.mb-3 > button[status="danger"]',
	confirmDeleteButtonCss: 'nb-card-footer > button[status="danger"]',
	footerCss: 'nb-card-footer.text-right',
	equipmentSharingButtonCss: 'div.card-header-title > div.mr-2 > button[status="primary"]',
	selectEquipmentDropdownCss: '[formcontrolname="equipment"]',
	selectEquipmentDropdownOptionCss: '.option-list nb-option',
	selectPolicyDropdownCss: '[formcontrolname="equipmentSharingPolicyId"]',
	selectPolicyDropdownOptionCss: '.option-list nb-option',
	dateInputCss: '[formcontrolname="shareRequestDay"]',
	startDateInputCss: '[formcontrolname="shareStartDay"]',
	endDateInputCss: '[formcontrolname="shareEndDay"]',
	equipmentSharingPolicyButtonCss: 'nb-card-header.main-header > div.main-header > button[status="primary"]',
	policyDescriptionInputCss: '[formcontrolname="description"]',
	backButtonCss: 'g[data-name="arrow-back"]',
	toastrMessageCss: 'nb-toast.ng-trigger',
	verifyPolicyCss: 'tr.angular2-smart-row',
	verifySharingCss: 'div.ng-star-inserted',
	verifyEquipmentCss: 'ga-picture-name-tags > div > div.d-block',
	spinnerCss: 'nb-spinner'
};
