export const ApprovalRequestPage = {
	gridButtonCss: 'div.layout-switch > button',
	addApprovalRequestButtonCss: 'nb-card-body > div.mb-3 > button[status="success"]',
	editApprovalRequestButtonCss: 'div.mb-3 > button[status="info"]',
	deleteApprovalRequestButtonCss: 'div.mb-3 > button[status="danger"]',
	nameInputCss: '[formcontrolname="name"]',
	minCountInputCss: '[formcontrolname="min_count"]',
	approvalPolicyDropdownCss: '[formcontrolname="approvalPolicyId"]',
	checkApprovalPolicyDropdownOptionCss: '.option-list nb-option',
	usersMultiSelectCss: 'button[class="select-button placeholder"]',
	checkUsersMultiSelectCss: '.option-list nb-option',
	saveButtonCss: 'nb-card-footer > button[status="success"]',
	selectTableRowCss: 'table > tbody > tr.angular2-smart-row',
	approvalPolicyButtonCss: 'div.mr-2 > button[status="primary"]',
	descriptionInputCss: '[formcontrolname="description"]',
	backButtonCss: 'g[data-name="arrow-back"]',
	toastrMessageCss: 'nb-toast.ng-trigger',
	verifyApprovalPolicyCss: 'div.ng-star-inserted',
	verifyRequestCss: 'div.d-block',
	addTagsDropdownCss: '#addTags',
	tagsDropdownOption: 'div.ng-option',
	headerCss: 'nb-dialog-container > nb-card.main',
	approvalRefuseButtonCss: 'table > tbody > tr.angular2-smart-row > td.ng-star-inserted',
	approvalStatusCss: 'table > tbody > tr.angular2-smart-row',
	searchByNameInputCss: 'input[placeholder="Name"]',
	rowCss: 'table > tbody > tr.angular2-smart-row',
	tableBodyCss: 'table > tbody'
};
