export const ManageEmployeesPage = {
	gridButtonCss: 'div.layout-switch > button',
	inviteButtonCss: 'div.mb-3 > button[status="primary"]',
	emailsInputCss: '#emails',
	dateInputCss: '[formcontrolname="startedWorkOn"]',
	selectProjectDropdownCss: '#projectSelection',
	selectProjectDropdownOptionCss: 'div.ng-option > span.ng-option-label',
	sendInviteButtonCss: 'nb-card-footer.text-right > button[status="success"]',
	addEmployeeButtonCss: 'button[status="success"]',
	editEmployeeButtonCss: 'div.mb-3 > button[status="info"]',
	selectTableRowCss: 'table > tbody > tr.angular2-smart-row',
	deleteEmployeeButtonCss: 'div.mb-3 > button[status="danger"]',
	confirmDeleteButtonCss: 'nb-card-footer > button[status="danger"]',
	endWorkButtonCss: 'div.mb-3 > button[status="warning"]',
	endWorkDateInputCss: '[placeholder="Pick a Date"]',
	confirmEndWorkButtonCss: 'nb-card-footer.text-right > button[status="success"]',
	firstNameInputCss: '#firstName',
	lastNameInputCss: '#lastName',
	usernameInputCss: '#username',
	emailInputCss: '#email',
	passwordInputCss: '#password',
	addTagsDropdownCss: 'ng-select#addTags>div>span',
	tagsDropdownOption: 'div.ng-option',
	imgInputCss: '#inputImageUrl',
	nextButtonCss: 'button[type="submit"]',
	nextStepButtonCss: 'button[type="submit"]',
	lastStepButtonCss: 'button.mr-3.ml-3',
	saveEditButtonCss: 'button[status="success"]',
	backButtonCss: 'div.main > button[status="primary"]',
	usernameEditInputCss: '#username',
	emailEditInputCss: '#email',
	firstNameEditInputCss: '#firstName',
	lastNameEditInputCss: '#lastName',
	usernameEditSecondInputCss: '#usernameInput',
	emailEditSecondInputCss: '#emailInput',
	firstNameSecondEditInputCss: '#firstNameInput',
	lastNameSecondEditInputCss: '#lastNameInput',
	preferredLanguageDropdownCss: 'div.col-sm-6 > div.form-group > ngx-language-selector > ng-select',
	preferredLanguageOptionCss: 'ng-dropdown-panel.ng-dropdown-panel > div.ng-dropdown-panel-items div.ng-option',
	cardBodyCss: 'ga-employee-mutation nb-card-body',
	manageInvitesButtonCss: 'div.card-header-title > div.mr-2 > button[status="primary"]',
	copyLinkButtonCss: 'button[status="success"]',
	resendInviteButtonCss: 'button[status="warning"]',
	deleteInviteButtonCss: 'button[status="danger"]',
	confirmResendInviteButtonCss: 'nb-card-footer > button[status="success"]',
	confirmDeleteInviteButtonCss: 'nb-card-footer > button[status="danger"]',
	toastrMessageCss: 'nb-toast.ng-trigger',
	verifyEmployeeCss: 'div.d-block',
	verifyInviteCss: 'div.ng-star-inserted'
};
