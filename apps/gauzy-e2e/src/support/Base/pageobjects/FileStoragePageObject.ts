export const FileStoragePage = {
	headerTextCss: 'nb-card-header > h4',
	subheaderTextCss: 'nb-card-header > h6',
	selectDropdownCss:
		'nb-card-body > div.form-group > nb-select > button.select-button',
	saveButtonCss: 'nb-card-footer.text-right > button[status="success"]',
	optionDropdownCss: '.option-list nb-option',
	accesskeyIdInputCss: '[name="aws_access_key_id"]',
	secretAccessKeyInputCss: '[name="aws_secret_access_key"]',
	regionInputCss: '[name="aws_default_region"]',
	bucketInputCss: '[name="aws_bucket"]',
	toastrMessageCss: 'nb-toast.ng-trigger'
};
