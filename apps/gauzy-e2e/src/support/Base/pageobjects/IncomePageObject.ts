export const IncomePage = {
	gridButtonCss: 'div.layout-switch > button',
	addIncomeButtonCss: 'div.mb-3 > button[status="success"]',
	selectEmployeeDropdownCss: 'ga-employee-selector.employees',
	selectEmployeeDropdownOptionCss: 'div.ng-option',
	dateInputCss: '[formcontrolname="valueDate"]',
	organizationContactCss: '[formcontrolname="organizationContact"]',
	amountInputCss: '[formcontrolname="amount"]',
	addTagsDropdownCss: '#addTags',
	tagsDropdownOption: 'div.ng-option',
	notesInputCss: '[formcontrolname="notes"]',
	saveIncomeButtonCss: 'nb-card-footer > button[status="success"]',
	selectTableRowCss: 'table > tbody > tr.angular2-smart-row',
	editIncomeButtonCss: 'div.mb-3 > button[status="info"]',
	deleteIncomeButtonCss: 'div.mb-3 > button[status="danger"]',
	confirmDeleteButtonCss: 'nb-card-footer > button[status="danger"]',
	cardBodyCss: 'nb-card-footer.text-right',
	toastrMessageCss: 'nb-toast.ng-trigger',
	verifyIncomeCss: 'div.ng-star-inserted'
};
