export const OrganizationVendorsPage = {
	gridButtonCss: 'div.layout-switch > button',
	addVendorButtonCss: 'div.mb-3 > button[status="success"]',
	nameInputCss: '[formcontrolname="name"]',
	phoneInputCss: '[formcontrolname="phone"]',
	emailInputCss: '[formcontrolname="email"]',
	websiteInputCss: '[formcontrolname="website"]',
	addTagsDropdownCss: '#addTags',
	tagsDropdownOption: 'div.ng-option',
	saveVendorButtonCss: 'nb-card-footer > button[status="success"]',
	editVendorButtonCss: 'nb-actions > nb-action[icon="edit"]',
	deleteVendorButtonCss: 'nb-actions > nb-action[icon="close"]',
	confirmDeleteButtonCss: 'nb-card-footer > button[status="danger"]',
	toastrMessageCss: 'nb-toast.ng-trigger',
	verifyVendorCss: 'div.float-left > div.ng-star-inserted > span.mb-1'
};
