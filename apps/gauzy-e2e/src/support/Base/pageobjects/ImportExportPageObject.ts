export const ImportExportPage = {
	headerTextCss: 'nb-card-header > div.h4',
	subheaderTextCss: 'ngx-import-export > nb-card > nb-card-header > div.h4',
	infoTextCss: 'nb-card-header > div.info',
	importBtnCss:
		'nb-card-body > div.row > div[class="col-sm-12 col-md-12 m-2"] > button[status="success"]',
	exportBtnCss:
		'nb-card-body > div.row > div[class="col-sm-12 col-md-12 m-2"] > button[status="info"]',
	migrateBtnCss:
		'nb-card-body > div.row > div[class="col-sm-12 col-md-12 m-2"] > button[status="warning"]',
	downloadTemplatesButtonCss:
		'nb-card-body > div.row > div[class="col-sm-12 col-md-12 m-2"] > button[status="basic"]',
	passwordInputCss: 'div[class="nb-form-control-container nb-form-field-control-with-suffix"] > input[type="password"]',
	okBtnCss: 'nb-card-footer > button[status="success"]',
	cancelBtnCss: 'nb-card-footer > button[status="danger"]',
	browseFilesBtnCss: 'div.row > div.col-md-3 > button[status="primary"]',
	fileInputCss: 'div.row > div.col-md-3 > input[type="file"]',
	importFileBtnCss: 'div[class="col-md-9"] > div > button[class="btn btn-success btn-s"]',
	removeFileBtnCss: 'div[class="col-md-9"] > div > button[class="btn btn-danger btn-s"]',
	verifyFileNameCss: 'table.table > tbody > tr > td > strong',
	verifyUploadStatusCss: 'nb-icon > svg[class="eva eva-checkmark-circle-outline"] > g[data-name="Layer 2"] > g[data-name="checkmark-circle"]'
};
