export const CustomersPage = {
	gridButtonCss: 'div.layout-switch > button',
	addButtonCss: 'div.mb-3 > button[status="success"]',
	editButtonCss: 'div.mb-3 > button[status="success"]',
	inviteButtonCss: 'div.mb-3 > button[status="primary"]',
	deleteButtonCss: 'div.mb-3 > button[status="danger"]',
	nameInputCss: '#name',
	emailInputCss: '#email',
	budgetInputCss: 'input[formcontrolname="budget"]',
	lastStepBtnCss: 'div.step-content > form > div > button[status="success"]',
	phoneInputCss: '[formcontrolname="primaryPhone"]',
	countryDropdownCss: 'ga-country div.form-group nb-select',
	cityInputCss: '[formcontrolname="city"]',
	postCodeInputCss: '[formcontrolname="postcode"]',
	streetInputCss: '[formcontrolname="address"]',
	projectsDropdownCss: '[formcontrolname="projects"]',
	projectsDropdownOptionCss: 'div.ng-option',
	usersMultiSelectCss: 'div.form-group ga-employee-multi-select nb-select',
	dropdownOptionCss: '.option-list nb-option',
	addTagsDropdownCss: '#addTags',
	tagsDropdownOption: 'div.ng-option',
	websiteInputCss: '[formcontrolname="website"]',
	cardBodyCss: 'div.contact-container',
	saveButtonCss: 'div.form-group > button[status="success"]',
	nextButtonCss: 'div.fields > div > button[status="success"]',
	finishButtonCss: 'div.fields > div > button[status="success"]',
	selectTableRowCss: 'table > tbody > tr.angular2-smart-row',
	confirmDeleteButtonCss: 'nb-card-footer > button[status="danger"]',
	customerNameCss: '[formcontrolname="name"]',
	customerPhoneCss: '[formcontrolname="primaryPhone"]',
	customerEmailCss: '#emailInput',
	toastrMessageCss: 'nb-toast.ng-trigger',
	saveInviteButtonCss: 'nb-card-footer > button[status="success"]',
	verifyCustomerCss: 'div.d-block'
};
