export const ManageUserInvitesPage = {
	manageInvitesButtonCss: 'div.card-header-title > div.mr-2 > button[status="primary"]',
	gridButtonCss: 'div.layout-switch > button',
	selectTableRowCss: 'table > tbody > tr.angular2-smart-row',
	copyLinkButtonCss: 'div.mb-3 > button[status="success"]',
	resendInviteButtonCss: 'div.mb-3 > button[status="warning"]',
	deleteInviteButtonCss: 'div.mb-3 > button[status="danger"]',
	cancelResendInviteButtonCss: 'nb-card-footer > button[status="danger"]',
	confirmResendInviteButtonCss: 'nb-card-footer > button[status="success"]',
	cancelDeleteInviteButtonCss: 'nb-card-footer > button[status="info"]',
	confirmDeleteInviteButtonCss: 'nb-card-footer > button[status="danger"]',
	toastrMessageCss: 'nb-toast.ng-trigger',
	inviteButtonCss: 'div.mb-3 > button[status="primary"]',
	emailInputCss: '#emails',
	dateInputCss: 'input[formcontrolname="startedWorkOn"]',
	saveButtonCss: 'nb-card-footer.text-right > button[status="success"]',
	verifyEmailCss: 'table-cell-view-mode > div > div.ng-star-inserted',
	rolesInputCss: 'nb-select[ng-reflect-placeholder="Select Role"]',
	rolesDropdownCss: 'nb-option',
	searchEmailInputCss: 'input-filter > input[placeholder="Email"]',
	clientsTableData: 'td.ng-star-inserted',
	clientsTableRow: 'tr[class="angular2-smart-row ng-star-inserted"]'
};
