export const OrganizationProjectsPage = {
	gridButtonCss: 'div.layout-switch > button',
	selectTableRowCss: 'table > tbody > tr.angular2-smart-row',
	tagsSelectCss: '#addTags',
	tagsSelectOptionCss: '[type="checkbox"]',
	closeTagsMultiSelectDropdownCss: '.ng-select-container > .ng-arrow-wrapper',
	selectEmployeeMultiSelectCss: 'button[class="select-button placeholder"]',
	selectEmployeeDropdownOptionCss: '.option-list nb-option',
	requestNewProjectButtonCss: 'button[status="success"]',
	projectNameInputCss: '#name',
	codeInputCss: '#code',
	budgetInputCss: '[formcontrolname="budget"]',
	projectDescriptionCss: '[formcontrolname="description"]',
	tabButtonCss: 'span.tab-text',
	saveProjectButtonCss: 'div.form-group > button[status="success"]',
	editProjectButtonCss: 'button[status="info"]',
	deleteProjectButtonCss: 'button[status="danger"]',
	confirmDeleteButtonCss: 'nb-card-footer > button[status="danger"]',
	colorInputCss: '[formcontrolname="color"]',
	footerCss: 'nb-card-body > div.form-group',
	toastrMessageCss: 'nb-toast.ng-trigger',
	verifyProjectCss: 'ga-picture-name-tags > div > div.d-block'
};
