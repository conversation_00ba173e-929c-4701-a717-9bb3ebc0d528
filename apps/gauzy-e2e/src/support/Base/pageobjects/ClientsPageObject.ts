export const ClientsPage = {
	gridButtonCss: 'div.layout-switch > button',
	addButtonCss: 'div.mb-3 > button[status="success"]',
	editButtonCss: 'div.mb-3 > button[status="success"]',
	inviteButtonCss: 'div.mb-3 > button[status="primary"]',
	deleteButtonCss: 'div.mb-3 > button[status="danger"]',
	nameInputCss: '#name',
	emailInputCss: '#email',
	lastStepBtnCss: 'div.step-content > form > div > button[status="success"]',
	phoneInputCss: '[formcontrolname="primaryPhone"]',
	budgetInputCss: 'input[formcontrolname="budget"]',
	countryDropdownCss: 'ga-country > div.form-group > nb-select',
	cityInputCss: '[formcontrolname="city"]',
	postCodeInputCss: '[formcontrolname="postcode"]',
	streetInputCss: '[formcontrolname="address"]',
	projectsDropdownCss: '[formcontrolname="projects"]',
	projectsDropdownOptionCss: 'div.ng-option',
	usersMultiSelectCss: 'div.form-group ga-employee-multi-select nb-select',
	dropdownOptionCss: '.option-list nb-option',
	addTagsDropdownCss: '#addTags',
	tagsDropdownOption: 'div.ng-option',
	websiteInputCss: '[formcontrolname="website"]',
	cardBodyCss: 'div.contact-container',
	saveButtonCss: 'div.form-group > button[status="success"]',
	nextButtonCss: 'div.fields > div > button[status="success"]',
	selectTableRowCss: 'table > tbody > tr.angular2-smart-row',
	confirmDeleteButtonCss: 'nb-card-footer > button[status="danger"]',
	clientNameCss: '[formcontrolname="name"]',
	clientPhoneCss: '[formcontrolname="primaryPhone"]',
	clientEmailCss: '#emailInput',
	toastrMessageCss: 'nb-toast.ng-trigger',
	saveInviteButtonCss: 'nb-card-footer > button[status="success"]',
	verifyClientCss: 'div.d-block',
	searchNameInputCss: 'input-filter > input[placeholder="Name"]',
	clientsTableData: 'td.ng-star-inserted',
	clientsTableRow: 'tr[class="angular2-smart-row ng-star-inserted"]',
	viewButtonCss: 'div.mb-3 > button[status="info"]',
	clientNameViewCss: 'div.profile-user-title-name',
	clientTypeViewCss: 'div.profile-user-title-type',
	backBtn: 'ngx-back-navigation > div > button[status="primary"]'
};
