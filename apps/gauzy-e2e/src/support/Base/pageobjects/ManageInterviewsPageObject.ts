export const ManageInterviewsPage = {
	addInterviewButtonCss: 'nb-card-header > div > button[status="primary"]',
	candidateDropdownCss:
		'div.step-content > div.form-group > ga-candidate-select > nb-select > button[class="select-button placeholder"]',
	candidateDropdownOptionCss: '.option-list nb-option',
	titleInputCss: '#inputTitle',
	dateInputCss: 'input[name="date"]',
	employeeMultiselectCss:
		'ga-candidate-interview-form > form > div.form-group > ga-employee-multi-select > nb-select > button[class="select-button placeholder"]',
	employeeMultiselectDropdownOptionCss: 'nb-option-list > ul.option-list > nb-option',
	radioButtonCss: 'nb-radio > label > span.inner-circle',
	locationInputCss: '#location',
	noteInputCss: '#note',
	nextButtonCss: 'div.text-right > button[status="success"]',
	nextStepButtonCss: 'div.button-container > button[status="success"]',
	notifyCandidateCss: 'div.notify-checkboxes nb-checkbox > label > span.custom-checkbox',
	saveButtonCss: 'div.button-container > button[status="success"]',
	verifyCandidateCss: 'div.d-block',
	toastrMessageCss: 'nb-toast.ng-trigger',
	scrollElementCss: 'nb-card-body.stepper',
	nameFilterInputCss: 'input[placeholder="Candidate"]',
	titleFilterInputCss: 'input[placeholder="Title"]',
	nameTableCellCss: 'table > tbody > tr.angular2-smart-row:first-of-type > td:nth-child(2)',
	titleTableCellCss: 'table > tbody > tr.angular2-smart-row:first-of-type > td:nth-child(3)',
	tableOptionsButtonsCss: 'table > tbody > tr:first-of-type > td:last-of-type nb-icon',
	addInterviewerDropdownCss: '[ng-reflect-placeholder="Add Interviewer"]',
	ratingInputCss: 'ga-star-rating-input > div.rating div.rating-star > span.rating-star-icon',
	radioGroupCss: 'nb-radio-group > nb-radio',
	radioGroupInputCss: 'nb-radio-group > nb-radio input.native-input',
	feedbackDescriptionCss: '#description',
	feedbackSaveButtonCss: 'nb-card button[status=success]',
	interviewCheckboxFiltersCss: 'nb-checkbox[status="warning"]',
	interviewCheckboxFiltersInputsCss: 'nb-checkbox[status="warning"] input.native-input',
	notesTableCellCss: 'table > tbody > tr > td:nth-child(9)',
	archiveInterviewOkButtonCss: 'nb-card-footer > button[status="danger"]',
	archiveBadgeCss: 'ga-interview-actions div.badges > div.badge',
	deleteButtonCss: 'ga-delete-interview button[status="danger"]',
	customCheckBoxCss: 'nb-checkbox[status="warning"] > span.custom-checkbox'
};
