export const AddTaskPage = {
	gridButtonCss: 'div.layout-switch > button',
	addTaskButtonCss: 'button[status="success"]',
	selectProjectDropdownCss: 'ga-project-selector.ng-untouched > .ng-select-taggable > .ng-select-container > .ng-value-container > .ng-input > input',
	selectProjectDropdownOptionCss: 'div.ng-dropdown-panel-items.scroll-host',
	editTaskButtonCss: '.ng-star-inserted .appearance-filled.ng-star-inserted.nb-transition:nth-of-type(2)',
	duplicateTaskButtonCss: '.ng-star-inserted .nb-transition:nth-of-type(3)',
	deleteTaskButtonCss: '.appearance-filled.icon-end.ng-star-inserted',
	selectTableRowCss: 'table > tbody > tr.angular2-smart-row',
	selectTableFirstRowCss: 'tr.angular2-smart-row.ng-star-inserted.selected',
	tagsSelectCss: '#addTags',
	tagsSelectOptionCss: '[type="checkbox"]',
	closeTagsMultiSelectDropdownCss: '.ng-select-container > .ng-arrow-wrapper',
	confirmDuplicateOrEditTaskButtonCss: 'nb-card-footer > button[status="success"]',
	confirmEditTaskButtonCss: '',
	confirmDeleteTaskButtonCss: 'button.mr-3.ml-3',
	addTitleInputCss: '[formControlName="title"]',
	selectEmployeeMultiSelectCss: 'button[class="select-button placeholder"]',
	selectEmployeeDropdownOptionCss: '.option-list nb-option',
	dueDateInputCss: '[formControlName="dueDate"]',
	estimateDaysInputCss: '[formControlName="estimateDays"]',
	estimateHoursInputCss: '[formControlName="estimateHours"]',
	estimateMinsInputCss: '[formControlName="estimateMinutes"]',
	descriptionTextareaCss: '[formControlName="description"]',
	saveNewTaskButtonCss: 'nb-card-footer > button[status="success"]',
	verifyTextCss:
		'[ng-reflect-settings] [ng-reflect-ng-class="[object Object]"]:nth-of-type(1) .ng-star-inserted:nth-of-type(2) div:nth-of-type(1) > .ng-star-inserted:nth-of-type(2)',
	toastrMessageCss: 'nb-toast.ng-trigger',
	searchTitleInputCss: 'ng-component > input[placeholder="Title"]'
};
