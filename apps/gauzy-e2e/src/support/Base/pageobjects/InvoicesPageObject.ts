export const InvoicesPage = {
	gridButtonCss: 'div.layout-switch > button',
	tabButtonCss: 'ul.tabset > li.tab  > a.tab-link',
	addButtonCss: 'div.actions-container > button[status="success"]',
	editButtonCss: 'div.btn-group > button[status="info"]',
	setStatusButtonCss: 'div.btn-group > nb-select[status="info"]',
	viewButtonCss: 'div.actions-container > button[status="info"]',
	popoverButtonCss: 'div.popover-container > button[status="info"]',
	deleteButtonCss: 'div.popover-container > button[status="danger"]',
	addTagsDropdownCss: '#addTags',
	tagsDropdownOption: 'div.ng-option',
	discountInputCss: '#inputDiscountValue',
	discountTypeDropdownCss: '[formcontrolname="discountType"]',
	dropdownOptionCss: '.option-list nb-option',
	organizationContactDropdownCss: 'div.col-sm-6 > ga-contact-select > ng-select',
	contactOptionCss: 'div.ng-option',
	taxInputCss: '#inputTax',
	taxTypeDropdownCss: '[formcontrolname="taxType"]',
	invoiceTypeDropdownCss: '[formcontrolname="invoiceType"]',
	generateItemsButtonCss: 'div.buttons > button[status="success"]',
	selectEmployeeCss: 'div.form-group ga-employee-multi-select nb-select',
	saveAsDraftButtonCss: 'nb-card-footer > button[status="success"]',
	emailInputCss: '#email',
	moreButtonCss: 'div.actions-container > div > button > nb-icon[icon="more-vertical-outline"]',
	tableRowCss: 'table > tbody > tr.angular2-smart-row',
	toastrMessageCss: 'nb-toast.ng-trigger',
	cardBodyCss: 'nb-card-header.d-flex',
	backButtonCss: 'g[data-name="arrow-back"]',
	deleteItemCss: 'i.nb-trash',
	confirmButtonCss: 'nb-card-footer.text-right > button[status="success"]',
	confirmDeleteButtonCss: 'nb-card-footer > button[status="danger"]',
	verifyInvoiceCss: 'tr.ng-star-inserted > td',
	draftBadgeCss: 'div.badge-warning',
	successBadgeCss: 'div.badge-success',
	emailCardCss: 'nb-card-body.invoice-email-body',
	inputInvoiceNumberCss: '#inputInvoiceNumber',
	estimateDateCss: '#inputInvoiceDate',
	dueDateInputCss: '#inputDueDate',
	totalValueInputCss: '#inputTotalValue',
	currencySelectCss: '#currencySelect',
	inputStatusCss: '#inputStatus',
	searchButtonCss: 'div.col-sm-4 > button[status="success"]',
	resetButtonCss: 'div.col-sm-4 > button[status="info"]'
};
