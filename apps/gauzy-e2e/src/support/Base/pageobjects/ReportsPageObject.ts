export const ReportsPage = {
	headerTextCss: 'nb-card-header > h4',
	subheaderTextCss: 'div.mb-4 > h5',
	titleCss: 'div.align-items-center > h6',
	checkboxCss: 'input.native-input',
	sidebarBtnCss: 'span.menu-title',
	totalHoursCss:
		'ga-daily-statistics > div.row > div.col-sm-4 > nb-card > nb-card-body > div.h1',
	sliderBtnCss:
		'div[class="col-auto filter-item ml-auto pl-1 ng-star-inserted"] > button[status="basic"]',
	sliderCss: 'ng5-slider.ng5-slider',
	timeAndActivityProjectCss:
		'div[class="col-sm-2 project-name"] > span.ng-star-inserted',
	amountOwedEmployeeCss:
		'div[class="col-sm-3 employee-name"] > ngx-avatar > div[class="row align-items-center"] > div.col > a',
	projectsBudgetsProjectCss:
		'div[class="col-3 project-name"] > span.ng-star-inserted',
	clientsBudgetsClientCss:
		'div[class="col-3 client-name"] > span.ng-star-inserted',
	progressContainerCss: 'div.progress-container > div.progress-value > span.ng-star-inserted'
};
