export const AppsIntegrationsPage = {
	verifyHeaderCss: 'ngx-integrations-list > nb-card > nb-card-header > h4',
	dropdownCss: 'div.integration-filters > nb-select > button.select-button',
	dropdownOptionCss: '.option-list nb-option',
	searchInputCss: '#integrationSearch',
	clearAllButtonCss: 'button[status="danger"]',
	integrationCss: 'div.integrations-list > a.list-item',
	verifyCardHeaderCss: 'nb-card-header.d-flex > h5',
	backButtonCss: 'div.main > button[status="primary"]',
	hubstaffClientIdInputCss: '#client_id',
	upworkApiKeyInputCss: '#consumerKey',
	upworkSecretInputCss: '#consumerSecret'
};
