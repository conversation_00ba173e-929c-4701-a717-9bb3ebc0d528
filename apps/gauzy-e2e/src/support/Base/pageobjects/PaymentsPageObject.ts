export const PaymentsPage = {
	gridButtonCss: 'div.layout-switch > button',
	addPaymentButtonCss: 'div.mb-3 > button[status="success"]',
	dateInputCss: '[formcontrolname="paymentDate"]',
	vendorInputCss: '[formcontrolname="vendor"]',
	projectDropdownCss: '[formcontrolname="project"]',
	projectDropdownOptionCss: 'div.ng-option',
	addTagsDropdownCss: '#addTags',
	tagsDropdownOption: 'div.ng-option',
	paymentMethodDropdownCss: '[formcontrolname="paymentMethod"]',
	paymentMethodDropdownOptionCss: '.option-list nb-option',
	amountInputCss: '#inputAmount',
	noteInputCss: '#inputNote',
	saveExpenseButtonCss: 'nb-card-footer > button[status="success"]',
	selectTableRowCss: 'table > tbody > tr.angular2-smart-row',
	editPaymentButtonCss: 'div.mb-3 > button[status="info"]',
	deletePaymentButtonCss: 'div.mb-3 > button[status="danger"]',
	confirmDeleteButtonCss: 'nb-card-footer > button[status="danger"]',
	cardBodyCss: 'nb-card-footer.text-right',
	toastrMessageCss: 'nb-toast.ng-trigger',
	verifyPaymentCss: 'div.ng-star-inserted',
	sidebarBtnCss: 'span.menu-title',
	accountingPaymentsSidebarBtnCss: 'a[href="#/pages/accounting/payments"] > span.menu-title',
	reportsPaymentsSidebarBtnCss: 'a[href="#/pages/reports/payments"] > span.menu-title',
	paymentTableCellCss: 'div[class="col-sm-2 project-name"] > span.ng-star-inserted',
	amountTableCellCss: 'div[class="col text-center day-col"]',
	groupByCss: 'div.ml-3 > nb-select > button.select-button',
	dropdownOptionCss: '.option-list nb-option'
};
