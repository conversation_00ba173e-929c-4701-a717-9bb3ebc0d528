export const OrganizationTeamsPage = {
	gridButtonCss: 'div.layout-switch > button',
	addTeamButtonCss: 'nb-card-body > div > button[status="success"]',
	teamNameInputCss: '[placeholder="Team Name"]',
	tagsSelectCss: '#addTags',
	tagsSelectOptionCss: 'div.ng-option',
	selectTableRowCss: 'table > tbody > tr.angular2-smart-row',
	cardBodyCss: 'nb-card-body',
	employeeMultiSelectCss: '[ng-reflect-placeholder="Add or Remove Team Members"]',
	managerMultiSelectCss: '[ng-reflect-placeholder="Add or Remove Team Managers"]',
	selectDropdownOptionCss: '.option-list nb-option',
	saveButtonCss: 'nb-card-footer > button[status="success"]',
	editButtonCss: 'nb-card-body > div > button[status="info"]',
	deleteButtonCss: 'nb-card-body > div > button[status="danger"]',
	confirmDeleteButtonCss: 'nb-card-footer > button[status="danger"]',
	toastrMessageCss: 'nb-toast.ng-trigger',
	verifyTeamCss: 'div.ng-star-inserted'
};
