export const GoalsGeneralSettingsPage = {
	headerTextCss: 'nb-card-header.header-selector-wrapper > div > h4',
	subheaderTextCss: 'div.col-md-4 > p.font-weight-bold',
	nbToggleCss: 'div.toggle',
	checkboxCss: 'input.native-input',
	goalsInpuCss: '#max-goals',
	keyResultInputCss: '#max-key-result',
	objectivesDropdownCss: '[formcontrolname="canOwnObjectives"]',
	keyResultDropdownCss: '[formcontrolname="canOwnKeyResult"]',
	optionDropdownCss: '.option-list nb-option',
	toastrMessageCss: 'nb-toast.ng-trigger'
};
