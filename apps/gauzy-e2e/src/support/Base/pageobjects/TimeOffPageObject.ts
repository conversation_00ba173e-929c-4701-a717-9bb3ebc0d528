export const TimeOffPage = {
	requestButtonCss: 'div.col-4 > button[status="primary"]',
	employeeDropdownCss: 'ngx-time-off-request-mutation > nb-card.main ga-employee-selector.employees',
	employeeDropdownOptionCss: 'div.ng-option[role="option"]',
	timeOffPolicyDropdownCss: 'nb-select[id="policy"]',
	timeOffPolicyDropdownOptionCss: '.option-list nb-option',
	startDateInputCss: '[formControlName="start"]',
	endDateInputCss: '[formControlName="end"]',
	descriptionInputCss: '[formControlName="description"]',
	saveRequestButtonCss: 'nb-card-footer.text-right > button[status="success"]',
	addHolidayButtonCss: 'div.col-4 > button[status="info"]',
	selectHolidayDropdownOptionCss: '.option-list nb-option',
	selectEmployeeCss: 'button[class="select-button placeholder"]',
	selectEmployeeDropdownOptionCss: '.option-list nb-option',
	startHolidayDateCss: '[formControlName="start"]',
	endHolidayDateCss: '[formControlName="end"]',
	saveButtonCss: 'nb-card-footer > button[status="success"]',
	selectTableRowCss: 'table > tbody > tr.angular2-smart-row',
	deleteTimeOfRequestButtonCss: 'div.actions-container > button[status="danger"]',
	confirmDeleteTimeOfButtonCss: 'nb-card-footer > button[status="danger"]',
	editTimeOfRequestButtonCss: 'div.actions-container > button[status="primary"]',
	denyTimeOffRequestButtonCss: 'div.actions-container > button[status="warning"]',
	approveTimeOffRequestButtonCss: 'div.actions-container > button[status="success"]',
	timeOffSettingsButtonCss: 'div.mb-3 > div > button[status="primary"]',
	addNewPolicyButtonCss: 'div.mb-3 > button[status="success"]',
	editPolicyButtonCss: 'div.mb-3 > button[status="info"]',
	deletePolicyButtonCss: 'div.mb-3 > button[status="danger"]',
	addNewPolicyInputCss: '[placeholder="Policy Name"]',
	backButtonCss: 'div.main > button[status="primary"]',
	toastrMessageCss: 'nb-toast.ng-trigger',
	verifyPolicyCss: 'div.ng-star-inserted',
	holidayNameSelectCss: 'nb-select[ng-reflect-placeholder="Select Holiday name"]',
	employeeSelectorCss: 'nb-select[ng-reflect-placeholder="Add or Remove Employees"]',
	timeOffPolicySelectorCss: 'nb-select[ng-reflect-placeholder="Select Time-off Policy"]'
};
