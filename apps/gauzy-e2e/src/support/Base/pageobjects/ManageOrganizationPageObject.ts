export const ManageOrganizationPage = {
	gridButtonCss: 'div.layout-switch > button',
	manageButtonCss: 'div.mb-3 > button[status="info"]',
	tabButtonCss: 'ul.route-tabset > li > a.tab-link',
	languageDropdownCss: '#lang',
	dropdownOptionCss: '.option-list nb-option',
	nameInputCss: '#nameInput',
	codeInputCss: '#codeInput',
	selectTableRowCss: 'table > tbody > tr.angular2-smart-row',
	saveButtonCss: 'div.actions > button[status="success"]',
	toastrMessageCss: 'nb-toast.ng-trigger',
	organizationNameFieldCss: '#nameInput',
	currencyFieldCss: '#currencySelect',
	officialNameFieldCss: '#officialNameInput',
	taxFieldCss: '#taxIdInput',
	verifyOrganizationCss: 'div.d-block',
	countryDropdownCss: 'ga-country div.form-group nb-select',
	cityInputCss: '#cityInput',
	postCodeInputCss: '#postcodeInput',
	streetInputCss: '#addressInput',
	bonusTypeDropdownCss: '[formcontrolname="bonusType"]',
	bonusPercentageCss: '[formcontrolname="bonusPercentage"]',
	expiryPeriodInputCss: '[formcontrolname="inviteExpiryPeriod"]',
	dateTypeDropdownCss: '[formcontrolname="defaultValueDateType"]',
	startOfWeekDropdownCss: '#startWeekOnSelect',
	regionCodeDropdownCss: '[formcontrolname="regionCode"]',
	numberFormatDropdownCss: '[formcontrolname="numberFormat"]',
	dateFormatDropdownCss: '[formcontrolname="dateFormat"]',
	timeZoneDropdownCss: '[formcontrolname="timeZone"]',
	timeZoneDropdownOptionCss: 'div.ng-option',
	tableRowCss: 'table > tbody > tr.angular2-smart-row',
	cardBodyCss: 'nb-layout-column.ng-star-inserted'
};
