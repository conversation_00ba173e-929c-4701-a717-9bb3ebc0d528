export const OrganizationInventoryPage = {
	gridButtonCss: 'div.layout-switch > button',
	addButtonCss: 'div.mb-3 > button[status="success"]',
	languageDropdownCss: 'div[class="form-group mb-3"] > ngx-language-selector > ng-select',
	dropdownOptionCss: '.option-list nb-option',
	nameInputCss: 'input[id="name"]',
	codeInputCss: 'input[id="codeInput"]',
	productTypeDropdownCss: 'div[class="col-sm-6 mb-4"] > nb-select[id="productTypeId"]',
	productCategoryDropdownCss: 'div[class="col-sm-6 mb-4"] > nb-select[id="productCategoryId"]',
	descriptionInputCss: '#description',
	selectTableRowCss: 'table > tbody > tr.angular2-smart-row',
	saveButtonCss: 'nb-card-footer.text-right > button[status="success"]',
	editButtonCss: 'div.mb-3 > button[status="info"]',
	deleteButtonCss: 'div.mb-3 > button[status="danger"]',
	confirmDeleteButtonCss: 'nb-card-footer > button[status="danger"]',
	addCategoryOrTypeButtonCss: 'div.mb-3 > button[status="success"]',
	backButtonCss: 'g[data-name="arrow-back"]',
	backFromInventoryButtonCss: 'div.navigate > button[status="primary"]',
	backFromCategoryButtonCss: 'nb-card-header.d-flex div.navigate button[status="primary"]',
	toastrMessageCss: 'nb-toast.ng-trigger',
	verifyTypeCss: 'div.ng-star-inserted',
	verifyCategoryCss: 'div.ng-star-inserted',
	verifyInventoryCss: 'ng-component > div > div.d-block',
	merchantsOrWarehousesBtnCss: 'div.mb-3 > button[status="warning"]',
	addMerchantBtnCss: 'div.mb-3 > button[status="success"]',
	merchantNameInputCss: 'div.form-group > input[id="name"]',
	merchantCodeInputCss: 'div.form-group > input[id="code"]',
	merchantEmailInputCss: 'div.form-group > input[id="email"]',
	merchantPhoneInputCss: 'div.form-group > input[id="phone"]',
	merchantCurrencySelectCss: 'div.form-group > nb-select[id="currencySelect"] > button.select-button',
	merchantFaxInputCss: 'div[class="col-md-6 col-sm-12"] > input[id="fax"]',
	merchantFiscalInfoInputCss: 'div.form-group > input[formcontrolname="fiscalInformation"]',
	merchantWebsiteInputCss: 'div.form-group > input[id="website"]',
	tagsSelectCss: 'ga-tags-color-input > div > ng-select[id="addTags"]',
	tagsDropdownOptionCss: 'div.ng-option',
	merchantDescriptionInputCss: 'div.form-group > textarea[formcontrolname="description"]',
	merchantActiveCheckboxCss: 'nb-checkbox > label > span.custom-checkbox',
	merchantNextBtnCss: 'div.row > div[class="form-group col-4"] > button[status="primary"]',
	countrySelectCss: 'div.col-4 > ga-country > div.form-group > nb-select > button.select-button',
	cityInputCss: 'div.form-group > input[id="cityInput"]',
	postCodeInputCss: 'div.form-group > input[id="postcodeInput"]',
	addressInputCss: 'div.form-group > input[id="addressInput"]',
	saveMerchantBtnCss: 'div.row > div[class="form-group col-4"] > button[status="success"]',
	editMerchantBtnCss: 'div.mb-3 > button[status="info"]',
	deleteMerchantBtnCss: 'div.mb-3 > button[status="danger"]',
	addWarehouseBtnCss: 'div.mb-3 > button[status="success"]',
	warehousesSelectCss: 'div[class="col-md-9 col-sm-12 pl-5"] > div.form-group > nb-select > button.select-button',
	warehouseNameInputCss: 'div.form-group > input[id="name"]',
	warehouseCodeInputCss: 'div.form-group > input[id="code"]',
	warehouseEmailInputCss: 'div.form-group > input[id="email"]',
	warehouseActiveCheckboxCss: 'nb-checkbox > label > span.custom-checkbox',
	warehouseDescriptionInputCss: 'div.form-group > textarea[id="description"]',
	tabBtnCss: 'div[class="step ng-star-inserted"] > div.label > span.ng-star-inserted',
	saveWarehouseBtnCss: 'nb-card-footer.text-right > button[status="success"]',
	editWarehouseBtnCss: 'div.mb-3 > button[status="info"]',
	deleteWarehouseBtnCss: 'div.mb-3 > button[status="danger"]',
	sidebarBtnCss: 'span.menu-title',
	inventorySidebarBtnCss: 'a[href="#/pages/organization/inventory"] > span.menu-title',
	verifyMerchantWarehouseCss: 'div.d-block'
};
