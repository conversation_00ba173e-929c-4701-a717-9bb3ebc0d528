export const EditEmployeePage = {
	employeeCss: 'div.block-info > div.row > div.col',
	editButtonCss: 'nb-icon.edit-icon',
	usernameInputCss: '#usernameInput',
	tabButtonCss: 'nb-route-tabset > ul.route-tabset > li.route-tab',
	firstNameInputCss: '#firstNameInput',
	lastNameInputCss: '#lastNameInput',
	emailInputCss: '#emailInput',
	preferredLanguageCss: '#preferredLanguage',
	dropdownOptionCss: '.option-list nb-option',
	linkedInInputCss: '#linkedIn',
	githubInputCss: '#github',
	upworkInputCss: '#upwork',
	descriptionInputCss: '#shortDescription',
	offerDateInputCss: '#offerDateInput',
	acceptDateInputCss: '#acceptDateInput',
	countryDropdownCss: 'ga-country > div.form-group > nb-select.d-block',
	cityInputCss: '#cityInput',
	postCodeInputCss: '#postcodeInput',
	addressInputCss: '#addressInput',
	payPeriodDropdownCss: '#payPeriodsSelect',
	weeklyLimitInputCss: '#reWeeklyLimitInput',
	billRateValueInputCss: '#billRateValueInput',
	projectOrContactsDropdownCss: '#departmentsSelect',
	projectOrContactDropdownOptionCss: 'div.ng-option',
	addProjectOrContactButtonCss: 'nb-card-header > button[status="success"]',
	saveProjectOrContactButtonCss: 'span.col-2 > button[status="success"]',
	verifyProjectOrContactCss: 'nb-card.ng-star-inserted > nb-card-body',
	saveButtonCss: 'div.actions > button[status="success"]',
	verifyEmployeeCss: 'span.employee-name',
	toastrMessageCss: 'nb-toast.ng-trigger'
};
