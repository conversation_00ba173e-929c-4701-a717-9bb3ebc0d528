export const OrganizationPublicPage = {
	organizationDropdownCss: 'ga-organization-selector ng-select',
	organizationDropdownOptionsCss: 'ng-dropdown-panel > div.ng-dropdown-panel-items div.ng-option',
	nameFilterInputCss: 'input[placeholder="Client Name"]',
	organizationTableRowCss: 'table > tbody > tr:first-of-type',
	organizationNameTableCellCss: 'table > tbody > tr:first-of-type > td:first-of-type div.d-block',
	manageButtonCss: 'button[status="info"]',
	profileLinkInputCss: 'input[formcontrolname="profile_link"]',
	saveButtonCss: 'div.actions > button[status="success"]',
	toastrMessageCss: 'nb-toast.ng-trigger',
	editPageButtonCss: 'div.edit-icon > nb-icon',
	companyNameInputCss: 'input[formcontrolname="name"]',
	companySizeInputCss: 'input[formcontrolname="totalEmployees"]',
	yearFoundedInputCss: 'input[formcontrolname="founded"]',
	bannerInputCss: 'input[formcontrolname="banner"]',
	minimumProjectSizeDropdownCss: '#minimumProjectSize > button',
	minimumProjectSizeDropdownOptionsCss: 'nb-option-list > ul.option-list > nb-option',
	clientFocusDropdownCss: '#addClientFocus > div.ng-select-container',
	clientFocusDropdownOptionsCss: 'ng-dropdown-panel > div.ng-dropdown-panel-items div.ng-option',
	shortDescriptionInputCss: 'textarea[formcontrolname="short_description"]',
	cardBodyCss: 'nb-card-body.body',
	awardsTabCss: 'form > nb-tabset > ul.tabset > li.tab',
	addAwardsButtonCss: 'nb-tab button[status="success"]',
	awardNameInputCss: 'input[placeholder="Name"]',
	awardYearInputCss: 'input[placeholder="Year"]',
	awardsSaveButtonCss: 'nb-tab button[status="success"]',
	skillsTabCss: 'form > nb-tabset > ul.tabset > li.tab',
	skillsDropdownCss: 'ng-select[formcontrolname="skills"]',
	skillsDropdownOptionsCss: 'ng-dropdown-panel > div.ng-dropdown-panel-items div.ng-option',
	languagesTabCss: 'form > nb-tabset > ul.tabset > li.tab',
	addLanguageButtonCss: 'nb-tab[ng-reflect-tab-title="Languages"] button[status="success"]',
	languageDropdownCss: 'ngx-language-selector div[role="combobox"]',
	languageDropdownOptionsCss: 'ng-dropdown-panel > div.ng-dropdown-panel-items div.ng-option',
	languageLevelDropdownCss: 'nb-select[placeholder="Select Showcase"]',
	languageLevelDropdownOptionsCss: 'nb-option-list > ul.option-list > nb-option',
	languageSaveButtonCss: 'nb-tab[ng-reflect-tab-title="Languages"] button[status="success"]',
	updateButtonCss: 'nb-card-footer.text-right > button[status="success"]',
	companyNameCss: 'div.org-head h4.org-name',
	bannerCss: 'div.org-head span.org-banner',
	companySizeCss: 'div.org-head div.org-size > h5.org-title',
	totalClientsCss: 'div.org-head div.org-size > h5.org-title',
	clientFocusCss: 'div.org-head div.org-client-focus > h5.org-title > span.client-focus',
	skillsCss: 'div.org-skills > .org-value > li.org_list_data',

	organizationNameFieldCss: '[placeholder="Organization Name"]',
	currencyFieldCss: '#currencySelect',
	dropdownOptionCss: '.option-list nb-option',
	officialNameFieldCss: '#officialNameInput',
	gridButtonCss: 'div.layout-switch > button',
	taxFieldCss: '#taxIdInput',
	nextButtonCss: 'button[type="submit"]',
	addButtonCss: 'nb-card-body > div > button[status="success"]',
	verifyOrganizationCss: 'div.d-block',
	countryDropdownCss: 'ga-country div.form-group nb-select',
	cityInputCss: '#cityInput',
	postCodeInputCss: '#postcodeInput',
	streetInputCss: '#addressInput',
	bonusTypeDropdownCss: '[formcontrolname="bonusType"]',
	bonusPercentageCss: '[formcontrolname="bonusPercentage"]',
	expiryPeriodInputCss: '[formcontrolname="inviteExpiryPeriod"]',
	dateTypeDropdownCss: '[formcontrolname="defaultValueDateType"]',
	startOfWeekDropdownCss: '#startWeekOnSelect',
	regionCodeDropdownCss: '[formcontrolname="regionCode"]',
	numberFormatDropdownCss: '[formcontrolname="numberFormat"]',
	dateFormatDropdownCss: '[formcontrolname="dateFormat"]',
	timeZoneDropdownCss: '[formcontrolname="timeZone"]',
	timeZoneDropdownOptionCss: 'div.ng-option',
	tableRowCss: 'table > tbody > tr.angular2-smart-row'
};
