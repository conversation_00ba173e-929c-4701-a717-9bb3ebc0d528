export const ManageInterviewsCriterionsPage = {
	nameInputCss: 'input[placeholder="Add technology"]',
	saveButtonCss: 'span.col-2 > button[status="success"]',
	verifyTechnologyTextCss: 'div.technology > p.technology-text',
	editTechnologyButtonCss: 'div.technology-buttons > nb-icon[icon="edit"]',
	deleteTechnologyButtonCss: 'div.technology-buttons > nb-icon[icon="close"]',
	verifyQualityTextCss: 'div.quality > p.quality-text',
	editQualityButtonCss: 'div.quality-buttons > nb-icon[icon="edit"]',
	deleteQualityButtonCss: 'div.quality-buttons > nb-icon[icon="close"]',
	toastrMessageCss: 'nb-toast.ng-trigger',
	qualityInputCss: 'input[placeholder="Add personal quality"]'
};
