export const OrganizationTagsPage = {
	addTagButtonCss: 'button[status="success"]',
	closeDialogButtonCss: 'g[data-name="close"]',
	tagNameInputCss: '#inputName',
	tagColorInputCss: '#inputColor',
	tagTenantCheckboxCss: 'span.custom-checkbox',
	tagDescriptionCss: '#inputDescription',
	cancelButtonCss: '[class="mr-3 appearance-outline size-medium shape-rectangle status-basic nb-transition"]',
	saveButtonCss: '.text-left [status="success"]',
	gridButtonCss: 'div.layout-switch > button',
	selectTableRowCss: 'table > tbody > tr.angular2-smart-row',
	editTagButtonCss: 'button[status="info"]',
	deleteTagButtonCss: 'button[status="danger"]',
	confirmDeleteTagButtonCss: 'nb-card-footer > button[status="danger"]',
	cancelDeleteTagButtonCss: 'nb-card-footer > button[status="info"]',
	toastrMessageCss: 'nb-toast.ng-trigger',
	verifyTagCss: 'ngx-tags-color > div > nb-badge',
	filterNameInputCss: '[placeholder="Name"]',
	firstTableCellTagCss: 'tbody > tr.angular2-smart-row:first-of-type > td:first-of-type'
};
