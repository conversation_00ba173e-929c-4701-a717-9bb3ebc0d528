export const EmployeeAddInfoPage = {
    gridButtonCss: 'div.layout-switch > button',
	addNewLevelButtonCss: 'div.mb-3 > button[status="success"]',
	newLevelInputCss: '[placeholder="Level name"]',
	tagsSelectCss: '#addTags',
	tagsSelectOptionCss: '[type="checkbox"]',
	saveNewLevelButtonCss: 'div.d-flex > button[status="success"]',
    cancelNewLevelButtonCss: 'div.d-flex > button[status="danger"]',
    toastrMessageCss: 'nb-toast.ng-trigger',
    menuButtonsCss: 'nb-menu > ul.menu-items > li',
    employeeSelectorCss: 'ga-employee-selector',
	selectEmployeeDropdownOptionCss: 'ng-dropdown-panel > div[class="ng-dropdown-panel-items scroll-host"] > div > div.ng-star-inserted',
    editIconBtnCss: 'div.employee-details > span > nb-icon',
    tabBtnCss: 'ul.route-tabset > li',
    shortDecsInputCss: 'input[id="shortDescription"]',
    formCss: 'ga-edit-employee-employment',
    levelInputFieldCss: 'ng-select[formcontrolname="employeeLevel"]',
    levelDropdownOptCss: 'div.ng-option',
    saveBtnCss: 'div.actions > button[status="success"]',
    shortDecsCss: 'div.employee-info'

};