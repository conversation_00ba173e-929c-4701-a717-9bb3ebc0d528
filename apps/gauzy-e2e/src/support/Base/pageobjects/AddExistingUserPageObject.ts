export const AddExistingUserPage = {
	addUserButtonCss: '.icon-start.status-warning',
	selectTableRowCss: 'ga-picture-name-tags',
	removeUserButtonCss: '.action.icon-end',
	usersMultiSelectCss: 'button[class="select-button placeholder"]',
	checkUsersMultiSelectCss: '.option-list nb-option',
	saveSelectedUsersButtonCss: 'div.form-group > button[status="success"]',
	cancelAddUsersButtonCss: 'button.mr-3.appearance-outline',
	confirmRemoveUserButtonCss: 'nb-card-footer > button[status="danger"]'
};
