export const AddEmployeeLevelPage = {
	gridButtonCss: 'div.layout-switch > button',
	addNewLevelButtonCss: 'button[status="success"]',
	newLevelInputCss: '[placeholder="Level name"]',
	tagsSelectCss: '#addTags',
	tagsSelectOptionCss: '[type="checkbox"]',
	saveNewLevelButtonCss: 'button[ng-reflect-disabled="false"]',
	cancelNewLevelButtonCss: 'button.delete.mr-3',
	editEmployeeLevelButtonCss: 'button.action.primary',
	removeEmployeeLevelButtonCss: 'button.action.appearance-filled',
	confirmDeleteLevelButtonCss: 'nb-card-footer > button[status="danger"]',
	editLevelInputCss: 'div.d-flex > input[type="text"]',
	verifyTextCss: 'ga-notes-with-tags > div > div.ng-star-inserted',
	cardBodyCss: 'nb-card-body',
	toastrMessageCss: 'nb-toast.ng-trigger',
	cancelButtonCss: 'button.delete.mr-3',
	selectEmployeeLevelRow: 'ga-notes-with-tags',
	selectEmployeeLevelRowtoDelete: 'ga-notes-with-tags',
	updateLevelButtonCss: '.appearance-filled.size-medium'
};
