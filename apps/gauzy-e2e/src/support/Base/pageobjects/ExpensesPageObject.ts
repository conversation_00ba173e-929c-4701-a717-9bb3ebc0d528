export const ExpensesPage = {
	gridButtonCss: 'div.layout-switch > button',
	addExpenseButtonCss: 'div.mb-3 > button[status="success"]',
	selectEmployeeDropdownCss: 'ga-employee-selector.employees',
	selectEmployeeDropdownOptionCss: 'div.ng-option',
	categoryInputCss: '[formcontrolname="category"]',
	dateInputCss: '[formcontrolname="valueDate"]',
	vendorInputCss: '[formcontrolname="vendor"]',
	organizationContactCss: '[formcontrolname="organizationContact"]',
	amountInputCss: '[formcontrolname="amount"]',
	projectDropdownCss: '[formcontrolname="project"]',
	projectDropdownOptionCss: 'div.ng-option',
	addTagsDropdownCss: '#addTags',
	tagsDropdownOption: 'div.ng-option',
	purposeInputCss: '[formcontrolname="purpose"]',
	saveExpenseButtonCss: 'nb-card-footer > button[status="success"]',
	selectTableRowCss: 'table > tbody > tr.angular2-smart-row',
	editExpenseButtonCss: 'div.mb-3 > button[status="info"]',
	deleteExpenseButtonCss: 'div.mb-3 > button[status="danger"]',
	confirmDeleteButtonCss: 'nb-card-footer > button[status="danger"]',
	cardBodyCss: 'nb-card-footer.text-right',
	duplicateExpenseButtonCss: 'div.mb-3 > button[status="primary"]',
	manageCategoriesButtonCss: 'div.card-header-title > button[status="primary"]',
	expenseNameInputCss: '[placeholder="Expense name"]',
	SaveCategoryButtonCss: 'div.justify-content-end > button[status="success"]',
	backButtonCss: 'div.main > button[status="primary"]',
	categoryCardCss: 'nb-card.ng-star-inserted',
	toastrMessageCss: 'nb-toast.ng-trigger',
	notBillableBadgeCss: 'div.badge-danger',
	verifyCategoryCss: 'ga-notes-with-tags > div > div > div.ng-star-inserted',
	sidebarBtnCss: 'span.menu-title',
	accountingExpensesSidebarBtnCss: 'a[href="#/pages/accounting/expenses"] > span.menu-title',
	reportsExpenseSidebarBtnCss: 'a[href="#/pages/reports/expense"] > span.menu-title',
	expenseTableCellCss: 'div[class="col-sm-2 project-name"] > span.ng-star-inserted',
	amountTableCellCss: 'div[class="col text-center day-col"]',
	groupByCss: 'div.ml-3 > nb-select > button.select-button',
	dropdownOptionCss: '.option-list nb-option'
};
