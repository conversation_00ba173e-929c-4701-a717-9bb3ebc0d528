export const RecurringExpensesPage = {
	addNewExpenseButtonCss: 'nb-card > nb-card-body > button[status="success"]',
	employeeDropdownCss: '[placeholder="Employee"]',
	dropdownOptionCss: 'div.ng-option',
	expenseDropdownCss: '#positionInput',
	valueInputCss: '#valueInput',
	saveExpenseButtonCss:
		'nb-card-footer.text-right > button[status="success"]',
	settingsButtonCss: 'g[data-name="settings-2"]',
	editExpenseButtonCss: 'div.block-settings g[data-name="edit"]',
	deleteExpenseButtonCss: 'g[data-name="close"]',
	deleteAllButtonCss: 'nb-radio[value="all"]',
	confirmDeleteExpenseButtonCss: 'nb-card-footer > button[status="danger"]',
	toastrMessageCss: 'nb-toast.ng-trigger',
	verifyExpenseCss: 'div.expense > div.block-item > span.block-amount'
};
