export const GoalsKPIPage = {
	gridButtonCss: 'div.layout-switch > button',
	tabButtonCss: 'nb-tabset > ul.tabset > li.tab',
	kpiTitleInputCss: '#kpi-title',
	kpiDescriptionInputCss: '#kpi-description',
	employeeMultiSelectCss: '#kpi-lead',
	employeeDropdownCss: '.option-list nb-option',
	currentValueInputCss: '#current-value',
	addKPIButtonCss: 'nb-tab[tabid="kpi"] > div.mb-3 > button[status="success"]',
	selectTableRowCss: 'table > tbody > tr.angular2-smart-row',
	saveKPIButtonCss: 'nb-card-footer.text-right > button[status="success"]',
	editButtonCss: 'nb-tab[tabid="kpi"] > div.mb-3 > button[status="info"]',
	deleteButtonCss: 'nb-tab[tabid="kpi"] > div.mb-3 > button[status="danger"]',
	confirmDeleteButtonCss: 'nb-card-footer > button[status="success"]',
	verifyKPICss: 'div.ng-star-inserted',
	verifyEmptyTableCss: 'tr.ng-star-inserted > td',
	toastrMessageCss: 'nb-toast.ng-trigger',
	searchNameInputCss: 'input-filter > input[placeholder="Name"]'
};
