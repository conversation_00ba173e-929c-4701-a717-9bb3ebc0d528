export const CandidatesPage = {
	gridButtonCss: 'div.layout-switch > button',
	inviteButtonCss: 'div.float-left > button[status="primary"]',
	emailInputCss: '#emails',
	dateInputCss: '[placeholder="Date"]',
	sendInviteButtonCss: 'nb-card-footer.text-right > button[status="success"]',
	addButtonCss: 'div.float-left > button[status="success"]',
	firstNameInputCss: '#firstName',
	lastNameInputCss: '#lastName',
	usernameInputCss: '#username',
	newCandidateEmailInputCss: '#email',
	passwordInputCss: '#password',
	imageInputCss: '#inputImageUrl',
	newCandidateDateInputCss: '#appliedDate',
	addTagsDropdownCss: '#addTags',
	tagsDropdownOption: 'div.ng-option',
	nextButtonCss: 'div.text-right > button[status="success"]',
	nextStepButtonCss: 'div.button-container > button[status="success"]',
	allCurrentCandidatesButtonCss: 'div.step-container > button[status="success"]',
	selectTableRowCss: 'table > tbody > tr.angular2-smart-row',
	editButtonCss: '.status-info',
	archiveButtonCss: 'div.float-left > button[status="warning"]',
	rejectButtonCss: 'div.float-left > button[status="danger"]',
	saveEditButtonCss: 'div.actions > button[status="success"]',
	backButtonCss: 'button[status="primary"]',
	confirmActionButtonCss: 'nb-card-footer > button[status="danger"]',
	checkboxButtonCss: 'span.custom-checkbox',
	manageAddButtonCss: 'div.header > button[status="success"]',
	locationButtonCss: '[router-link="/pages/employees/candidates/ed"]',
	countryDropdownCss: '#countrySelect',
	selectDropdownOptionCss: '.option-list nb-option',
	cityInputCss: '#cityInput',
	addressOneInputCss: 'addressInput',
	postCodeInputCss: '#postcodeInput',
	saveActionButtonCss: 'ga-edit-candidate-main div.actions > button[status="success"]',
	ratesButtonCss: '.route-tab:nth-child(5) > .tab-text',
	payPeriodDropdownCss: '#payPeriodsSelect',
	billRateInputCss: '#billRateValueInput',
	experienceButtonCss: '.route-tab:nth-child(7) > .tab-text',
	addExperienceButtonCss: 'div.btn > button[status="success"]',
	schoolNameInputCss: '[formcontrolname="schoolName"]',
	degreeInputCss: '[formcontrolname="degree"]',
	saveExperienceButtonCss: 'div.col-2 > button[status="success"]',
	toastrMessageCss: 'nb-toast.ng-trigger',
	verifyCandidateCss: 'div.d-block',
	badgeCss: 'div.badge-danger'
};
