export const OrganizationEmploymentTypesPage = {
	gridButtonCss: 'div.layout-switch > button',
	addButtonCss: 'div.mb-3 > button[status="success"]',
	nameInputCss: '[formcontrolname="name"]',
	editNameInputCss: 'div[class="col-sm-12 col-md-3 d-flex flex-column justify-content-end"] > input.input-full-width',
	addTagsDropdownCss: '#addTags',
	tagsDropdownOption: 'div.ng-option',
	saveButtonCss: 'div.col-sm-12 > button[status="success"]',
	editButtonCss: 'nb-actions g[data-name="edit"]',
	deleteButtonCss: 'nb-actions g[data-name="close"]',
	confirmDeleteButtonCss: 'nb-card-footer > button[status="danger"]',
	cardBodyCss: 'nb-card-body',
	toastrMessageCss: 'nb-toast.ng-trigger',
	verifyTextCss: 'ga-notes-with-tags > div > div > div.ng-star-inserted'
};
