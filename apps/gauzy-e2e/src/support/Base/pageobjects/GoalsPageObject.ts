export const GoalsPage = {
	addButtonCss: 'nb-actions > nb-action[icon="plus-circle"]',
	optionDropdownCss: 'nb-list[role="list"] nb-list-item',
	nameInputCss: '[formcontrolname="name"]',
	ownerDropdownCss: '[formcontrolname="owner"]',
	dropdownOptionCss: '.option-list nb-option',
	leadDropdownCss: '#objective-lead',
	timeframeOptionCss: 'div.col-md-4 > nb-list[role="list"] nb-list-item',
	confirmButtonCss: 'nb-card-footer > button[status="success"]',
	editButtonCss: 'nb-actions > nb-action[icon="edit"]',
	viewButtonCss: 'nb-actions > nb-action[icon="eye"]',
	deleteButtonCss: 'div.mt-3 > span.ml-5 > button[status="danger"]',
	toastrMessageCss: 'nb-toast.ng-trigger',
	keyResultInputCss: '#key-result-title',
	initialValueCss: '#initial-value',
	targetValueCss: '#target-value',
	toggleButtonCss: 'div.toggle',
	keyResultOwnerCss: '#key-result-owner',
	keyResultLeadCss: '#key-result-lead',
	tableRowCss: 'nb-accordion nb-accordion-item',
	addDeadlineButtonCss: 'div.main-header > button[status="primary"]',
	updatedValueCss: '#updated-value',
	weightTypeButtonCss: 'nb-actions > nb-action[icon="percent"]',
	weightParameterDropdownCss: '#key-result-weight',
	saveDeadlineButtonCss: 'div.d-flex > button[status="success"]',
	progressBarCss: 'div.progress-value',
	verifyGoalCss: 'nb-accordion-item-header'
};
