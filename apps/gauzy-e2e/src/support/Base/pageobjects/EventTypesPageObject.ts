export const EventTypesPage = {
	gridButtonCss: 'div.layout-switch > button',
	addEventTypeButtonCss: 'nb-card-body > div.mb-3 > button[status="success"]',
	editEventTypeButtonCss: 'div.mb-3 > button[status="info"]',
	deleteEventTypeButtonCss: 'div.mb-3 > button[status="danger"]',
	selectEmployeeDropdownCss: 'ga-employee-multi-select[id="employee"]',
	selectEmployeeDropdownOptionCss: 'nb-option-list > ul.option-list > nb-option',
	titleInputCss: '#title',
	descriptionInputCss: '#description',
	durationInputCss: '#durationInput',
	activeCheckboxCss: '[formcontrolname="isActive"]',
	saveButtonCss: 'nb-card-footer > button[status="success"]',
	selectTableRowCss: 'table > tbody > tr.angular2-smart-row',
	confirmDeleteEventTypeButtonCss: 'nb-card-footer > button[status="danger"]',
	verifyEventTypeCss: 'ga-notes-with-tags > div > div.ng-star-inserted',
	toastrMessageCss: 'nb-toast.ng-trigger'
};
