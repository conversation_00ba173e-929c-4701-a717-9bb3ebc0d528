export const SettingsFeaturesPageData = {
	headerText: 'Settings',
	subheaderTextTenant: "Manage Features for",
	subheaderTextOrganization: "Manage Features for 'Ever Technologies LTD'",
	// Tenant
	taskDashboard: 'Task Dashboard',
	teamTaskDashboard: 'Team Task Dashboard',
	myTaskDashboard: 'My Task Dashboard',
	managePayment: 'Manage Payment, Create First Payment',
	manageProposal: 'Manage Proposal, Register First Proposal',
	proposalTemplate: 'Proposal Template',
	createFirstExpense: 'Create First Expense',
	employeeRecurringExpense: 'Employee Recurring Expense',
	organizationRecurringExpenses: 'Organization Recurring Expenses',
	manageInvoice: 'Manage Invoice, Create First Invoice',
	invoiceReceived: 'Invoice Received',
	jobSearch: 'Job Search & Jobs Matching',
	manageTimeActivity:
		'Manage Time Activity, Screenshots, App, Visited Sites, Activities',
	employeeAppointment:
		'Employee Appointment, Schedules & Book Public Appointment',
	manageOrganizationDetails:
		'Manage Organization Details, Location and Settings',
	helpCenter: 'Help Center',
	organizationEmploymentType: 'Organization Employment Type',
	organizationDepartment: 'Organization Department',
	organizationVendor: 'Organization Vendor',
	organizationEquipment: 'Organization Equipment',
	organizationTag: 'Organization Tag',
	manageProject: 'Manage Project, Create First Project',
	manageOrganizationDocument:
		'Manage Organization Document, Create First Document',
	manageGoals: 'Manage Goals and Objectives',
	goalTimeFrame: 'Goal Time Frame & KPI',
	manageTenantUsers: 'Manage Tenant Users',
	manageAvailableApps:
		'Manage Available Apps & Integrations Like Upwork & Hubstaff',
	manageSetting: 'Manage Setting',
	fileStorage: 'File Storage',
	SMSGateway: 'SMS Gateway',
	manageTenant: 'Manage Tenant & Organization Custom SMTP',
	downloadDesktopApp: 'Download Desktop App, Create First Timesheet',
	manageEstimate: 'Manage Estimate, Create First Estimate',
	estimateReceived: 'Estimate Received',
	createFirstIncome: 'Create First Income',
	goToDashboard:
		'Go to dashboard, Manage Employee Statistics, Time Tracking Dashboard',
	createSalesPipeline: 'Create Sales Pipeline',
	salesPipelineDeal: 'Sales Pipeline Deal',
	manageEmployees: 'Manage Employees, Add or Invite Employees',
	employeeApproval: 'Employee Approval',
	employeeLevel: 'Employee Level',
	employeePosition: 'Employee Position',
	employeeTimeOff: 'Employee Time Off',
	employeeApprovalPolicy: 'Employee Approval Policy',
	manageEmployeeTimesheetDaily:
		'Manage Employee Timesheet Daily, Weekly, Calendar, Create First Timesheet',
	manageCandidates: 'Manage Candidates, Interviews & Invites',
	manageInvite: 'Manage Invite',
	manageInterview: 'Manage Interview',
	manageProductInventory: 'Manage Product Inventory, Create First Product',
	manageOrganizationTeam: 'Manage Organization Team, Create First Team',
	manageLeads:
		'Manage Leads, Customers and Clients, Create First Customer/Clients',
	manageExpense: 'Manage Expense, Weekly, Time & Activity and etc reports',
	manageTenantOrganizations: 'Manage Tenant Organizations',
	manageEmailHistory: 'Manage Email History',
	customEmailTemplate: 'Custom Email Template',
	manageEntity: 'Manage Entity Import and Export',
	manageRoles: 'Manage Roles & Permissions'
};
