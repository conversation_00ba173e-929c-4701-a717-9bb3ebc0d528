/**
 * @license
 * Copyright Akveo. All Rights Reserved.
 * Licensed under the MIT License. See License.txt in the project root for license information.
 */
@use "sass:string";

$theme: (
   color-primary-100: #fff2f7,
   color-primary-200: #ffd4e3,
   color-primary-300: #fc9abc,
   color-primary-400: #f24681,
   color-primary-500: #e91d63,
   color-primary-600: #c71451,
   color-primary-700: #a80d43,
   color-primary-800: #870935,
   color-primary-900: #70062a,

   color-primary-transparent-100: rgba(233, 29, 99, 0.08),
   color-primary-transparent-200: rgba(233, 29, 99, 0.16),
   color-primary-transparent-300: rgba(233, 29, 99, 0.24),
   color-primary-transparent-400: rgba(233, 29, 99, 0.32),
   color-primary-transparent-500: rgba(233, 29, 99, 0.4),
   color-primary-transparent-600: rgba(233, 29, 99, 0.48),

   color-success-100: #edfbd1,
   color-success-200: #d7f7a6,
   color-success-300: #b4e775,
   color-success-400: #8fcf50,
   color-success-500: #60af20,
   color-success-600: #499617,
   color-success-700: #357d10,
   color-success-800: #24650a,
   color-success-900: #175306,

   color-success-transparent-100: rgba(96, 175, 32, 0.08),
   color-success-transparent-200: rgba(96, 175, 32, 0.16),
   color-success-transparent-300: rgba(96, 175, 32, 0.24),
   color-success-transparent-400: rgba(96, 175, 32, 0.32),
   color-success-transparent-500: rgba(96, 175, 32, 0.4),
   color-success-transparent-600: rgba(96, 175, 32, 0.48),

   color-info-100: #ccf7fe,
   color-info-200: #99e9fd,
   color-info-300: #66d3f9,
   color-info-400: #40bbf4,
   color-info-500: #0495ee,
   color-info-600: #0273cc,
   color-info-700: #0256ab,
   color-info-800: #013d8a,
   color-info-900: #002b72,

   color-info-transparent-100: rgba(4, 149, 238, 0.08),
   color-info-transparent-200: rgba(4, 149, 238, 0.16),
   color-info-transparent-300: rgba(4, 149, 238, 0.24),
   color-info-transparent-400: rgba(4, 149, 238, 0.32),
   color-info-transparent-500: rgba(4, 149, 238, 0.4),
   color-info-transparent-600: rgba(4, 149, 238, 0.48),

   color-warning-100: #fff3cd,
   color-warning-200: #ffe49b,
   color-warning-300: #ffd169,
   color-warning-400: #ffbe43,
   color-warning-500: #ff9f05,
   color-warning-600: #db8003,
   color-warning-700: #b76302,
   color-warning-800: #934a01,
   color-warning-900: #7a3800,

   color-warning-transparent-100: rgba(255, 159, 5, 0.08),
   color-warning-transparent-200: rgba(255, 159, 5, 0.16),
   color-warning-transparent-300: rgba(255, 159, 5, 0.24),
   color-warning-transparent-400: rgba(255, 159, 5, 0.32),
   color-warning-transparent-500: rgba(255, 159, 5, 0.4),
   color-warning-transparent-600: rgba(255, 159, 5, 0.48),

   color-danger-100: #fbd2c8,
   color-danger-200: #f79e94,
   color-danger-300: #e75d5c,
   color-danger-400: #cf3341,
   color-danger-500: #b00020,
   color-danger-600: #970029,
   color-danger-700: #7e002e,
   color-danger-800: #66002f,
   color-danger-900: #54002f,

   color-danger-transparent-100: rgba(176, 0, 32, 0.08),
   color-danger-transparent-200: rgba(176, 0, 32, 0.16),
   color-danger-transparent-300: rgba(176, 0, 32, 0.24),
   color-danger-transparent-400: rgba(176, 0, 32, 0.32),
   color-danger-transparent-500: rgba(176, 0, 32, 0.4),
   color-danger-transparent-600: rgba(176, 0, 32, 0.48),

   color-basic-100: #ffffff,
   color-basic-200: #f5f5f5,
   color-basic-300: #f5f5f5,
   color-basic-400: #d4d4d4,
   color-basic-500: #b3b3b3,
   color-basic-600: #808080,
   color-basic-700: #404040,
   color-basic-800: #353535,
   color-basic-900: #303030,
   color-basic-1000: #1f1f1f,
   color-basic-1100: #141414,

   color-basic-transparent-100: rgba(128, 128, 128, 0.08),
   color-basic-transparent-200: rgba(128, 128, 128, 0.16),
   color-basic-transparent-300: rgba(128, 128, 128, 0.24),
   color-basic-transparent-400: rgba(128, 128, 128, 0.32),
   color-basic-transparent-500: rgba(128, 128, 128, 0.4),
   color-basic-transparent-600: rgba(128, 128, 128, 0.48),

   color-basic-control-transparent-100: rgba(255, 255, 255, 0.08),
   color-basic-control-transparent-200: rgba(255, 255, 255, 0.16),
   color-basic-control-transparent-300: rgba(255, 255, 255, 0.24),
   color-basic-control-transparent-400: rgba(255, 255, 255, 0.32),
   color-basic-control-transparent-500: rgba(255, 255, 255, 0.4),
   color-basic-control-transparent-600: rgba(255, 255, 255, 0.48),

   color-basic-focus: color-basic-400,
   color-basic-hover: color-basic-200,
   color-basic-default: color-basic-300,
   color-basic-active: color-basic-400,
   color-basic-disabled: color-basic-transparent-300,
   color-basic-focus-border: color-basic-500,
   color-basic-hover-border: color-basic-hover,
   color-basic-default-border: color-basic-default,
   color-basic-active-border: color-basic-active,
   color-basic-disabled-border: color-basic-disabled,

   color-basic-transparent-focus: color-basic-transparent-300,
   color-basic-transparent-hover: color-basic-transparent-200,
   color-basic-transparent-default: color-basic-transparent-100,
   color-basic-transparent-active: color-basic-transparent-300,
   color-basic-transparent-disabled: color-basic-transparent-200,
   color-basic-transparent-focus-border: color-basic-500,
   color-basic-transparent-hover-border: color-basic-500,
   color-basic-transparent-default-border: color-basic-500,
   color-basic-transparent-active-border: color-basic-500,
   color-basic-transparent-disabled-border: color-basic-transparent-300,

   color-primary-focus: color-primary-600,
   color-primary-hover: color-primary-400,
   color-primary-default: color-primary-500,
   color-primary-active: color-primary-600,
   color-primary-disabled: color-basic-transparent-300,
   color-primary-focus-border: color-primary-700,
   color-primary-hover-border: color-primary-hover,
   color-primary-default-border: color-primary-default,
   color-primary-active-border: color-primary-active,
   color-primary-disabled-border: color-primary-disabled,

   color-primary-transparent-focus: color-primary-transparent-300,
   color-primary-transparent-hover: color-primary-transparent-200,
   color-primary-transparent-default: color-primary-transparent-100,
   color-primary-transparent-active: color-primary-transparent-300,
   color-primary-transparent-disabled: color-basic-transparent-200,
   color-primary-transparent-focus-border: color-primary-500,
   color-primary-transparent-hover-border: color-primary-500,
   color-primary-transparent-default-border: color-primary-500,
   color-primary-transparent-active-border: color-primary-500,
   color-primary-transparent-disabled-border: color-basic-transparent-300,

   color-success-focus: color-success-600,
   color-success-hover: color-success-400,
   color-success-default: color-success-500,
   color-success-active: color-success-600,
   color-success-disabled: color-basic-transparent-300,
   color-success-focus-border: color-success-700,
   color-success-hover-border: color-success-hover,
   color-success-default-border: color-success-default,
   color-success-active-border: color-success-active,
   color-success-disabled-border: color-success-disabled,

   color-success-transparent-focus: color-success-transparent-300,
   color-success-transparent-hover: color-success-transparent-200,
   color-success-transparent-default: color-success-transparent-100,
   color-success-transparent-active: color-success-transparent-300,
   color-success-transparent-disabled: color-basic-transparent-200,
   color-success-transparent-focus-border: color-success-500,
   color-success-transparent-hover-border: color-success-500,
   color-success-transparent-default-border: color-success-500,
   color-success-transparent-active-border: color-success-500,
   color-success-transparent-disabled-border: color-basic-transparent-300,

   color-info-focus: color-info-600,
   color-info-hover: color-info-400,
   color-info-default: color-info-500,
   color-info-active: color-info-600,
   color-info-disabled: color-basic-transparent-300,
   color-info-focus-border: color-info-700,
   color-info-hover-border: color-info-hover,
   color-info-default-border: color-info-default,
   color-info-active-border: color-info-active,
   color-info-disabled-border: color-info-disabled,

   color-info-transparent-focus: color-info-transparent-300,
   color-info-transparent-hover: color-info-transparent-200,
   color-info-transparent-default: color-info-transparent-100,
   color-info-transparent-active: color-info-transparent-300,
   color-info-transparent-disabled: color-basic-transparent-200,
   color-info-transparent-focus-border: color-info-500,
   color-info-transparent-hover-border: color-info-500,
   color-info-transparent-default-border: color-info-500,
   color-info-transparent-active-border: color-info-500,
   color-info-transparent-disabled-border: color-basic-transparent-300,

   color-warning-focus: color-warning-600,
   color-warning-hover: color-warning-400,
   color-warning-default: color-warning-500,
   color-warning-active: color-warning-600,
   color-warning-disabled: color-basic-transparent-300,
   color-warning-focus-border: color-warning-700,
   color-warning-hover-border: color-warning-hover,
   color-warning-default-border: color-warning-default,
   color-warning-active-border: color-warning-active,
   color-warning-disabled-border: color-warning-disabled,

   color-warning-transparent-focus: color-warning-transparent-300,
   color-warning-transparent-hover: color-warning-transparent-200,
   color-warning-transparent-default: color-warning-transparent-100,
   color-warning-transparent-active: color-warning-transparent-300,
   color-warning-transparent-disabled: color-basic-transparent-200,
   color-warning-transparent-focus-border: color-warning-500,
   color-warning-transparent-hover-border: color-warning-500,
   color-warning-transparent-default-border: color-warning-500,
   color-warning-transparent-active-border: color-warning-500,
   color-warning-transparent-disabled-border: color-basic-transparent-300,

   color-danger-focus: color-danger-600,
   color-danger-hover: color-danger-400,
   color-danger-default: color-danger-500,
   color-danger-active: color-danger-600,
   color-danger-disabled: color-basic-transparent-300,
   color-danger-focus-border: color-danger-700,
   color-danger-hover-border: color-danger-hover,
   color-danger-default-border: color-danger-default,
   color-danger-active-border: color-danger-active,
   color-danger-disabled-border: color-danger-disabled,

   color-danger-transparent-focus: color-danger-transparent-300,
   color-danger-transparent-hover: color-danger-transparent-200,
   color-danger-transparent-default: color-danger-transparent-100,
   color-danger-transparent-active: color-danger-transparent-300,
   color-danger-transparent-disabled: color-basic-transparent-200,
   color-danger-transparent-focus-border: color-danger-500,
   color-danger-transparent-hover-border: color-danger-500,
   color-danger-transparent-default-border: color-danger-500,
   color-danger-transparent-active-border: color-danger-500,
   color-danger-transparent-disabled-border: color-basic-transparent-300,

   color-control-focus: color-basic-300,
   color-control-hover: color-basic-200,
   color-control-default: color-basic-100,
   color-control-active: color-basic-300,
   color-control-disabled: color-basic-transparent-300,
   color-control-focus-border: color-basic-500,
   color-control-hover-border: color-control-hover,
   color-control-default-border: color-control-default,
   color-control-active-border: color-control-active,
   color-control-disabled-border: color-control-disabled,

   color-control-transparent-focus: color-basic-control-transparent-300,
   color-control-transparent-hover: color-basic-control-transparent-200,
   color-control-transparent-default: color-basic-control-transparent-100,
   color-control-transparent-active: color-basic-control-transparent-300,
   color-control-transparent-disabled: color-basic-transparent-200,
   color-control-transparent-focus-border: color-basic-100,
   color-control-transparent-hover-border: color-basic-100,
   color-control-transparent-default-border: color-basic-100,
   color-control-transparent-active-border: color-basic-100,
   color-control-transparent-disabled-border: color-basic-transparent-300,

   background-basic-color-1: color-basic-1000,
   background-basic-color-2: color-basic-1000,
   background-basic-color-3: color-basic-900,
   background-basic-color-4: color-basic-1100,

   background-alternative-color-1: color-basic-100,
   background-alternative-color-2: color-basic-200,
   background-alternative-color-3: color-basic-300,
   background-alternative-color-4: color-basic-400,

   border-basic-color-1: color-basic-800,
   border-basic-color-2: color-basic-900,
   border-basic-color-3: color-basic-1000,
   border-basic-color-4: color-basic-1100,
   border-basic-color-5: color-basic-1100,

   border-alternative-color-1: color-basic-100,
   border-alternative-color-2: color-basic-200,
   border-alternative-color-3: color-basic-300,
   border-alternative-color-4: color-basic-400,
   border-alternative-color-5: color-basic-500,

   border-primary-color-1: color-primary-500,
   border-primary-color-2: color-primary-600,
   border-primary-color-3: color-primary-700,
   border-primary-color-4: color-primary-800,
   border-primary-color-5: color-primary-900,

   border-success-color-1: color-success-500,
   border-success-color-2: color-success-600,
   border-success-color-3: color-success-700,
   border-success-color-4: color-success-800,
   border-success-color-5: color-success-900,

   border-info-color-1: color-info-500,
   border-info-color-2: color-info-600,
   border-info-color-3: color-info-700,
   border-info-color-4: color-info-800,
   border-info-color-5: color-info-900,

   border-warning-color-1: color-warning-500,
   border-warning-color-2: color-warning-600,
   border-warning-color-3: color-warning-700,
   border-warning-color-4: color-warning-800,
   border-warning-color-5: color-warning-900,

   border-danger-color-1: color-danger-500,
   border-danger-color-2: color-danger-600,
   border-danger-color-3: color-danger-700,
   border-danger-color-4: color-danger-800,
   border-danger-color-5: color-danger-900,

   text-basic-color: color-basic-100,
   text-alternate-color: color-basic-900,
   text-control-color: color-basic-100,
   text-disabled-color: color-basic-transparent-600,
   text-hint-color: color-basic-600,

   text-primary-color: color-primary-default,
   text-primary-focus-color: color-primary-focus,
   text-primary-hover-color: color-primary-hover,
   text-primary-active-color: color-primary-active,
   text-primary-disabled-color: color-primary-400,

   text-success-color: color-success-default,
   text-success-focus-color: color-success-focus,
   text-success-hover-color: color-success-hover,
   text-success-active-color: color-success-active,
   text-success-disabled-color: color-success-400,

   text-info-color: color-info-default,
   text-info-focus-color: color-info-focus,
   text-info-hover-color: color-info-hover,
   text-info-active-color: color-info-active,
   text-info-disabled-color: color-info-400,

   text-warning-color: color-warning-default,
   text-warning-focus-color: color-warning-focus,
   text-warning-hover-color: color-warning-hover,
   text-warning-active-color: color-warning-active,
   text-warning-disabled-color: color-warning-400,

   text-danger-color: color-danger-default,
   text-danger-focus-color: color-danger-focus,
   text-danger-hover-color: color-danger-hover,
   text-danger-active-color: color-danger-active,
   text-danger-disabled-color: color-danger-400,

   font-family-primary: string.unquote('Roboto, sans-serif'),

   shadow: string.unquote(
     '0px 2px 1px -1px rgba(0, 0, 0, 0.2), 0px 1px 1px 0px rgba(0, 0, 0, 0.14), 0px 1px 3px 0px rgba(0, 0, 0, 0.12)'
     ),
   card-shadow: shadow,
   header-shadow: string.unquote(
     '0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12)'
   ),

   header-background-color: color-primary-default,
   footer-background-color: color-primary-default,
   header-text-color: text-basic-color,
   footer-text-color: text-basic-color,
   footer-text-highlight-color: footer-text-color,
   sidebar-background-color: background-basic-color-2,

   material-regular-font-weight: 400,
   menu-text-font-weight: material-regular-font-weight,
   menu-text-color: rgba(255, 255, 255, 0.7),
   menu-item-hover-text-color: rgba(255, 255, 255, 0.7),
   menu-item-hover-background-color: rgba(255, 255, 255, 0.04),
   menu-item-active-background-color: rgba(0, 0, 0, 0.25),

   menu-item-icon-color: rgba(255, 255, 255, 0.7),
   menu-item-icon-hover-color: rgba(255, 255, 255, 0.7),

   menu-submenu-item-hover-background-color: rgba(255, 255, 255, 0.04),
   menu-submenu-item-active-hover-background-color: rgba(255, 255, 255, 0.1),
   menu-submenu-item-active-background-color: rgba(0, 0, 0, 0.25),

   card-border-style: none,
   card-background-color: color-basic-800,
   card-divider-color: color-basic-700,

   input-border-width: 1px,
   input-basic-border-color: rgba(255, 255, 255, 0.7),
   input-basic-focus-border-color: color-primary-focus,
   input-basic-disabled-border-color: input-basic-border-color,
   input-basic-hover-border-color: input-basic-border-color,
   input-basic-background-color: transparent,
   input-basic-focus-background-color: transparent,
   input-basic-disabled-background-color: transparent,
   input-basic-hover-background-color: transparent,
   input-rectangle-border-radius: 0.25rem,
   input-semi-round-border-radius: 0.25rem,
   input-round-border-radius: 0.25rem,
   input-medium-padding: 0.4375rem 1rem,
   input-large-padding: 1rem 1rem,
   input-small-text-font-weight: text-paragraph-font-weight,
   input-medium-text-font-weight: text-paragraph-font-weight,
   input-large-text-font-weight: text-paragraph-font-weight,
   input-primary-background-color: input-basic-background-color,
   input-primary-focus-background-color: input-basic-focus-background-color,
   input-primary-disabled-background-color: input-basic-disabled-background-color,
   input-primary-hover-background-color: input-basic-hover-background-color,
   input-info-background-color: input-basic-background-color,
   input-info-focus-background-color: input-basic-focus-background-color,
   input-info-disabled-background-color: input-basic-disabled-background-color,
   input-info-hover-background-color: input-basic-hover-background-color,
   input-success-background-color: input-basic-background-color,
   input-success-focus-background-color: input-basic-focus-background-color,
   input-success-disabled-background-color: input-basic-disabled-background-color,
   input-success-hover-background-color: input-basic-hover-background-color,
   input-warning-background-color: input-basic-background-color,
   input-warning-focus-background-color: input-basic-focus-background-color,
   input-warning-disabled-background-color: input-basic-disabled-background-color,
   input-warning-hover-background-color: input-basic-hover-background-color,
   input-danger-background-color: input-basic-background-color,
   input-danger-focus-background-color: input-basic-focus-background-color,
   input-danger-disabled-background-color: input-basic-disabled-background-color,
   input-danger-hover-background-color: input-basic-hover-background-color,
   input-control-background-color: input-basic-background-color,
   input-control-focus-background-color: input-basic-focus-background-color,
   input-control-disabled-background-color: input-basic-disabled-background-color,
   input-control-hover-background-color: input-basic-hover-background-color,

   select-tiny-text-font-weight: material-regular-font-weight,
   select-small-text-font-weight: material-regular-font-weight,
   select-medium-text-font-weight: material-regular-font-weight,
   select-large-text-font-weight: material-regular-font-weight,
   select-giant-text-font-weight: material-regular-font-weight,
   select-rectangle-border-radius: 0,
   select-semi-round-border-radius: 0,
   select-round-border-radius: 0,
   select-outline-border-width: 0 0 1px 0,
   select-outline-basic-border-color: rgba(255, 255, 255, 0.7),
   select-outline-basic-focus-border-color: color-primary-focus,
   select-outline-basic-hover-border-color: select-outline-basic-border-color,
   select-outline-basic-disabled-border-color: select-outline-basic-border-color,
   select-outline-basic-background-color: transparent,
   select-outline-basic-focus-background-color: transparent,
   select-outline-basic-hover-background-color: transparent,
   select-outline-basic-disabled-background-color: transparent,
   select-outline-primary-background-color: select-outline-basic-background-color,
   select-outline-primary-focus-background-color: select-outline-basic-focus-background-color,
   select-outline-primary-hover-background-color: select-outline-basic-hover-background-color,
   select-outline-primary-disabled-background-color: select-outline-basic-disabled-background-color,
   select-outline-success-background-color: select-outline-basic-background-color,
   select-outline-success-focus-background-color: select-outline-basic-focus-background-color,
   select-outline-success-hover-background-color: select-outline-basic-hover-background-color,
   select-outline-success-disabled-background-color: select-outline-basic-disabled-background-color,
   select-outline-info-background-color: select-outline-basic-background-color,
   select-outline-info-focus-background-color: select-outline-basic-focus-background-color,
   select-outline-info-hover-background-color: select-outline-basic-hover-background-color,
   select-outline-info-disabled-background-color: select-outline-basic-disabled-background-color,
   select-outline-warning-background-color: select-outline-basic-background-color,
   select-outline-warning-focus-background-color: select-outline-basic-focus-background-color,
   select-outline-warning-hover-background-color: select-outline-basic-hover-background-color,
   select-outline-warning-disabled-background-color: select-outline-basic-disabled-background-color,
   select-outline-danger-background-color: select-outline-basic-background-color,
   select-outline-danger-focus-background-color: select-outline-basic-focus-background-color,
   select-outline-danger-hover-background-color: select-outline-basic-hover-background-color,
   select-outline-danger-disabled-background-color: select-outline-basic-disabled-background-color,
   select-outline-control-background-color: select-outline-basic-background-color,
   select-outline-control-focus-background-color: select-outline-basic-focus-background-color,
   select-outline-control-hover-background-color: select-outline-basic-hover-background-color,
   select-outline-control-disabled-background-color: select-outline-basic-disabled-background-color,
   option-list-shadow: shadow,
   option-list-border-style: none,
   option-list-adjacent-border-style: none,
   option-background-color: color-basic-700,
   option-hover-background-color: #4a4a4a,
   option-focus-background-color: option-hover-background-color,
   option-selected-background-color: #525252,
   option-selected-hover-background-color: option-selected-background-color,
   option-selected-focus-background-color: option-selected-background-color,
   option-selected-text-color: text-primary-color,
   option-selected-hover-text-color: text-primary-color,
   option-selected-focus-text-color: text-primary-color,
   option-tiny-text-font-weight: material-regular-font-weight,
   option-small-text-font-weight: material-regular-font-weight,
   option-medium-text-font-weight: material-regular-font-weight,
   option-large-text-font-weight: material-regular-font-weight,
   option-giant-text-font-weight: material-regular-font-weight
 );

 $nb-themes: nb-register-theme($theme, material-dark, dark);
