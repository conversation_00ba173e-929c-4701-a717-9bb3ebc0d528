@import url('https://fonts.googleapis.com/css?family=Open+Sans:400,600,700&display=swap');
@import url('https://fonts.googleapis.com/css?family=Crimson+Text:600i&display=swap');
@import url('https://fonts.googleapis.com/css?family=Roboto:200,300,400,500,600,700,800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');

// themes - our custom or/and out of the box themes
@import './themes';

// framework component themes (styles tied to theme variables)
@import '@nebular/theme/styles/globals';
@import '@nebular/auth/styles/globals';

@import 'bootstrap/scss/functions';
@import 'bootstrap/scss/variables';
@import 'bootstrap/scss/mixins';
@import 'bootstrap/scss/grid';

@import './material/angular-material';

// loading progress bar theme
@import './pace.theme';

@import './layout';
@import './overrides';
@import './material/material-overrides';
@import './gauzy/gauzy-overrides';

@import './site-menu.scss';

@import './includes/fullcalendar';
@import './includes/tabset';
@import './includes/ng-select';
@import './includes/ng5-slider';
@import './includes/dialog';
@import './includes/scroll';

// install swiper.js
@import 'swiper/css';
@import 'swiper/css/pagination';
@import 'swiper/css/navigation';

// install the framework and custom global styles
@include nb-install() {
  @include angular-material();

  // framework global styles
  @include nb-theme-global();
  @include nb-auth-global();

  @include ngx-layout();
  // loading progress bar
  @include ngx-pace-theme();

  @include nb-overrides();
  @include material-overrides();
  @include gauzy-overrides();
}

::-webkit-scrollbar {
  display: none;
}
