module.exports = (config) => {
	config.externals = {
		'amqp-connection-manager': 'amqp-connection-manager',
		amqplib: 'amqplib',
		atpl: 'atpl',
		'babel-core': 'babel-core',
		'bracket-template': 'bracket-template',
		'coffee-script': 'coffee-script',
		'dustjs-linkedin': 'dustjs-linkedin',
		eco: 'eco',
		ect: 'ect',
		grpc: 'grpc',
		'haml-coffee': 'haml-coffee',
		hamlet: 'hamlet',
		hamljs: 'hamljs',
		'hogan.js': 'hogan.js',
		htmling: 'htmling',
		jazz: 'jazz',
		jqtpl: 'jqtpl',
		just: 'just',
		kafkajs: 'kafkajs',
		liquor: 'liquor',
		marko: 'marko',
		mote: 'mote',
		mqtt: 'mqtt',
		nats: 'nats',
		'pg-native': 'pg-native',
		plates: 'plates',
		ractive: 'ractive',
		react: 'react',
		'react-dom/server': 'react-dom/server',
		redis: 'redis',
		slm: 'slm',
		'teacup/lib/express': 'teacup/lib/express',
		templayed: 'templayed',
		toffee: 'toffee',
		twig: 'twig',
		vash: 'vash',
		velocityjs: 'velocityjs',
		walrus: 'walrus',
		whiskers: 'whiskers',
		npm: 'npm',
		fsevents: 'fsevents',
		bcrypt: 'bcrypt',
		sqlite3: 'commonjs sqlite3',
		ioredis: 'ioredis',
		debug: 'debug',
		'@as-integrations/fastify': '@as-integrations/fastify',
		'@apollo/gateway': '@apollo/gateway',
		'class-transformer/storage': 'class-transformer/storage',
		'@fastify/static': '@fastify/static',
		'@nestjs/mongoose': '@nestjs/mongoose',
		'@nestjs/sequelize': '@nestjs/sequelize',
		'@nestjs/terminus': '@nestjs/terminus',
		tedious: 'tedious',
		oracledb: 'oracledb',
		hbs: 'hbs',
		'react-native-sqlite-storage': 'react-native-sqlite-storage',
		bufferutil: 'bufferutil',
		'utf-8-validate': 'utf-8-validate',
		'@mongodb-js/zstd': '@mongodb-js/zstd',
		'aws-sdk/credential-providers': 'aws-sdk/credential-providers',
		snappy: 'snappy',
		kerberos: 'kerberos',
		'arc-templates': 'arc-templates',
		'dustjs-helpers': 'dustjs-helpers',
		'then-jade': 'then-jade',
		jade: 'jade',
		tinyliquid: 'tinyliquid',
		nunjucks: 'nunjucks',
		'mariadb/callback': 'mariadb/callback',
		'@opentelemetry/exporter-jaeger': '@opentelemetry/exporter-jaeger'
	};

	config.module = config.module || {};

	config.module.noParse = [
		// This will match any path containing `node_modules/@libsql`
		/[\\/]node_modules[\\/]@libsql/
	];

	return config;
};
