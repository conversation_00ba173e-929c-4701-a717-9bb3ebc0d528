services:
  db:
    image: postgres:17-alpine
    container_name: db
    restart: always
    environment:
      POSTGRES_DB: ${DB_NAME:-gauzy}
      POSTGRES_USER: ${DB_USER:-postgres}
      POSTGRES_PASSWORD: ${DB_PASS:-gauzy_password}
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U $${POSTGRES_USER} -d $${POSTGRES_DB} || exit 1']
      interval: 10s
      timeout: 5s
      retries: 5
    volumes:
      - postgres_data:/var/lib/postgresql/data/
      - ./.deploy/db/init-user-db.sh:/docker-entrypoint-initdb.d/init-user-db.sh
    ports:
      - '5432:5432'
    networks:
      - overlay

  zipkin:
    image: ghcr.io/openzipkin/zipkin-slim:latest
    container_name: zipkin
    # Environment settings are defined here https://github.com/openzipkin/zipkin/blob/master/zipkin-server/README.md#environment-variables
    environment:
      - STORAGE_TYPE=mem
      # Uncomment to enable self-tracing
      # - SELF_TRACING_ENABLED=true
      # Uncomment to increase heap size
      # - JAVA_OPTS=-Xms128m -Xmx128m -XX:+ExitOnOutOfMemoryError
    ports:
      # Port used for the Zipkin UI and HTTP Api
      - 9411:9411
    networks:
      - overlay

  cube:
    image: cubejs/cube:latest
    container_name: cube
    ports:
      - '4000:4000' # Cube Playground
      - '5430:5430' # Port for Cube SQL
    environment:
      CUBEJS_DEV_MODE: 'true'
      CUBEJS_DB_TYPE: postgres
      CUBEJS_DB_HOST: db
      CUBEJS_DB_PORT: 5432
      CUBEJS_DB_NAME: ${DB_NAME:-gauzy}
      CUBEJS_DB_USER: ${DB_USER:-postgres}
      CUBEJS_DB_PASS: ${DB_PASS:-gauzy_password}
      # Credentials to connect to Cube SQL APIs
      CUBEJS_PG_SQL_PORT: 5430
      CUBEJS_SQL_USER: ${CUBE_USER:-cube_user}
      CUBEJS_SQL_PASSWORD: ${CUBE_PASS:-cube_pass}
    volumes:
      - 'cube_data:/cube/conf'
    links:
      - db
    networks:
      - overlay

  jitsu:
    container_name: jitsu
    image: jitsucom/jitsu:latest
    extra_hosts:
      - 'host.docker.internal:host-gateway'
    environment:
      - REDIS_URL=redis://redis:6379
      # Retroactive users recognition can affect RAM significant.
      # Read more about the solution https://jitsu.com/docs/other-features/retroactive-user-recognition
      - USER_RECOGNITION_ENABLED=true
      - USER_RECOGNITION_REDIS_URL=redis://jitsu_redis_users_recognition:6380
      - TERM=xterm-256color
    depends_on:
      redis:
        condition: service_healthy
      jitsu_redis_users_recognition:
        condition: service_healthy
    user: 'root'
    volumes:
      - ./.deploy/jitsu/configurator/data/logs:/home/<USER>/data/logs
      - ./.deploy/jitsu/server/data/logs:/home/<USER>/data/logs
      - ./.deploy/jitsu/server/data/logs/events:/home/<USER>/data/logs/events
      - /var/run/docker.sock:/var/run/docker.sock
      - jitsu_workspace:/home/<USER>/data/airbyte
    restart: always
    ports:
      - '8000:8000'
    networks:
      - overlay

  opensearch:
    image: opensearchproject/opensearch:latest
    container_name: opensearch
    environment:
      - discovery.type=single-node
      - http.port=9200
      - http.cors.enabled=true
      - http.cors.allow-origin=http://localhost:1358,http://127.0.0.1:1358
      - http.cors.allow-headers=X-Requested-With,X-Auth-Token,Content-Type,Content-Length,Authorization
      - http.cors.allow-credentials=true
      - bootstrap.memory_lock=true
      - 'ES_JAVA_OPTS=-Xms512m -Xmx512m'
      - OPENSEARCH_INITIAL_ADMIN_PASSWORD=${OPENSEARCH_INITIAL_ADMIN_PASSWORD:-Gauzy_password_123}
    ulimits:
      memlock:
        soft: -1
        hard: -1
      nofile:
        soft: 65536
        hard: 65536
    volumes:
      - opensearch-data:/usr/share/opensearch/data
    ports:
      - 9200:9200
      - 9300:9300
      - 9600:9600
    networks:
      - overlay

  opensearch-dashboards:
    image: opensearchproject/opensearch-dashboards:latest
    container_name: opensearch-dashboards
    ports:
      - 5601:5601
    expose:
      - '5601'
    environment:
      OPENSEARCH_HOSTS: '["https://opensearch:9200"]'
    networks:
      - overlay

  # Search Management UI
  dejavu:
    image: appbaseio/dejavu:3.8.3
    container_name: dejavu
    ports:
      - '1358:1358'
    links:
      - opensearch
    networks:
      - overlay

  # TODO: For now used in Jitsu, but we will need to create another one dedicated for Jitsu later
  redis:
    image: 'redis:alpine'
    container_name: redis
    restart: unless-stopped
    healthcheck:
      test: ['CMD-SHELL', 'redis-cli -h localhost -p 6379 PING']
      interval: 1s
      timeout: 30s
    ports:
      - '6379'
    volumes:
      - ./.deploy/redis/data:/data
    networks:
      - overlay

  jitsu_redis_users_recognition:
    image: 'redis:alpine'
    container_name: jitsu_redis_users_recognition
    command: redis-server /usr/local/etc/redis/redis.conf
    restart: unless-stopped
    healthcheck:
      test: ['CMD-SHELL', 'redis-cli -h localhost -p 6380 PING']
      interval: 1s
      timeout: 30s
    ports:
      - '6380'
    volumes:
      - ./.deploy/redis/jitsu_users_recognition/data:/data
      - ./.deploy/redis/jitsu_users_recognition/redis.conf:/usr/local/etc/redis/redis.conf
    networks:
      - overlay

  minio:
    restart: unless-stopped
    image: quay.io/minio/minio:latest
    container_name: minio
    volumes:
      - minio_data:/data
    environment:
      MINIO_ROOT_USER: ever-gauzy-access-key
      MINIO_ROOT_PASSWORD: ever-gauzy-secret-key
    command: server /data --address :9000 --console-address ":9001"
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:9000/minio/health/live']
      interval: 5s
      timeout: 5s
      retries: 5
    ports:
      - 9000:9000
      - 9001:9001
    networks:
      - overlay

  minio_create_buckets:
    image: minio/mc
    environment:
      MINIO_ROOT_USER: ever-gauzy-access-key
      MINIO_ROOT_PASSWORD: ever-gauzy-secret-key
    entrypoint:
      - '/bin/sh'
      - '-c'
    command:
      - "until (/usr/bin/mc alias set minio http://minio:9000 $$MINIO_ROOT_USER $$MINIO_ROOT_PASSWORD) do
        echo 'Waiting to start minio...' && sleep 1;
        done;
        if /usr/bin/mc ls minio/ever-gauzy >/dev/null 2>&1; then
        echo 'Bucket already exists, skipping creation.';
        else
        /usr/bin/mc mb minio/ever-gauzy --region=eu-north-1;
        fi;
        exit 0;"
    depends_on:
      minio:
        condition: service_healthy
    networks:
      - overlay

  pgweb:
    image: sosedoff/pgweb
    container_name: pgweb
    restart: always
    depends_on:
      - db
    links:
      - db:${DB_HOST:-db}
    environment:
      POSTGRES_DB: ${DB_NAME:-gauzy}
      POSTGRES_USER: ${DB_USER:-postgres}
      POSTGRES_PASSWORD: ${DB_PASS:-gauzy_password}
      PGWEB_DATABASE_URL: postgres://${DB_USER:-postgres}:${DB_PASS:-gauzy_password}@${DB_HOST:-db}:${DB_PORT:-5432}/${DB_NAME:-gauzy}?sslmode=disable
    ports:
      - '8081:8081'
    networks:
      - overlay

volumes:
  postgres_data: {}
  redis_data: {}
  minio_data: {}
  cube_data: {}
  certificates: {}
  jitsu_workspace: {}
  opensearch-data: {}

networks:
  overlay:
    driver: bridge
