# Contributing to Gauzy Platform

We would love for you to contribute to Gauzy Platform!

## Submitting Pull Requests

If you're changing the structure of the repository please create an issue first.

By default, when you submitting contributions with Pull Requests, we require electronic submission of individual [Contributor Assignment Agreement (CAA)](https://gist.github.com/evereq/95f74ae09510766ffa9379006715ccfd). In some cases (when contributions are very small, at our discretion) instead of CAA we may accept submission of individual [Contributor License Agreement (CLA)](https://gist.github.com/evereq/53ddec283243481344fb61df1706ec40).

If you submitting contribution on behalf of some legal entity, you need to submit Entity Contributor Assignment Agreement (CAA) or Entity Contributor License Agreement (CLA), which you can request by sending us an <NAME_EMAIL>.

We are using open-source [CLA assistant](https://github.com/cla-assistant/cla-assistant) project to collect your signatures on CAA.  
The templates for our CAA/CLA documents generated by http://www.harmonyagreements.org.

## Submitting bug reports

Make sure you are on latest changes.
If you can, please provide more information about your environment such as browser, operating system, node version, and yarn version.

## Feature requests

You are more than welcome to submit future requests here https://github.com/ever-co/feature-requests/issues

## Legal

This is an open source project.  
Contributions you make to this public Gauzy Platform repository are completely voluntary.  
When you submit an issue, bug report, question, enhancement, pull request, etc., you are offering your contribution without expectation of payment, you expressly waive any future pay claims against the Ever Co. LTD related to your contribution, and you acknowledge that this does not create an obligation on the part of the Ever Co. LTD of any kind. Furthermore, your contributing to this project does not create an employer-employee relationship between the Ever Co. LTD and the contributor.

See also "Submitting Pull Requests" section above for more information on CAA/CLA, required for any contributions.
