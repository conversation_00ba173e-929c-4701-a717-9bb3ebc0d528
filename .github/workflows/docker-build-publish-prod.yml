name: Build and Publish Docker Images Prod

on:
  push:
    branches: master

concurrency:
  group: ${{ github.ref }}-${{ github.workflow }}
  cancel-in-progress: true

jobs:
  gauzy-api:
    runs-on: buildjet-16vcpu-ubuntu-2204
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          driver: docker

      - name: Build
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./.deploy/api/Dockerfile
          load: true
          platforms: linux/amd64
          tags: |
            ghcr.io/ever-co/gauzy-api:latest
            everco/gauzy-api:latest
            registry.digitalocean.com/ever/gauzy-api:latest
            ${{ secrets.CW_DOCKER_REGISTRY }}/ever-co/gauzy-api:latest
          cache-from: type=registry,ref=ghcr.io/ever-co/gauzy-api:latest
          cache-to: type=inline
          build-args: |
            NODE_ENV=production

      - name: Docker images list
        run: |
          sudo docker image list

      - name: Login to DockerHub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Push to Docker Hub Registry
        run: |
          docker push everco/gauzy-api:latest

      - name: Install doctl
        uses: digitalocean/action-doctl@v2
        with:
          token: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}

      - name: Log in to DigitalOcean Container Registry with short-lived credentials
        run: doctl registry login --expiry-seconds 3600

      - name: Push to DigitalOcean Registry
        run: |
          docker push registry.digitalocean.com/ever/gauzy-api:latest

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Push to Github Registry
        run: |
          docker push ghcr.io/ever-co/gauzy-api:latest

      - name: Login to CW Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ secrets.CW_DOCKER_REGISTRY }}
          username: ${{ secrets.CW_DOCKER_USER }}
          password: ${{ secrets.CW_DOCKER_USER_PASSWORD }}

    #  - name: Push to CW Registry
    #    run: |
    #      docker push ${{ secrets.CW_DOCKER_REGISTRY }}/ever-co/gauzy-api:latest

  gauzy-webapp:
    runs-on: buildjet-16vcpu-ubuntu-2204
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./.deploy/webapp/Dockerfile
          load: true
          platforms: linux/amd64
          tags: |
            ghcr.io/ever-co/gauzy-webapp:latest
            everco/gauzy-webapp:latest
            registry.digitalocean.com/ever/gauzy-webapp:latest
            ${{ secrets.CW_DOCKER_REGISTRY }}/ever-co/gauzy-webapp:latest
          cache-from: type=registry,ref=ghcr.io/ever-co/gauzy-webapp:latest
          cache-to: type=inline
          build-args: |
            NODE_ENV=production

      - name: Docker images list
        run: |
          sudo docker image list

      - name: Login to DockerHub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Push to Docker Hub Registry
        run: |
          docker push everco/gauzy-webapp:latest

      - name: Install doctl
        uses: digitalocean/action-doctl@v2
        with:
          token: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}

      - name: Log in to DigitalOcean Container Registry with short-lived credentials
        run: doctl registry login --expiry-seconds 3600

      - name: Push to DigitalOcean Registry
        run: |
          docker push registry.digitalocean.com/ever/gauzy-webapp:latest

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Push to Github Registry
        run: |
          docker push ghcr.io/ever-co/gauzy-webapp:latest

      - name: Login to CW Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ secrets.CW_DOCKER_REGISTRY }}
          username: ${{ secrets.CW_DOCKER_USER }}
          password: ${{ secrets.CW_DOCKER_USER_PASSWORD }}

    #  - name: Push to CW Registry
    #    run: |
    #      docker push ${{ secrets.CW_DOCKER_REGISTRY }}/ever-co/gauzy-webapp:latest
