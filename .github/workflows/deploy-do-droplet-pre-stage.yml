name: Deploy to DigitalOcean Droplet Stage Pre

on:
  push:
    branches:
      - droplets

jobs:
  deploy:
    runs-on: buildjet-4vcpu-ubuntu-2204

    environment: stage

    steps:
      - name: checkout out code
        uses: actions/checkout@v4

      - name: Globalise Ingress certificate type env
        run: |
          echo "INGRESS_CERT_TYPE=${{ env.INGRESS_CERT_TYPE }}" >> $GITHUB_ENV
        env:
          INGRESS_CERT_TYPE: 'letsencrypt'

      - name: Generate TLS Secrets for DO Droplet
        run: |
          rm -f $GITHUB_WORKSPACE/.deploy/ssh/with-cloudflare/stage/ingress.api.crt $GITHUB_WORKSPACE/.deploy/ssh/with-cloudflare/stage/ingress.api.key
          echo ${{ secrets.INGRESS_API_CERT }} | base64 --decode > $GITHUB_WORKSPACE/.deploy/ssh/with-cloudflare/stage/ingress.api.crt
          echo ${{ secrets.INGRESS_API_CERT_KEY }} | base64 --decode > $GITHUB_WORKSPACE/.deploy/ssh/with-cloudflare/stage/ingress.api.key

      - name: Copy file via scp - cloudflare
        uses: appleboy/scp-action@master
        with:
          host: ${{secrets.DO_DROPLET_STAGE_HOST}}
          username: ${{secrets.DO_DROPLET_USERNAME}}
          key: ${{secrets.DO_DROPLET_KEY}}
          source: '.deploy/ssh/with-cloudflare/stage/docker-compose.api.stage.cloudflare.pre.yml,.deploy/ssh/with-cloudflare/stage/nginx.stage.pre.cloudflare.conf,.deploy/ssh/with-cloudflare/stage/ingress.api.crt,.deploy/ssh/with-cloudflare/stage/ingress.api.key'
          target: '.'
      - name: Copy file via scp - letsencrypt
        uses: appleboy/scp-action@master
        with:
          host: ${{secrets.DO_DROPLET_STAGE_HOST}}
          username: ${{secrets.DO_DROPLET_USERNAME}}
          key: ${{secrets.DO_DROPLET_KEY}}
          source: '.deploy/ssh/with-letsencrypt/stage/docker-compose.api.stage.letsencrypt.pre.yml,.deploy/ssh/with-letsencrypt/stage/user_conf.d,.deploy/ssh/with-letsencrypt/stage/nginx-certbot.env'
          target: '.'
      - name: Install Docker
        uses: appleboy/ssh-action@master
        with:
          host: ${{secrets.DO_DROPLET_STAGE_HOST}}
          username: ${{secrets.DO_DROPLET_USERNAME}}
          key: ${{secrets.DO_DROPLET_KEY}}
          script: |
            if ! command -v docker &> /dev/null; then
                echo "Docker not installed. Installing..."
                sudo apt-get update
                sudo apt-get install -y docker.io
                sudo systemctl start docker
                sudo systemctl enable docker
            else
                echo "Docker is already installed."
            fi
      - name: Install Docker Compose
        uses: appleboy/ssh-action@master
        with:
          host: ${{secrets.DO_DROPLET_STAGE_HOST}}
          username: ${{secrets.DO_DROPLET_USERNAME}}
          key: ${{secrets.DO_DROPLET_KEY}}
          script: |
            if ! command -v docker-compose &> /dev/null; then
                echo "Docker Compose not installed. Installing..."
                sudo curl -L "https://github.com/docker/compose/releases/download/1.29.2/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
                sudo chmod +x /usr/local/bin/docker-compose
            else
                echo "Docker Compose is already installed."
            fi
      - name: Deploy to DigitalOcean Droplet
        uses: appleboy/ssh-action@master
        with:
          host: ${{secrets.DO_DROPLET_STAGE_HOST}}
          username: ${{secrets.DO_DROPLET_USERNAME}}
          key: ${{secrets.DO_DROPLET_KEY}}
          script: |
            if [ "${{ env.INGRESS_CERT_TYPE }}" = "cloudflare" ]; then
                docker-compose -f .deploy/ssh/with-letsencrypt/stage/docker-compose.api.stage.letsencrypt.pre.yml down
                docker-compose -f .deploy/ssh/with-cloudflare/stage/docker-compose.api.stage.cloudflare.pre.yml up -d
            elif [ "${{ env.INGRESS_CERT_TYPE }}" = "letsencrypt" ]; then
                docker-compose -f .deploy/ssh/with-cloudflare/stage/docker-compose.api.stage.cloudflare.pre.yml down
                docker-compose -f .deploy/ssh/with-letsencrypt/stage/docker-compose.api.stage.letsencrypt.pre.yml up -d
            else
                echo "Unknown INGRESS_CERT_TYPE: $INGRESS_CERT_TYPE"
                exit 1
            fi
