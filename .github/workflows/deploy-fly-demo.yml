name: Deploy to Fly Demo

on:
  workflow_run:
    workflows: ['Build and Publish Docker Images Demo']
    branches: [fly]
    types:
      - completed

jobs:
  deploy-demo:
    runs-on: buildjet-4vcpu-ubuntu-2204

    environment: demo

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - uses: superfly/flyctl-actions/setup-flyctl@master

      - name: Deploy API
        run: flyctl deploy --remote-only --config ./.fly/applications/api/fly.toml
        env:
          FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

      - name: Deploy Frontend
        run: flyctl deploy --remote-only --config ./.fly/applications/webapp/fly.toml
        env:
          FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}
