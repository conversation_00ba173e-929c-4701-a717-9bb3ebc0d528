name: Deploy to CoreWeave Demo

on:
  workflow_run:
    workflows: ['Build and Publish Docker Images Demo']
    branches: [cw]
    types:
      - completed

jobs:
  deploy-demo:
    runs-on: buildjet-4vcpu-ubuntu-2204

    environment: demo

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Create kubeconfig
        run: |
          mkdir ${HOME}/.kube
          echo ${{ secrets.CW_KUBECONFIG }} | base64 --decode > ${HOME}/.kube/config

      - name: Generate TLS Secrets for DemoCW and APIDemoCW
        run: |
          rm -f ${HOME}/ingress.api.crt ${HOME}/ingress.api.key ${HOME}/ingress.webapp.crt ${HOME}/ingress.webapp.key
          echo ${{ secrets.INGRESS_API_CERT }} | base64 --decode > ${HOME}/ingress.api.crt
          echo ${{ secrets.INGRESS_API_CERT_KEY }} | base64 --decode > ${HOME}/ingress.api.key
          echo ${{ secrets.INGRESS_WEBAPP_CERT }} | base64 --decode > ${HOME}/ingress.webapp.crt
          echo ${{ secrets.INGRESS_WEBAPP_CERT_KEY }} | base64 --decode > ${HOME}/ingress.webapp.key
          kubectl create secret tls apidemocw.gauzy.co-tls --save-config --dry-run=client --cert=${HOME}/ingress.api.crt --key=${HOME}/ingress.api.key -o yaml | kubectl apply -f -
          kubectl create secret tls democw.gauzy.co-tls --save-config --dry-run=client --cert=${HOME}/ingress.webapp.crt --key=${HOME}/ingress.webapp.key -o yaml | kubectl apply -f -

      - name: Apply k8s manifests changes in CoreWeave k8s cluster (if any)
        run: |
          envsubst < $GITHUB_WORKSPACE/.deploy/k8s/k8s-manifest.cw.demo.yaml | kubectl --context coreweave apply -f -
        env:
          # below we are using GitHub secrets for both frontend and backend
          CLOUD_PROVIDER: 'CW'
          DB_NAME: '${{ secrets.DB_NAME }}'
          SENTRY_DSN: '${{ secrets.SENTRY_DSN }}'
          SENTRY_TRACES_SAMPLE_RATE: '${{ secrets.SENTRY_TRACES_SAMPLE_RATE }}'
          SENTRY_PROFILE_SAMPLE_RATE: '${{ secrets.SENTRY_PROFILE_SAMPLE_RATE }}'
          SENTRY_HTTP_TRACING_ENABLED: '${{ secrets.SENTRY_HTTP_TRACING_ENABLED }}'
          SENTRY_POSTGRES_TRACKING_ENABLED: '${{ secrets.SENTRY_POSTGRES_TRACKING_ENABLED }}'
          SENTRY_PROFILING_ENABLED: '${{ secrets.SENTRY_PROFILING_ENABLED }}'
          OTEL_ENABLED: '${{ secrets.OTEL_ENABLED }}'
          OTEL_PROVIDER: '${{ secrets.OTEL_PROVIDER }}'
          OTEL_EXPORTER_OTLP_TRACES_ENDPOINT: '${{ secrets.OTEL_EXPORTER_OTLP_TRACES_ENDPOINT }}'
          OTEL_EXPORTER_OTLP_HEADERS: '${{ secrets.OTEL_EXPORTER_OTLP_HEADERS }}'
          FEATURE_OPEN_STATS: '${{ vars.FEATURE_OPEN_STATS }}'

      # we need this step because for now we just use :latest tag
      # note: for production we will use different strategy later
      - name: Restart Pods to pick up :latest tag version
        run: |
          kubectl --context coreweave rollout restart deployment/gauzy-demo-api
          kubectl --context coreweave rollout restart deployment/gauzy-demo-webapp
