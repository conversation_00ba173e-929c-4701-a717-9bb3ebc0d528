name: Currents Cypress Tests
on:
  push:
    branches: [nope]

concurrency:
  group: ${{ github.ref }}-${{ github.workflow }}
  cancel-in-progress: true

jobs:
  prepare:
    runs-on: buildjet-4vcpu-ubuntu-2204
    outputs:
      uuid: ${{ steps.uuid.outputs.value }}
    steps:
      - name: Generate unique ID 💎
        id: uuid
        # take the current commit + timestamp together
        # the typical value would be something like
        # "sha-5d3fe...35d3-time-1620841214"
        run: echo "::set-output name=value::sha-$GITHUB_SHA-time-$(date +"%s")"

      - name: Print unique ID 🖨`
        run: echo "Generated Unique Build ID ${{ steps.uuid.outputs.value }}"

  e2e-tests:
    runs-on: [self-hosted]
    needs: ['prepare']

    # we can try to run tests in the Docker containers later
    # container: cypress/browsers:node14.17.0-chrome88-ff89

    strategy:
      # when one test fails, DO NOT cancel the other
      # containers, because this will kill Cypress processes
      # leaving the Dashboard hanging ...
      # https://github.com/cypress-io/github-action/issues/48
      fail-fast: false

      matrix:
        # run copies of the current job in parallel on different runners
        containers: [1, 2]

    env:
      # Note: Cache folder will be different on Linux runners
      CYPRESS_CACHE_FOLDER: C:\Users\<USER>\AppData\Local\Cypress\Cache

      # We are using Currents Dashboard for now, but we can replace with our self-hosted if needed
      CYPRESS_API_URL: https://cy.currents.dev/

      # Note: CYPRESS_PACKAGE_CONFIG_PATH value should be adjusted for Linux runners
      CYPRESS_PACKAGE_CONFIG_PATH: C:\Users\<USER>\AppData\Local\Cypress\Cache\8.3.1\Cypress\resources\app\packages\server\config\app.yml

      # Currents use different record key compared to Cypress
      CURRENTS_CYPRESS_RECORD_KEY: ${{ secrets.CURRENTS_CYPRESS_RECORD_KEY }}

      # we don't use original Cypress Dashboard here, but we want to override values with correct from Currents
      CYPRESS_RECORD_KEY: ${{ secrets.CURRENTS_CYPRESS_RECORD_KEY }}
      CYPRESS_PROJECT_ID: ${{ secrets.CURRENTS_CYPRESS_PROJECT_ID }}

    timeout-minutes: 360

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Print custom build id 🖨
        run: echo "Custom build id is ${{ needs.prepare.outputs.uuid }}"

      - name: Get yarn cache directory path
        id: yarn-cache-dir-path
        run: echo "::set-output name=dir::$(yarn cache dir)"

      - uses: actions/cache@v3
        id: yarn-cache
        with:
          path: ${{ steps.yarn-cache-dir-path.outputs.dir }}
          key: ${{ runner.os }}-yarn-${{ hashFiles('yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-yarn-

      # - name: Increase file limit
      #  run: echo fs.inotify.max_user_watches=524288 | sudo tee -a /etc/sysctl.conf && sudo sysctl -p

      - name: Output build number
        run: echo "Running in Build ${{ github.run_id }}-${{ github.run_number }}"

      - name: Add Yarn to path
        run: echo "C:\Users\<USER>\AppData\Roaming\npm" | Out-File -FilePath $env:GITHUB_PATH -Encoding utf8 -Append

      - name: Install Packages & Bootstrap
        run: yarn bootstrap

      - name: Build all packages
        run: yarn build:package:all

      - name: Install forever package
        run: npm install forever -g

      - name: Run API in background
        run: yarn start:api:forever

      - name: Run UI in background
        run: yarn start:gauzy:forever

      - name: Install Required Packages
        run: npm install -g wait-on cypress@8.3.1 @currents/cli https://github.com/sorry-cypress/cy2#feat/use-CYPRESS_PACKAGE_CONFIG_PATH

      # - name: Install Cypress for our e2e tests
      #   run: |
      #     cd apps/gauzy-e2e
      #     yarn cypress install

      - name: Cypress info
        env:
          # make sure every Cypress install prints minimal information
          CI: 1
        # print Cypress and OS info
        run: |
          cypress verify
          cypress info
          cypress version
          cypress version --component package
          cypress version --component binary
          cypress version --component electron
          cypress version --component node

      - name: Wait API
        run: |
          wait-on http://localhost:3000/api -l -t 600000

      - name: Wait UI
        run: |
          wait-on http://localhost:4200 -l -t 600000

      - name: Currents Cypress run
        env:
          DEBUG: cy2*
        run: |
          echo 'Starting Cypress Tests ...'
          cd apps/gauzy-e2e
          cy2 run --parallel --record --key ${{ secrets.CURRENTS_CYPRESS_RECORD_KEY }} --ci-build-id ${{ needs.prepare.outputs.uuid }} -C cypress.json -c "projectId=${{ secrets.CURRENTS_CYPRESS_PROJECT_ID }}" --group "1 - all e2e tests"
