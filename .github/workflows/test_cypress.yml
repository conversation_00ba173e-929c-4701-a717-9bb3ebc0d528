name: Cypress Tests
on:
  push:
    branches: [nope]

concurrency:
  group: ${{ github.ref }}-${{ github.workflow }}
  cancel-in-progress: true

jobs:
  e2e-tests-setup:
    runs-on: buildjet-4vcpu-ubuntu-2204

    timeout-minutes: 360

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Cypress install
        uses: cypress-io/github-action@v5

        # let's give this action an ID so we can refer to its output values later
        id: cypress

        with:
          # we want to install Cypress only
          install: true
          install-command: node -p 'os.cpus()'

          # we don't want to run tests in this job, we only setup Cypress here
          runTests: false

        env:
          CYPRESS_RECORD_KEY: ${{ secrets.CYPRESS_RECORD_KEY }}
          CYPRESS_PROJECT_ID: ${{ secrets.CYPRESS_PROJECT_ID }}
          # pass GitHub token to allow accurately detecting a build vs a re-run build
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  e2e-tests:
    runs-on: [self-hosted]

    # we can try to run tests in the Docker containers later
    # container: cypress/browsers:node14.17.0-chrome88-ff89

    needs: e2e-tests-setup

    strategy:
      # when one test fails, DO NOT cancel the other
      # containers, because this will kill Cypress processes
      # leaving the Dashboard hanging ...
      # https://github.com/cypress-io/github-action/issues/48
      fail-fast: false

      matrix:
        # run copies of the current job in parallel on different runners
        containers: [1, 2]

    env:
      CYPRESS_CACHE_FOLDER: C:\Users\<USER>\AppData\Local\Cypress\Cache

    timeout-minutes: 360

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Get yarn cache directory path
        id: yarn-cache-dir-path
        run: echo "::set-output name=dir::$(yarn cache dir)"

      - uses: actions/cache@v3
        id: yarn-cache
        with:
          path: ${{ steps.yarn-cache-dir-path.outputs.dir }}
          key: ${{ runner.os }}-yarn-${{ hashFiles('yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-yarn-

      # - name: Increase file limit
      #  run: echo fs.inotify.max_user_watches=524288 | sudo tee -a /etc/sysctl.conf && sudo sysctl -p

      - name: Add Yarn to path
        run: echo "C:\Users\<USER>\AppData\Roaming\npm" | Out-File -FilePath $env:GITHUB_PATH -Encoding utf8 -Append

      - name: Install Packages & Bootstrap
        run: yarn bootstrap

      - name: Build all packages
        run: yarn build:package:all

      - name: Install forever package
        run: npm install forever -g

      - name: Run API in background
        run: yarn start:api:forever

      - name: Run UI in background
        run: yarn start:gauzy:forever

      - name: Install Cypress Package
        run: npm install -g cypress@8.3.1

      - name: Install Cypress for our e2e tests
        run: |
          cd apps/gauzy-e2e
          yarn cypress install

      - name: Cypress info
        env:
          # make sure every Cypress install prints minimal information
          CI: 1
        # print Cypress and OS info
        run: |
          npx cypress verify
          npx cypress info
          npx cypress version
          npx cypress version --component package
          npx cypress version --component binary
          npx cypress version --component electron
          npx cypress version --component node

      - name: Cypress run
        uses: cypress-io/github-action@v5

        # let's give this action an ID so we can refer to its output values later
        id: cypress

        # Continue the build in case of an error, as we need to set the
        # commit status in the next step, both in case of success and failure
        continue-on-error: true

        with:
          # we have already installed all dependencies above
          install: false

          # record using CYPRESS_RECORD_KEY defined in env
          record: true

          # run tests in parallel
          parallel: true

          group: '1 - all e2e tests'

          # Cypress tests and config file are in "apps/gauzy-e2e" folder
          working-directory: 'apps/gauzy-e2e'

          # We wait till both API and UI runs completely here
          wait-on: 'http://localhost:3000/api,http://localhost:4200'

          # wait up to 20 minutes for the servers to respond
          wait-on-timeout: 1200

          browser: chrome

          headless: true

          config-file: cypress.json

        env:
          CYPRESS_RECORD_KEY: ${{ secrets.CYPRESS_RECORD_KEY }}
          CYPRESS_PROJECT_ID: ${{ secrets.CYPRESS_PROJECT_ID }}
          # pass GitHub token to allow accurately detecting a build vs a re-run build
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Print Dashboard URL
        run: |
          echo Cypress finished with: ${{ steps.cypress.outcome }}
          echo See results at ${{ steps.cypress.outputs.dashboardUrl }}
