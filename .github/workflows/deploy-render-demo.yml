name: Deploy to Render Demo

on:
  workflow_run:
    workflows: ['Build and Publish Docker Images Demo']
    branches: [render]
    types:
      - completed

jobs:
  deploy-demo:
    runs-on: buildjet-4vcpu-ubuntu-2204

    environment: demo

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Render CLI
        run: |
          wget  -O render https://github.com/render-oss/render-cli/releases/download/v0.1.8/render-linux-x86_64
          chmod +x render
          sudo mv render /usr/local/bin/render
          mkdir ~/.render
          cat << EOF > ~/.render/config.yaml
                version: 1
                sshPreserveHosts: true
                profiles:
                  default:
                    defaultRegion: oregon
                    apiKey: ${{ secrets.RENDER_API_KEY }}
                  demo:
                    defaultRegion: oregon
                    apiKey: ${{ secrets.RENDER_API_KEY }}
                EOF

      - name: Deploy Services
        run: |
          cp .render/render.demo.yaml ./render.yaml
          render blueprint launch --profile demo
