name: 'Check Spelling and Typos with cspell'
on:
  push:
    branches: [develop]
  pull_request:
    branches: [develop]

concurrency:
  group: ${{ github.ref }}-${{ github.workflow }}
  cancel-in-progress: true

jobs:
  spellcheck:
    name: Cspell
    runs-on: buildjet-4vcpu-ubuntu-2204
    steps:
      - uses: actions/checkout@v4
      - uses: streetsidesoftware/cspell-action@v2
        with:
          # Github token used to fetch the list of changed files in the commit.
          github_token: ${{ secrets.GITHUB_TOKEN }}
          strict: true
          config: '.cspell.json'
