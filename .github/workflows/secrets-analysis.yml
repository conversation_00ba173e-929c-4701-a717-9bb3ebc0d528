name: 'Secrets Analysis'

on:
  push:
    branches:
      - develop
  pull_request:

concurrency:
  group: ${{ github.ref }}-${{ github.workflow }}
  cancel-in-progress: true

jobs:
  TruffleHog:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Secret Scanning (TruffleHog OSS)
        uses: trufflesecurity/trufflehog@v3.63.4
        with:
          path: ./
          extra_args: --only-verified
