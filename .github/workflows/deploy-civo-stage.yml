name: Deploy to Civo Stage

on:
  workflow_run:
    workflows: ['Build and Publish Docker Images Stage']
    branches: [civo]
    types:
      - completed

jobs:
  deploy-stage:
    runs-on: buildjet-4vcpu-ubuntu-2204

    environment: stage

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Create kubeconfig
        run: |
          mkdir ${HOME}/.kube
          echo ${{ secrets.CIVO_KUBECONFIG }} | base64 --decode > ${HOME}/.kube/config

      - name: Generate TLS Secrets for DemoCW and APIDemoCW
        run: |
          rm -f ${HOME}/ingress.api.crt ${HOME}/ingress.api.key ${HOME}/ingress.webapp.crt ${HOME}/ingress.webapp.key
          echo ${{ secrets.INGRESS_API_CERT }} | base64 --decode > ${HOME}/ingress.api.crt
          echo ${{ secrets.INGRESS_API_CERT_KEY }} | base64 --decode > ${HOME}/ingress.api.key
          echo ${{ secrets.INGRESS_WEBAPP_CERT }} | base64 --decode > ${HOME}/ingress.webapp.crt
          echo ${{ secrets.INGRESS_WEBAPP_CERT_KEY }} | base64 --decode > ${HOME}/ingress.webapp.key
          kubectl create secret tls apistagecivo.gauzy.co-tls --save-config --dry-run=client --cert=${HOME}/ingress.api.crt --key=${HOME}/ingress.api.key -o yaml | kubectl apply -f -
          kubectl create secret tls stagecivo.gauzy.co-tls --save-config --dry-run=client --cert=${HOME}/ingress.webapp.crt --key=${HOME}/ingress.webapp.key -o yaml | kubectl apply -f -

      - name: Write PostgreSQL Certificate file
        run: |
          echo "$DB_CA_CERT" | base64 --decode > ${HOME}/ca-certificate.crt
        env:
          DB_CA_CERT: '${{ secrets.DB_CA_CERT }}'

      - name: Apply k8s manifests changes in DigitalOcean k8s cluster (if any)
        run: |
          envsubst < $GITHUB_WORKSPACE/.deploy/k8s/k8s-manifest.civo.stage.yaml | kubectl --context ever apply -f -
        env:
          # below we are using GitHub secrets for both frontend and backend
          DB_TYPE: '${{ secrets.DB_TYPE }}'
          DB_URI: '${{ secrets.DB_URI }}'
          # Note: for now we are using DB in different provider, so we have to use public hostname
          DB_HOST: '${{ secrets.DB_HOST_CIVO }}'
          DB_USER: '${{ secrets.DB_USER }}'
          DB_PASS: '${{ secrets.DB_PASS }}'
          # Note that for staging we are using for now same server, just different DB name
          DB_NAME: '${{ secrets.DB_NAME }}'
          DB_PORT: '${{ secrets.DB_PORT }}'
          DB_CA_CERT: '${{ secrets.DB_CA_CERT }}'
          DB_SSL_MODE: '${{ secrets.DB_SSL_MODE }}'
          DB_POOL_SIZE: '${{ secrets.DB_POOL_SIZE }}'
          DB_POOL_SIZE_KNEX: '${{ secrets.DB_POOL_SIZE_KNEX }}'
          REDIS_ENABLED: '${{ secrets.REDIS_ENABLED }}'
          REDIS_URL: '${{ secrets.REDIS_URL }}'
          CLOUD_PROVIDER: 'CIVO'
          SENTRY_DSN: '${{ secrets.SENTRY_DSN }}'
          SENTRY_TRACES_SAMPLE_RATE: '${{ secrets.SENTRY_TRACES_SAMPLE_RATE }}'
          SENTRY_PROFILE_SAMPLE_RATE: '${{ secrets.SENTRY_PROFILE_SAMPLE_RATE }}'
          SENTRY_HTTP_TRACING_ENABLED: '${{ secrets.SENTRY_HTTP_TRACING_ENABLED }}'
          SENTRY_POSTGRES_TRACKING_ENABLED: '${{ secrets.SENTRY_POSTGRES_TRACKING_ENABLED }}'
          SENTRY_PROFILING_ENABLED: '${{ secrets.SENTRY_PROFILING_ENABLED }}'
          AWS_ACCESS_KEY_ID: '${{ secrets.AWS_ACCESS_KEY_ID }}'
          AWS_SECRET_ACCESS_KEY: '${{ secrets.AWS_SECRET_ACCESS_KEY }}'
          AWS_REGION: '${{ secrets.AWS_REGION }}'
          AWS_S3_BUCKET: '${{ secrets.AWS_S3_BUCKET }}'
          WASABI_ACCESS_KEY_ID: '${{ secrets.WASABI_ACCESS_KEY_ID }}'
          WASABI_SECRET_ACCESS_KEY: '${{ secrets.WASABI_SECRET_ACCESS_KEY }}'
          WASABI_REGION: '${{ secrets.WASABI_REGION }}'
          WASABI_SERVICE_URL: '${{ secrets.WASABI_SERVICE_URL }}'
          WASABI_S3_BUCKET: '${{ secrets.WASABI_S3_BUCKET }}'
          WASABI_S3_FORCE_PATH_STYLE: '${{ secrets.WASABI_S3_FORCE_PATH_STYLE }}'
          DIGITALOCEAN_ACCESS_KEY_ID: '${{ secrets.DIGITALOCEAN_ACCESS_KEY_ID }}'
          DIGITALOCEAN_SECRET_ACCESS_KEY: '${{ secrets.DIGITALOCEAN_SECRET_ACCESS_KEY }}'
          DIGITALOCEAN_REGION: '${{ secrets.DIGITALOCEAN_REGION }}'
          DIGITALOCEAN_SERVICE_URL: '${{ secrets.DIGITALOCEAN_SERVICE_URL }}'
          DIGITALOCEAN_CDN_URL: '${{ secrets.DIGITALOCEAN_CDN_URL }}'
          DIGITALOCEAN_S3_BUCKET: '${{ secrets.DIGITALOCEAN_S3_BUCKET }}'
          DIGITALOCEAN_S3_FORCE_PATH_STYLE: '${{ secrets.DIGITALOCEAN_S3_FORCE_PATH_STYLE }}'
          EXPRESS_SESSION_SECRET: '${{ secrets.EXPRESS_SESSION_SECRET }}'
          JWT_SECRET: '${{ secrets.JWT_SECRET }}'
          JWT_REFRESH_TOKEN_SECRET: '${{ secrets.JWT_REFRESH_TOKEN_SECRET }}'
          JWT_REFRESH_TOKEN_EXPIRATION_TIME: '${{ secrets.JWT_REFRESH_TOKEN_EXPIRATION_TIME }}'
          CLOUDINARY_API_KEY: '${{ secrets.CLOUDINARY_API_KEY }}'
          CLOUDINARY_API_SECRET: '${{ secrets.CLOUDINARY_API_SECRET }}'
          CLOUDINARY_CLOUD_NAME: '${{ secrets.CLOUDINARY_CLOUD_NAME }}'
          MAIL_FROM_ADDRESS: '${{ secrets.MAIL_FROM_ADDRESS }}'
          MAIL_HOST: '${{ secrets.MAIL_HOST }}'
          MAIL_PORT: '${{ secrets.MAIL_PORT }}'
          MAIL_USERNAME: '${{ secrets.MAIL_USERNAME }}'
          MAIL_PASSWORD: '${{ secrets.MAIL_PASSWORD }}'
          ALLOW_SUPER_ADMIN_ROLE: '${{ secrets.ALLOW_SUPER_ADMIN_ROLE }}'
          GOOGLE_CLIENT_ID: '${{ secrets.GOOGLE_CLIENT_ID }}'
          GOOGLE_CLIENT_SECRET: '${{ secrets.GOOGLE_CLIENT_SECRET }}'
          GOOGLE_CALLBACK_URL: '${{ secrets.GOOGLE_CALLBACK_URL }}'
          FACEBOOK_CLIENT_ID: '${{ secrets.FACEBOOK_CLIENT_ID }}'
          FACEBOOK_CLIENT_SECRET: '${{ secrets.FACEBOOK_CLIENT_SECRET }}'
          FACEBOOK_GRAPH_VERSION: '${{ secrets.FACEBOOK_GRAPH_VERSION }}'
          FACEBOOK_CALLBACK_URL: '${{ secrets.FACEBOOK_CALLBACK_URL }}'
          INTEGRATED_USER_DEFAULT_PASS: '${{ secrets.INTEGRATED_USER_DEFAULT_PASS }}'
          UPWORK_REDIRECT_URL: '${{ secrets.UPWORK_REDIRECT_URL }}'
          FILE_PROVIDER: '${{ secrets.FILE_PROVIDER }}'
          GAUZY_AI_GRAPHQL_ENDPOINT: '${{ secrets.GAUZY_AI_GRAPHQL_ENDPOINT }}'
          GAUZY_AI_REST_ENDPOINT: '${{ secrets.GAUZY_AI_REST_ENDPOINT }}'
          UNLEASH_APP_NAME: '${{ secrets.UNLEASH_APP_NAME }}'
          UNLEASH_API_URL: '${{ secrets.UNLEASH_API_URL }}'
          UNLEASH_INSTANCE_ID: '${{ secrets.UNLEASH_INSTANCE_ID }}'
          UNLEASH_REFRESH_INTERVAL: '${{ secrets.UNLEASH_REFRESH_INTERVAL }}'
          UNLEASH_METRICS_INTERVAL: '${{ secrets.UNLEASH_METRICS_INTERVAL }}'
          UNLEASH_API_KEY: '${{ secrets.UNLEASH_API_KEY }}'
          JITSU_SERVER_URL: '${{ secrets.JITSU_SERVER_URL }}'
          JITSU_SERVER_WRITE_KEY: '${{ secrets.JITSU_SERVER_WRITE_KEY }}'
          OTEL_ENABLED: '${{ secrets.OTEL_ENABLED }}'
          OTEL_PROVIDER: '${{ secrets.OTEL_PROVIDER }}'
          OTEL_EXPORTER_OTLP_TRACES_ENDPOINT: '${{ secrets.OTEL_EXPORTER_OTLP_TRACES_ENDPOINT }}'
          OTEL_EXPORTER_OTLP_HEADERS: '${{ secrets.OTEL_EXPORTER_OTLP_HEADERS }}'
          GAUZY_GITHUB_CLIENT_ID: '${{ secrets.GAUZY_GITHUB_CLIENT_ID }}'
          GAUZY_GITHUB_CLIENT_SECRET: '${{ secrets.GAUZY_GITHUB_CLIENT_SECRET }}'
          GAUZY_GITHUB_APP_PRIVATE_KEY: '${{ secrets.GAUZY_GITHUB_APP_PRIVATE_KEY }}'
          GAUZY_GITHUB_WEBHOOK_URL: '${{ secrets.GAUZY_GITHUB_WEBHOOK_URL }}'
          GAUZY_GITHUB_WEBHOOK_SECRET: '${{ secrets.GAUZY_GITHUB_WEBHOOK_SECRET }}'
          GAUZY_GITHUB_APP_NAME: '${{ secrets.GAUZY_GITHUB_APP_NAME }}'
          GAUZY_GITHUB_REDIRECT_URL: '${{ secrets.GAUZY_GITHUB_REDIRECT_URL }}'
          GAUZY_GITHUB_POST_INSTALL_URL: '${{ secrets.GAUZY_GITHUB_POST_INSTALL_URL }}'
          GAUZY_GITHUB_APP_ID: '${{ secrets.GAUZY_GITHUB_APP_ID }}'
          GAUZY_GITHUB_OAUTH_CLIENT_ID: '${{ secrets.GAUZY_GITHUB_OAUTH_CLIENT_ID }}'
          GAUZY_GITHUB_OAUTH_CLIENT_SECRET: '${{ secrets.GAUZY_GITHUB_OAUTH_CLIENT_SECRET }}'
          GAUZY_GITHUB_OAUTH_CALLBACK_URL: '${{ secrets.GAUZY_GITHUB_OAUTH_CALLBACK_URL }}'
          JITSU_BROWSER_URL: '${{ secrets.JITSU_BROWSER_URL }}'
          JITSU_BROWSER_WRITE_KEY: '${{ secrets.JITSU_BROWSER_WRITE_KEY }}'
          MAGIC_CODE_EXPIRATION_TIME: '${{ secrets.MAGIC_CODE_EXPIRATION_TIME }}'
          APP_NAME: '${{ secrets.APP_NAME }}'
          APP_LOGO: '${{ secrets.APP_LOGO }}'
          APP_SIGNATURE: '${{ secrets.APP_SIGNATURE }}'
          APP_LINK: '${{ secrets.APP_LINK }}'
          APP_EMAIL_CONFIRMATION_URL: '${{ secrets.APP_EMAIL_CONFIRMATION_URL }}'
          APP_MAGIC_SIGN_URL: '${{ secrets.APP_MAGIC_SIGN_URL }}'
          COMPANY_LINK: '${{ secrets.COMPANY_LINK }}'
          COMPANY_NAME: '${{ secrets.COMPANY_NAME }}'
          FEATURE_OPEN_STATS: '${{ vars.FEATURE_OPEN_STATS }}'

      # we need this step because for now we just use :latest tag
      # note: for production we will use different strategy later
      - name: Restart Pods to pick up :latest tag version
        run: |
          kubectl --context ever rollout restart deployment/gauzy-stage-api
          kubectl --context ever rollout restart deployment/gauzy-stage-webapp
