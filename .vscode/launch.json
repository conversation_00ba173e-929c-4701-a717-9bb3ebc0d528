{
	// Use IntelliSense to learn about possible attributes.
	// Hover to view descriptions of existing attributes.
	// For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
	"version": "0.2.0",
	"configurations": [
	
		{
			"type": "node",
			"request": "attach",
			"name": "Debug Server",
			"sourceMaps": true,
			"restart": true,
			"port": 9229
		},
		{
			"type": "node",
			"request": "launch",
			"name": "Launch Program",
			"program": "${workspaceFolder}\\start:gauzy",
			"preLaunchTask": "tsc: build - tsconfig.json",
			"outFiles": ["${workspaceFolder}/dist/out-tsc/**/*.js"]
		},

		{
			"type": "node",
			"request": "launch",
			"name": "Debug Nest Framework",
			"args": ["${workspaceFolder}/src/main.ts"],
			"runtimeArgs": ["--nolazy", "-r", "ts-node/register"],
			"sourceMaps": true,
			"cwd": "${workspaceRoot}",
			"protocol": "inspector"
		}
	]
}
