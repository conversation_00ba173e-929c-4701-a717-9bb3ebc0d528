app = "ever-gauzy-webapp"
kill_signal = "SIGINT"
kill_timeout = 30
primary_region = "lax"

[build]
  image = "ghcr.io/ever-co/gauzy-webapp-demo:latest"

[env]
  DEMO="true"
  API_BASE_URL="https://ever-gauzy-api.fly.dev"
  CLIENT_BASE_URL="https://ever-gauzy-webapp.fly.dev"
  SENTRY_DSN="https://<EMAIL>/4397292"
  CHATWOOT_SDK_TOKEN="jFoSXEjGmqhUhqU3zfgkFfMt"
  CLOUDINARY_API_KEY="256868982483961"
  CLOUDINARY_CLOUD_NAME="dv6ezkfxg"
  GOOGLE_MAPS_API_KEY="AIzaSyCJmnKzgTSq5Pc93HXar5bZrdmANTwtSIo"
  GOOGLE_PLACE_AUTOCOMPLETE="true"
  DEFAULT_LATITUDE="42.6459136"
  DEFAULT_LONGITUDE="23.3332736"
  DEFAULT_CURRENCY="USD"

[http_service]
  internal_port = 4200
  force_https = true
  auto_stop_machines = true
  auto_start_machines = true
  min_machines_running = 0
  processes = ["app"]

[checks]
  [checks.http_health_check]
    port = 4200
    type = "http"
    interval = "15s"
    timeout = "60s"
    grace_period = "60s"
    method = "get"
    path = "/"
