app = "ever-gauzy-api"
kill_signal = "SIGINT"
kill_timeout = 300
primary_region = "lax"

[[mounts]]
  source = "api_data"
  destination = "/srv/gauzy/apps/api/data"
  processes= ["app"]

[build]
  image = "ghcr.io/ever-co/gauzy-api-demo:latest"

[env]
  API_HOST="0.0.0.0"
  API_PORT="3000"
  DEMO="true"
  NODE_ENV="development"
  ADMIN_PASSWORD_RESET="true"
  LOG_LEVEL="info"
  SENTRY_DSN="https://<EMAIL>/4397292"
  SENTRY_HTTP_TRACING_ENABLED="false"
  SENTRY_PROFILING_ENABLED="false"
  SENTRY_POSTGRES_TRACKING_ENABLED="false"
  API_BASE_URL="https://ever-gauzy-api.fly.dev"
  CLIENT_BASE_URL="https://ever-gauzy-webapp.fly.dev"
  EXPRESS_SESSION_SECRET="gauzy"
  JWT_SECRET="secretKey"
  JWT_REFRESH_TOKEN_SECRET="refreshSecretKey"
  JWT_REFRESH_TOKEN_EXPIRATION_TIME="86400"

[http_service]
  internal_port = 3000
  force_https = true
  auto_stop_machines = true
  auto_start_machines = true
  min_machines_running = 0
  processes = ["app"]

[checks]
  [checks.http_health_check]
    port = 3000
    type = "http"
    interval = "15s"
    timeout = "60s"
    grace_period = "60s"
    method = "get"
    path = "/api/health"
