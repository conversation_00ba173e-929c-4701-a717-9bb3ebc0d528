{"compilerOptions": {"outDir": "./dist/out-tsc", "resolveJsonModule": true, "typeRoots": ["node_modules/@types"], "paths": {"@angular/*": ["./node_modules/@angular/*"], "@env-desktop/environment": ["./apps/desktop/src/environments/environment.ts"], "@gauzy/auth": ["./packages/auth/src/index.ts"], "@gauzy/common": ["./packages/common/src/index.ts"], "@gauzy/config": ["./packages/config/src/index.ts"], "@gauzy/constants": ["./packages/constants/src/index.ts"], "@gauzy/contracts": ["./packages/contracts/src/index.ts"], "@gauzy/core": ["./packages/core/src/index.ts"], "@gauzy/desktop-activity": ["./packages/desktop-activity/src/index.ts"], "@gauzy/desktop-core": ["./packages/desktop-core/src/index.ts"], "@gauzy/desktop-timer": ["./packages/desktop-timer/src/index.ts"], "@gauzy/desktop-lib": ["./packages/desktop-lib/src/index.ts"], "@gauzy/desktop-ui-lib": ["./packages/desktop-ui-lib/src/index.ts"], "@gauzy/desktop-window": ["./packages/desktop-window/src/index.ts"], "@gauzy/plugin": ["./packages/plugin/src/index.ts"], "@gauzy/plugin-changelog": ["./packages/plugins/changelog/src/index.ts"], "@gauzy/plugin-integration-activepieces": ["./packages/plugins/integration-activepieces/src/index.ts"], "@gauzy/plugin-integration-ai": ["./packages/plugins/integration-ai/src/index.ts"], "@gauzy/plugin-integration-ai-ui": ["./packages/plugins/integration-ai-ui/src/index.ts"], "@gauzy/plugin-integration-github": ["./packages/plugins/integration-github/src/index.ts"], "@gauzy/plugin-integration-github-ui": ["./packages/plugins/integration-github-ui/src/index.ts"], "@gauzy/plugin-integration-hubstaff": ["./packages/plugins/integration-hubstaff/src/index.ts"], "@gauzy/plugin-integration-hubstaff-ui": ["./packages/plugins/integration-hubstaff-ui/src/index.ts"], "@gauzy/plugin-integration-jira": ["./packages/plugins/integration-jira/src/index.ts"], "@gauzy/plugin-integration-make-com": ["./packages/plugins/integration-make-com/src/index.ts"], "@gauzy/plugin-integration-make-com-ui": ["./packages/plugins/integration-make-com-ui/src/index.ts"], "@gauzy/plugin-integration-zapier": ["./packages/plugins/integration-zapier/src/index.ts"], "@gauzy/plugin-integration-zapier-ui": ["./packages/plugins/integration-zapier-ui/src/index.ts"], "@gauzy/plugin-integration-upwork": ["./packages/plugins/integration-upwork/src/index.ts"], "@gauzy/plugin-integration-upwork-ui": ["./packages/plugins/integration-upwork-ui/src/index.ts"], "@gauzy/plugin-integration-wakatime": ["./packages/plugins/integration-wakatime/src/index.ts"], "@gauzy/plugin-jitsu-analytics": ["./packages/plugins/jitsu-analytics/src/index.ts"], "@gauzy/plugin-job-employee-ui": ["./packages/plugins/job-employee-ui/src/index.ts"], "@gauzy/plugin-job-matching-ui": ["./packages/plugins/job-matching-ui/src/index.ts"], "@gauzy/plugin-job-proposal": ["./packages/plugins/job-proposal/src/index.ts"], "@gauzy/plugin-job-proposal-ui": ["./packages/plugins/job-proposal-ui/src/index.ts"], "@gauzy/plugin-job-search": ["./packages/plugins/job-search/src/index.ts"], "@gauzy/plugin-job-search-ui": ["./packages/plugins/job-search-ui/src/index.ts"], "@gauzy/plugin-legal-ui": ["./packages/plugins/legal-ui/src/index.ts"], "@gauzy/plugin-knowledge-base": ["./packages/plugins/knowledge-base/src/index.ts"], "@gauzy/plugin-maintenance-ui": ["./packages/plugins/maintenance-ui/src/index.ts"], "@gauzy/plugin-onboarding-ui": ["./packages/plugins/onboarding-ui/src/index.ts"], "@gauzy/plugin-product-reviews": ["./packages/plugins/product-reviews/src/index.ts"], "@gauzy/plugin-public-layout-ui": ["./packages/plugins/public-layout-ui/src/index.ts"], "@gauzy/plugin-registry": ["./packages/plugins/registry/src/index.ts"], "@gauzy/plugin-sentry": ["./packages/plugins/sentry-tracing/src/index.ts"], "@gauzy/plugin-posthog": ["./packages/plugins/posthog/src/index.ts"], "@gauzy/plugin-posthog-ui": ["./packages/plugins/posthog-ui/src/index.ts"], "@gauzy/plugin-videos": ["./packages/plugins/videos/src/index.ts"], "@gauzy/plugin-videos-ui": ["./packages/plugins/videos-ui/src/index.ts"], "@gauzy/plugin-camshot": ["./packages/plugins/camshot/src/index.ts"], "@gauzy/plugin-soundshot": ["./packages/plugins/soundshot/src/index.ts"], "@gauzy/ui-auth": ["./packages/ui-auth/src/index.ts"], "@gauzy/ui-config": ["./packages/ui-config/src/index.ts"], "@gauzy/ui-core": ["./packages/ui-core/src/index.ts"], "@gauzy/ui-core/*": ["./packages/ui-core/*/src/index.ts"], "@gauzy/utils": ["./packages/utils/src/index.ts"], "@nebular/*": ["./node_modules/@nebular/*"], "ng2-completer": ["./node_modules/@akveo/ng2-completer"], "ngx-daterangepicker-material/*": ["./node_modules/ngx-daterangepicker-material/*"]}, "preserveConstEnums": true, "allowSyntheticDefaultImports": true, "noUnusedLocals": false, "noImplicitAny": false, "esModuleInterop": false, "useDefineForClassFields": false}, "extends": "./tsconfig.base.json"}