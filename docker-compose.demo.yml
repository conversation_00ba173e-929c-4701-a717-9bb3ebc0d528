services:
  db:
    image: postgres:17-alpine
    container_name: db
    restart: always
    environment:
      POSTGRES_DB: ${DB_NAME:-gauzy}
      POSTGRES_USER: ${DB_USER:-postgres}
      POSTGRES_PASSWORD: ${DB_PASS:-gauzy_password}
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U $${POSTGRES_USER} -d $${POSTGRES_DB} || exit 1']
      interval: 10s
      timeout: 5s
      retries: 5
    volumes:
      - postgres_data:/var/lib/postgresql/data/
      - ./.deploy/db/init-user-db.sh:/docker-entrypoint-initdb.d/init-user-db.sh
    ports:
      - '5432:5432'
    networks:
      - overlay

  api:
    container_name: api
    image: ghcr.io/ever-co/gauzy-api:latest
    environment:
      API_HOST: ${API_HOST:-api}
      API_PORT: ${API_PORT:-3000}
      NODE_ENV: ${NODE_ENV:-development}
      DB_HOST: db
      API_BASE_URL: ${API_BASE_URL:-http://localhost:3000}
      CLIENT_BASE_URL: ${CLIENT_BASE_URL:-http://localhost:4200}
      CLOUD_PROVIDER: ${CLOUD_PROVIDER:-}
      SENTRY_DSN: ${SENTRY_DSN:-}
      SENTRY_HTTP_TRACING_ENABLED: ${SENTRY_HTTP_TRACING_ENABLED:-}
      SENTRY_POSTGRES_TRACKING_ENABLED: ${SENTRY_POSTGRES_TRACKING_ENABLED:-}
      SENTRY_PROFILING_ENABLED: ${SENTRY_PROFILING_ENABLED:-}
      POSTHOG_KEY: ${POSTHOG_KEY:-}
      POSTHOG_HOST: ${POSTHOG_HOST:-}
      POSTHOG_ENABLED: ${POSTHOG_ENABLED:-}
      POSTHOG_FLUSH_INTERVAL: ${POSTHOG_FLUSH_INTERVAL:-}
      JITSU_SERVER_URL: ${JITSU_SERVER_URL:-}
      JITSU_SERVER_WRITE_KEY: ${JITSU_SERVER_WRITE_KEY:-}
      OTEL_EXPORTER_OTLP_TRACES_ENDPOINT: ${OTEL_EXPORTER_OTLP_TRACES_ENDPOINT:-}
      OTEL_EXPORTER_OTLP_HEADERS: ${OTEL_EXPORTER_OTLP_HEADERS:-}
      OTEL_ENABLED: ${OTEL_ENABLED:-}
      OTEL_PROVIDER: ${OTEL_PROVIDER:-}
      GAUZY_GITHUB_CLIENT_ID: ${GAUZY_GITHUB_CLIENT_ID:-}
      GAUZY_GITHUB_CLIENT_SECRET: ${GAUZY_GITHUB_CLIENT_SECRET:-}
      GAUZY_GITHUB_WEBHOOK_URL: ${GAUZY_GITHUB_WEBHOOK_URL:-}
      GAUZY_GITHUB_WEBHOOK_SECRET: ${GAUZY_GITHUB_WEBHOOK_SECRET:-}
      GAUZY_GITHUB_APP_PRIVATE_KEY: ${GAUZY_GITHUB_APP_PRIVATE_KEY:-}
      GAUZY_GITHUB_APP_ID: ${GAUZY_GITHUB_APP_ID:-}
      GAUZY_GITHUB_APP_NAME: ${GAUZY_GITHUB_APP_NAME:-}
      GAUZY_GITHUB_POST_INSTALL_URL: ${GAUZY_GITHUB_POST_INSTALL_URL:-}
      GAUZY_GITHUB_OAUTH_CLIENT_ID: ${GAUZY_GITHUB_OAUTH_CLIENT_ID:-}
      GAUZY_GITHUB_OAUTH_CLIENT_SECRET: ${GAUZY_GITHUB_OAUTH_CLIENT_SECRET:-}
      GAUZY_GITHUB_OAUTH_CALLBACK_URL: ${GAUZY_GITHUB_OAUTH_CALLBACK_URL:-}
      MAGIC_CODE_EXPIRATION_TIME: ${MAGIC_CODE_EXPIRATION_TIME:-}
      APP_NAME: ${APP_NAME:-}
      APP_LOGO: ${APP_LOGO:-}
      APP_SIGNATURE: ${APP_SIGNATURE:-}
      APP_LINK: ${APP_LINK:-}
      APP_EMAIL_CONFIRMATION_URL: ${APP_EMAIL_CONFIRMATION_URL:-}
      APP_MAGIC_SIGN_URL: ${APP_MAGIC_SIGN_URL:-}
      COMPANY_LINK: ${COMPANY_LINK:-}
      COMPANY_NAME: ${COMPANY_NAME:-}
      MAKE_WEBHOOK_URL: ${MAKE_WEBHOOK_URL:-}

    env_file:
      - .env.demo.compose
    entrypoint: './entrypoint.compose.sh'
    command: ['node', 'main.js']
    restart: on-failure
    depends_on:
      db:
        condition: service_healthy
    links:
      - db:${DB_HOST:-db}
    # volumes:
    # - webapp_node_modules:/srv/gauzy/node_modules
    # - api_node_modules:/srv/gauzy/apps/api/node_modules
    ports:
      - '3000:${API_PORT:-3000}'
    networks:
      - overlay

  webapp:
    container_name: webapp
    image: ghcr.io/ever-co/gauzy-webapp:latest
    environment:
      WEB_HOST: ${WEB_HOST:-webapp}
      WEB_PORT: ${WEB_PORT:-4200}
      NODE_ENV: ${NODE_ENV:-development}
      API_BASE_URL: ${API_BASE_URL:-http://localhost:3000}
      CLIENT_BASE_URL: ${CLIENT_BASE_URL:-http://localhost:4200}
      SENTRY_DSN: ${SENTRY_DSN:-}
      SENTRY_TRACES_SAMPLE_RATE: ${SENTRY_TRACES_SAMPLE_RATE:-0.1}
      SENTRY_PROFILE_SAMPLE_RATE: ${SENTRY_PROFILE_SAMPLE_RATE:-1}
      CHATWOOT_SDK_TOKEN: ${CHATWOOT_SDK_TOKEN:-}
      CLOUDINARY_CLOUD_NAME: ${CLOUDINARY_CLOUD_NAME:-}
      CLOUDINARY_API_KEY: ${CLOUDINARY_API_KEY:-}
      GOOGLE_MAPS_API_KEY: ${GOOGLE_MAPS_API_KEY:-}
      GOOGLE_PLACE_AUTOCOMPLETE: ${GOOGLE_PLACE_AUTOCOMPLETE:-false}
      DEFAULT_LATITUDE: ${DEFAULT_LATITUDE:-42.6459136}
      DEFAULT_LONGITUDE: ${DEFAULT_LONGITUDE:-23.3332736}
      DEFAULT_CURRENCY: ${DEFAULT_CURRENCY:-USD}
      GAUZY_GITHUB_CLIENT_ID: ${GAUZY_GITHUB_CLIENT_ID:-}
      GAUZY_GITHUB_APP_NAME: ${GAUZY_GITHUB_APP_NAME:-}
      GAUZY_GITHUB_REDIRECT_URL: ${GAUZY_GITHUB_REDIRECT_URL:-}
      GAUZY_GITHUB_POST_INSTALL_URL: ${GAUZY_GITHUB_POST_INSTALL_URL:-}
      GAUZY_GITHUB_APP_ID: ${GAUZY_GITHUB_APP_ID:-}
      JITSU_BROWSER_URL: ${JITSU_BROWSER_URL:-}
      JITSU_BROWSER_WRITE_KEY: ${JITSU_BROWSER_WRITE_KEY:-}
      DEMO: 'true'
      API_HOST: ${API_HOST:-api}
      API_PORT: ${API_PORT:-3000}
    entrypoint: './entrypoint.compose.sh'
    command: ['nginx', '-g', 'daemon off;']
    env_file:
      - .env.demo.compose
    restart: on-failure
    links:
      - db:${DB_HOST:-db}
      - api:${API_HOST:-api}
    depends_on:
      db:
        condition: service_healthy
      api:
        condition: service_started
    # volumes:
    # - webapp_node_modules:/srv/gauzy/node_modules
    ports:
      - '4200:${UI_PORT:-4200}'
    networks:
      - overlay

volumes:
  # webapp_node_modules:
  # api_node_modules:
  postgres_data: {}

networks:
  overlay:
    driver: bridge
